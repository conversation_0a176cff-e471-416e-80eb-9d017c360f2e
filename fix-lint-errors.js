const fs = require('fs');
const path = require('path');

// List of files with unused error variables that need to be prefixed with underscore
const filesToFix = [
  'src/components/brand/edit-brand-dialog.tsx',
  'src/components/chat/channel-management-modal.tsx',
  'src/components/chat/manage-participants-modal.tsx',
  'src/components/chat/message-input.tsx',
  'src/components/chat/message-item.tsx',
  'src/components/chat/message-list.tsx',
  'src/components/collaboration-hub/brief-form-dialog.tsx',
  'src/components/collaboration-hub/create-collaboration-hub-dialog.tsx',
  'src/components/collaboration-hub/delete-collaboration-hub-dialog.tsx',
  'src/components/collaboration-hub/edit-collaboration-hub-dialog.tsx',
  'src/components/collaboration-hub/hub-header.tsx',
  'src/components/collaboration-hub/manage-participants-dialog.tsx',
  'src/components/collaboration-hub/tabs/briefs-tab.tsx',
  'src/components/collaboration-hub/tabs/chat-tab.tsx',
  'src/components/invoice/delete-invoice-dialog.tsx',
  'src/components/invoice/form-sections/basic-info-section.tsx',
  'src/components/invoice/invoice-form-dialog.tsx',
  'src/components/invoice/invoice-view-dialog.tsx',
  'src/components/invoice/send-invoice-dialog.tsx',
  'src/components/invoice/status-update-dialog.tsx',
  'src/components/media/media-carousel.tsx',
  'src/components/posts/comment-form.tsx',
  'src/components/posts/comment-item.tsx',
  'src/components/posts/post-card.tsx',
  'src/components/posts/post-dialog.tsx',
  'src/components/posts/post-form-dialog.tsx',
  'src/components/posts/post-review-form.tsx',
  'src/hooks/auth/use-auth-initialization.ts',
  'src/hooks/auth/use-logout.ts',
  'src/hooks/chat/use-chat-channels.ts',
  'src/hooks/chat/use-send-message.ts',
  'src/hooks/media/use-media-url-refresh.ts',
  'src/lib/websocket/chat-websocket-client.ts',
  'src/lib/websocket/websocket-context.tsx',
  'src/pages/invoices/invoices.tsx'
];

function fixUnusedErrorVariables(filePath) {
  try {
    const fullPath = path.join(__dirname, 'fe', filePath);
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Replace unused error variables with underscore prefix
    content = content.replace(/} catch \(error\) {/g, '} catch (_error) {');
    content = content.replace(/} catch \(([^)]+error[^)]*)\) {/g, '} catch (_$1) {');
    content = content.replace(/onError: \(error\) =>/g, 'onError: (_error) =>');
    content = content.replace(/onError: \(([^)]+error[^)]*)\) =>/g, 'onError: (_$1) =>');
    content = content.replace(/\.catch\(error =>/g, '.catch(_error =>');
    content = content.replace(/\.catch\(([^)]+error[^)]*) =>/g, '.catch(_$1 =>');
    
    // Fix unused result variables
    content = content.replace(/const result = /g, 'const _result = ');
    content = content.replace(/let result = /g, 'let _result = ');
    
    // Fix unused presence variables
    content = content.replace(/\(([^,]+), presence\)/g, '($1, _presence)');
    
    fs.writeFileSync(fullPath, content);
    console.log(`Fixed: ${filePath}`);
  } catch (err) {
    console.error(`Error fixing ${filePath}:`, err.message);
  }
}

// Fix all files
filesToFix.forEach(fixUnusedErrorVariables);

console.log('Finished fixing unused error variables');
