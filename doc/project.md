# Collaboration Hub

Collaboration Hub is a modern SaaS platform designed for **content creators** and **small agencies** to manage collaborations with brands, streamline content approvals, and handle invoicing — all in one place.

## ✨ Overview

Collaboration Hub simplifies the entire workflow around influencer-brand partnerships, from content submission and review to invoicing and CRM. The platform enables both **internal teams** and **external stakeholders** (e.g., brands and freelance creators) to work together seamlessly through invite-based access and scoped collaboration spaces.

## 🎯 Target Users

- **Independent content creators** managing their own collaborations.
- **Small influencer marketing agencies** coordinating campaigns between creators and brands.
- **Brands** participating as external reviewers and collaborators.

## 🧩 Key Features

### 🧑‍🤝‍🧑 Collaboration Spaces
- Create a **collaboration hub** per project or campaign.
- Invite both internal team members and external participants (brands or creators).
- External access via **magic link** and **One-Time Token (OTT)** login — no account required.
- Upload posts, provide feedback, and iterate directly in the hub.

### 💬 Chat & Content Feedback
- Real-time chat for all participants.
- Structured discussions with comment threads on individual posts.
- Internal-only and scoped discussions for more granular team communication.

### 🧾 Invoicing
- Create and manage professional invoices.
- Link invoices to posts or collaboration hubs for clarity.
- Track status (`draft`, `sent`, `paid`, `overdue`, `voided`) and manually update as needed.
- Send invoices via email with public (tokenized) view link.
- Track delivery and open events.

### 🏷️ Brand CRM
- Manage brands you work with in one place.
- Store company details, invoicing info, and contact persons.
- Use CRM data to auto-fill invoice fields and streamline communication.

## 🔐 Access & Security

- **Account-based multi-tenancy** to isolate data between teams.
- **Role-based access** scoped per account.
- **JWT access and refresh tokens**, issued via Spring Authorization Server.
- External users authenticate via **email-based magic links** using OTT.

## 🛠️ Tech Stack (High-Level)

| Layer       | Tech                                     |
|-------------|------------------------------------------|
| Frontend    | React, Vite, TypeScript, Shadcn UI       |
| Backend     | Spring Boot, jOOQ, Spring Security       |
| Auth        | Spring Authorization Server, OTT login   |
| Database    | PostgreSQL, Flyway migrations            |
| PDF/Email   | Server-side PDF generation & email       |
| Hosting     | Dockerized deployment (details TBD)      |

---

> _Collaboration Hub is built to empower creators and agencies with structure, clarity, and control over their content partnerships._
