# Invoice Backend Implementation Specification

## 1. Data Model

### Primary Tables

#### `invoices`
- **Primary Key:** `id` (BIGINT using sequence)
- **Columns:**
    - `account_id` (bigint, not null) — Tenant identifier for multi-tenancy
    - `invoice_number` (varchar(50), not null) — User-defined pattern, auto-incremented per account
    - `status` (enum: 'draft', 'sent', 'overdue', 'paid') — Invoice status
    - `issuer_snapshot` (jsonb, not null) — Immutable issuer data at creation
    - `recipient_snapshot` (jsonb, not null) — Immutable recipient data at creation
    - `bank_details_snapshot` (jsonb, not null) — Immutable bank details at creation
    - `template` (varchar(50), default 'default') — PDF template name
    - `currency` (varchar(3), default 'EUR') — Currency code
    - `issue_date` (date, not null) — Invoice issue date
    - `due_date` (date, not null) — Payment due date
    - `notes` (text, nullable) — Additional notes
    - `subtotal` (decimal(12,2), not null) — Amount before VAT
    - `total_vat` (decimal(12,2), default 0) — Total VAT amount
    - `total_amount` (decimal(12,2), not null) — Final amount including VAT
    - `deleted_at` (timestamp, nullable) — Soft deletion timestamp
    - `created_at` (timestamp, not null)
    - `updated_at` (timestamp, not null)
- **Constraints:**
    - UNIQUE(`account_id`, `invoice_number`) — Invoice numbers unique per tenant
    - CHECK(`due_date >= issue_date`) — Due date validation
    - CHECK(`subtotal >= 0 AND total_vat >= 0 AND total_amount >= 0`) — Amount validation

#### `invoice_items`
- **Primary Key:** `id` (BIGINT using sequence)
- **Foreign Key:** `invoice_id` references `invoices(id)` ON DELETE CASCADE
- **Columns:**
    - `description` (text, not null) — Item description
    - `quantity` (decimal(10,3), default 1) — Item quantity
    - `unit_price` (decimal(12,2), not null) — Price per unit
    - `vat_rate` (decimal(5,4), default 0) — VAT rate (0-1)
    - `line_total` (decimal(12,2), not null) — Line total before VAT
    - `vat_amount` (decimal(12,2), default 0) — VAT amount for this line
    - `sort_order` (integer, default 0) — Display order
    - `created_at` (timestamp, not null)
- **Constraints:**
    - CHECK(`quantity > 0`) — Positive quantity required
    - CHECK(`unit_price >= 0`) — Non-negative price
    - CHECK(`vat_rate >= 0 AND vat_rate <= 1`) — Valid VAT rate

#### `invoice_recipients`
- **Primary Key:** `id` (BIGINT using sequence)
- **Foreign Key:** `invoice_id` references `invoices(id)` ON DELETE CASCADE
- **Columns:**
    - `email` (varchar(255), not null) — Recipient email address
    - `type` (enum: 'original', 'copy') — Recipient type
    - `source` (enum: 'brand_contact', 'manual') — How recipient was added
    - `brand_contact_id` (bigint, nullable) — References brand_contacts(id)
    - `first_sent_at` (timestamp, nullable) — First delivery timestamp
    - `last_sent_at` (timestamp, nullable) — Last delivery timestamp
    - `send_count` (integer, default 0) — Number of times sent
    - `created_at` (timestamp, not null)
- **Constraints:**
    - UNIQUE(`invoice_id`, `email`) — One entry per email per invoice
    - CHECK(email validation pattern) — Valid email format

#### `invoice_delivery_logs`
- **Primary Key:** `id` (BIGINT using sequence)
- **Foreign Key:** `invoice_id` references `invoices(id)` ON DELETE CASCADE
- **Columns:**
    - `recipient_email` (varchar(255), not null) — Target email
    - `delivery_status` (varchar(50), default 'pending') — Delivery status
    - `message_content` (text, nullable) — Email message content
    - `pdf_version` (varchar(50), nullable) — PDF version sent
    - `is_resend` (boolean, default false) — Whether this was a resend
    - `error_message` (text, nullable) — Error details if failed
    - `created_at` (timestamp, not null)

---

## 2. Multi-Tenancy & Access Control

- All operations are scoped by `account_id` from authenticated user's context
- Invoice numbers are unique per tenant, not globally
- Users can only access invoices within their own account
- Soft-deleted records (`deleted_at` IS NOT NULL) are excluded from normal queries
- Foreign key references (issuer, recipient, bank details) must belong to same account
- 
---

## 3. Business Logic & Validation Rules

### Invoice Status Transitions
- `draft` → `sent`, `paid`
- `sent` → `paid`, `overdue`
- `overdue` → `paid`
- `paid` → No transitions allowed

### Update Permissions by Status
- **Draft:** All fields editable
- **Sent:** Only `notes`, `due_date`, `status` editable
- **Paid/Overdue:** Only `status`, `notes` editable

### Automatic Calculations
- Line totals = `quantity * unit_price`
- VAT amounts = `line_total * vat_rate`
- Invoice subtotal = sum of all line totals
- Invoice total VAT = sum of all VAT amounts
- Invoice total = subtotal + total VAT

### Invoice Number Generation
- User provides initial invoice number (e.g., "INV-2024-001", "2024/001", "A001")
- System auto-increments based on the pattern from the last character/number
- Examples:
    - "INV-2024-001" → "INV-2024-002" → "INV-2024-003"
    - "2024/A" → "2024/B" → "2024/C"
    - "INV001" → "INV002" → "INV003"
- Generated on creation, never changes after that
- Unique constraint per account ensures no duplicates within tenant

---

## 4. API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/invoices` | List all invoices for current account with filtering |
| POST | `/api/invoices` | Create new draft invoice |
| GET | `/api/invoices/{id}` | Get specific invoice with full details |
| PUT | `/api/invoices/{id}` | Update invoice (restrictions by status) |
| DELETE | `/api/invoices/{id}` | Soft delete invoice (draft only) |
| PATCH | `/api/invoices/{id}/status` | Update invoice status |
| GET | `/api/invoices/{id}/preview` | Generate PDF preview |
| POST | `/api/invoices/{id}/send` | Send invoice to recipients |
| POST | `/api/invoices/{id}/resend` | Resend to specific recipients |
| POST | `/api/invoices/{id}/duplicate` | Create duplicate draft invoice |
| GET | `/api/invoices/{id}/delivery-log` | Get delivery history |

---

## 5. Request and Response Examples

### Create Invoice

**Request**
```http
POST /api/invoices
Content-Type: application/json
Authorization: Bearer <token>
```

```json
{
  "issuer_id": 123,
  "recipient_id": 456,
  "bank_details_id": 789,
  "invoice_number": "INV-2024-001",
  "template": "modern",
  "currency": "EUR",
  "issue_date": "2024-05-01",
  "due_date": "2024-05-15",
  "notes": "Thank you for your business",
  "items": [
    {
      "description": "Web Development Services",
      "quantity": 40,
      "unit_price": 85.00,
      "vat_rate": 0.21
    }
  ],
  "recipients": [
    {
      "email": "<EMAIL>",
      "type": "original",
      "source": "brand_contact",
      "brand_contact_id": 456
    }
  ]
}
```

**Response**
```json
{
  "id": 12345,
  "invoice_number": "INV-2024-001",
  "status": "draft",
  "subtotal": 3400.00,
  "total_vat": 714.00,
  "total_amount": 4114.00,
  "currency": "EUR",
  "issue_date": "2024-05-01",
  "due_date": "2024-05-15",
  "next_invoice_number": "INV-2024-002",
  "created_at": "2024-05-01T10:30:00Z",
  "updated_at": "2024-05-01T10:30:00Z"
}
```

---

### List Invoices

**Request**
```http
GET /api/invoices?status=sent&page=1&limit=20&sort_by=created_at&sort_order=desc
Authorization: Bearer <token>
```

**Response**
```json
{
  "data": [
    {
      "id": 12345,
      "invoice_number": "INV-2024-001",
      "status": "sent",
      "recipient_name": "Client Company Ltd",
      "total_amount": 4114.00,
      "currency": "EUR",
      "issue_date": "2024-05-01",
      "due_date": "2024-05-15",
      "days_until_due": 14,
      "created_at": "2024-05-01T10:30:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total_pages": 5,
    "total_count": 87,
    "has_next": true,
    "has_previous": false
  },
  "summary": {
    "total_amount": 125432.50,
    "total_outstanding": 45678.90,
    "counts": {
      "draft": 5,
      "sent": 23,
      "overdue": 8,
      "paid": 51
    }
  }
}
```

---

### Get Invoice Details

**Request**
```http
GET /api/invoices/12345
Authorization: Bearer <token>
```

**Response**
```json
{
  "id": 12345,
  "invoice_number": "INV-2024-001",
  "status": "sent",
  "template": "modern",
  "currency": "EUR",
  "issue_date": "2024-05-01",
  "due_date": "2024-05-15",
  "notes": "Thank you for your business",
  "subtotal": 3400.00,
  "total_vat": 714.00,
  "total_amount": 4114.00,
  "issuer": {
    "name": "Acme Corp Ltd",
    "address": "123 Business St",
    "city": "London",
    "postal_code": "SW1A 1AA",
    "country": "UK",
    "vat_number": "GB123456789",
    "email": "<EMAIL>"
  },
  "recipient": {
    "name": "Client Company Ltd",
    "address": "456 Client Ave",
    "city": "Manchester",
    "postal_code": "M1 1AA",
    "country": "UK",
    "email": "<EMAIL>"
  },
  "bank_details": {
    "bank_name": "HSBC Bank",
    "account_holder": "Acme Corp Ltd",
    "iban": "**********************",
    "swift": "HBUKGB4B"
  },
  "items": [
    {
      "id": 1001,
      "description": "Web Development Services",
      "quantity": 40,
      "unit_price": 85.00,
      "vat_rate": 0.21,
      "line_total": 3400.00,
      "vat_amount": 714.00,
      "sort_order": 0
    }
  ],
  "recipients": [
    {
      "id": 2001,
      "email": "<EMAIL>",
      "type": "original",
      "source": "brand_contact",
      "send_count": 1,
      "last_sent_at": "2024-05-01T14:30:00Z"
    }
  ],
  "public_links": [
    {
      "recipient_email": "<EMAIL>",
      "public_url": "https://app.example.com/invoices/public/abc123def456",
      "accessed_count": 3,
      "last_accessed_at": "2024-05-02T09:15:00Z"
    }
  ],
  "created_at": "2024-05-01T10:30:00Z",
  "updated_at": "2024-05-01T14:30:00Z"
}
```

---

### Update Invoice

**Request**
```http
PUT /api/invoices/12345
Content-Type: application/json
Authorization: Bearer <token>
```

```json
{
  "due_date": "2024-05-20",
  "notes": "Updated payment terms",
  "items": [
    {
      "description": "Web Development Services",
      "quantity": 45,
      "unit_price": 85.00,
      "vat_rate": 0.21
    }
  ]
}
```

**Response**
```json
{
  "id": 12345,
  "invoice_number": "INV-2024-001",
  "status": "draft",
  "subtotal": 3825.00,
  "total_vat": 803.25,
  "total_amount": 4628.25,
  "updated_fields": ["due_date", "notes", "items"],
  "updated_at": "2024-05-01T15:45:00Z"
}
```

---

### Send Invoice

**Request**
```http
POST /api/invoices/12345/send
Content-Type: application/json
Authorization: Bearer <token>
```

```json
{
  "message": "Please find attached your invoice. Payment is due within 14 days.",
  "force_resend": false
}
```

**Response**
```json
{
  "invoice_id": 12345,
  "status": "sent",
  "sent_to": [
    {
      "email": "<EMAIL>",
      "type": "original",
      "delivery_status": "sent",
      "public_link": "https://app.example.com/invoices/public/abc123def456"
    }
  ],
  "skipped": [],
  "failed": [],
  "sent_at": "2024-05-01T14:30:00Z"
}
```

---

### Update Invoice Status

**Request**
```http
PATCH /api/invoices/12345/status
Content-Type: application/json
Authorization: Bearer <token>
```

```json
{
  "status": "paid",
  "note": "Payment received via bank transfer"
}
```

**Response**
```json
{
  "id": 12345,
  "status": "paid",
  "previous_status": "sent",
  "updated_at": "2024-05-10T09:30:00Z"
}
```

---

### Soft Delete Invoice

**Request**
```http
DELETE /api/invoices/12345
Authorization: Bearer <token>
```

**Response**
```http
204 No Content
```

---

### Preview Invoice PDF

**Request**
```http
GET /api/invoices/12345/preview?type=original&format=stream
Authorization: Bearer <token>
```

**Response**
```http
200 OK
Content-Type: application/pdf
Content-Disposition: attachment; filename="INV-2024-001.pdf"
Content-Length: 245760

[PDF Binary Data]
```

---

### Resend Invoice

**Request**
```http
POST /api/invoices/12345/resend
Content-Type: application/json
Authorization: Bearer <token>
```

```json
{
  "recipient_emails": ["<EMAIL>"],
  "message": "Updated invoice with corrected due date",
  "add_new_recipients": [
    {
      "email": "<EMAIL>",
      "type": "copy",
      "source": "manual"
    }
  ]
}
```

**Response**
```json
{
  "invoice_id": 12345,
  "resent_to": ["<EMAIL>"],
  "newly_added": ["<EMAIL>"],
  "failed": [],
  "resent_at": "2024-05-02T10:15:00Z"
}
```

---

### Duplicate Invoice

**Request**
```http
POST /api/invoices/12345/duplicate
Content-Type: application/json
Authorization: Bearer <token>
```

```json
{
  "preserve_recipients": true,
  "update_dates": true,
  "clear_notes": false
}
```

**Response**
```json
{
  "id": 12346,
  "invoice_number": "INV-2024-002",
  "original_invoice_id": 12345,
  "status": "draft",
  "next_invoice_number": "INV-2024-003",
  "created_at": "2024-05-02T11:00:00Z"
}
```

---

### Get Delivery Log

**Request**
```http
GET /api/invoices/12345/delivery-log
Authorization: Bearer <token>
```

**Response**
```json
{
  "invoice_id": 12345,
  "invoice_number": "INV-2024-001",
  "delivery_events": [
    {
      "id": 5001,
      "recipient_email": "<EMAIL>",
      "delivery_status": "delivered",
      "message_content": "Please find attached...",
      "pdf_version": "original",
      "is_resend": false,
      "created_at": "2024-05-01T14:30:00Z"
    },
    {
      "id": 5002,
      "recipient_email": "<EMAIL>",
      "delivery_status": "delivered",
      "message_content": "Updated invoice attached...",
      "pdf_version": "original",
      "is_resend": true,
      "created_at": "2024-05-02T10:15:00Z"
    }
  ],
  "summary": {
    "total_sends": 2,
    "unique_recipients": 1,
    "successful_deliveries": 2,
    "failed_deliveries": 0,
    "first_sent_at": "2024-05-01T14:30:00Z",
    "last_sent_at": "2024-05-02T10:15:00Z"
  }
}
```

---

## 6. Error Handling & Validation

### HTTP Status Codes
- `200 OK` — Successful GET, PUT, PATCH operations
- `201 Created` — Successful POST operations
- `204 No Content` — Successful DELETE operations
- `400 Bad Request` — Invalid input data or business rule violations
- `401 Unauthorized` — Missing or invalid authentication
- `403 Forbidden` — Access denied (wrong tenant/account)
- `404 Not Found` — Invoice not found or doesn't belong to account
- `422 Unprocessable Entity` — Validation errors

### Validation Rules
- Invoice number must be unique within account
- Issue date cannot be in the future
- Due date must be >= issue date
- At least one invoice item required
- At least one recipient required
- All referenced entities (issuer, recipient, bank details) must belong to same account
- Status transitions must follow business rules
- Amounts must be non-negative
- VAT rates must be between 0 and 1

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The provided data is invalid",
    "details": {
      "due_date": ["Due date must be after issue date"],
      "items": ["At least one item is required"],
      "recipients.0.email": ["Invalid email format"]
    }
  }
}
```

---

## 7. Background Jobs & Processing

### PDF Generation
- Asynchronous PDF generation for large invoices
- Template compilation with invoice data
- Multiple format support (A4, Letter)
- Watermark support for different recipient types

### Email Delivery
- Queue-based email sending to handle failures
- Retry logic with exponential backoff
- Delivery status tracking and webhooks
- Bulk sending optimization

### Status Updates
- Automatic overdue detection based on due dates
- Scheduled jobs to update invoice statuses
- Payment matching integration hooks

### Audit Trail
- All invoice changes logged with user context
- Immutable audit records for compliance
- Data retention policies for logs

---

## 8. Performance Considerations

### Database Optimization
- Indexes on frequently queried fields (`account_id`, `status`, `due_date`)
- Partial indexes for active invoices (`deleted_at IS NULL`)
- Composite indexes for filtering combinations
- Read replicas for reporting queries

### Caching Strategy
- Redis caching for frequently accessed invoices
- PDF caching with versioning
- Public link token caching
- Account-scoped cache invalidation

### API Performance
- Pagination limits to prevent large result sets
- Field selection for list endpoints
- Async processing for heavy operations
- Rate limiting per account

---

## 9. Security & Compliance

### Data Protection
- All sensitive data encrypted at rest
- PII data handling in snapshots
- GDPR compliance for recipient data
- Data retention policies

### Access Control
- JWT token validation on all endpoints
- Account-scoped data access only
- Role-based permissions (admin, user, readonly)
- API key authentication for integrations

### Audit Requirements
- EU invoice compliance (invoice numbering, VAT handling)
- Immutable invoice records after sending
- Complete audit trail for all changes
- Export capabilities for compliance reporting

---

## 10. Integration Points

### External Services
- Email service provider (SendGrid, Mailgun)
- PDF generation service

### API Versioning
- Versioned endpoints (`/api/v1/invoices`)
- Backward compatibility guarantees
- Deprecation notices in headers
- Migration guides for version updates

---

This comprehensive backend specification provides a robust, multi-tenant invoice management system with EU compliance, complete audit trails, and frontend-friendly API responses.