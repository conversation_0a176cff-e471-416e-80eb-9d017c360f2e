# Invoice Management Frontend Design Specification

## Main Invoice List Page

### Layout Structure
The primary interface should be a clean, modern dashboard with a **card-based layout** instead of traditional tables. The page structure includes:

- **Header Section**: Page title "Invoices" with total count and key metrics
- **Action Bar**: New invoice button (primary CTA) and bulk actions
- **Filter Panel**: Collapsible sidebar or top bar with filtering options
- **Invoice Cards Grid**: Responsive grid layout (3-4 cards per row on desktop, 1-2 on tablet, 1 on mobile)
- **Pagination Controls**: Bottom pagination with page size selector

### Invoice Card Design
Each invoice displays as a visually distinct card with:

**Card Structure:**
- Clean white/light background with subtle shadows and rounded corners
- Hover effects with slight elevation increase
- Click-to-expand or click-to-navigate functionality

**Card Header:**
- Invoice number prominently displayed (e.g., "INV-2024-001")
- Status badge with color coding:
    - Green: Paid
    - Blue: Sent
    - Orange: Overdue
    - Gray: Draft
- Three-dot menu icon for quick actions

**Card Body:**
- Client name and company in clear typography
- Invoice date and due date with icons
- Total amount in large, bold typography with currency
- Visual due date indicators:
    - Red text/icon if overdue
    - Yellow if due within 3 days
    - Green if paid
    - Gray if draft

**Card Footer:**
- Last activity timestamp (e.g., "Sent 2 days ago", "Created yesterday")
- Quick action buttons (Send, Preview, Edit) that appear on hover
- Delivery status indicators (opened, bounced, etc.)

### Filtering and Search

**Filter Panel Features:**
- **Search Bar**: Full-text search across invoice numbers, client names, and notes
- **Status Filter**: Multi-select checkboxes for all status types
- **Date Range Picker**: For invoice date, due date, and sent date
- **Amount Range**: Min/max amount sliders or input fields
- **Client Filter**: Dropdown with autocomplete for client selection
- **Overdue Toggle**: Quick filter for overdue invoices only

**Filter UI Design:**
- Collapsible sidebar on desktop
- Slide-out drawer on mobile
- Active filter chips displayed above cards
- Clear all filters button
- Save filter presets functionality

### Pagination and Loading
- Standard pagination with "Previous/Next" buttons
- Page numbers with ellipsis for large datasets
- Page size selector (10, 25, 50, 100 items)
- Skeleton loading cards during data fetch
- Infinite scroll option as alternative

## Invoice Detail Page

### Page Layout
- **Breadcrumb Navigation**: Home > Invoices > INV-2024-001
- **Header Section**: Invoice number, status, and primary actions
- **Two-Column Layout**: Invoice details left, actions panel right

### Invoice Information Display

**Left Column - Invoice Details:**
- **Invoice Header Card**:
    - Invoice number and status badge
    - Client information block
    - Invoice and due dates
    - Total amount prominently displayed

- **Line Items Card**:
    - Clean table-like display of services/products
    - Quantity, rate, and totals
    - Subtotal, tax, and final total calculations

- **Notes and Terms Card**:
    - Payment terms
    - Additional notes
    - Internal notes (if any)

**Right Column - Actions Panel:**
- **Status Management Card**:
    - Current status with timeline
    - Status update dropdown with confirmation
    - Status history log

- **Quick Actions Card**:
    - Send/Resend buttons with email preview
    - PDF preview and download
    - Duplicate invoice
    - Edit invoice
    - Delete invoice (with confirmation)

- **Delivery Information Card**:
    - Recipient list with delivery status
    - Email open tracking
    - Public link access logs
    - Delivery attempts history

## Invoice Form (Create/Edit)

### Form Layout
- **Multi-step approach** or **single scrollable form** with clear sections
- **Sticky save/cancel buttons** at bottom or top
- **Auto-save functionality** for drafts
- **Form validation** with inline error messages

### Form Sections

**1. Basic Information Section:**
- Invoice number (auto-generated with manual override option)
- Invoice date (defaults to today)
- Due date (calculated from payment terms)
- Reference number (optional)

**2. Client Information Section:**
- Client search/select dropdown with autocomplete
- "Add new client" quick action
- Display selected client's details
- Billing address display with edit option

**3. Line Items Section:**
- **Add Item Button**: Prominent CTA to add line items
- **Item Rows**: Each with description, quantity, rate, and total
- **Drag-and-drop reordering** of line items
- **Bulk actions**: Delete multiple items, duplicate items
- **Calculations Panel**: Live subtotal, tax, and total updates

**4. Additional Details Section:**
- **Payment Terms**: Dropdown with common terms (Net 30, Due on Receipt, etc.)
- **Notes**: Rich text editor for additional information
- **Internal Notes**: Admin-only notes section
- **Attachments**: File upload area for supporting documents

**5. Recipients Section:**
- **Primary Recipients**: Email addresses for invoice delivery
- **CC Recipients**: Additional recipients for copies
- **Email Template**: Customizable message template
- **Delivery Options**: Send immediately or schedule for later

### Form Interactions
- **Real-time validation** with helpful error messages
- **Conditional fields** that show/hide based on selections
- **Keyboard shortcuts** for power users (Tab navigation, Enter to add items)
- **Mobile-optimized** input fields and interactions

## Status Management Interface

### Status Update Modal
- **Current Status Display**: Clear indication of current state
- **Available Transitions**: Only show valid next states
- **Confirmation Step**: Preview of changes before applying
- **Note Addition**: Optional note field for status changes
- **Timestamp Recording**: Automatic logging of status change time

### Status History Timeline
- **Visual Timeline**: Chronological display of all status changes
- **User Attribution**: Who made each change
- **Notes Display**: Any notes added during status changes
- **Delivery Events**: Integration with email sending events

## Email and Delivery Management

### Send Invoice Modal
- **Recipient Review**: List of recipients with edit options
- **Message Customization**: Editable email template
- **PDF Preview**: Inline preview of invoice PDF
- **Delivery Options**: Immediate send or schedule later
- **Force Resend Toggle**: Option to resend to previously sent recipients

### Delivery Tracking Dashboard
- **Email Status Indicators**: Sent, delivered, opened, bounced
- **Public Link Analytics**: Click tracking and access logs
- **Recipient Management**: Add/remove recipients after sending
- **Resend Options**: Selective resending to specific recipients

## Responsive Design Considerations

### Desktop (1200px+)
- Full three-column layout with sidebar filters
- 3-4 invoice cards per row
- Detailed hover states and animations
- Keyboard navigation support

### Tablet (768px - 1199px)
- Collapsible filter sidebar
- 2-3 cards per row
- Touch-optimized interactions
- Swipe gestures for card actions

### Mobile (< 768px)
- Single column card layout
- Bottom sheet filters
- Full-screen forms
  -Touch-friendly buttons and inputs
- Simplified navigation with bottom tabs

## Performance and User Experience

### Loading States
- **Skeleton Cards**: During initial load
- **Progressive Loading**: Cards appear as data loads
- **Infinite Scroll**: For large datasets
- **Search Debouncing**: Delayed search to reduce API calls

### Error Handling
- **Inline Validation**: Real-time form validation
- **Toast Notifications**: Success/error messages
- **Retry Mechanisms**: For failed operations
- **Graceful Degradation**: Fallbacks for failed features

### Accessibility Features
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Proper ARIA labels
- **High Contrast Mode**: Support for accessibility themes
- **Focus Management**: Clear focus indicators

## Advanced Features

### Bulk Operations
- **Multi-select Cards**: Checkbox selection on cards
- **Bulk Actions Bar**: Appears when items selected
- **Common Operations**: Bulk status updates, delete, export
- **Progress Indicators**: For long-running bulk operations

### Export and Reporting
- **Export Options**: CSV, PDF, Excel formats
- **Custom Date Ranges**: Flexible reporting periods
- **Summary Statistics**: Key metrics dashboard
- **Scheduled Reports**: Email delivery of regular reports


This design specification provides a comprehensive, user-friendly interface that leverages modern web design principles while maintaining the functionality required for professional invoice management.