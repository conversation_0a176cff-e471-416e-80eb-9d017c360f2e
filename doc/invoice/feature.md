# 🧾 Invoices Feature

## Purpose

Enable users to create, manage, send, and track invoices in compliance with EU regulations. Invoices serve as the primary billing and documentation mechanism for transactions, including legally required details, recipient tracking, PDF generation, and audit logging.

## Key Capabilities

- Full CRUD for invoices, scoped per user account.
- Support for EU-compliant invoice data including:
    - Invoice number (auto-generated after first manual entry)
    - Issuer (linked to Account Company)
    - Recipient (brand/contact or free-form email)
    - Bank details
    - Itemized billing lines
    - VAT breakdowns (if applicable)
    - Currency and total amounts
    - Due date, issue date
    - Notes and additional terms
- Assign statuses to invoices:
    - `draft` — being prepared
    - `sent` — issued to recipients (automatically set when emailed)
    - `overdue` — computed when due date passes and invoice is unpaid
    - `paid` — invoice has been settled
- Manual status updates allowed via UI
- Soft deletable invoices (retained in DB, hidden from UI by default)

---

## PDF Generation & Delivery

- Generate downloadable PDF for every invoice
- Support for multiple **hardcoded invoice templates**, selectable per invoice
- Mark invoices as **Original** or **Copy** depending on recipient type
- Recipients can be:
    - Selected from a brand's saved contacts
    - Or entered as manual emails
- PDF contents:
    - Header with issuer logo/info and invoice metadata
    - Footer with terms, payment instructions, legal notes
    - Optional "DRAFT" watermark
- Each recipient gets a **public link** to their invoice (no authentication required)

---

## Recipient & Delivery Tracking

- Email PDFs to recipients (original + copy)
- Each invoice maintains a recipient list:
    - Email address
    - Type: `original` or `copy`
    - Source: `brand_contact` or `manual`
    - Sent timestamp, delivery status
- **Automatic transition to `sent`** when email is first sent
- **Duplicate protection**:
    - Email is sent **only to recipients who have not previously received it**
    - If user **explicitly chooses to re-send**, selected recipients will get the updated PDF again
- Allow users to **configure the email message** (simple, non-technical field)

---

## Audit Logs

- Each (re)send action is **logged per recipient**:
    - Timestamp
    - Email
    - Whether this was a first-time send or a manual resend
- Audit log is queryable per invoice to understand delivery history

---

## Status Management

- Manual status updates supported
- Automatic status update to `sent` on first successful email
- `overdue` is computed automatically based on due date if not paid
- UI provides filters and indicators by status
- Status transitions are not enforced — users may override freely

---

## Data Integrity & Compliance

- Issuer, recipient, and bank data is **snapshotted** at invoice creation time
- Editing the underlying entities does **not affect** historical invoices
- Supports VAT inclusion/exclusion and jurisdiction-specific compliance notes
- Future-proofed for accounting system export (CSV, UBL, etc.)

---

## User Workflow

1. Navigate to **Invoices** screen
2. Click **New Invoice**
3. Select:
    - Issuer (default: account company)
    - Recipient (brand contact or email)
    - Bank details
    - Invoice template (from predefined options)
4. Add line items, VAT, and terms
5. **Preview PDF** for both original and copy
6. Save as `draft` or send to recipients
    - Sending updates status to `sent`
    - Sends emails with PDF and public link
    - Logs delivery event
7. User can manually:
    - Mark as paid
    - Resend to selected recipients
    - Update status or contents
    - Duplicate or delete invoice

---

## Benefits

- Compliant, audit-ready invoices for freelancers, agencies, and businesses
- Clear lifecycle with `draft`, `sent`, `overdue`, `paid` status visibility
- Accurate and reusable issuer/recipient/bank data
- Flexible email and PDF generation with traceable delivery records

---

## Recommended MVP Features

### 🖨 Invoice Templates (Branding & Layout)

- Support multiple **hardcoded invoice templates**
- User can switch template per invoice
- Layouts include differences in logo placement, font, structure, etc.

### 🔁 Save & Duplicate Invoices

- Save invoices in `draft` mode
- Duplicate existing invoice with one click for recurring clients or re-use

### 🔗 Link Invoices for Credit Notes or Refunds

- Allow linking to another invoice:
    - Internally by `invoice_id`
    - Externally by `invoice number` + `issue date`
- Support for credit notes, partial refunds, or corrections

### ⏰ Automatic Overdue Reminders

- Automatically notify **issuers** (your users) when invoices are overdue
- No emails are sent directly to the recipients/customers
- Reminders appear as system notifications and/or email alerts

### 👁 Invoice Preview Before Sending

- Full PDF preview (Original and Copy) before sending
- Prevents accidental errors in amounts, dates, or recipients

### 💬 Configurable Send Message

- Users can **customize the message** that accompanies the invoice email
- Simple WYSIWYG or plain text field — no HTML or technical skills required
