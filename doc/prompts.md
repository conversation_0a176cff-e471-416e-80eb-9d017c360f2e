# 🛠️ Exception Handling Implementation Prompt

## 🎯 Objective

Implement a **centralized exception handling mechanism** for a Spring Boot application using **Spring Security** and **Hibernate Validator**.

## 📦 Requirements

### 1. ✅ Unified Error Response Format

All exceptions must return a consistent JSON payload:

```json
{
  "error": {
    "code": "validation_failed",
    "message": "Validation failed",
    "details": [
      {
        "field": "email",
        "code": "field_invalid_format",
        "message": "Email must be valid",
        "params": {}
      },
      {
        "field": "password",
        "code": "field_too_short",
        "message": "This field must be at least 8 characters long",
        "params": {
          "min": 8
        }
      },
      {
        "field": "username",
        "code": "field_too_long",
        "message": "This field must be at most 25 characters long",
        "params": {
          "max": 25
        }
      },
      {
        "field": "name",
        "code": "field_invalid_length",
        "message": "This field must be between 3 and 40 characters long",
        "params": {
          "min": 3,
          "max": 40
        }
      },
      {
        "field": "firstName",
        "code": "field_required",
        "message": "This field is required",
        "params": {}
      }
    ]
  }
}
```

- The `code` field (both top-level and per-field) is used for frontend i18n translation.
- The `message` is a fallback for when no translation exists.
- The `details` array is optional and used for field-level validation errors.

### 2. 📍 Scope of Handling

Support exception handling for the following:

- ✅ **Custom application exceptions**  
  Should return appropriate HTTP status codes and use the unified error response format.

- ✅ **Spring Boot framework exceptions**  
  For example: `MethodArgumentTypeMismatchException`, `HttpMessageNotReadableException`, etc.

- ✅ **Hibernate Validator (`javax.validation`) exceptions**  
  Must extract validation metadata like `min`, `max`, etc. and include them in `params`.

- ✅ **Spring Security exceptions**  
  Must include handling for:
    - `AuthenticationException`
    - `AccessDeniedException`
    - Exceptions thrown before the request reaches the controller (e.g. in filters)

### 3. 🧱 Design Requirements

- Use an `enum` to define and manage error codes (e.g. `VALIDATION_FAILED`, `FIELD_REQUIRED`, etc.).
- Implement exception handling with `@ControllerAdvice` for general and validation errors.
- For Spring Security exceptions:
    - Create a custom `AuthenticationEntryPoint`
    - Create a custom `AccessDeniedHandler`
- Follow clean architecture principles.
- The solution should be:
    - Reusable
    - Extensible
    - Testable
    - Aligned with Spring Boot best practices and conventions

### 4. 📌 Parameter Mapping

For validation errors, extract annotation values and map them into the `params` field of the response. For example:

```java
@Size(min = 3, max = 40)
private String name;
```

Should produce:

```json
{
  "field": "name",
  "code": "field_invalid_length",
  "message": "This field must be between 3 and 40 characters long",
  "params": {
    "min": 3,
    "max": 40
  }
}
```

## 🧠 Notes

- If anything is unclear or you notice trade-offs or missing pieces, please ask before making assumptions.
- Assume the frontend team will rely on consistent and structured error payloads for displaying and translating error messages.


### Authentication - access/refresh token
# 🔐 Access & Refresh Token Authentication System

## 👤 You Are

A **Principal Software Engineer** responsible for implementing secure, scalable authentication for a multi-tenant SaaS platform using **Spring Boot**, **Spring Security**, and **Spring Authorization Server**.

---

## 🎯 Objective

Implement a **JWT-based access token** and **opaque refresh token** solution with:

- Web (React) clients now, **mobile support later**
- All auth logic lives in the **same Spring Boot app** as business logic
- Built-in **refresh token rotation**
- Support for **multi-tenancy**, scoped by `account_id`

---

## 📦 Dependencies

Use the official Spring stack (no custom logic unless necessary):

```kotlin
implementation("org.springframework.boot:spring-boot-starter-security")
implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")
implementation("org.springframework.boot:spring-boot-starter-oauth2-authorization-server")
```

---

## 🔐 Authentication Requirements

### ✅ JWT Access Tokens
- Must include these claims:
    - `sub`: User ID (BIGINT)
    - `email`: User email
    - `display_name`
    - `role`: User's role
    - `account_id`: Tenancy scope
    - `internal`: Boolean indicating internal vs. external user

### ✅ Refresh Tokens
- Opaque, random token (not JWT)
- Stored in the database
- Supports **refresh token rotation**
- Must track:
    - `issued_at`, `expires_at`
    - `revoked`
    - `replaced_by` token
    - `user_agent`, `ip_address`
    - `metadata` (optional JSON field)
- Configurable TTL and absolute lifetime

### ✅ OWASP Compliance
Follow **OWASP** and security best practices:

- ✅ Passwords: use `BCryptPasswordEncoder`
- ✅ Avoid token leakage through logs
- ✅ HTTPS-only cookie support for frontend (if used) (consider development environment where we cant use https)
- ✅ Rate limiting on login/refresh endpoints
- ✅ CSRF protection (where applicable)
- ✅ Token audience (`aud`) and issuer (`iss`) validation
- ✅ Input validation and proper exception handling (no raw stack traces)
- ✅ JWTs must have `exp`, `iat`, `nbf`, `jti`

---

## 🧱 Database Schema

### 1. `account` Table

```sql
CREATE TABLE account (
  id BIGINT PRIMARY KEY,
  name VARCHAR(255) NOT NULL
);
```

---

### 2. `users` Table

```sql
CREATE TABLE users (
  id BIGINT PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  display_name VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL,
  account_id BIGINT NOT NULL REFERENCES account(id),
  internal BOOLEAN NOT NULL DEFAULT true,
  enabled BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now()
);
```

---

### 3. `refresh_token` Table

```sql
CREATE TABLE refresh_token (
  id BIGINT PRIMARY KEY,
  token VARCHAR(255) UNIQUE NOT NULL,
  user_id BIGINT NOT NULL REFERENCES users(id),
  issued_at TIMESTAMP NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  revoked BOOLEAN NOT NULL DEFAULT false,
  replaced_by BIGINT REFERENCES refresh_token(id),
  user_agent VARCHAR(255),
  ip_address VARCHAR(255),
  metadata JSONB
);
```

---

## 🔐 Roles & Permissions

```java
public enum Role {
    ADMIN, USER // Extend as needed
}

public enum Permission {
    // Add permissions here
}
```

---

## 🧾 Authenticated User POJO

Create a reusable Java POJO to extract claims from access tokens:

```java
public class AuthenticatedUser {
    Long id;
    String email;
    String displayName;
    Role role;
    Long accountId;
    boolean internal;
}
```

- Accessible via `@AuthenticationPrincipal` or a custom security context

---

## ⚙️ Configuration

Make all of the following **externally configurable**:

- Access token TTL
- Refresh token TTL
- Absolute refresh token lifetime
- Rotation behavior (enabled/disabled)
- JWT issuer, audience
- Signature algorithm
- Clock skew

---

## ❓ Clarifications

- If anything is unclear or could be implemented more securely or efficiently, **please raise questions**
- Use Spring conventions before custom implementations



