# 🏢 Account Companies Feature

## Purpose

Allow users to store and manage their own company profiles containing official business details. These profiles serve as the issuer information on invoices, ensuring consistent, accurate, and reusable data while minimizing repetitive entry.

## Key Capabilities

- Manage multiple company profiles per user account.
- Store essential and optional company information, including:
    - Company name (required)
    - Company address (street, city, postal code, country)
    - VAT / Tax Identification Number(s)
    - Registration number (optional)
    - Phone number and email address
    - Bank details (optional)
    - Website URL (optional)
    - Additional notes
- Select a default company profile to pre-fill invoice issuer fields.
- Allow switching issuer company per invoice when necessary.
- Support multi-tenancy by scoping company profiles per user account.
- Enforce access control so only users within the same account can access these company profiles.

## Data Reusability & Integrity

- Company data is stored centrally to avoid repetitive entry and ensure consistency.
- Updates to company profiles affect all future invoices using that company as the issuer.
- Historical invoices retain the original issuer data captured at the time of creation to maintain auditability and accuracy.

## User Workflow Changes

- Users can create, update, and manage multiple company profiles in their account settings.
- Users choose a default company profile to simplify invoice creation.
- During invoice creation, the issuer fields auto-fill based on the selected company profile.
- Users can override the default issuer selection on a per-invoice basis if needed.

## Benefits

- Ensures consistent and professional invoice issuer data.
- Reduces manual entry and errors during invoice creation.
- Supports multiple business entities for agencies or multi-company users.
- Maintains data integrity for auditing and compliance.
