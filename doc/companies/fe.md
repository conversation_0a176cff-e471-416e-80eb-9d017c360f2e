# Frontend Documentation — Account Companies Feature

## Overview

The **Account Companies** feature allows users to manage company profiles linked to their account. These companies serve as issuers or recipients on invoices and other documents, ensuring accurate and consistent company information across the platform.

---

## 1. Routes & Navigation

| Route                  | Purpose                              | Access                                 |
|------------------------|------------------------------------|--------------------------------------|
| `/account-companies`   | List all companies                  | Logged-in users with **read** permission  |
| `/account-companies/new` | Create a new company               | Users with **write** permission       |
| `/account-companies/:id` | View and edit a specific company  | Users with **read** (view), **write** (edit) permission |

- Add an **Account Companies** link in the main navigation or user sidebar.
- Routes are protected and require authentication.
- Enforce role-based access control per route and UI actions.

---

## 2. UI Components & Pages

### 2.1 Account Companies List Page (`/account-companies`)

- **Layout:** Responsive card layout for clear, accessible display.
- **Each Card Displays:**
    - Company Name (prominent)
    - Company Address (street, city, postal code, country)
    - VAT Number (if applicable)
    - Contact Email
    - Action buttons (Edit, Delete) shown only if user has **write** permission.
- **Mobile Responsiveness:**
    - Cards stack vertically on narrow/mobile screens.
    - Use truncation or expandable sections for long addresses.
- **Features:**
    - "+ Add Company" button visible only to users with **write** access.
    - Soft delete with confirmation modal for company removal.
    - Empty state with friendly message encouraging adding companies.
- **UX:**
    - Loading spinner during data fetch.
    - Display error message if fetching fails.

### 2.2 Account Company Form Page (`/account-companies/new` and `/account-companies/:id`)

- **Form Fields:**
    - Company Name (required)
    - Address fields (street, city, postal code, country) (required)
    - VAT Number (optional)
    - Contact Email (optional, email validation)
    - Phone Number (optional)
- **Access Control:**
    - Editable fields for users with **write** permission.
    - Read-only view for users with **read** permission only; no submit buttons.
- **UX:**
    - Client-side validation with inline error feedback.
    - Tooltips or helper text for complex fields.
    - Save button disabled if the form is invalid or during saving.
    - Cancel button navigates back to `/account-companies`.
    - On success: redirect to list with success toast.
    - On failure: show error without clearing inputs.

### 2.3 Soft Delete Confirmation Modal

- Available only for users with **write** permission.
- **Content:**
    - Confirmation text: “Are you sure you want to delete company ‘[Company Name]’? This action can only be undone by an admin.”
    - `Cancel` and `Delete` buttons.
- **UX:**
    - Dismissible modal.
    - On successful deletion, optimistically remove the company card from the list.

---

## 3. State Management

- Use React Query or similar for server state handling.
- Cache companies list and invalidate cache on create/update/delete.
- Manage form state locally.
- Provide toast notifications or alerts on success/error events.

---

## 4. API Integration

- Use authenticated bearer tokens for all API requests.
- Enforce role-based access control both on frontend and backend.
- Implement optimistic UI updates on deletion.
- Handle loading, success, and error states gracefully.

---

## 5. Accessibility & Responsiveness

- Cards and forms must be fully keyboard accessible.
- Use proper labels and ARIA attributes on inputs, buttons, and modals.
- Responsive design:
    - Desktop: multi-column card grid.
    - Tablet/Mobile: stacked single column.
- Manage focus appropriately on modals and after form submission.

---

## 6. Visual Style & Consistency

- Follow project design system (e.g., Shadcn UI).
- Consistent spacing, typography, and colors.
- Use appropriate icons on action buttons (edit, delete).
- Provide clear feedback on button presses and disabled states.

---

## 7. Error Scenarios

- Display inline error messages and/or banner alerts for:
    - API fetch failures.
    - Save/update failures.
    - Delete failures.
- Allow retry where possible.
- Provide contact/support info for persistent issues.

---

## 8. Summary

- Responsive card-based UI for listing companies.
- Role-based **read/write** access controlling UI elements and interactivity.
- Clear user feedback with loading and error states.
- Smooth API integration with authentication and permissions.
- Accessibility and mobile-first design principles.

---

Let me know if you want me to generate React component suggestions or UI prototype sketches for this!
