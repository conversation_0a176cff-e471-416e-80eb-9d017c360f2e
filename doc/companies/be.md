
# Backend API & Logic for Account Companies (with Soft Deletion)

## 1. Data Model

- **Table:** `account_companies`
- **Primary Key:** `id` (bigint)
- **Columns:**
    - `account_id` (bigint, not null) — Tenant identifier for multi-tenancy
    - `company_name` (string, not null) — Legal name of the company
    - `address_street` (string, nullable) — Street address
    - `address_city` (string, nullable)
    - `address_postal_code` (string, nullable)
    - `address_country` (string, nullable)
    - `vat_number` (string, nullable) — VAT or tax identification number
    - `registration_number` (string, nullable) — Business registration number (optional)
    - `phone` (string, nullable)
    - `email` (string, nullable)
    - `website` (string, nullable)
    - `deleted_at` (timestamp, nullable) — Soft deletion timestamp
    - `created_at` (timestamp, not null)
    - `updated_at` (timestamp, not null)

---

## 2. Multi-Tenancy & Access Control

- All operations are scoped by `account_id`.
- Users belong to exactly one `account` (tenant).
- Queries only return records where `account_id` matches the current user’s account.
- Soft-deleted records (`deleted_at` IS NOT NULL) are excluded from normal queries.
- Admin or audit endpoints can optionally include soft-deleted records.

---

## 3. CRUD Operations & Business Logic

### Create Account Company

- **Input:** `company_name` (required), address fields, `vat_number`, etc.
- Validate that `company_name` is not empty.
- Insert a new record with the current user’s `account_id`.
- Return the created account company.

### Read Account Companies

- **List:** Return all account companies for the user’s `account_id` where `deleted_at` IS NULL.
- **Single Item:** Return account company by `id` if it belongs to user’s `account_id` and is not soft deleted.

### Update Account Company

- Allow update only if the company belongs to user’s `account_id` and is not soft deleted.
- Validate input fields.
- Update specified fields and `updated_at`.

### Soft Delete Account Company

- Mark account company as deleted by setting `deleted_at` to current timestamp.
- Only allowed for companies owned by user’s `account_id`.
- Soft-deleted companies are excluded from normal queries and UI.


---

## 4. API Endpoints

| Method | Endpoint                       | Description                                         |
|--------|-------------------------------|---------------------------------------------------|
| GET    | `/api/account-companies`       | List all active (not soft deleted) account companies for current account |
| POST   | `/api/account-companies`       | Create a new account company for current account  |
| GET    | `/api/account-companies/{id}`  | Get specific account company by ID (if owned and active) |
| PUT    | `/api/account-companies/{id}`  | Update an account company by ID (if owned and active) |
| DELETE | `/api/account-companies/{id}`  | Soft delete an account company by ID (if owned)   |

---

## 5. Error Handling & Validation

- Return `404 Not Found` if account company does not exist or does not belong to the user.
- Return `400 Bad Request` for invalid input (missing `company_name`).
- Return `403 Forbidden` if attempting to access or modify company outside user’s account.
- Return appropriate success codes (`200`, `201`, `204`) for successful operations.

---

## 6. Additional Notes

- All date/time fields use UTC timestamps.
- Soft deletion ensures that historical invoices linked to these companies remain accurate.
- API responses exclude soft-deleted companies by default.
- Pagination and filtering can be added to list endpoint as needed.
- Add read and write permissions to restrict API usage.

