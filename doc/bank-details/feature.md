# 🏦 Bank Details Feature (with Soft Deletion)

## Purpose

Allow users to store and manage bank account information for invoices, ensuring consistent payment details while minimizing repetitive data entry.

## Key Capabilities

- Manage multiple bank accounts per account.
- Store required and optional fields: name (required), bank name, IBAN, BIC/SWIFT.
- Link bank details to invoices for automatic inclusion on PDF invoices.
- Support multi-tenancy by scoping data per account.
- Enforce access control: only users within the same account can access these details.
- **Soft deletion** of bank details to preserve historical data and maintain data integrity.

## Soft Deletion Behavior

- Instead of permanently deleting a bank detail record, the system marks it as **deleted** by setting a `deleted_at` timestamp.
- Soft-deleted bank details are:
    - **Excluded** from all normal list views and selection dropdowns in the UI.
    - **Retained** in the database to maintain referential integrity (especially if linked to past invoices).
    - Available for potential restoration by admins or support (optional feature).
- The API filters out soft-deleted bank details unless explicitly requested.
- When a bank detail is soft deleted:
    - Existing invoices linked to it continue referencing the original bank detail without disruption.
    - New invoices cannot select soft-deleted bank details.

## Data Model Additions

- Add a nullable `deleted_at` timestamp column to track soft deletion.
- A non-null value means the record is deleted; a null value means active.

## User Workflow Changes

- When a user deletes a bank detail, the system confirms and then sets `deleted_at` without removing the record.
- Deleted bank details disappear from user interfaces.
- Undo or restore options can be considered for future enhancements.

## Benefits

- Prevents accidental data loss.
- Preserves historical invoice accuracy.
- Supports audit and compliance requirements.
- Enables safe cleanup without breaking invoice references.
