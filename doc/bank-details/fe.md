# Frontend Documentation — Bank Details Feature

## Overview

The **Bank Details** feature lets users manage their bank accounts information easily. This data is used to autofill invoicing forms and maintain consistency.

---

## 1. Routes & Navigation

| Route                | Purpose                              | Access           |
|----------------------|------------------------------------|------------------|
| `/bank-details`      | List all bank details               | Logged-in users with **read** permission  |
| `/bank-details/new`  | Create a new bank detail            | Users with **write** permission  |
| `/bank-details/:id`  | View and edit a specific bank detail | Users with **read** for viewing, **write** for editing  |

- Include a “Bank Details” link in the main navigation or user sidebar.
- Routes are protected and require authentication.
- Role-based access controls are enforced per route and UI actions.

---

## 2. UI Components & Pages

### 2.1 Bank Details List Page (`/bank-details`)

- **Layout:** Responsive card layout instead of tables.
- **Each Card Displays:**
    - **Name** (prominent)
    - **Bank Name**
    - **IBAN**
    - **BIC/SWIFT**
    - Action buttons (Edit, Delete) shown only if user has **write** permission.
- **Mobile Responsiveness:**
    - Cards stack vertically on narrow/mobile screens.
    - Use collapsible sections or truncation with “Show more” for long IBAN/BIC.
- **Features:**
    - Button to create a new bank detail (`+ Add Bank Detail`) visible only to users with **write** access.
    - Soft delete option with confirmation modal on each card for users with **write** permission.
    - Empty state: show a friendly message and prompt to add a bank detail.
- **UX:**
    - Loading spinner while fetching.
    - Error message on fetch failure.

---

### 2.2 Bank Detail Form Page (`/bank-details/new` and `/bank-details/:id`)

- **Form Fields:**
    - **Name** (required)
    - **Bank Name** (optional)
    - **IBAN** (optional, with input mask and validation)
    - **BIC/SWIFT** (optional, validated)
- **Access Control:**
    - If user has **write** permission, form fields are editable.
    - If user only has **read** permission, show fields in read-only mode without submit buttons.
- **UX:**
    - Client-side validation with inline error messages.
    - Tooltips or help text for each field.
    - Save button disabled when invalid or while saving.
    - Cancel button returns to `/bank-details`.
    - On successful save, redirect to list page with success toast.
    - On failure, display error without clearing form.

---

### 2.3 Soft Delete Confirmation Modal

- **Shown only to users with** **write** permission.
- **Content:**
    - Confirmation text: “Are you sure you want to delete bank detail ‘[Name]’? This can be undone only by an admin.”
    - `Cancel` and `Delete` buttons.
- **UX:**
    - Dismissible modal.
    - On delete success, remove the card from the list optimistically.

---

## 3. State Management

- Use React Query or similar for fetching, caching, and mutations.
- Cache bank details list and invalidate after create/update/delete.
- Form state managed locally.
- Show toasts or alerts for success and error states.

---

## 4. API Integration

- All API calls use authenticated bearer tokens.
- Enforce role permissions both on frontend UI and backend API.
- Implement optimistic UI updates for delete action.
- Handle loading, error, and success states gracefully.

---

## 5. Accessibility & Responsiveness

- Cards and forms are fully keyboard accessible.
- Use proper labels and ARIA attributes on inputs and modals.
- Responsive card grid adapts to screen size:
    - Desktop: multi-column grid
    - Tablet/mobile: single column stacked cards
- Focus management on modals and form submission.

---

## 6. Visual Style & Consistency

- Follow project’s design system (e.g., Shadcn UI).
- Use consistent spacing, colors, and typography.
- Use icons on action buttons (edit, delete).
- Provide feedback for button presses and disabled states.

---

## 7. Error Scenarios

- Display inline and banner errors for:
    - API fetch failures.
    - Save/update failures.
    - Delete failures.
- Allow retry where possible.
- Provide contact support info for persistent errors.

---

## 8. Summary

- **Card-based responsive UI** for listing bank details.
- Role-based **read/write permissions** controlling visibility and interactivity.
- Clear user feedback and loading states.
- Seamless API integration with proper auth and error handling.
- Accessibility and mobile-friendly design.

---

Let me know if you want a React component structure or UI prototypes for this!
