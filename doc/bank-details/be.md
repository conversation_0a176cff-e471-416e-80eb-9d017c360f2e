
# Backend API & Logic for Bank Details

## 1. Data Model

- **Table:** `bank_details`
- **Primary Key:** `id` (bigint)
- **Columns:**
    - `account_id` (bigint, not null) — Tenant identifier for multi-tenancy
    - `name` (string, not null) — Bank account label
    - `bank_name` (string, nullable) — Bank's name
    - `iban` (string, nullable) — International Bank Account Number
    - `bicswift` (string, nullable) — BIC or SWIFT code
    - `deleted_at` (timestamp, nullable) — Soft deletion timestamp
    - `created_at` (timestamp, not null)
    - `updated_at` (timestamp, not null)

---

## 2. Multi-Tenancy & Access Control

- All operations are scoped by `account_id`.
- Users belong to exactly one `account` (tenant).
- Queries only return records where `account_id` matches the current user’s account.
- Soft-deleted records (`deleted_at` IS NOT NULL) are excluded from normal queries.
- Admin or audit endpoints can optionally include soft-deleted records.

---

## 3. CRUD Operations & Business Logic

### Create Bank Detail

- **Input:** `name` (required), `bank_name`, `iban`, `bicswift`
- Validate that `name` is not empty.
- Optionally validate IBAN and BIC format.
- Insert a new record with the current user’s `account_id`.
- Return the created bank detail.

### Read Bank Details

- **List:** Return all bank details for the user’s `account_id` where `deleted_at` IS NULL.
- **Single Item:** Return bank detail by `id` if it belongs to user’s `account_id` and is not soft deleted.

### Update Bank Detail

- Allow update only if the bank detail belongs to user’s `account_id` and is not soft deleted.
- Validate input fields.
- Update specified fields and `updated_at`.

### Soft Delete Bank Detail

- Mark bank detail as deleted by setting `deleted_at` to current timestamp.
- Only allowed for bank details owned by user’s `account_id`.
- Soft-deleted bank details are excluded from normal queries and UI.

---

## 4. API Endpoints

| Method | Endpoint                  | Description                                       |
|--------|---------------------------|-------------------------------------------------|
| GET    | `/api/bank-details`        | List all active (not soft deleted) bank details for current account |
| POST   | `/api/bank-details`        | Create a new bank detail for current account     |
| GET    | `/api/bank-details/{id}`   | Get specific bank detail by ID (if owned and active) |
| PUT    | `/api/bank-details/{id}`   | Update a bank detail by ID (if owned and active) |
| DELETE | `/api/bank-details/{id}`   | Soft delete a bank detail by ID (if owned)       |

---

## 5. Request and Response Examples

### Create Bank Detail

**Request**

```json
POST /api/bank-details
Content-Type: application/json

{
  "name": "Main Company Account",
  "bank_name": "Big Bank",
  "iban": "**********************",
  "bicswift": "BYLADEM1001"
}
```

**Response**

```json
201 Created
Content-Type: application/json

{
  "id": 123,
  "account_id": 45,
  "name": "Main Company Account",
  "bank_name": "Big Bank",
  "iban": "**********************",
  "bicswift": "BYLADEM1001",
  "deleted_at": null,
  "created_at": "2025-05-26T12:00:00Z",
  "updated_at": "2025-05-26T12:00:00Z"
}
```

---

### List Bank Details

**Request**

```http
GET /api/bank-details
Authorization: Bearer <token>
```

**Response**

```json
200 OK
Content-Type: application/json

[
  {
    "id": 123,
    "account_id": 45,
    "name": "Main Company Account",
    "bank_name": "Big Bank",
    "iban": "**********************",
    "bicswift": "BYLADEM1001",
    "deleted_at": null,
    "created_at": "2025-05-26T12:00:00Z",
    "updated_at": "2025-05-26T12:00:00Z"
  },
  {
    "id": 124,
    "account_id": 45,
    "name": "Savings Account",
    "bank_name": "Savings Bank",
    "iban": "**********************",
    "bicswift": "NWBKGB2L",
    "deleted_at": null,
    "created_at": "2025-05-20T09:00:00Z",
    "updated_at": "2025-05-20T09:00:00Z"
  }
]
```

---

### Get Single Bank Detail

**Request**

```http
GET /api/bank-details/123
Authorization: Bearer <token>
```

**Response**

```json
200 OK
Content-Type: application/json

{
  "id": 123,
  "account_id": 45,
  "name": "Main Company Account",
  "bank_name": "Big Bank",
  "iban": "**********************",
  "bicswift": "BYLADEM1001",
  "deleted_at": null,
  "created_at": "2025-05-26T12:00:00Z",
  "updated_at": "2025-05-26T12:00:00Z"
}
```

---

### Update Bank Detail

**Request**

```json
PUT /api/bank-details/123
Content-Type: application/json

{
  "name": "Main Company Account Updated",
  "bank_name": "Big Bank",
  "iban": "**********************",
  "bicswift": "BYLADEM1001"
}
```

**Response**

```json
200 OK
Content-Type: application/json

{
  "id": 123,
  "account_id": 45,
  "name": "Main Company Account Updated",
  "bank_name": "Big Bank",
  "iban": "**********************",
  "bicswift": "BYLADEM1001",
  "deleted_at": null,
  "created_at": "2025-05-26T12:00:00Z",
  "updated_at": "2025-05-26T15:00:00Z"
}
```

---

### Soft Delete Bank Detail

**Request**

```http
DELETE /api/bank-details/123
Authorization: Bearer <token>
```

**Response**

```http
204 No Content
```

---

## 6. Error Handling & Validation

- Return `404 Not Found` if bank detail does not exist or does not belong to the user.
- Return `400 Bad Request` for invalid input (missing `name`, invalid IBAN/BIC).
- Return `403 Forbidden` if attempting to access or modify bank detail outside user’s account.
- Return appropriate success codes (`200`, `201`, `204`) for successful operations.

---

## 7. Additional Notes

- All date/time fields use UTC timestamps.
- Soft deletion ensures invoice history remains intact by preserving referenced bank details.
- API responses exclude soft-deleted bank details by default.
- Pagination and filtering can be added to list endpoint as needed.
- Add read and write permissions to restrict API usage.
