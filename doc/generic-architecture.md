
# SaaS Project Architecture

---

## 1. Spring Boot Backend Folder Structure

```
backend/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── yourcompany/
│   │   │           └── saasapp/
│   │   │               ├── config/                   # Global Spring configurations (security, OAuth2, etc.)
│   │   │               │   ├── SecurityConfig.java
│   │   │               │   └── SwaggerConfig.java
│   │   │               ├── exception/                # Global exceptions and handlers
│   │   │               │   ├── ApiException.java
│   │   │               │   └── GlobalExceptionHandler.java
│   │   │               ├── modules/                   # Decoupled feature modules
│   │   │               │   ├── auth/
│   │   │               │   │   ├── controller/        # REST API controllers
│   │   │               │   │   │   └── AuthController.java
│   │   │               │   │   ├── dto/               # Data Transfer Objects
│   │   │               │   │   │   ├── LoginRequest.java
│   │   │               │   │   │   └── LoginResponse.java
│   │   │               │   │   ├── model/             # JPA Entities or domain models
│   │   │               │   │   │   └── User.java
│   │   │               │   │   ├── repository/        # Repository interfaces
│   │   │               │   │   │   └── UserRepository.java
│   │   │               │   │   ├── service/           # Business logic services
│   │   │               │   │   │   └── AuthService.java
│   │   │               │   │   └── security/          # Security-related classes for this module
│   │   │               │   │       └── JwtTokenProvider.java
│   │   │               │   ├── invoices/
│   │   │               │   │   ├── controller/
│   │   │               │   │   │   └── InvoiceController.java
│   │   │               │   │   ├── dto/
│   │   │               │   │   │   ├── InvoiceRequest.java
│   │   │               │   │   │   └── InvoiceResponse.java
│   │   │               │   │   ├── model/
│   │   │               │   │   │   └── Invoice.java
│   │   │               │   │   ├── repository/
│   │   │               │   │   │   └── InvoiceRepository.java
│   │   │               │   │   └── service/
│   │   │               │   │       └── InvoiceService.java
│   │   │               │   ├── collaboration/
│   │   │               │   │   ├── controller/
│   │   │               │   │   │   └── CollaborationController.java
│   │   │               │   │   ├── dto/
│   │   │               │   │   │   ├── CollaborationRequest.java
│   │   │               │   │   │   └── CollaborationResponse.java
│   │   │               │   │   ├── model/
│   │   │               │   │   │   └── CollaborationHub.java
│   │   │               │   │   ├── repository/
│   │   │               │   │   │   └── CollaborationRepository.java
│   │   │               │   │   └── service/
│   │   │               │   │       └── CollaborationService.java
│   │   │               │   └── ... other modules ...
│   │   │               ├── util/                      # Utility classes and helpers
│   │   │               │   └── DateUtils.java
│   │   │               └── SaasAppApplication.java    # Main Spring Boot Application class
│   │   └── resources/
│   │       ├── db/
│   │       │   └── migration/                         # Flyway migration scripts
│   │       ├── application.yml                        # Configuration file(s)
│   │       ├── static/                                # Static resources if any
│   │       └── templates/                             # Email or Thymeleaf templates if any
│   └── test/
│       └── java/
│           └── com/
│               └── yourcompany/
│                   └── saasapp/
│                       ├── modules/
│                       │   ├── auth/
│                       │   ├── invoices/
│                       │   └── collaboration/
│                       └── ... unit and integration tests ...
├── build.gradle (or pom.xml)                          # Build configuration
└── README.md
```

### File Naming Conventions (Backend)

- Classes are named in **PascalCase** (e.g., `InvoiceService.java`)
- Controllers end with `Controller` (e.g., `AuthController.java`)
- DTOs end with `Request` or `Response` suffix (e.g., `LoginRequest.java`)
- Repositories end with `Repository` (e.g., `UserRepository.java`)
- Services end with `Service` (e.g., `InvoiceService.java`)
- Exceptions end with `Exception` (e.g., `ApiException.java`)
- Configuration classes end with `Config` (e.g., `SecurityConfig.java`)

---

## 2. React + Vite Frontend Folder Structure

```
frontend/
├── public/                            # Static files (favicon, index.html, manifest, robots.txt)
├── src/
│   ├── assets/                       # Images, fonts, icons
│   ├── components/                   # Reusable UI components
│   │   ├── Button.tsx
│   │   ├── Modal.tsx
│   │   └── ...
│   ├── features/                     # Feature modules (domain-specific)
│   │   ├── auth/
│   │   │   ├── Login.tsx
│   │   │   ├── Register.tsx
│   │   │   └── authSlice.ts          # Redux slice or React Query hooks
│   │   ├── invoices/
│   │   │   ├── InvoiceList.tsx
│   │   │   ├── InvoiceDetail.tsx
│   │   │   └── invoiceApi.ts         # API functions related to invoices
│   │   ├── collaboration/
│   │   │   └── CollaborationHub.tsx
│   │   └── ... other feature modules ...
│   ├── hooks/                       # Custom React hooks (e.g. useAuth.ts)
│   ├── layouts/                     # Layout components (Sidebar.tsx, Header.tsx)
│   ├── routes/                      # Route components and route definitions
│   ├── services/                   # API client wrappers (axios instance etc.)
│   ├── stores/                     # State management (Redux, Zustand, React Query config)
│   ├── styles/                     # Global styles, themes, variables
│   ├── utils/                      # Utility/helper functions
│   ├── i18n/                      # Internationalization files
│   ├── App.tsx                    # Main App component with Router
│   ├── main.tsx                   # ReactDOM entry point
│   └── vite-env.d.ts              # Vite TypeScript environment definitions
├── .env                           # Environment variables
├── index.html                     # Entry HTML file
├── package.json
├── tsconfig.json
└── vite.config.ts
```

### File Naming Conventions (Frontend)

- React components use **PascalCase** and `.tsx` extension (e.g., `InvoiceList.tsx`)
- Hook files start with `use` prefix in **camelCase** (e.g., `useAuth.ts`)
- API/service files use camelCase (e.g., `invoiceApi.ts`)
- Redux slices or state files end with `Slice` (e.g., `authSlice.ts`)
- Utility files use camelCase (e.g., `dateUtils.ts`)

---

If you want me to generate sample boilerplate code for any module or setup scripts for backend/frontend, just ask!
