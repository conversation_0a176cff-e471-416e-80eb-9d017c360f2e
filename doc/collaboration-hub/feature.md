# 🤝 Collaboration Hub Feature Specification

## 1. Purpose

The Collaboration Hub is a core workspace for cross-functional teams and external collaborators to co-create, review, and approve social media content tied to a brand. It centralizes content creation, reviews, chats, and briefs in a single hub with strict role-based access control (RBAC) and scoped visibility to streamline communication and workflow.

---

## 2. Entities and Roles

### 2.1 Collaboration Hub

- Attributes:
    - `id`: unique identifier
    - `name`: string, user-defined
    - `brand_id`: foreign key to Brands table
    - `created_at`, `updated_at`
- Created by an internal user who becomes the **Admin** participant in that hub.

### 2.2 Participants

- Link users (internal or external) to a Collaboration Hub with a defined **role**.
- Roles:
    - **Admin** (internal only): full access, can manage hub, participants, and all content.
    - **Content Creator**: create posts, assign reviewers, view only own posts.
    - **Reviewer**: review assigned posts only, cannot create posts.
    - **Reviewer & Content Creator**: full content creation and review abilities.
- Participants can be:
    - **Internal Users:** existing platform users, receive in-app notifications.
    - **External Users:** invited by email, access via magic links, restricted to Reviewer role.

---

## 3. Functional Capabilities

### 3.1 Hub Creation and Management

- Create a hub by providing a **name** and selecting a **brand** from existing brands.
- Hub creator auto-assigned as Admin participant.
- Admins can:
    - Invite participants (internal/external).
    - Remove participants.
    - Change participant roles (except creator Admin).
    - Configure hub settings (e.g., chat, brief visibility defaults).

### 3.2 Participant Invitation Flow

- **Internal users:**
    - Invited via user selection.
    - Receive in-app notification.
- **External users:**
    - Invited by email.
    - Receive a secure magic link via email allowing login without registration.
    - Only assigned Reviewer role is allowed for external users.

### 3.3 Posts

- Created **only** by Content Creators, Reviewer & Content Creators and Admins.
- Post attributes:
    - `id`, `creator_id` (participant), `caption` (text + emoji), `media` (images/videos array), `created_at`, `updated_at`.
    - Assigned reviewers: subset of hub participants with Reviewer role.
    - Reviewer notes: optional text for guiding reviewers.
    - Review status: `pending` (default), `approved`, `rework`.
    - Review checklist and notes (added by reviewers when marking rework).
- Access & Visibility:
    - Content creators see **only their own posts**.
    - Reviewers see posts assigned to them by default.
    - Reviewers can toggle a filter to view **all posts** in the hub (read-only, no review action unless assigned).
    - Admins see all posts.
- Posts support:
    - Multiple media uploads.
    - Emoji support in captions.
    - Assigning multiple reviewers.
- Reviewers can approve or mark for rework, adding checklist items and notes visible to all participants.

### 3.4 Comments on Posts

- Each post has a comment thread.
- Participants with access to the post can comment.
- Comments support emojis and @mentions of participants.
- Comment visibility scoped to post visibility.

### 3.5 Chat Channels

- Pre-created, persistent chat channels scoped per hub:
    - **Admins-only** channel.
    - **Admins + Reviewers** channel.
    - One channel per **Content Creator**, including all Admins and Reviewers, isolating creators from each other.
- Chats support:
    - Real-time messaging.
    - Emoji and @mentions.
    - Clear UI distinction from post comments.
- Roles determine channel membership and message visibility.

### 3.6 Collaboration Briefs

- Rich text notes or documents associated with the hub.
- Attributes:
    - `id`, `title`, `body` (rich text), `created_by`, `created_at`, `updated_at`.
    - Access tags / visibility:
        - Admins and Reviewers only.
        - Specific Content Creators.
        - Everyone in the hub.
- Brief creators select visibility at creation/edit.
- Briefs support:
    - Inline formatting.
- Access tags displayed on brief cards for transparency.

---

## 4. Access Control and Permissions Summary

| Role                | Create Posts | Assign Reviewers | Review Posts Assigned | See All Posts | See Others' Posts | Comments on Posts | Access All Chats | Access Creator Chat | Manage Participants | Create Briefs | See Briefs Based on Visibility |
|---------------------|--------------|------------------|----------------------|---------------|-------------------|-------------------|------------------|---------------------|---------------------|---------------|-------------------------------|
| Admin (internal only)| Yes          | Yes              | Yes                  | Yes           | Yes               | Yes               | Yes              | Yes                 | Yes                 | Yes           | Yes                           |
| Content Creator      | Yes (own only)| Yes (reviewers)  | No                   | No            | No                | Yes (own posts)   | No               | Yes (own chat)      | No                  | Yes           | Depends on access tags         |
| Reviewer            | No           | No               | Yes (assigned only)   | Optional      | Yes (read-only)   | Yes | Yes              | Yes                 | No                  | Yes           | Depends on access tags         |
| Reviewer & Creator   | Yes (own only)| Yes              | Yes (assigned only)   | Optional      | Yes (read-only)   | Yes               | Yes              | Yes                 | No                  | Yes           | Depends on access tags         |

---

## 5. User Workflows

### 5.1 Hub Creation

1. User selects “Create Collaboration Hub.”
2. Inputs name and selects brand.
3. System creates hub, assigns creator as Admin participant.
4. User lands on hub page, can invite participants.

### 5.2 Inviting Participants

- Admin selects existing internal users or enters external emails (or select from brand's contracts).
- For externals, system sends magic link email with limited Reviewer/Content creator or both access.
- Internal users get in-app notification.

### 5.3 Creating Posts

- Content creator clicks “New Post.”
- Uploads media, adds caption with emoji support.
- Assigns one or more reviewers.
- Adds optional reviewer notes.
- Saves post; reviewers notified.

### 5.4 Reviewing Posts

- Reviewer views “Assigned to me” posts by default.
- Can toggle “All posts” view (read-only).
- Selects a post to review.
- Marks status:
    - Pending (default)
    - Approved
    - Rework — can add checklist and notes.
- Review changes visible to all relevant participants.

### 5.5 Commenting on Posts

- Participants with access can add comments.
- Comments support emoji and @mentions.
- Comments ordered chronologically; UI distinguishes commenters.

### 5.6 Chat Usage

- Users join pre-created scoped channels.
- Send real-time messages with emojis and mentions.
- Admins moderate chats if needed.

### 5.7 Creating & Viewing Briefs

- Participants create briefs with title and rich text.
- Choose access tags to restrict visibility.
- Briefs displayed in a list with visibility tags.
- Participants see briefs based on access rights.

---

## 6. UI/UX Considerations

- Clear separation between **Post Comments** (threaded, per-post) and **Chat Channels** (broader scoped conversations).
- Post list default filter is “Assigned to me” for reviewers.
- Toggle to view “All posts” (read-only if not assigned).
- Chat channel list grouped by scope: Admins, Admins+Reviewers, Per Creator.
- Brief cards show visibility tags and creator info.
- Emoji picker and @mention autocomplete in comments and chats.
- Magic links for external reviewers are one-time use and expire after a set period.
- Responsive UI for desktop and mobile:
    - Simplified navigation on mobile.
    - Tabbed views for posts, chats, briefs.
- Notifications:
    - In-app for internal users.
    - Email for external reviewers (new assignments, comments, review requests).

---

## 7. Edge Cases & Future Enhancements

- **Revoking external access:** Admins can disable magic links or remove external participants at any time.
- **Audit Logs:** Track actions like post creation, review status changes, participant invitations.
- **Post Versioning:** Track edits to posts and captions.
- **Brief Versioning:** Support edit history for briefs.
- **Advanced Filtering:** Search posts by status, creator, reviewer.
- **Offline Support:** Cache recent chats and posts for mobile.
- **Notifications Preferences:** Allow users to customize notifications.

---

## 8. Data Model Summary (Core Tables)

- `collaboration_hubs` (id, name, brand_id, created_at, updated_at)
- `hub_participants` (id, hub_id, user_id (nullable for external), email (nullable for internal), role, is_external, magic_link_token, magic_link_expiry, invited_at, accepted_at)
- `posts` (id, hub_id, creator_participant_id, caption, media_uris JSON[], reviewer_notes, review_status ENUM(pending, approved, rework), created_at, updated_at)
- `post_reviewers` (id, post_id, participant_id, assigned_at, review_status, checklist JSON[], review_notes TEXT)
- `post_comments` (id, post_id, participant_id, content TEXT, created_at)
- `chat_channels` (id, hub_id, name, scope ENUM(admins, admins_reviewers, creator_{participant_id}))
- `chat_messages` (id, channel_id, participant_id, content TEXT, created_at)
- `collaboration_briefs` (id, hub_id, title, body TEXT, created_by_participant_id, access_tags JSON[], created_at, updated_at)

---

## 9. Security & Compliance

- Role-based access enforced at API and UI layers.
- External users restricted via expiring magic links.
- All media uploads scanned for security.
- Data encryption at rest and in transit.
- GDPR compliance for external user email and data handling.

---

# End of Specification
