# Role-Based Participant Visibility

## Overview

This document describes the implementation of role-based participant visibility in collaboration hubs. The feature ensures that participants can only see other participants based on their role permissions.

## Visibility Rules

### Admin
- **Can see**: All participants (admin, reviewer, reviewer_creator, content_creator)
- **Rationale**: Admins need full visibility for hub management

### Reviewer
- **Can see**: All participants (admin, reviewer, reviewer_creator, content_creator)
- **Rationale**: Reviewers need to see all participants to understand who is contributing content

### Reviewer Creator
- **Can see**: All participants (admin, reviewer, reviewer_creator, content_creator)
- **Rationale**: Has both review and creation capabilities, needs full visibility

### Content Creator
- **Can see**: Only admins, reviewers, and reviewer_creators
- **Cannot see**: Other content creators
- **Rationale**: Content creators should focus on communicating with reviewers and admins, not other content creators

## Implementation

### Repository Layer

#### New Methods Added to `HubParticipantRepositoryImpl`

1. **`findParticipantsWithRoleBasedVisibility()`**
   - Filters participants based on the requesting user's role
   - Applies role-based visibility rules
   - Supports pagination and additional filtering (status, role)

2. **`countParticipantsWithRoleBasedVisibility()`**
   - Counts participants with role-based visibility applied
   - Used for pagination calculations

#### Key Implementation Details

```java
// Content creators can only see admins, reviewers, and reviewer_creators
if (currentUserRole == HubParticipantRole.content_creator) {
    query = query.and(HUB_PARTICIPANT.ROLE.in(
        HubParticipantRole.admin,
        HubParticipantRole.reviewer,
        HubParticipantRole.reviewer_creator
    ));
}
// Other roles (admin, reviewer, reviewer_creator) see all participants
```

### Service Layer

#### Modified `HubParticipantService.getHubParticipants()`

1. **Role Detection**: Gets the current user's role in the hub
2. **Validation**: Ensures user is a participant before applying visibility rules
3. **Filtering**: Uses role-based repository methods instead of generic ones
4. **Consistency**: Both participant list and count use the same visibility rules

#### New Helper Method

- **`getCurrentUserRoleInHub()`**: Retrieves the current user's role in the specified hub

### Controller Layer

No changes required - the existing endpoint automatically applies the new visibility rules through the service layer.

## API Behavior

### GET `/api/hubs/{hubId}/participants`

The endpoint behavior now varies based on the requesting user's role:

#### For Admin/Reviewer/Reviewer-Creator Users
```json
{
  "content": [
    {"role": "admin", "name": "Admin User"},
    {"role": "reviewer", "name": "Reviewer User"},
    {"role": "reviewer_creator", "name": "Reviewer Creator"},
    {"role": "content_creator", "name": "Content Creator 1"},
    {"role": "content_creator", "name": "Content Creator 2"}
  ],
  "totalElements": 5
}
```

#### For Content Creator Users
```json
{
  "content": [
    {"role": "admin", "name": "Admin User"},
    {"role": "reviewer", "name": "Reviewer User"},
    {"role": "reviewer_creator", "name": "Reviewer Creator"}
  ],
  "totalElements": 3
}
```

## Filtering Behavior

### Role Filter with Content Creator User

When a content creator applies a role filter:

- **`?role=admin`**: Returns admin participants (visible)
- **`?role=reviewer`**: Returns reviewer participants (visible)
- **`?role=content_creator`**: Returns empty list (not visible)

### Status Filter

Status filtering works normally within the visible participant set:

- **`?status=active`**: Shows active participants that are visible to the user
- **`?status=pending`**: Shows pending participants that are visible to the user

## Pagination

Pagination works correctly with role-based visibility:

- **`totalElements`**: Reflects only visible participants
- **`totalPages`**: Calculated based on visible participant count
- **Page content**: Contains only visible participants

## Security Considerations

1. **Access Control**: Users must be participants in the hub to view any participant list
2. **Role Validation**: User's role is validated on each request
3. **Consistent Filtering**: Both list and count operations use identical visibility rules
4. **No Information Leakage**: Content creators cannot determine the existence of other content creators

## Testing

Comprehensive tests have been added at three levels:

1. **Repository Tests**: `HubParticipantRepositoryRoleBasedVisibilityTest`
   - Tests role-based filtering logic
   - Verifies pagination and counting
   - Tests various filter combinations

2. **Service Tests**: `HubParticipantServiceRoleBasedVisibilityTest`
   - Integration tests for service layer
   - Tests user role detection
   - Verifies access control

3. **Controller Tests**: `HubParticipantControllerRoleBasedVisibilityTest`
   - End-to-end API tests
   - Tests HTTP responses
   - Verifies JSON structure

## Migration Impact

This is a **backward-compatible** change:

- **Existing API**: No changes to request/response format
- **Admin/Reviewer Users**: See the same participants as before
- **Content Creator Users**: See fewer participants (security improvement)
- **Database**: No schema changes required

## Performance Considerations

- **Query Efficiency**: Role-based filtering is applied at the database level
- **Index Usage**: Existing indexes on `hub_id` and `role` are utilized
- **Minimal Overhead**: Only one additional query to determine user's role

## Future Enhancements

Potential future improvements:

1. **Caching**: Cache user roles to reduce database queries
2. **Granular Permissions**: More fine-grained visibility rules
3. **External User Visibility**: Special rules for external participants
4. **Audit Logging**: Track who views participant lists
