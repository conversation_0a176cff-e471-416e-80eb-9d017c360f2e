# 🤝 Enhanced Collaboration Hubs Backend API & Logic

## Overview

The Collaboration Hubs feature provides a comprehensive workspace for cross-functional teams and external collaborators to create, review, and approve social media content. This enhanced specification includes detailed request/response examples, business logic descriptions, and implementation guidelines.

---

## 1. Enhanced Data Models

### Table: `collaboration_hubs`
```sql
CREATE TABLE collaboration_hubs (
                                    id BIGINT PRIMARY KEY,
                                    account_id BIGINT NOT NULL,
                                    brand_id BIGINT NOT NULL,
                                    name VARCHAR(255) NOT NULL,
                                    description TEXT,
                                    created_by BIGINT NOT NULL,
                                    created_at TIMESTAMP DEFAULT NOW(),
                                    updated_at TIMESTAMP DEFAULT NOW(),
                                    FOREIG<PERSON> KEY (account_id) REFERENCES accounts(id),
                                    FOREIGN KEY (brand_id) REFERENCES brands(id),
                                    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### Table: `hub_participants`
```sql
CREATE TABLE hub_participants (
                                  id BIGINT PRIMARY KEY,
                                  hub_id BIGINT NOT NULL,
                                  user_id BIGINT NULL, -- NULL for external users
                                  email VARCHAR(255) NOT NULL,
                                  role ENUM('admin', 'content_creator', 'reviewer', 'reviewer_creator') NOT NULL,
                                  is_external BOOLEAN DEFAULT FALSE,
                                  magic_link_token VARCHAR(255) NULL,
                                  magic_link_expiry TIMESTAMP NULL,
                                  invited_at TIMESTAMP DEFAULT NOW(),
                                  joined_at TIMESTAMP NULL,
                                  removed_at TIMESTAMP NULL,
                                  created_at TIMESTAMP DEFAULT NOW(),
                                  FOREIGN KEY (hub_id) REFERENCES collaboration_hubs(id),
                                  FOREIGN KEY (user_id) REFERENCES users(id),
                                  UNIQUE(hub_id, email)
);
```

### Table: `posts`
```sql
CREATE TABLE posts (
                       id BIGINT PRIMARY KEY,
                       hub_id BIGINT NOT NULL,
                       creator_participant_id BIGINT NOT NULL,
                       caption TEXT,
                       media_uris JSONB DEFAULT '[]',
                       reviewer_notes TEXT,
                       review_status ENUM('pending', 'approved', 'rework') DEFAULT 'pending',
                       created_at TIMESTAMP DEFAULT NOW(),
                       updated_at TIMESTAMP DEFAULT NOW(),
                       FOREIGN KEY (hub_id) REFERENCES collaboration_hubs(id),
                       FOREIGN KEY (creator_participant_id) REFERENCES hub_participants(id)
);
```

### Table: `post_reviewers`
```sql
CREATE TABLE post_reviewers (
                                id BIGINT PRIMARY KEY,
                                post_id BIGINT NOT NULL,
                                participant_id BIGINT NOT NULL,
                                assigned_at TIMESTAMP DEFAULT NOW(),
                                review_status ENUM('pending', 'approved', 'rework') DEFAULT 'pending',
                                checklist JSONB DEFAULT '[]',
                                review_notes TEXT,
                                reviewed_at TIMESTAMP NULL,
                                FOREIGN KEY (post_id) REFERENCES posts(id),
                                FOREIGN KEY (participant_id) REFERENCES hub_participants(id),
                                UNIQUE(post_id, participant_id)
);
```

### Table: `post_comments`
```sql
CREATE TABLE post_comments (
                               id BIGINT PRIMARY KEY,
                               post_id BIGINT NOT NULL,
                               participant_id BIGINT NOT NULL,
                               content TEXT NOT NULL,
                               created_at TIMESTAMP DEFAULT NOW(),
                               updated_at TIMESTAMP DEFAULT NOW(),
                               FOREIGN KEY (post_id) REFERENCES posts(id),
                               FOREIGN KEY (participant_id) REFERENCES hub_participants(id)
);
```

### Table: `chat_channels`
```sql
CREATE TABLE chat_channels (
                               id BIGINT PRIMARY KEY,
                               hub_id BIGINT NOT NULL,
                               name VARCHAR(255) NOT NULL,
                               scope ENUM('admins', 'admins_reviewers', 'creator_specific') NOT NULL,
                               creator_participant_id BIGINT NULL, -- Only for creator_specific channels
                               created_at TIMESTAMP DEFAULT NOW(),
                               FOREIGN KEY (hub_id) REFERENCES collaboration_hubs(id),
                               FOREIGN KEY (creator_participant_id) REFERENCES hub_participants(id)
);
```

### Table: `chat_messages`
```sql
CREATE TABLE chat_messages (
                               id BIGINT PRIMARY KEY,
                               channel_id BIGINT NOT NULL,
                               participant_id BIGINT NOT NULL,
                               content TEXT NOT NULL,
                               created_at TIMESTAMP DEFAULT NOW(),
                               FOREIGN KEY (channel_id) REFERENCES chat_channels(id),
                               FOREIGN KEY (participant_id) REFERENCES hub_participants(id)
);
```

### Table: `collaboration_briefs`
```sql
CREATE TABLE collaboration_briefs (
                                      id BIGINT PRIMARY KEY,
                                      hub_id BIGINT NOT NULL,
                                      title VARCHAR(255) NOT NULL,
                                      body TEXT,
                                      created_by_participant_id BIGINT NOT NULL,
                                      access_tags JSONB DEFAULT '[]', -- ["admins", "reviewers", "creator_123"]
                                      created_at TIMESTAMP DEFAULT NOW(),
                                      updated_at TIMESTAMP DEFAULT NOW(),
                                      FOREIGN KEY (hub_id) REFERENCES collaboration_hubs(id),
                                      FOREIGN KEY (created_by_participant_id) REFERENCES hub_participants(id)
);
```

---

## 2. API Endpoints with Request/Response Examples

### 2.1 Collaboration Hubs Management

#### Create Hub
**POST** `/api/hubs`

**Request:**
```json
{
  "name": "Summer Campaign 2025",
  "brand_id": 123,
  "description": "Collaborative workspace for summer social media campaign"
}
```

**Response (201):**
```json
{
  "id": 456,
  "name": "Summer Campaign 2025",
  "brand_id": 123,
  "description": "Collaborative workspace for summer social media campaign",
  "created_by": 789,
  "participant_count": 1,
  "created_at": "2025-05-29T10:30:00Z"
}
```

**Business Logic:**
1. Validate user has access to the specified brand
2. Create hub record with account_id from authenticated user
3. Auto-create hub participant record with role 'admin' for creator
4. Create default chat channels (admins-only, admins+reviewers)
5. Return hub details with participant count

#### List Hubs
**GET** `/api/hubs?brand_id=123&page=1&limit=20`

**Response (200):**
```json
{
  "hubs": [
    {
      "id": 456,
      "name": "Summer Campaign 2025",
      "brand_name": "Brand ABC",
      "brand_id": 123,
      "participant_count": 5,
      "unread_posts": 3,
      "unread_messages": 12,
      "my_role": "admin",
      "created_at": "2025-05-29T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "total_pages": 1
  }
}
```

**Business Logic:**
1. Filter hubs by account_id and optional brand_id
2. Join with hub_participants to get user's role
3. Calculate unread counts for posts and messages
4. Apply pagination and return results

#### Get Hub Details
**GET** `/api/hubs/456`

**Response (200):**
```json
{
  "id": 456,
  "name": "Summer Campaign 2025",
  "brand_id": 123,
  "brand_name": "Brand ABC",
  "description": "Collaborative workspace for summer social media campaign",
  "my_role": "admin",
  "participants": [
    {
      "id": 1,
      "user_id": 789,
      "email": "<EMAIL>",
      "name": "John Admin",
      "role": "admin",
      "is_external": false,
      "joined_at": "2025-05-29T10:30:00Z"
    },
    {
      "id": 2,
      "user_id": null,
      "email": "<EMAIL>",
      "name": "External Reviewer",
      "role": "reviewer",
      "is_external": true,
      "joined_at": null
    }
  ],
  "stats": {
    "total_posts": 8,
    "pending_posts": 3,
    "approved_posts": 5,
    "active_chats": 3
  },
  "created_at": "2025-05-29T10:30:00Z"
}
```

**Business Logic:**
1. Verify user has access to hub (is participant)
2. Get hub details with brand information
3. Load all participants with user details (if internal)
4. Calculate hub statistics
5. Determine user's role in hub

### 2.2 Participant Management

#### Invite Participants
**POST** `/api/hubs/456/participants`

**Request:**
```json
{
  "participants": [
    {
      "type": "internal",
      "user_id": 987,
      "role": "content_creator"
    },
    {
      "type": "external",
      "email": "<EMAIL>",
      "role": "reviewer",
      "name": "External Reviewer"
    }
  ]
}
```

**Response (201):**
```json
{
  "invited": [
    {
      "id": 3,
      "user_id": 987,
      "email": "<EMAIL>",
      "role": "content_creator",
      "is_external": false,
      "status": "invited"
    },
    {
      "id": 4,
      "user_id": null,
      "email": "<EMAIL>",
      "role": "reviewer",
      "is_external": true,
      "magic_link_token": "abc123def456",
      "magic_link_expiry": "2025-06-05T10:30:00Z",
      "status": "invited"
    }
  ]
}
```

**Business Logic:**
1. Verify requester is admin of the hub
2. For internal users: validate user_id exists and has access to account
3. For external users: generate secure magic link token with 7-day expiry
4. Create participant records
5. Send notifications (in-app for internal, email for external)
6. For external users, create limited-scope chat channels if they're content creators

#### Remove Participant
**DELETE** `/api/hubs/456/participants/3`

**Response (200):**
```json
{
  "message": "Participant removed successfully"
}
```

**Business Logic:**
1. Verify requester is admin and not removing themselves
2. Set removed_at timestamp (soft delete)
3. Revoke magic link token if external user
4. Remove from all assigned posts as reviewer
5. Archive or transfer their posts if content creator

### 2.3 Posts Management

#### Create Post
**POST** `/api/hubs/456/posts`

**Request:**
```json
{
  "caption": "Check out our new summer collection! 🌞 #SummerVibes #Fashion",
  "media_uris": [
    "https://cdn.example.com/image1.jpg",
    "https://cdn.example.com/video1.mp4"
  ],
  "reviewer_ids": [2, 4],
  "reviewer_notes": "Please focus on brand consistency and hashtag usage"
}
```

**Response (201):**
```json
{
  "id": 789,
  "caption": "Check out our new summer collection! 🌞 #SummerVibes #Fashion",
  "media_uris": [
    "https://cdn.example.com/image1.jpg",
    "https://cdn.example.com/video1.mp4"
  ],
  "review_status": "pending",
  "reviewer_notes": "Please focus on brand consistency and hashtag usage",
  "assigned_reviewers": [
    {
      "id": 2,
      "name": "Internal Reviewer",
      "email": "<EMAIL>",
      "status": "pending"
    },
    {
      "id": 4,
      "name": "External Reviewer",
      "email": "<EMAIL>",
      "status": "pending"
    }
  ],
  "created_at": "2025-05-29T14:30:00Z"
}
```

**Business Logic:**
1. Verify user is content_creator, reviewer_creator, or admin
2. Validate media URIs are accessible and belong to account
3. Validate reviewer_ids are valid participants with reviewer role
4. Create post record with pending status
5. Create post_reviewers records for each assigned reviewer
6. Send notifications to assigned reviewers
7. Process @mentions in caption for notifications

#### List Posts
**GET** `/api/hubs/456/posts?filter=assigned_to_me&status=pending&page=1&limit=10`

**Response (200):**
```json
{
  "posts": [
    {
      "id": 789,
      "caption": "Check out our new summer collection! 🌞",
      "media_count": 2,
      "review_status": "pending",
      "creator": {
        "id": 3,
        "name": "Content Creator",
        "email": "<EMAIL>"
      },
      "my_review_status": "pending",
      "comment_count": 2,
      "assigned_reviewer_count": 2,
      "approved_by_count": 0,
      "created_at": "2025-05-29T14:30:00Z",
      "can_edit": false,
      "can_review": true,
      "can_comment": true
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_pages": 1
  },
  "filters": {
    "available": ["all", "assigned_to_me", "created_by_me", "needs_review"],
    "current": "assigned_to_me"
  }
}
```

**Business Logic:**
1. Apply role-based filtering:
    - Content creators: only their own posts by default
    - Reviewers: assigned posts by default, can toggle to see all (read-only)
    - Admins: all posts
2. Apply status and other filters
3. Calculate permissions for each post based on user role
4. Include summary statistics for quick overview

#### Get Post Details
**GET** `/api/posts/789`

**Response (200):**
```json
{
  "id": 789,
  "caption": "Check out our new summer collection! 🌞 #SummerVibes #Fashion",
  "media_uris": [
    {
      "url": "https://cdn.example.com/image1.jpg",
      "type": "image",
      "size": 1024576
    },
    {
      "url": "https://cdn.example.com/video1.mp4",
      "type": "video",
      "duration": 30
    }
  ],
  "review_status": "pending",
  "reviewer_notes": "Please focus on brand consistency and hashtag usage",
  "creator": {
    "id": 3,
    "name": "Content Creator",
    "email": "<EMAIL>"
  },
  "reviewers": [
    {
      "id": 2,
      "name": "Internal Reviewer",
      "review_status": "pending",
      "reviewed_at": null,
      "review_notes": null,
      "checklist": []
    },
    {
      "id": 4,
      "name": "External Reviewer",
      "review_status": "approved",
      "reviewed_at": "2025-05-29T15:30:00Z",
      "review_notes": "Looks great! Brand voice is on point.",
      "checklist": [
        {
          "item": "Brand guidelines followed",
          "checked": true
        },
        {
          "item": "Hashtags appropriate",
          "checked": true
        }
      ]
    }
  ],
  "permissions": {
    "can_edit": false,
    "can_review": true,
    "can_comment": true,
    "can_assign_reviewers": false
  },
  "created_at": "2025-05-29T14:30:00Z",
  "updated_at": "2025-05-29T15:30:00Z"
}
```

**Business Logic:**
1. Verify user has access to post based on role and assignment
2. Load complete post details with media metadata
3. Get all reviewer statuses and notes
4. Calculate user permissions for the post
5. Include edit history if available

### 2.4 Review Management

#### Submit Review
**POST** `/api/posts/789/reviews`

**Request:**
```json
{
  "status": "rework",
  "review_notes": "Great concept! Please adjust the caption to better align with brand voice guidelines.",
  "checklist": [
    {
      "item": "Brand guidelines followed",
      "checked": false,
      "note": "Caption needs minor adjustments"
    },
    {
      "item": "Visual quality acceptable",
      "checked": true
    },
    {
      "item": "Hashtags appropriate",
      "checked": true
    }
  ]
}
```

**Response (200):**
```json
{
  "review_id": 123,
  "status": "rework",
  "review_notes": "Great concept! Please adjust the caption to better align with brand voice guidelines.",
  "checklist": [
    {
      "item": "Brand guidelines followed",
      "checked": false,
      "note": "Caption needs minor adjustments"
    },
    {
      "item": "Visual quality acceptable",
      "checked": true
    },
    {
      "item": "Hashtags appropriate",
      "checked": true
    }
  ],
  "reviewed_at": "2025-05-29T16:00:00Z",
  "post_status": "rework"
}
```

**Business Logic:**
1. Verify user is assigned as reviewer for this post
2. Update post_reviewers record with review details
3. Update overall post status based on all reviewer responses:
    - If any reviewer marks "rework": post status = "rework"
    - If all assigned reviewers approve: post status = "approved"
    - Otherwise: status remains "pending"
4. Send notifications to post creator and other stakeholders
5. Log review action for audit trail

### 2.5 Comments Management

#### Add Comment
**POST** `/api/posts/789/comments`

**Request:**
```json
{
  "content": "Love the visual! @john_reviewer what do you think about the lighting? 💡"
}
```

**Response (201):**
```json
{
  "id": 456,
  "content": "Love the visual! @john_reviewer what do you think about the lighting? 💡",
  "author": {
    "id": 3,
    "name": "Content Creator",
    "email": "<EMAIL>",
    "is_external": false
  },
  "mentions": [
    {
      "participant_id": 2,
      "name": "john_reviewer"
    }
  ],
  "created_at": "2025-05-29T16:15:00Z"
}
```

**Business Logic:**
1. Verify user has access to comment on the post
2. Parse @mentions and validate mentioned participants
3. Create comment record
4. Send notifications to mentioned participants
5. Update post's last activity timestamp

#### Get Comments
**GET** `/api/posts/789/comments?page=1&limit=20`

**Response (200):**
```json
{
  "comments": [
    {
      "id": 456,
      "content": "Love the visual! @john_reviewer what do you think about the lighting? 💡",
      "author": {
        "id": 3,
        "name": "Content Creator",
        "email": "<EMAIL>",
        "is_external": false
      },
      "created_at": "2025-05-29T16:15:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "total_pages": 1
  }
}
```

### 2.6 Chat Management

#### List Chat Channels
**GET** `/api/hubs/456/chats`

**Response (200):**
```json
{
  "channels": [
    {
      "id": 101,
      "name": "Admins Only",
      "scope": "admins",
      "description": "Private channel for administrators",
      "participant_count": 2,
      "unread_count": 0,
      "last_message": {
        "content": "Hub setup is complete",
        "sender_name": "John Admin",
        "created_at": "2025-05-29T10:45:00Z"
      },
      "can_access": true
    },
    {
      "id": 102,
      "name": "Reviewers & Admins",
      "scope": "admins_reviewers",
      "description": "Channel for reviewers and administrators",
      "participant_count": 4,
      "unread_count": 3,
      "last_message": {
        "content": "New posts ready for review",
        "sender_name": "Review Manager",
        "created_at": "2025-05-29T15:30:00Z"
      },
      "can_access": true
    },
    {
      "id": 103,
      "name": "Creator: Sarah Johnson",
      "scope": "creator_specific",
      "description": "Private channel for Sarah's content",
      "participant_count": 3,
      "unread_count": 1,
      "creator_participant_id": 3,
      "can_access": false
    }
  ]
}
```

**Business Logic:**
1. Get all chat channels for the hub
2. Filter channels based on user's role and permissions:
    - Admins: access to all channels
    - Reviewers: access to admins_reviewers and creator_specific channels
    - Content Creators: access to their own creator_specific channel and admins_reviewers
3. Calculate unread message counts per channel
4. Include last message preview for accessible channels

#### Get Chat Messages
**GET** `/api/chats/102/messages?page=1&limit=50`

**Response (200):**
```json
{
  "messages": [
    {
      "id": 1001,
      "content": "New posts ready for review! Please check the summer collection posts 🌞",
      "sender": {
        "id": 2,
        "name": "Review Manager",
        "email": "<EMAIL>",
        "is_external": false
      },
      "mentions": [],
      "created_at": "2025-05-29T15:30:00Z"
    },
    {
      "id": 1002,
      "content": "Thanks @review_manager, I'll review them shortly ✅",
      "sender": {
        "id": 4,
        "name": "External Reviewer",
        "email": "<EMAIL>",
        "is_external": true
      },
      "mentions": [
        {
          "participant_id": 2,
          "name": "review_manager"
        }
      ],
      "created_at": "2025-05-29T15:45:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 2,
    "total_pages": 1
  },
  "channel_info": {
    "id": 102,
    "name": "Reviewers & Admins",
    "scope": "admins_reviewers"
  }
}
```

#### Send Chat Message
**POST** `/api/chats/102/messages`

**Request:**
```json
{
  "content": "Hey team! Just finished reviewing the latest posts. @sarah_creator great work on the summer theme! 🎉"
}
```

**Response (201):**
```json
{
  "id": 1003,
  "content": "Hey team! Just finished reviewing the latest posts. @sarah_creator great work on the summer theme! 🎉",
  "sender": {
    "id": 2,
    "name": "Review Manager",
    "email": "<EMAIL>",
    "is_external": false
  },
  "mentions": [
    {
      "participant_id": 3,
      "name": "sarah_creator"
    }
  ],
  "created_at": "2025-05-29T16:30:00Z"
}
```

**Business Logic:**
1. Verify user has access to the chat channel
2. Parse @mentions and validate mentioned participants
3. Create message record
4. Send real-time notifications via WebSocket to channel participants
5. Send push/email notifications to mentioned participants
6. Update channel's last activity timestamp

### 2.7 Brief Management

#### Create Brief
**POST** `/api/hubs/456/briefs`

**Request:**
```json
{
  "title": "Summer Campaign Guidelines",
  "body": "<h2>Brand Voice Guidelines</h2><p>Our summer campaign should emphasize <strong>warmth</strong>, <em>adventure</em>, and <u>authenticity</u>.</p><ul><li>Use warm, inviting language</li><li>Include seasonal hashtags</li><li>Maintain consistent brand colors</li></ul>",
  "access_tags": ["admins", "reviewers", "creator_3"]
}
```

**Response (201):**
```json
{
  "id": 789,
  "title": "Summer Campaign Guidelines",
  "body": "<h2>Brand Voice Guidelines</h2><p>Our summer campaign should emphasize <strong>warmth</strong>, <em>adventure</em>, and <u>authenticity</u>.</p><ul><li>Use warm, inviting language</li><li>Include seasonal hashtags</li><li>Maintain consistent brand colors</li></ul>",
  "access_tags": ["admins", "reviewers", "creator_3"],
  "created_by": {
    "id": 1,
    "name": "John Admin",
    "email": "<EMAIL>"
  },
  "access_description": "Visible to admins, reviewers, and Sarah Johnson",
  "created_at": "2025-05-29T17:00:00Z"
}
```

**Business Logic:**
1. Verify user has permission to create briefs (all roles can create)
2. Validate access_tags contain valid participant roles or specific participant IDs
3. Sanitize HTML content in body to prevent XSS
4. Create brief record with access control metadata
5. Send notifications to participants who have access

#### List Briefs
**GET** `/api/hubs/456/briefs?page=1&limit=10`

**Response (200):**
```json
{
  "briefs": [
    {
      "id": 789,
      "title": "Summer Campaign Guidelines",
      "body_preview": "Our summer campaign should emphasize warmth, adventure, and authenticity...",
      "access_tags": ["admins", "reviewers", "creator_3"],
      "access_description": "Visible to admins, reviewers, and Sarah Johnson",
      "created_by": {
        "id": 1,
        "name": "John Admin"
      },
      "can_edit": true,
      "can_delete": true,
      "created_at": "2025-05-29T17:00:00Z",
      "updated_at": "2025-05-29T17:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_pages": 1
  }
}
```