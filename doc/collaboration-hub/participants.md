# 🤝 Collaboration Hub Participants Backend API & Logic

## Overview

The Participants feature manages access control and collaboration within collaboration hubs. Participants can be internal users (from the same account), external users (custom name/email), or brand contacts. Each participant has a specific role that determines their permissions within the hub.

---

## 1. Data Model

### Table: `hub_participant`

- **Primary Key:** `id` (bigint)
- **Columns:**
    - `hub_id` (bigint, not null) — Foreign key to `collaboration_hub.id`
    - `user_id` (bigint, nullable) — Foreign key to `user.id` (null for external participants)
    - `email` (varchar(255), not null) — Participant email address
    - `role` (hub_participant_role, not null) — Participant role enum
    - `is_external` (boolean, not null, default false) — Whether participant is external
    - `magic_link_token` (varchar(255), nullable) — Token for external access
    - `magic_link_expiry` (timestamp, nullable) — Magic link expiration time
    - `invited_at` (timestamp, not null) — When participant was invited
    - `joined_at` (timestamp, nullable) — When participant joined (null for pending)
    - `removed_at` (timestamp, nullable) — Soft deletion timestamp
    - `created_at` (timestamp, not null) — Record creation time
- **Constraints:**
    - `UNIQUE(hub_id, email)` — One participant per email per hub
    - `ON DELETE CASCADE` for hub_id reference

### Enum: `hub_participant_role`

```sql
CREATE TYPE hub_participant_role AS ENUM (
    'admin',           -- Full hub management permissions
    'content_creator', -- Can create and edit posts
    'reviewer',        -- Can review and approve posts  
    'reviewer_creator' -- Can both create posts and review others' posts
);
```

---

## 2. Participant Types

### 2.1 Internal Users (Account Members)

- **Characteristics:**
    - `user_id` is set to valid user ID from same account
    - `is_external` = false
    - `magic_link_token` and `magic_link_expiry` are null
    - `joined_at` is set immediately upon invitation
- **Authentication:** Standard JWT-based authentication
- **Name Resolution:** Uses `user.display_name` from user table

### 2.2 External Users (Custom Participants)

- **Characteristics:**
    - `user_id` is null
    - `is_external` = true
    - `magic_link_token` and `magic_link_expiry` are set
    - `joined_at` is null until they access via magic link
- **Authentication:** Magic link with One-Time Token (OTT)
- **Name Resolution:** Uses email prefix or custom name provided during invitation

### 2.3 Brand Contact Participants

- **Characteristics:**
    - Can be invited using existing brand contact information
    - Treated as external users once invited
    - `user_id` is null, `is_external` = true
    - Contact information pre-filled from brand_contact table
- **Authentication:** Magic link with OTT
- **Name Resolution:** Uses `brand_contact.name`

---

## 3. Role-Based Permissions

### 3.1 Admin Role
- **Hub Management:** Create, update, delete hub
- **Participant Management:** Invite, remove, change roles
- **Content Access:** View, create, edit, delete all posts
- **Review Access:** Review any post, assign reviewers
- **Chat Access:** All channels including admin-only channels

### 3.2 Content Creator Role
- **Content Management:** Create, edit own posts
- **Content Access:** View all posts (read-only for others' posts)
- **Review Access:** Cannot review posts
- **Chat Access:** General channels and creator-specific channels

### 3.3 Reviewer Role
- **Content Access:** View all posts assigned for review
- **Review Management:** Review assigned posts, provide feedback
- **Content Creation:** Cannot create posts
- **Chat Access:** General channels and admin-reviewer channels

### 3.4 Reviewer Creator Role
- **Content Management:** Create, edit own posts
- **Review Management:** Review assigned posts from other creators
- **Content Access:** View all posts
- **Chat Access:** All non-admin channels

---

## 4. Multi-Tenancy & Access Control

- All participant operations are scoped by hub's `account_id`
- Internal users must belong to the same account as the hub
- External users are validated by magic link token
- Soft deletion via `removed_at` timestamp preserves audit trail
- Role changes require admin permissions
- Email uniqueness enforced per hub (not globally)

---

## 5. Magic Link Authentication

### 5.1 Token Generation
- Secure random token (UUID format)
- 7-day expiration period
- Single-use tokens (revoked after first use)
- Cryptographically secure generation

### 5.2 Access Flow
1. External user receives magic link via email
2. Token validation on access
3. Temporary session creation
4. `joined_at` timestamp updated on first access
5. Token revocation after successful authentication

---

## 6. Business Logic Operations

### 6.1 Invite Internal User
1. Validate user exists in same account
2. Check user not already participant
3. Create participant record with immediate `joined_at`
4. Send in-app notification
5. Grant role-based permissions immediately

### 6.2 Invite External User
1. Validate email format and uniqueness within hub
2. Generate secure magic link token
3. Set 7-day expiration
4. Create participant record with pending status
5. Send email invitation with magic link
6. `joined_at` remains null until first access

### 6.3 Invite Brand Contact
1. Validate brand contact exists and belongs to account
2. Use contact's name and email for participant creation
3. Follow external user invitation flow
4. Pre-populate participant details from contact info

### 6.4 Remove Participant
1. Verify admin permissions
2. Prevent self-removal by admins
3. Set `removed_at` timestamp (soft delete)
4. Revoke magic link tokens if external
5. Remove from assigned post reviews
6. Archive or transfer created content

### 6.5 Role Management
1. Verify admin permissions for role changes
2. Validate role transition rules
3. Update participant role
4. Adjust permissions and access immediately
5. Notify participant of role change

---

## 7. Integration Points

### 7.1 User Management Integration
- Validates internal user existence and account membership
- Retrieves user display names and email addresses
- Enforces account-level permissions

### 7.2 Brand Contact Integration
- Imports contact information for external invitations
- Maintains reference to original brand contact
- Supports bulk invitation from brand contact lists

### 7.3 Post Management Integration
- Enforces creator-participant relationships
- Manages reviewer assignments
- Controls content access based on participant roles

### 7.4 Chat System Integration
- Determines channel access based on participant roles
- Manages participant visibility in chat channels
- Controls message permissions and notifications

---

## 8. Error Handling & Validation

### 8.1 Common Validations
- Email format validation
- Role enum validation
- Hub access permissions
- Account membership verification
- Magic link token validation and expiration

### 8.2 Error Responses
- `400 Bad Request` - Invalid email format, missing required fields
- `401 Unauthorized` - Invalid or expired magic link token
- `403 Forbidden` - Insufficient permissions, cross-account access
- `404 Not Found` - Hub, user, or participant not found
- `409 Conflict` - Duplicate email within hub, role conflicts

### 8.3 Security Considerations
- Rate limiting on magic link generation
- Token entropy and secure generation
- Cross-account access prevention
- Audit logging for sensitive operations
- Input sanitization and validation

---

## 9. Performance Considerations

### 9.1 Database Optimization
- Indexed queries on hub_id and email
- Efficient joins with user and brand_contact tables
- Pagination for large participant lists
- Bulk operations for multiple invitations

### 9.2 Caching Strategy
- Participant role caching for permission checks
- Magic link token validation caching
- User display name caching
- Hub participant count caching

---

## 10. Permissions & Authorization

### 10.1 Hub Participant Permissions

The system uses dedicated hub participant permissions separate from general collaboration hub permissions:

| Permission | Description | Admin | User |
|------------|-------------|-------|------|
| `hub-participant:read` | View participant lists and details | ✅ | ✅ |
| `hub-participant:write` | Basic participant operations | ✅ | ✅ |
| `hub-participant:invite` | Invite new participants | ✅ | ✅ |
| `hub-participant:manage` | Role changes, removal (admin-only) | ✅ | ❌ |

### 10.2 Permission Separation

Hub participant permissions are **separate** from general hub permissions:
- **Hub permissions** (`hub:read`, `hub:write`, `hub:delete`) - Manage hub settings, metadata
- **Participant permissions** (`hub-participant:*`) - Manage hub participants and access

This separation ensures granular access control and follows the principle of least privilege.

## 11. API Endpoints

### 11.1 Participant Management

| Method | Endpoint | Description | Required Permission |
|--------|----------|-------------|-------------------|
| POST | `/api/hubs/{hubId}/participants` | Invite participants to hub | `hub-participant:invite` |
| GET | `/api/hubs/{hubId}/participants` | List hub participants | `hub-participant:read` |
| GET | `/api/hubs/{hubId}/participants/{participantId}` | Get participant details | `hub-participant:read` |
| PUT | `/api/hubs/{hubId}/participants/{participantId}/role` | Update participant role | `hub-participant:manage` |
| DELETE | `/api/hubs/{hubId}/participants/{participantId}` | Remove participant from hub | `hub-participant:manage` |
| POST | `/api/hubs/{hubId}/participants/{participantId}/resend-invite` | Resend invitation | `hub-participant:invite` |

### 11.2 Magic Link Authentication

| Method | Endpoint | Description | Required Permission |
|--------|----------|-------------|-------------------|
| GET | `/api/auth/magic-link/{token}` | Validate and authenticate via magic link | None (public) |
| POST | `/api/auth/magic-link/refresh` | Refresh external participant tokens | None (cookie-based) |

### 11.3 Request/Response Examples

#### Invite Participants
**POST** `/api/hubs/456/participants`

**Request:**
```json
{
  "participants": [
    {
      "type": "internal",
      "user_id": 123,
      "role": "content_creator"
    },
    {
      "type": "external",
      "email": "<EMAIL>",
      "name": "External Reviewer",
      "role": "reviewer"
    },
    {
      "type": "brand_contact",
      "brand_contact_id": 789,
      "role": "reviewer"
    }
  ]
}
```

**Response (201):**
```json
{
  "invited": [
    {
      "id": 101,
      "user_id": 123,
      "email": "<EMAIL>",
      "name": "John Creator",
      "role": "content_creator",
      "is_external": false,
      "invited_at": "2025-06-19T10:30:00Z",
      "joined_at": "2025-06-19T10:30:00Z",
      "status": "active"
    },
    {
      "id": 102,
      "user_id": null,
      "email": "<EMAIL>",
      "name": "External Reviewer",
      "role": "reviewer",
      "is_external": true,
      "magic_link_token": "abc123def456",
      "magic_link_expiry": "2025-06-26T10:30:00Z",
      "invited_at": "2025-06-19T10:30:00Z",
      "joined_at": null,
      "status": "pending"
    },
    {
      "id": 103,
      "user_id": null,
      "email": "<EMAIL>",
      "name": "Brand Contact",
      "role": "reviewer",
      "is_external": true,
      "magic_link_token": "def456ghi789",
      "magic_link_expiry": "2025-06-26T10:30:00Z",
      "invited_at": "2025-06-19T10:30:00Z",
      "joined_at": null,
      "status": "pending"
    }
  ]
}
```

#### List Participants
**GET** `/api/hubs/456/participants?status=active&role=reviewer`

**Response (200):**
```json
{
  "participants": [
    {
      "id": 102,
      "user_id": null,
      "email": "<EMAIL>",
      "name": "External Reviewer",
      "role": "reviewer",
      "is_external": true,
      "invited_at": "2025-06-19T10:30:00Z",
      "joined_at": "2025-06-19T14:15:00Z",
      "status": "active"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "total_pages": 1
  },
  "filters": {
    "available_statuses": ["active", "pending", "removed"],
    "available_roles": ["admin", "content_creator", "reviewer", "reviewer_creator"],
    "current_status": "active",
    "current_role": "reviewer"
  }
}
```

---

## 12. Audit & Compliance

### 12.1 Audit Trail
- Participant invitation events
- Role change history
- Access attempts and authentication
- Removal and restoration events

### 12.2 Data Retention
- Soft deletion preserves historical data
- Magic link token cleanup after expiration
- Participant activity logging
- GDPR compliance for external user data
