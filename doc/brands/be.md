# Backend API & Logic for Brands (with Contacts & Soft Deletion)

## 1. Data Model

### Table: `brands`

- **Primary Key:** `id` (bigint)
- **Columns:**
    - `account_id` (bigint, not null) — Tenant identifier for multi-tenancy
    - `name` (string, not null) — Display name of the brand
    - `company_name` (string, not null) — Legal name of the company
    - `address_street` (string, nullable)
    - `address_city` (string, nullable)
    - `address_postal_code` (string, nullable)
    - `address_country` (string, nullable)
    - `vat_number` (string, nullable)
    - `registration_number` (string, nullable)
    - `phone` (string, nullable)
    - `email` (string, nullable)
    - `website` (string, nullable)
    - `deleted_at` (timestamp, nullable)
    - `created_at` (timestamp, not null)
    - `updated_at` (timestamp, not null)

### Table: `brand_contacts`

- **Primary Key:** `id` (bigint)
- **Columns:**
    - `brand_id` (bigint, not null) — Foreign key to `brands.id`
    - `account_id` (bigint, not null) — Redundant for multi-tenancy checks
    - `name` (string, not null) — Contact name
    - `email` (string, not null) — Contact email
    - `notes` (string, nullable) — Internal notes about the contact
    - `created_at` (timestamp, not null)
    - `updated_at` (timestamp, not null)

---

## 2. Multi-Tenancy & Access Control

- All operations are scoped by `account_id`.
- Users can only access brands and contacts that belong to their current `account_id`.
- `deleted_at IS NOT NULL` records are excluded from normal queries.
- Contacts must also belong to brands within the same account.
- All brand-contact operations are guarded to ensure access only to entities within the current tenant context.

---

## 3. CRUD Operations & Business Logic

### Create Brand

- **Input:** `name`, `company_name`, optional address, VAT, registration, contact info, etc.
- Validate that `name` and `company_name` are not empty.
- Insert a new brand under the current user’s `account_id`.
- **Create Contacts:** For each contact included in the request, insert them as associated contacts for the new brand. Contacts should have their `brand_id` and `account_id` set appropriately.
- Return the created brand and its contacts.

### Read Brands

- **List:** Return all brands for current `account_id` where `deleted_at IS NULL`.
- **Single Item:** Return brand by `id` if it belongs to user’s account and is not deleted. The response will also include associated contacts.

### Update Brand

- Allow update only if brand belongs to current account and is not deleted.
- Validate updated fields.
- Update fields and `updated_at`.
- **Update Contacts:** Allow updating existing contacts. Update contact fields (`name`, `email`, `notes`) for contacts associated with the brand. Ensure that contacts remain scoped to the correct brand.
- Return the updated brand and its contacts.

### Soft Delete Brand

- Set `deleted_at` to current timestamp.
- Soft-deleted brands are excluded from normal queries.
- **Soft Delete Contacts:** Contacts associated with a soft-deleted brand will also be excluded from future reads.
- Return the soft-deleted brand and associated contacts.

---

## 4. API Endpoints

### Brand Endpoints

| Method | Endpoint                 | Description                                                    |
|--------|--------------------------|----------------------------------------------------------------|
| GET    | `/api/brands`            | List all active (not deleted) brands for current account       |
| POST   | `/api/brands`            | Create a new brand and associated contacts                     |
| GET    | `/api/brands/{id}`       | Get brand by ID if active and belongs to current account       |
| PUT    | `/api/brands/{id}`       | Update brand and associated contacts                           |
| DELETE | `/api/brands/{id}`       | Soft delete brand and associated contacts                      |

---

## 5. Error Handling & Validation

- Return `404 Not Found` if brand/contact does not exist or does not belong to the account.
- Return `400 Bad Request` for missing required fields (e.g., `name`, `company_name`, `email`).
- Return `403 Forbidden` for unauthorized access outside current tenant scope.
- Use `201 Created`, `200 OK`, or `204 No Content` for successful operations.
- Contacts cannot be added to soft-deleted brands.

---

## 6. Additional Notes

- All date/timestamp fields are in UTC.
- Soft-deletion preserves historical data integrity (e.g., previously issued invoices).
- Contacts are designed for reusability in invoice delivery and future CRM features.
- Brands and contacts should be treated as a unified resource for management purposes.
- Pagination, sorting, and filtering can be added to list endpoints.
- Consider adding audit logs for brand/contact modifications if compliance is required.
