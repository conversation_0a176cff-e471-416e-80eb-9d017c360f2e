# Frontend Documentation — Brands Feature

## Overview

The **Brands** feature allows users to manage companies they work with, typically clients or recipients of invoices. Each brand includes legal and contact information, including optional invoice contacts used when sending invoices. Brands are scoped per account and support soft deletion.

---

## 1. Routes & Navigation

| Route                | Purpose                                         | Access                                          |
|----------------------|-------------------------------------------------|-------------------------------------------------|
| `/brands`            | List all active (non-deleted) brands           | Logged-in users with **read** permission        |
| `/brands/new`        | Create a new brand                             | Users with **write** permission                 |
| `/brands/:id`        | View and edit a specific brand                 | Users with **read** (view), **write** (edit) permission |

- Add a **Brands** link in the main navigation or sidebar.
- Routes are protected and require authentication.
- Enforce role-based access control per route and UI actions.

---

## 2. UI Components & Pages

### 2.1 Brand List Page (`/brands`)

- **Layout:** Responsive list or card grid.
- **Each Card Displays:**
    - Brand `name` and `company_name`
    - Primary contact email (if available)
    - Country or address preview
    - Invoice contact count (e.g. "3 contacts")
    - Action buttons (Edit, Delete) shown only if user has **write** permission.

- **Features:**
    - "+ Add Brand" button for users with **write** access.
    - Soft delete with confirmation modal.
    - Empty state encouraging adding brands.
    - Loading spinner and error state for data fetch.

- **Mobile Responsiveness:**
    - Cards stack vertically.
    - Use expandable/collapsible sections for long data.

### 2.2 Brand Form Page (`/brands/new`, `/brands/:id`)

- **Form Fields:**
    - Name (required)
    - Company Name (required)
    - Address: street, city, postal code, country
    - VAT Number (optional)
    - Registration Number (optional)
    - Phone (optional)
    - Email (optional, must be valid format)
    - Website (optional)

- **Invoice Contacts Section:**
    - Add/edit/remove contacts
    - Each contact includes:
        - Full name (optional)
        - Email (required)
        - Notes (optional)
    - Client-side email validation

- **Access Control:**
    - Editable form for users with **write** access
    - Read-only mode for users with **read** access

- **UX:**
    - Client-side validation
    - Disabled Save button while submitting or invalid
    - Cancel button returns to `/brands`
    - On success: redirect and show toast
    - On error: retain inputs, show inline messages

### 2.3 Soft Delete Confirmation Modal

- **Only for users with write permission**
- **Content:**
    - "Are you sure you want to delete brand ‘[Brand Name]’? This will hide it from the platform and future invoices."
- **UX:**
    - Dismissable modal with Cancel and Delete buttons
    - Optimistic UI update on delete
    - Show error toast on failure

---

## 3. State Management

- Use React Query for data fetching and cache invalidation
- Cache brand list and single brand
- Invalidate list after create/update/delete
- Track form state locally
- Use toast notifications for user feedback

---

## 4. API Integration

- Use authenticated bearer token for all requests
- Integrate with backend endpoints:

| Method | Endpoint                              | Description                                                     |
|--------|----------------------------------------|-----------------------------------------------------------------|
| GET    | `/api/brands`                          | List all active brands for current account                      |
| POST   | `/api/brands`                          | Create a new brand                                              |
| GET    | `/api/brands/{id}`                     | Fetch brand details by ID                                       |
| PUT    | `/api/brands/{id}`                     | Update brand details                                            |
| DELETE | `/api/brands/{id}`                     | Soft delete a brand and its contacts                            |

---

## 5. Accessibility & Responsiveness

- Fully keyboard accessible pages and modals
- Proper label and `aria-*` usage
- Mobile-first responsive layout:
    - Card/grid on desktop
    - Single column stack on mobile
- Manage focus after modal open/close and form submit

---

## 6. Visual Style & Consistency

- Follow design system (e.g., Shadcn UI)
- Consistent use of spacing, colors, typography
- Icons on edit/delete buttons
- Clear disabled states and click feedback

---

## 7. Error Scenarios

- Inline error messages for:
    - Invalid form input
    - API fetch/save/update/delete failures
- Retry option for transient errors
- Display support/help option for persistent errors

---

## 8. Summary

- Role-based access with soft deletion support
- Rich brand forms including contacts
- Optimistic UI updates and toast feedback
- Fully integrated with RESTful API
- Accessible, responsive, and consistent UX

---

Let me know if you want a prototype layout or component tree suggestion!
