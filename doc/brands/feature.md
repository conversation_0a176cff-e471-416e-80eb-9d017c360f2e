# 🏷️ Brands Feature

## Purpose

Enable users to store and manage brand profiles representing clients or organizations they do business with. These profiles serve as the recipients of invoices and other business documents. Brands are scoped per account to support multi-tenancy and maintain clean data segregation.

## Key Capabilities

- Create and manage multiple brand profiles per account.
- Store comprehensive brand details, including:
    - Brand display name
    - Legal company name (required)
    - Company address: street, city, postal code, country (all optional)
    - VAT or Tax Identification Number (optional)
    - Business registration number (optional)
    - Phone number and email address (optional)
    - Website URL (optional)
- Support soft deletion of brands via a `deleted_at` timestamp.
- Track creation and update timestamps.
- Associate each brand with one or more contacts for communication purposes.
- Select brand contacts as recipients during invoice sending.
- Enforce account-level isolation and access control for multi-tenancy.

## Contact Management

Each brand can have one or more associated contacts:

- Contact fields include:
    - Name (required)
    - Email (required)
    - Notes (optional)
- Contacts are stored per brand and scoped to the same account.
- During invoice sending, users can select one or more contacts from the chosen brand as email recipients.
- Contacts help streamline invoice delivery while maintaining recipient-specific notes.

## Data Reusability & Integrity

- Brand and contact data is centrally stored and reusable across invoices.
- Changes to brand or contact info affect future invoices but not past ones.
- Historical invoices retain recipient data at the time of sending to ensure audit accuracy.

## User Workflow Changes

- Users can create and manage brand profiles under a dedicated "Brands" section.
- When creating an invoice, users select a brand as the recipient.
- Available contacts for the selected brand are shown during invoice sending.
- Soft-deleted brands are hidden from active selections but remain for reference and data consistency.

## Benefits

- Streamlines recipient management for invoices.
- Centralizes client data for reuse and consistency.
- Reduces repetitive data entry and potential errors.
- Supports agencies or businesses managing multiple client brands.
- Maintains clean multi-tenant data boundaries and secure access.
