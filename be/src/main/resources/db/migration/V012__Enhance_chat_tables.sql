-- Migration V011: Enhance chat tables for mentions, attachments, and editing support

-- 1. Add new columns to chat_message table
ALTER TABLE chat_message 
ADD COLUMN mentions JSONB DEFAULT '[]'::jsonb,
ADD COLUMN attachment_uris JSONB DEFAULT '[]'::jsonb,
ADD COLUMN updated_at TIMESTAMP,
ADD COLUMN edited_at TIMESTAMP;

-- 2. Add last_activity_at to chat_channel table for sorting and unread tracking
ALTER TABLE chat_channel 
ADD COLUMN last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
