-- Migration V019: Simplify authentication architecture
-- Use ENUM for user_type

-- Step 1: Create ENUM type
CREATE TYPE user_type_enum AS ENUM ('INTERNAL', 'EXTERNAL_PARTICIPANT', 'EXTERNAL_ACCOUNTANT');

-- Step 2: Add columns
ALTER TABLE refresh_token
    ADD COLUMN user_type user_type_enum NOT NULL DEFAULT 'INTERNAL';

ALTER TABLE refresh_token
    ADD COLUMN email VARCHAR(255);

ALTER TABLE verification_token
    ADD COLUMN user_type user_type_enum;

-- Step 3: Backfill user_type values
UPDATE verification_token
SET user_type = 'EXTERNAL_PARTICIPANT'
WHERE email IS NOT NULL AND token_type = 'MA<PERSON>C_LINK';

UPDATE verification_token
SET user_type = 'INTERNAL'
WHERE user_id IS NOT NULL;

-- Step 4: Add conditional CHECK on email vs user_type
ALTER TABLE refresh_token
    ADD CONSTRAINT chk_refresh_token_external_email
        CHECK (
            (user_type = 'INTERNAL' AND email IS NULL) OR
            (user_type IN ('EXTERNAL_PARTICIPANT', 'EXTERNAL_ACCOUNTANT') AND email IS NOT NULL)
            );
