-- Create sequence for account_companies IDs
CREATE SEQUENCE account_company_id_seq START WITH 1 INCREMENT BY 1;

-- Create account_companies table for brand/company CRM with soft deletion
CREATE TABLE account_company (
    id BIGINT PRIMARY KEY DEFAULT nextval('account_company_id_seq'),
    account_id BIGINT NOT NULL REFERENCES account(id),
    company_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    address_street VARCHAR(500),
    address_city VARCHAR(100),
    address_postal_code VARCHAR(20),
    address_country VARCHAR(100),
    vat_number VARCHAR(50),
    registration_number VARCHAR(100),
    phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(500),
    deleted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP
);