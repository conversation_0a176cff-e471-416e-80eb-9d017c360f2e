-- Remove magic link fields from hub_participants table
-- We're moving to email-based magic links using the verification_token table

ALTER TABLE hub_participant
DROP COLUMN IF EXISTS magic_token,
DROP COLUMN IF EXISTS magic_token_expires_at;

-- Add email column to verification_token table to support email-based magic links
-- This allows us to create magic links for emails that don't have user accounts yet
ALTER TABLE verification_token 
ADD COLUMN email VARCHAR(255) NULL;

-- Make user_id nullable for external user magic links
ALTER TABLE verification_token 
ALTER COLUMN user_id DROP NOT NULL;

-- Add constraint to ensure either user_id or email is provided
ALTER TABLE verification_token 
ADD CONSTRAINT chk_verification_token_user_or_email 
CHECK ((user_id IS NOT NULL AND email IS NULL) OR (user_id IS NULL AND email IS NOT NULL));

-- Add index for email-based lookups
CREATE INDEX idx_verification_token_email_type ON verification_token(email, token_type);

-- Add account_id column for multi-tenant isolation of external magic links
ALTER TABLE verification_token 
ADD COLUMN account_id BIGINT NULL;

-- Add index for account-based lookups
CREATE INDEX idx_verification_token_account ON verification_token(account_id);

-- Add comments for documentation
COMMENT ON COLUMN verification_token.email IS 'Email address for external user magic links (mutually exclusive with user_id)';
COMMENT ON COLUMN verification_token.account_id IS 'Account ID for multi-tenant isolation of external magic links';
