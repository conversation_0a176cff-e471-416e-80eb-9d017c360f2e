-- Migration to support email-based notification preferences for external participants
-- External participants have global preferences based only on email (multi-account access)
-- Internal users continue to use user_id but also store email for consistency

-- Step 1: Add email column for email-based preferences
ALTER TABLE notification_preference
ADD COLUMN email VARCHAR(255);

-- Step 2: Populate email for existing records from user table
UPDATE notification_preference
SET email = u.email
FROM "user" u
WHERE notification_preference.user_id = u.id;

-- Step 3: Make user_id nullable (for external participants)
ALTER TABLE notification_preference
ALTER COLUMN user_id DROP NOT NULL;

-- Step 4: Drop the old unique constraint
ALTER TABLE notification_preference
DROP CONSTRAINT uq_notification_preference_user_type_channel;

-- Step 5: Add check constraint to ensure either user_id OR email is set
ALTER TABLE notification_preference
ADD CONSTRAINT chk_notification_preference_user_or_email
CHECK (
    (user_id IS NOT NULL AND email IS NOT NULL) OR
    (user_id IS NULL AND email IS NOT NULL)
);

-- Step 6: Create new unique constraints for both cases
-- For internal users: user_id + type + channel must be unique
CREATE UNIQUE INDEX uq_notification_preference_user_type_channel
ON notification_preference (user_id, type, channel)
WHERE user_id IS NOT NULL;

-- For external participants: email + type + channel must be unique (global across accounts)
CREATE UNIQUE INDEX uq_notification_preference_email_type_channel
ON notification_preference (email, type, channel)
WHERE user_id IS NULL;

-- Step 7: Add performance indexes
CREATE INDEX idx_notification_preference_email
ON notification_preference (email)
WHERE user_id IS NULL;

-- Note: idx_notification_preference_user_id already exists from V023, no need to recreate

-- Step 8: Update table comments
COMMENT ON COLUMN notification_preference.email IS 'Email address for notification delivery (required for all users, global for external participants)';
COMMENT ON COLUMN notification_preference.user_id IS 'User ID for internal users (null for external participants)';

-- Step 9: Ensure all existing records have email populated
-- This should be 0 if step 2 worked correctly
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM notification_preference
        WHERE email IS NULL
    ) THEN
        RAISE EXCEPTION 'Migration failed: Some notification preferences are missing email';
    END IF;
END $$;

-- Step 10: Make email NOT NULL now that it's populated
ALTER TABLE notification_preference
ALTER COLUMN email SET NOT NULL;
