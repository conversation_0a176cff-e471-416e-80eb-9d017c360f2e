-- Migration: Add name field to hub_participant table
-- This allows storing display names for both internal and external participants

-- Add name column to hub_participant table
ALTER TABLE hub_participant 
ADD COLUMN name VARCHAR(255) NULL;

-- Add comment to document the purpose of the name field
COMMENT ON COLUMN hub_participant.name IS 'Display name for the participant. For internal users, this can override the user.name. For external users, this is their provided name.';

-- Create index for name-based searches (optional but recommended for performance)
CREATE INDEX idx_hub_participant_name ON hub_participant(name) WHERE name IS NOT NULL;
