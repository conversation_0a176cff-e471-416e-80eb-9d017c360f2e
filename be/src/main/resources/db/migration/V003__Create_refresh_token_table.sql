CREATE SEQUENCE refresh_token_id_seq START WITH 1 INCREMENT BY 1;

-- Create refresh_token table for opaque refresh token management
CREATE TABLE refresh_token (
    id BIGINT PRIMARY KEY default nextval('refresh_token_id_seq'),
    token VARCHAR(255) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL REFERENCES "user"(id),
    issued_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    revoked BOOLEAN NOT NULL DEFAULT false,
    replaced_by BIGINT REFERENCES refresh_token(id),
    user_agent VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);


-- Add check constraint to ensure expires_at is after issued_at
ALTER TABLE refresh_token 
ADD CONSTRAINT chk_refresh_token_dates 
CHECK (expires_at > issued_at);
