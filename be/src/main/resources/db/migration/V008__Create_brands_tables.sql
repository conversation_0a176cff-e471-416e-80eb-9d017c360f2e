-- Create sequence for brand IDs
CREATE SEQUENCE brand_id_seq START WITH 1 INCREMENT BY 1;

-- Create brands table for brand CRM with soft deletion
CREATE TABLE brand (
    id BIGINT PRIMARY KEY DEFAULT nextval('brand_id_seq'),
    account_id BIGINT NOT NULL REFERENCES account(id),
    name VA<PERSON>HAR(255) NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    address_street VARCHAR(500),
    address_city VARCHAR(100),
    address_postal_code VARCHAR(20),
    address_country VARCHAR(100),
    vat_number VARCHAR(50),
    registration_number VARCHAR(100),
    phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(500),
    deleted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP
);

-- Create sequence for brand contact IDs
CREATE SEQUENCE brand_contact_id_seq START WITH 1 INCREMENT BY 1;

-- Create brand_contacts table for managing brand contact persons
CREATE TABLE brand_contact (
    id BIGINT PRIMARY KEY DEFAULT nextval('brand_contact_id_seq'),
    brand_id BIGINT NOT NULL REFERENCES brand(id) ON DELETE CASCADE,
    account_id BIGINT NOT NULL REFERENCES account(id),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP
);
