-- Create sequence for verification token IDs
CREATE SEQUENCE verification_token_id_seq START WITH 1 INCREMENT BY 1;

-- Create verification_token table for email verification, magic links, and password reset
CREATE TABLE verification_token (
    id BIGINT PRIMARY KEY default nextval('verification_token_id_seq'),
    token VARCHAR(255) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL REFERENCES "user"(id),
    token_type VARCHAR(50) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    revoked BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add check constraint to ensure expires_at is in the future
ALTER TABLE verification_token 
ADD CONSTRAINT chk_verification_token_expiry 
CHECK (expires_at > created_at);

-- Create index for efficient token lookups
CREATE INDEX idx_verification_token_token ON verification_token(token);
CREATE INDEX idx_verification_token_user_type ON verification_token(user_id, token_type);
CREATE INDEX idx_verification_token_expiry ON verification_token(expires_at);
