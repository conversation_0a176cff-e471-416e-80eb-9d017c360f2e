-- Remove foreign key constraint from refresh_token table to allow external user IDs
-- External users don't need to exist in the user table as they're managed through hub participants
-- The refresh token metadata stores all necessary information (email, account_id)

ALTER TABLE refresh_token 
DROP CONSTRAINT IF EXISTS refresh_token_user_id_fkey;

-- Add comment to document that user_id can be external (non-FK) for external users
COMMENT ON COLUMN refresh_token.user_id IS 'User ID - can be internal user FK or external user generated ID';
