-- Migration V028: Create notification batching tables and add urgency support
-- Implements email notification batching to reduce email spam

-- Step 1: Create notification urgency enum
CREATE TYPE notification_urgency_enum AS ENUM (
    'LOW',      -- Can wait for batching (e.g., general comments)
    'NORMAL',   -- Standard batching (e.g., post reviews, assignments)
    'HIGH',     -- Shorter batching window (e.g., mentions, direct assignments)
    'URGENT'    -- Immediate delivery, bypass batching (e.g., security alerts)
);

-- Step 2: Add urgency column to notification table
ALTER TABLE notification 
ADD COLUMN urgency notification_urgency_enum NOT NULL DEFAULT 'NORMAL';

-- Step 3: Create notification batch queue table
CREATE SEQUENCE notification_batch_queue_id_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE notification_batch_queue (
    id BIGINT PRIMARY KEY DEFAULT nextval('notification_batch_queue_id_seq'),
    user_id BIGINT NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    notification_type notification_type_enum NOT NULL,
    urgency notification_urgency_enum NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Entity references for email context
    collaboration_hub_id BIGINT REFERENCES collaboration_hub(id) ON DELETE CASCADE,
    post_id BIGINT REFERENCES post(id) ON DELETE CASCADE,
    comment_id BIGINT REFERENCES post_comment(id) ON DELETE CASCADE,
    chat_channel_id BIGINT REFERENCES chat_channel(id) ON DELETE CASCADE,
    brief_id BIGINT REFERENCES collaboration_brief(id) ON DELETE CASCADE,
    
    -- Batching metadata
    batch_window_start TIMESTAMP NOT NULL,
    metadata JSONB,
    
    -- Processing status
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- PENDING, PROCESSING, SENT, FAILED
    retry_count INTEGER NOT NULL DEFAULT 0,
    last_retry_at TIMESTAMP,
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_batch_queue_status CHECK (status IN ('PENDING', 'PROCESSING', 'SENT', 'FAILED')),
    CONSTRAINT chk_batch_queue_retry_count CHECK (retry_count >= 0)
);

-- Step 4: Create indexes for efficient batch processing
CREATE INDEX idx_notification_batch_queue_user_batch_window ON notification_batch_queue(user_id, batch_window_start);
CREATE INDEX idx_notification_batch_queue_status_created ON notification_batch_queue(status, created_at);
CREATE INDEX idx_notification_batch_queue_batch_window ON notification_batch_queue(batch_window_start);
CREATE INDEX idx_notification_batch_queue_user_status ON notification_batch_queue(user_id, status);

-- Step 5: Create batch processing lock table for distributed processing
CREATE TABLE notification_batch_processing_lock (
    lock_key VARCHAR(100) PRIMARY KEY,
    locked_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    locked_by VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL
);

-- Step 6: Add indexes for notification urgency
CREATE INDEX idx_notification_urgency ON notification(urgency);
CREATE INDEX idx_notification_user_urgency ON notification(user_id, urgency);

-- Step 7: Add comments for documentation
COMMENT ON TABLE notification_batch_queue IS 'Queue for batching email notifications to reduce email spam';
COMMENT ON COLUMN notification_batch_queue.batch_window_start IS 'Start time of the batching window for grouping notifications';
COMMENT ON COLUMN notification_batch_queue.status IS 'Processing status: PENDING, PROCESSING, SENT, FAILED';
COMMENT ON COLUMN notification_batch_queue.retry_count IS 'Number of retry attempts for failed notifications';

COMMENT ON TABLE notification_batch_processing_lock IS 'Distributed lock table for batch processing coordination';
COMMENT ON COLUMN notification_batch_processing_lock.lock_key IS 'Unique key for the lock (e.g., batch_processor)';
COMMENT ON COLUMN notification_batch_processing_lock.locked_by IS 'Instance identifier that holds the lock';
COMMENT ON COLUMN notification_batch_processing_lock.expires_at IS 'Lock expiration time for automatic cleanup';

COMMENT ON COLUMN notification.urgency IS 'Notification urgency level affecting batching behavior';
