-- Create enum types for invoice-related fields
CREATE TYPE invoice_status AS ENUM ('draft', 'sent', 'overdue', 'paid');
CREATE TYPE recipient_type AS ENUM ('original', 'copy');
CREATE TYPE recipient_source AS ENUM ('brand_contact', 'manual');

-- Create sequence for invoice IDs
CREATE SEQUENCE invoice_id_seq START WITH 1 INCREMENT BY 1;

-- Create invoices table for invoice management with multi-tenancy and soft deletion
CREATE TABLE invoice (
    id BIGINT PRIMARY KEY DEFAULT nextval('invoice_id_seq'),
    account_id BIGINT NOT NULL REFERENCES account(id),
    invoice_number VARCHAR(50) NOT NULL,
    status invoice_status NOT NULL DEFAULT 'draft',
    issuer_snapshot JSONB NOT NULL,
    recipient_snapshot JSONB NOT NULL,
    bank_details_snapshot JSONB NOT NULL,
    template VARCHAR(50) DEFAULT 'default',
    currency VARCHAR(3) DEFAULT 'EUR',
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    notes TEXT,
    subtotal DECIMAL(12,2) NOT NULL,
    total_vat DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL,
    deleted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    
    -- Constraints
    CONSTRAINT uq_invoice_number_per_account UNIQUE(account_id, invoice_number),
    CONSTRAINT chk_invoice_due_date CHECK(due_date >= issue_date),
    CONSTRAINT chk_invoice_amounts CHECK(subtotal >= 0 AND total_vat >= 0 AND total_amount >= 0)
);

-- Create sequence for invoice item IDs
CREATE SEQUENCE invoice_item_id_seq START WITH 1 INCREMENT BY 1;

-- Create invoice_items table for line items
CREATE TABLE invoice_item (
    id BIGINT PRIMARY KEY DEFAULT nextval('invoice_item_id_seq'),
    invoice_id BIGINT NOT NULL REFERENCES invoice(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    quantity DECIMAL(10,3) DEFAULT 1,
    unit_price DECIMAL(12,2) NOT NULL,
    vat_rate DECIMAL(5,4) DEFAULT 0,
    line_total DECIMAL(12,2) NOT NULL,
    vat_amount DECIMAL(12,2) DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT chk_invoice_item_quantity CHECK(quantity > 0),
    CONSTRAINT chk_invoice_item_unit_price CHECK(unit_price >= 0),
    CONSTRAINT chk_invoice_item_vat_rate CHECK(vat_rate >= 0 AND vat_rate <= 1)
);

-- Create sequence for invoice recipient IDs
CREATE SEQUENCE invoice_recipient_id_seq START WITH 1 INCREMENT BY 1;

-- Create invoice_recipients table for tracking recipients
CREATE TABLE invoice_recipient (
    id BIGINT PRIMARY KEY DEFAULT nextval('invoice_recipient_id_seq'),
    invoice_id BIGINT NOT NULL REFERENCES invoice(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    type recipient_type NOT NULL,
    source recipient_source NOT NULL,
    brand_contact_id BIGINT,
    first_sent_at TIMESTAMP,
    last_sent_at TIMESTAMP,
    send_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT uq_invoice_recipient_email UNIQUE(invoice_id, email),
    CONSTRAINT chk_invoice_recipient_email CHECK(email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create sequence for invoice delivery log IDs
CREATE SEQUENCE invoice_delivery_log_id_seq START WITH 1 INCREMENT BY 1;

-- Create invoice_delivery_logs table for audit trail
CREATE TABLE invoice_delivery_log (
    id BIGINT PRIMARY KEY DEFAULT nextval('invoice_delivery_log_id_seq'),
    invoice_id BIGINT NOT NULL REFERENCES invoice(id) ON DELETE CASCADE,
    recipient_email VARCHAR(255) NOT NULL,
    delivery_status VARCHAR(50) DEFAULT 'pending',
    message_content TEXT,
    pdf_version VARCHAR(50),
    is_resend BOOLEAN DEFAULT false,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT chk_delivery_log_email CHECK(recipient_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);
