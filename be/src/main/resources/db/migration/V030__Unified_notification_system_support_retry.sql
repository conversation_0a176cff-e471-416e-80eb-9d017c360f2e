-- Migration V030: Unified notification system support for internal and external users (retry of V029)
-- This migration enables the notification system to handle both:
-- - Internal users: identified by user_id (references user table)
-- - External users: identified by email only (hub participants)

-- ========================================
-- STEP 1: Update notification table
-- ========================================

-- Add email column for external user support (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification' AND column_name = 'email'
    ) THEN
        ALTER TABLE notification ADD COLUMN email VARCHAR(255);
    END IF;
END $$;

-- Make user_id nullable to support external users (if not already nullable)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification' AND column_name = 'user_id' AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE notification ALTER COLUMN user_id DROP NOT NULL;
    END IF;
END $$;

-- Add constraint: either user_id OR email must be provided (not both, not neither)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'notification' AND constraint_name = 'chk_notification_recipient_type'
    ) THEN
        ALTER TABLE notification
        ADD CONSTRAINT chk_notification_recipient_type
        CHECK (
            (user_id IS NOT NULL AND email IS NULL) OR 
            (user_id IS NULL AND email IS NOT NULL)
        );
    END IF;
END $$;

-- Add constraint: email format validation for external users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'notification' AND constraint_name = 'chk_notification_email_format'
    ) THEN
        ALTER TABLE notification
        ADD CONSTRAINT chk_notification_email_format
        CHECK (
            email IS NULL OR 
            (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' AND LENGTH(email) <= 255)
        );
    END IF;
END $$;

-- ========================================
-- STEP 2: Update notification_batch_queue table
-- ========================================

-- Add email column for external user support (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification_batch_queue' AND column_name = 'email'
    ) THEN
        ALTER TABLE notification_batch_queue ADD COLUMN email VARCHAR(255);
    END IF;
END $$;

-- Make user_id nullable to support external users (if not already nullable)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification_batch_queue' AND column_name = 'user_id' AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE notification_batch_queue ALTER COLUMN user_id DROP NOT NULL;
    END IF;
END $$;

-- Add constraint: either user_id OR email must be provided (not both, not neither)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'notification_batch_queue' AND constraint_name = 'chk_batch_queue_recipient_type'
    ) THEN
        ALTER TABLE notification_batch_queue
        ADD CONSTRAINT chk_batch_queue_recipient_type
        CHECK (
            (user_id IS NOT NULL AND email IS NULL) OR 
            (user_id IS NULL AND email IS NOT NULL)
        );
    END IF;
END $$;

-- Add constraint: email format validation for external users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'notification_batch_queue' AND constraint_name = 'chk_batch_queue_email_format'
    ) THEN
        ALTER TABLE notification_batch_queue
        ADD CONSTRAINT chk_batch_queue_email_format
        CHECK (
            email IS NULL OR 
            (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' AND LENGTH(email) <= 255)
        );
    END IF;
END $$;

-- ========================================
-- STEP 3: Create performance indexes
-- ========================================

-- Indexes for external user notification queries (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_notification_email') THEN
        CREATE INDEX idx_notification_email ON notification(email) WHERE user_id IS NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_notification_email_status') THEN
        CREATE INDEX idx_notification_email_status ON notification(email, status) WHERE user_id IS NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_notification_email_created_at') THEN
        CREATE INDEX idx_notification_email_created_at ON notification(email, created_at DESC) WHERE user_id IS NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_notification_email_type') THEN
        CREATE INDEX idx_notification_email_type ON notification(email, type) WHERE user_id IS NULL;
    END IF;
END $$;

-- Indexes for external user batch queue queries (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_batch_queue_email') THEN
        CREATE INDEX idx_batch_queue_email ON notification_batch_queue(email) WHERE user_id IS NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_batch_queue_email_status') THEN
        CREATE INDEX idx_batch_queue_email_status ON notification_batch_queue(email, status) WHERE user_id IS NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_batch_queue_email_batch_window') THEN
        CREATE INDEX idx_batch_queue_email_batch_window ON notification_batch_queue(email, batch_window_start) WHERE user_id IS NULL;
    END IF;
END $$;

-- Composite indexes for mixed queries (both internal and external) (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_notification_recipient_status') THEN
        CREATE INDEX idx_notification_recipient_status ON notification(COALESCE(user_id::text, email), status);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_notification_recipient_created_at') THEN
        CREATE INDEX idx_notification_recipient_created_at ON notification(COALESCE(user_id::text, email), created_at DESC);
    END IF;
END $$;

-- ========================================
-- STEP 4: Update table comments
-- ========================================

COMMENT ON COLUMN notification.user_id IS 'User ID for internal users (null for external users identified by email)';
COMMENT ON COLUMN notification.email IS 'Email address for external users (null for internal users identified by user_id)';

COMMENT ON COLUMN notification_batch_queue.user_id IS 'User ID for internal users (null for external users identified by email)';
COMMENT ON COLUMN notification_batch_queue.email IS 'Email address for external users (null for internal users identified by user_id)';

-- Update table comments to reflect unified support
COMMENT ON TABLE notification IS 'Unified notification storage supporting both internal users (user_id) and external users (email)';
COMMENT ON TABLE notification_batch_queue IS 'Unified notification batching queue supporting both internal users (user_id) and external users (email)';
