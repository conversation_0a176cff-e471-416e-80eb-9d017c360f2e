-- Create sequence for user IDs
CREATE SEQUENCE users_id_seq START WITH 1 INCREMENT BY 1;

-- Create users table with multi-tenancy support
CREATE TABLE "user" (
    id BIGINT PRIMARY KEY default nextval('users_id_seq'),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    display_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    account_id BIGINT NOT NULL REFERENCES account(id),
    internal BOOLEAN NOT NULL DEFAULT true,
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP
);


