-- Migration V027: Add brief notifications support
-- Adds brief_id column to notification table and brief-related notification types

-- Step 1: Add brief-related notification types to the enum
ALTER TYPE notification_type_enum ADD VALUE 'BRIEF_CREATED';
ALTER TYPE notification_type_enum ADD VALUE 'BRIEF_UPDATED';
ALTER TYPE notification_type_enum ADD VALUE 'BRIEF_ASSIGNED';

-- Step 2: Add brief_id column to notification table
ALTER TABLE notification
ADD COLUMN brief_id BIGINT REFERENCES collaboration_brief(id) ON DELETE CASCADE;

-- Step 3: Create index for efficient lookups by brief_id
CREATE INDEX idx_notification_brief_id ON notification(brief_id);

-- Step 4: Add comments for documentation
COMMENT ON COLUMN notification.brief_id IS 'Reference to collaboration brief when notification is brief-related';

-- Step 5: Update table comment to reflect new brief support
COMMENT ON TABLE notification IS 'In-app notifications for users with status tracking and entity references (hubs, posts, comments, chats, briefs)';
