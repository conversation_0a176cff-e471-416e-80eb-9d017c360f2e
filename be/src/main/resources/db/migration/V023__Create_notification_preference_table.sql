-- Migration V023: Create notification preference table for user notification settings
-- Allows users to control which notifications they receive and through which channels

-- Create notification type enum
CREATE TYPE notification_type_enum AS ENUM (
    'INVITE_TO_HUB',
    'ASSIGNED_AS_REVIEWER', 
    'POST_REVIEWED',
    'COMMENT_ADDED',
    'COMMENT_MENTION',
    'CHAT_MENTION',
    'CHAT_ADDED'
);

-- Create notification channel enum  
CREATE TYPE notification_channel_enum AS ENUM (
    'IN_APP',
    'EMAIL'
);

-- Create sequence for notification preference IDs
CREATE SEQUENCE notification_preference_id_seq START WITH 1 INCREMENT BY 1;

-- Create notification_preference table
CREATE TABLE notification_preference (
    id BIGINT PRIMARY KEY DEFAULT nextval('notification_preference_id_seq'),
    user_id BIGINT NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    type notification_type_enum NOT NULL,
    channel notification_channel_enum NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    
    -- Ensure unique combination of user, type, and channel
    CONSTRAINT uq_notification_preference_user_type_channel UNIQUE(user_id, type, channel)
);

-- Create index for efficient lookups by user
CREATE INDEX idx_notification_preference_user_id ON notification_preference(user_id);

-- Create index for efficient lookups by user and type
CREATE INDEX idx_notification_preference_user_type ON notification_preference(user_id, type);

-- Add comments for documentation
COMMENT ON TABLE notification_preference IS 'User notification preferences for controlling which notifications are received through which channels';
COMMENT ON COLUMN notification_preference.user_id IS 'Reference to the user who owns these preferences';
COMMENT ON COLUMN notification_preference.type IS 'Type of notification (e.g., CHAT_MENTION, POST_REVIEWED)';
COMMENT ON COLUMN notification_preference.channel IS 'Delivery channel (IN_APP for internal users, EMAIL for all users)';
COMMENT ON COLUMN notification_preference.enabled IS 'Whether this notification type is enabled for this channel';
