-- Migration V015: Create centralized media storage tables
-- This migration creates a new media table for centralized file management
-- and junction tables for many-to-many relationships with posts and chat messages

-- 1. Create sequence for media IDs
CREATE SEQUENCE media_id_seq START WITH 1 INCREMENT BY 1;

-- 2. Create media table for centralized file storage
CREATE TABLE media (
    id BIGINT PRIMARY KEY DEFAULT nextval('media_id_seq'),
    account_id BIGINT NOT NULL REFERENCES account(id) ON DELETE CASCADE,
    original_filename VARCHAR(255) NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    file_extension VARCHAR(10) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    s3_key VARCHAR(500) NOT NULL,
    s3_bucket VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    -- Constraints
    CONSTRAINT chk_media_file_size CHECK (file_size_bytes > 0),
    CONSTRAINT chk_media_filename CHECK (original_filename != ''),
    CONSTRAINT chk_media_extension CHECK (file_extension != ''),
    CONSTRAINT chk_media_mime_type CHECK (mime_type != ''),
    CONSTRAINT chk_media_s3_key CHECK (s3_key != ''),
    CONSTRAINT chk_media_s3_bucket CHECK (s3_bucket != '')
);

-- 3. Create indexes for media table
CREATE INDEX idx_media_account_id ON media(account_id);
CREATE INDEX idx_media_created_at ON media(created_at);
CREATE INDEX idx_media_deleted_at ON media(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_media_s3_key ON media(s3_key);
CREATE INDEX idx_media_mime_type ON media(mime_type);

-- 4. Create sequences for junction tables
CREATE SEQUENCE post_media_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE chat_message_media_id_seq START WITH 1 INCREMENT BY 1;

-- 5. Create post_media junction table
CREATE TABLE post_media (
    id BIGINT PRIMARY KEY DEFAULT nextval('post_media_id_seq'),
    post_id BIGINT NOT NULL REFERENCES post(id) ON DELETE CASCADE,
    media_id BIGINT NOT NULL REFERENCES media(id) ON DELETE CASCADE,
    display_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_post_media_display_order CHECK (display_order >= 0),
    UNIQUE(post_id, media_id),
    UNIQUE(post_id, display_order)
);

-- 6. Create chat_message_media junction table
CREATE TABLE chat_message_media (
    id BIGINT PRIMARY KEY DEFAULT nextval('chat_message_media_id_seq'),
    chat_message_id BIGINT NOT NULL REFERENCES chat_message(id) ON DELETE CASCADE,
    media_id BIGINT NOT NULL REFERENCES media(id) ON DELETE CASCADE,
    display_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_chat_message_media_display_order CHECK (display_order >= 0),
    UNIQUE(chat_message_id, media_id),
    UNIQUE(chat_message_id, display_order)
);

-- 7. Create indexes for junction tables
CREATE INDEX idx_post_media_post_id ON post_media(post_id);
CREATE INDEX idx_post_media_media_id ON post_media(media_id);
CREATE INDEX idx_post_media_display_order ON post_media(post_id, display_order);

CREATE INDEX idx_chat_message_media_message_id ON chat_message_media(chat_message_id);
CREATE INDEX idx_chat_message_media_media_id ON chat_message_media(media_id);
CREATE INDEX idx_chat_message_media_display_order ON chat_message_media(chat_message_id, display_order);

-- 8. Add comments for documentation
COMMENT ON TABLE media IS 'Centralized storage for all media files (images, videos, documents) with account-based multi-tenancy';
COMMENT ON COLUMN media.account_id IS 'Account that owns this media file for multi-tenant isolation';
COMMENT ON COLUMN media.original_filename IS 'Original filename as uploaded by user';
COMMENT ON COLUMN media.file_size_bytes IS 'File size in bytes for usage tracking and validation';
COMMENT ON COLUMN media.file_extension IS 'File extension (e.g., jpg, png, mp4) for type identification';
COMMENT ON COLUMN media.mime_type IS 'MIME type for content validation and serving';
COMMENT ON COLUMN media.s3_key IS 'Full S3 object key/path for file retrieval';
COMMENT ON COLUMN media.s3_bucket IS 'S3 bucket name where file is stored';
COMMENT ON COLUMN media.deleted_at IS 'Soft deletion timestamp, NULL for active files';

COMMENT ON TABLE post_media IS 'Junction table linking posts to media files with display ordering';
COMMENT ON COLUMN post_media.display_order IS 'Order of media display in post (0-based, unique per post)';

COMMENT ON TABLE chat_message_media IS 'Junction table linking chat messages to media files with display ordering';
COMMENT ON COLUMN chat_message_media.display_order IS 'Order of media display in message (0-based, unique per message)';
