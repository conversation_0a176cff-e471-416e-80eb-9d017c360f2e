-- Migration V010: Create collaboration hub tables with ENUMs

-- 1. <PERSON><PERSON> ENUM types
CREATE TYPE hub_participant_role AS ENUM ('admin', 'content_creator', 'reviewer', 'reviewer_creator');
CREATE TYPE review_status AS ENUM ('pending', 'approved', 'rework');
CREATE TYPE chat_channel_scope AS ENUM ('admins', 'admins_reviewers', 'creator_specific');

-- 2. <PERSON>reate sequences
CREATE SEQUENCE collaboration_hub_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE hub_participant_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE post_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE post_reviewer_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE post_comment_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE chat_channel_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE chat_message_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE collaboration_brief_id_seq START WITH 1 INCREMENT BY 1;

-- 3. Create collaboration_hub table
CREATE TABLE collaboration_hub (
                                   id BIGINT PRIMARY KEY DEFAULT nextval('collaboration_hub_id_seq'),
                                   account_id BIGINT NOT NULL REFERENCES account(id),
                                   brand_id BIGINT NOT NULL REFERENCES brand(id),
                                   name VARCHAR(255) NOT NULL,
                                   description TEXT,
                                   created_by BIGINT NOT NULL REFERENCES "user"(id),
                                   created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 4. Create hub_participant table
CREATE TABLE hub_participant (
                                 id BIGINT PRIMARY KEY DEFAULT nextval('hub_participant_id_seq'),
                                 hub_id BIGINT NOT NULL REFERENCES collaboration_hub(id) ON DELETE CASCADE,
                                 user_id BIGINT NULL REFERENCES "user"(id),
                                 email VARCHAR(255) NOT NULL,
                                 role hub_participant_role NOT NULL,
                                 is_external BOOLEAN NOT NULL DEFAULT FALSE,
                                 magic_link_token VARCHAR(255) NULL,
                                 magic_link_expiry TIMESTAMP NULL,
                                 invited_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                 joined_at TIMESTAMP NULL,
                                 removed_at TIMESTAMP NULL,
                                 created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                 UNIQUE(hub_id, email)
);

-- 5. Create post table
CREATE TABLE post (
                      id BIGINT PRIMARY KEY DEFAULT nextval('post_id_seq'),
                      hub_id BIGINT NOT NULL REFERENCES collaboration_hub(id) ON DELETE CASCADE,
                      creator_participant_id BIGINT NOT NULL REFERENCES hub_participant(id),
                      caption TEXT,
                      media_uris JSONB NOT NULL DEFAULT '[]'::jsonb,
                      reviewer_notes TEXT,
                      review_status review_status NOT NULL DEFAULT 'pending',
                      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 6. Create post_reviewer table
CREATE TABLE post_reviewer (
                               id BIGINT PRIMARY KEY DEFAULT nextval('post_reviewer_id_seq'),
                               post_id BIGINT NOT NULL REFERENCES post(id) ON DELETE CASCADE,
                               participant_id BIGINT NOT NULL REFERENCES hub_participant(id),
                               assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                               review_status review_status NOT NULL DEFAULT 'pending',
                               checklist JSONB NOT NULL DEFAULT '[]'::jsonb,
                               review_notes TEXT,
                               reviewed_at TIMESTAMP NULL,
                               UNIQUE(post_id, participant_id)
);

-- 7. Create post_comment table
CREATE TABLE post_comment (
                              id BIGINT PRIMARY KEY DEFAULT nextval('post_comment_id_seq'),
                              post_id BIGINT NOT NULL REFERENCES post(id) ON DELETE CASCADE,
                              participant_id BIGINT NOT NULL REFERENCES hub_participant(id),
                              content TEXT NOT NULL,
                              created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 8. Create chat_channel table
CREATE TABLE chat_channel (
                              id BIGINT PRIMARY KEY DEFAULT nextval('chat_channel_id_seq'),
                              hub_id BIGINT NOT NULL REFERENCES collaboration_hub(id) ON DELETE CASCADE,
                              name VARCHAR(255) NOT NULL,
                              scope chat_channel_scope NOT NULL,
                              creator_participant_id BIGINT NULL REFERENCES hub_participant(id),
                              created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 9. Create chat_message table
CREATE TABLE chat_message (
                              id BIGINT PRIMARY KEY DEFAULT nextval('chat_message_id_seq'),
                              channel_id BIGINT NOT NULL REFERENCES chat_channel(id) ON DELETE CASCADE,
                              participant_id BIGINT NOT NULL REFERENCES hub_participant(id),
                              content TEXT NOT NULL,
                              created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 10. Create collaboration_brief table
CREATE TABLE collaboration_brief (
                                     id BIGINT PRIMARY KEY DEFAULT nextval('collaboration_brief_id_seq'),
                                     hub_id BIGINT NOT NULL REFERENCES collaboration_hub(id) ON DELETE CASCADE,
                                     title VARCHAR(255) NOT NULL,
                                     body TEXT,
                                     created_by_participant_id BIGINT NOT NULL REFERENCES hub_participant(id),
                                     access_tags JSONB NOT NULL DEFAULT '[]'::jsonb,
                                     created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                     updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
