-- Migration to update collaboration brief access control
-- Replace access_tags JSONB with structured scope and specific_participant_ids

-- Create enum for brief scope
CREATE TYPE brief_scope AS ENUM (
    'all_participants',
    'admins_reviewers',
    'admins_only',
    'custom_selection'
);

-- Add new columns to collaboration_brief table
ALTER TABLE collaboration_brief
ADD COLUMN scope brief_scope NOT NULL DEFAULT 'all_participants',
ADD COLUMN specific_participant_ids JSONB DEFAULT '[]'::jsonb;

-- Drop the old access_tags column (no data migration needed as database is empty)
ALTER TABLE collaboration_brief DROP COLUMN access_tags;

-- Add comments for documentation
COMMENT ON COLUMN collaboration_brief.scope IS 'Defines who can access this brief: all_participants, admins_reviewers, admins_only, or custom_selection';
COMMENT ON COLUMN collaboration_brief.specific_participant_ids IS 'Array of participant IDs when scope is custom_selection, empty array otherwise';

-- Add index for better query performance
CREATE INDEX idx_collaboration_brief_scope ON collaboration_brief(scope);
CREATE INDEX idx_collaboration_brief_specific_participants ON collaboration_brief USING GIN(specific_participant_ids);
