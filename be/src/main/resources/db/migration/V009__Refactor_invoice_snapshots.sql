-- Migration V009: Refactor invoice snapshots to use hash-based deduplication
-- This migration removes inline snapshot storage and introduces a separate snapshots table
-- with hash-based deduplication to reduce storage space.
-- Also adds entity ID columns that were missing from the original schema.

-- 1. Add entity ID columns to invoices table (these were missing from original schema)
ALTER TABLE invoice
ADD COLUMN issuer_id BIGINT,
ADD COLUMN recipient_id BIGINT,
ADD COLUMN bank_details_id BIGINT;

-- 2. Add new hash columns to invoices table
ALTER TABLE invoice
ADD COLUMN issuer_snapshot_hash VARCHAR(64),
ADD COLUMN recipient_snapshot_hash VARCHAR(64),
ADD COLUMN bank_details_snapshot_hash VARCHAR(64);

-- 3. Create new snapshots table for deduplicated storage
CREATE TABLE invoice_snapshots (
    id BIGSERIAL PRIMARY KEY,
    hash VARCHAR(64) UNIQUE NOT NULL,
    entity_type VARCHAR(20) NOT NULL CHECK (entity_type IN ('ISSUER', 'RECIPIENT', 'BANK_DETAILS')),
    snapshot_data JSONB NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 4. Drop old snapshot columns (breaking change - acceptable since not in production)
ALTER TABLE invoice
DROP COLUMN IF EXISTS issuer_snapshot,
DROP COLUMN IF EXISTS recipient_snapshot,
DROP COLUMN IF EXISTS bank_details_snapshot;
