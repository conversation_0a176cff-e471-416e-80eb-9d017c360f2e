-- Migration V024: Create notification table for in-app notification storage
-- Stores notifications for the in-app notification feed

-- Create notification status enum
CREATE TYPE notification_status_enum AS ENUM (
    'UNREAD',
    'READ'
);

-- Create sequence for notification IDs
CREATE SEQUENCE notification_id_seq START WITH 1 INCREMENT BY 1;

-- Create notification table
CREATE TABLE notification (
    id BIGINT PRIMARY KEY DEFAULT nextval('notification_id_seq'),
    user_id BIGINT NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    type notification_type_enum NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status notification_status_enum NOT NULL DEFAULT 'UNREAD',
    
    -- Optional reference to related entities
    collaboration_hub_id BIGINT REFERENCES collaboration_hub(id) ON DELETE CASCADE,
    post_id BIGINT REFERENCES post(id) ON DELETE CASCADE,
    comment_id BIGINT REFERENCES post_comment(id) ON DELETE CASCADE,
    chat_channel_id BIGINT REFERENCES chat_channel(id) ON DELETE CASCADE,
    
    -- Metadata
    metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP,
    
    -- Ensure notifications are ordered by creation time
    CONSTRAINT chk_notification_read_at CHECK (
        (status = 'UNREAD' AND read_at IS NULL) OR 
        (status = 'READ' AND read_at IS NOT NULL)
    )
);

-- Create indexes for efficient queries
CREATE INDEX idx_notification_user_id ON notification(user_id);
CREATE INDEX idx_notification_user_status ON notification(user_id, status);
CREATE INDEX idx_notification_user_created_at ON notification(user_id, created_at DESC);
CREATE INDEX idx_notification_type ON notification(type);

-- Create index for cleanup operations (old notifications)
CREATE INDEX idx_notification_created_at ON notification(created_at);

-- Add comments for documentation
COMMENT ON TABLE notification IS 'In-app notifications for users with status tracking and entity references';
COMMENT ON COLUMN notification.user_id IS 'User who should receive this notification';
COMMENT ON COLUMN notification.type IS 'Type of notification matching notification_type_enum';
COMMENT ON COLUMN notification.title IS 'Short notification title for display';
COMMENT ON COLUMN notification.message IS 'Full notification message content';
COMMENT ON COLUMN notification.status IS 'Read status of the notification';
COMMENT ON COLUMN notification.metadata IS 'Additional structured data for the notification';
COMMENT ON COLUMN notification.read_at IS 'Timestamp when notification was marked as read';
