-- Migration V022: Simplify chat channels to only general and custom types

-- 1. Drop and recreate chat_channel_scope enum with only general and custom
DROP TYPE IF EXISTS chat_channel_scope CASCADE;
CREATE TYPE chat_channel_scope AS ENUM ('general', 'custom');

-- 2. Recreate chat_channel table with new structure
DROP TABLE IF EXISTS chat_channel CASCADE;
CREATE SEQUENCE IF NOT EXISTS chat_channel_id_seq START 1;

CREATE TABLE chat_channel (
    id BIGINT PRIMARY KEY DEFAULT nextval('chat_channel_id_seq'),
    hub_id BIGINT NOT NULL REFERENCES collaboration_hub(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    scope chat_channel_scope NOT NULL,
    created_by_participant_id BIGINT NOT NULL REFERENCES hub_participant(id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Create sequence for chat_channel_participants
CREATE SEQUENCE IF NOT EXISTS chat_channel_participants_id_seq START 1;

-- 4. Create chat_channel_participants junction table
CREATE TABLE chat_channel_participants (
    id BIGINT PRIMARY KEY DEFAULT nextval('chat_channel_participants_id_seq'),
    channel_id BIGINT NOT NULL REFERENCES chat_channel(id) ON DELETE CASCADE,
    participant_id BIGINT NOT NULL REFERENCES hub_participant(id) ON DELETE CASCADE,
    added_by_participant_id BIGINT REFERENCES hub_participant(id),
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(channel_id, participant_id)
);

-- 5. Create indexes for performance
CREATE INDEX idx_chat_channel_participants_channel_id ON chat_channel_participants(channel_id);
CREATE INDEX idx_chat_channel_participants_participant_id ON chat_channel_participants(participant_id);
CREATE INDEX idx_chat_channel_created_by ON chat_channel(created_by_participant_id);

-- 6. Create general channels for existing hubs that don't have any channels
-- This ensures every hub has at least one general channel
INSERT INTO chat_channel (hub_id, name, description, scope, created_by_participant_id, created_at, last_activity_at)
SELECT
    ch.id as hub_id,
    'General' as name,
    'General channel for all hub participants' as description,
    'general' as scope,
    (SELECT hp.id
     FROM hub_participant hp
     WHERE hp.hub_id = ch.id
     AND hp.role = 'admin'
     AND hp.removed_at IS NULL
     ORDER BY hp.created_at ASC
     LIMIT 1) as created_by_participant_id,
    NOW() as created_at,
    NOW() as last_activity_at
FROM collaboration_hub ch
WHERE NOT EXISTS (
    SELECT 1 FROM chat_channel cc WHERE cc.hub_id = ch.id
);

-- 7. Add all hub participants to general channels
INSERT INTO chat_channel_participants (channel_id, participant_id, added_by_participant_id, added_at, created_at)
SELECT
    cc.id as channel_id,
    hp.id as participant_id,
    cc.created_by_participant_id as added_by_participant_id,
    NOW() as added_at,
    NOW() as created_at
FROM chat_channel cc
JOIN hub_participant hp ON hp.hub_id = cc.hub_id
WHERE cc.scope = 'general'
AND hp.removed_at IS NULL;
