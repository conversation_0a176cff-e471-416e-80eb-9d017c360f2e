-- Migration to fix notification preference unique constraints for jOOQ compatibility
-- Replace partial unique indexes with regular unique constraints

-- Step 1: Drop the partial unique indexes that don't work with jOOQ onConflict
DROP INDEX IF EXISTS uq_notification_preference_user_type_channel;
DROP INDEX IF EXISTS uq_notification_preference_email_type_channel;

-- Step 2: Since we can't have overlapping unique constraints, we'll rely on application logic
-- to prevent conflicts and use the database constraints for data integrity only

-- Create a regular unique constraint for internal users (when user_id is not null)
-- This will work with jOOQ's onConflict for internal users
CREATE UNIQUE INDEX uq_notification_preference_user_type_channel
ON notification_preference (user_id, type, channel)
WHERE user_id IS NOT NULL;

-- For external participants, we'll handle uniqueness in application code
-- since jOOQ doesn't work well with partial indexes in onConflict clauses

-- Step 3: Add a regular index for external participants for performance
CREATE INDEX idx_notification_preference_email_type_channel
ON notification_preference (email, type, channel)
WHERE user_id IS NULL;
