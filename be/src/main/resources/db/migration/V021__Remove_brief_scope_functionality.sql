-- Migration V021: Remove brief scope functionality
-- This migration removes the scope and specific_participant_ids columns from collaboration_brief table
-- and drops the brief_scope enum type as the functionality is being simplified to allow all hub participants
-- to access all briefs within their collaboration hub.

-- 1. Drop indexes first (to avoid dependency issues)
DROP INDEX IF EXISTS idx_collaboration_brief_scope;
DROP INDEX IF EXISTS idx_collaboration_brief_specific_participants;

-- 2. Remove scope and specific_participant_ids columns from collaboration_brief table
ALTER TABLE collaboration_brief 
DROP COLUMN IF EXISTS scope,
DROP COLUMN IF EXISTS specific_participant_ids;

-- 3. Drop the brief_scope enum type (after removing column dependencies)
DROP TYPE IF EXISTS brief_scope;

-- 4. Add comment to document the change
COMMENT ON TABLE collaboration_brief IS 'Collaboration briefs are now visible to all participants within their hub (scope functionality removed)';
