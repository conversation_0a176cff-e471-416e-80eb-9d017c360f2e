-- Create sequence for bank_details IDs
CREATE SEQUENCE bank_details_id_seq START WITH 1 INCREMENT BY 1;

-- Create bank_details table for managing bank account information with soft deletion
CREATE TABLE bank_details (
    id BIGINT PRIMARY KEY DEFAULT nextval('bank_details_id_seq'),
    account_id BIGINT NOT NULL REFERENCES account(id),
    name VARCHAR(255) NOT NULL,
    bank_name VA<PERSON>HA<PERSON>(255),
    iban VARCHAR(34),
    bicswift VARCHAR(11),
    deleted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP
);