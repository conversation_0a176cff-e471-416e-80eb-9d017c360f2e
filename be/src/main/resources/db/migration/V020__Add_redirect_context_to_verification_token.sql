-- Migration V020: Add redirect context to verification_token table
-- Add redirect functionality for magic link authentication to support dynamic redirects
-- based on user type and context (hub access, invoice access, etc.)

-- 1. Add redirect_type column to categorize different types of redirects
ALTER TABLE verification_token 
ADD COLUMN redirect_type VARCHAR(50) NULL;

-- 2. Add redirect_context column to store contextual information (hub_id, invoice_id, etc.)
ALTER TABLE verification_token 
ADD COLUMN redirect_context JSONB NULL;

-- 3. Add redirect_url column for direct URL redirects (optional, for future flexibility)
ALTER TABLE verification_token 
ADD COLUMN redirect_url VARCHAR(500) NULL;

ALTER TABLE verification_token
ADD CONSTRAINT chk_verification_token_redirect_type 
CHECK (redirect_type IN ('HUB_ACCESS', 'INVOICE_ACCESS', 'GENERAL_ACCESS'));

ALTER TABLE verification_token
ADD CONSTRAINT chk_verification_token_redirect_context 
CHECK (
    (redirect_type IS NULL) OR 
    (redirect_type IS NOT NULL AND (redirect_context IS NOT NULL OR redirect_url IS NOT NULL))
);

