-- Migration V016: Remove legacy media storage columns
-- This migration removes the old JSONB media storage columns from posts and chat_messages tables
-- after the new centralized media architecture has been established

-- 1. Remove media_uris column from post table
-- This column previously stored media URLs as JSONB array
ALTER TABLE post DROP COLUMN IF EXISTS media_uris;

-- 2. Remove attachment_uris column from chat_message table  
-- This column previously stored attachment URLs as JSONB array
ALTER TABLE chat_message DROP COLUMN IF EXISTS attachment_uris;

-- 3. Add comments for documentation
COMMENT ON TABLE post IS 'Posts table - media now managed through post_media junction table';
COMMENT ON TABLE chat_message IS 'Chat messages table - attachments now managed through chat_message_media junction table';
