# Notification Translations

This directory contains JSON translation files for the notification system.

## File Structure

Each language has its own JSON file named with the ISO 639-1 language code:
- `en.json` - English (default)
- `es.json` - Spanish
- `fr.json` - French

## JSON Structure

Each translation file follows this hierarchical structure:

```json
{
  "notification": {
    "title": {
      "notification_type": "Title text"
    },
    "message": {
      "notification_type": "Message template with {parameters}"
    },
    "email": {
      "subject": {
        "notification_type": "Email subject with {parameters}"
      },
      "body": {
        "notification_type": "Email body template with {parameters}"
      }
    }
  }
}
```

## Supported Notification Types

- `invite_to_hub` - Hub invitation notifications
- `assigned_as_reviewer` - Review assignment notifications
- `post_reviewed` - Post review completion notifications
- `comment_added` - New comment notifications
- `comment_mention` - Comment mention notifications
- `chat_mention` - Chat mention notifications
- `chat_added` - Added to chat notifications

## Parameter Substitution

Templates support parameter substitution using `{parameterName}` syntax. Common parameters include:

- `{recipientName}` - Name of the notification recipient
- `{inviterName}` - Name of the person sending invitation
- `{assignerName}` - Name of the person assigning review
- `{reviewerName}` - Name of the reviewer
- `{commenterName}` - Name of the commenter
- `{senderName}` - Name of the message sender
- `{adderName}` - Name of the person adding to chat
- `{hubName}` - Name of the collaboration hub
- `{postTitle}` - Title of the post
- `{channelName}` - Name of the chat channel
- `{reviewStatus}` - Status of the review (approved/rejected)
- `{commentPreview}` - Preview of the comment content
- `{messagePreview}` - Preview of the message content

## Adding New Languages

1. Create a new JSON file with the language code (e.g., `de.json` for German)
2. Copy the structure from `en.json`
3. Translate all text values while keeping parameter placeholders intact
4. Update `NotificationTranslationService.java` to include the new language in `supportedLanguages` array

## Fallback Behavior

The translation service uses the following fallback hierarchy:
1. Requested language translation
2. English (default) translation
3. Hardcoded default text

This ensures notifications are always delivered even if translations are missing.

## Best Practices

- Keep parameter names consistent across all languages
- Test parameter substitution after adding new translations
- Use appropriate formal/informal tone based on your application's style
- Consider cultural differences in communication style
- Keep email templates professional and branded
