<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">Unsubscribe Failed</title>
    <style th:replace="email/base :: email-styles"></style>
    <style>
        /* Additional styles for unsubscribe error pages */
        .error-icon {
            font-size: 48px;
            color: #ef4444;
            margin-bottom: 16px;
        }
        
        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 24px 0;
            text-align: center;
        }
        
        .error-title {
            color: #991b1b;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .error-description {
            color: #dc2626;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .help-box {
            background: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 8px;
            padding: 16px;
            margin: 24px 0;
            font-size: 14px;
            color: #0c4a6e;
            line-height: 1.5;
        }
        
        .action-buttons {
            text-align: center;
            margin: 32px 0;
        }
        
        .primary-button {
            display: inline-block;
            background-color: #111827;
            color: #ffffff !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            margin: 0 8px;
            border: 1px solid #111827;
            transition: all 0.2s ease;
        }
        
        .primary-button:hover {
            background-color: #1f2937;
            border-color: #1f2937;
        }
        
        .secondary-button {
            display: inline-block;
            background-color: #f3f4f6;
            color: #374151 !important;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            margin: 0 8px;
            border: 1px solid #d1d5db;
            transition: all 0.2s ease;
        }
        
        .secondary-button:hover {
            background-color: #e5e7eb;
            border-color: #9ca3af;
        }
        
        .troubleshooting {
            background: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            margin: 24px 0;
        }
        
        .troubleshooting h3 {
            color: #111827;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .troubleshooting ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .troubleshooting li {
            margin-bottom: 8px;
            color: #374151;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="email-logo">Collaboration Hub</h1>
            <p class="email-tagline">Streamline your content collaborations</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <div style="text-align: center;">
                <div class="error-icon">⚠</div>
                <h2 class="email-greeting">Unsubscribe Failed</h2>
            </div>
            
            <div class="error-message">
                <div class="error-title">Unable to process your unsubscribe request</div>
                <div class="error-description" th:text="${errorMessage}">
                    The unsubscribe link may have expired or already been used.
                </div>
            </div>

            <div class="help-box">
                <strong>Need help?</strong> Our support team is here to assist you with managing your email preferences.
                We can help you unsubscribe from notification emails while keeping important account communications active.
            </div>

            <div class="troubleshooting">
                <h3>Common reasons for unsubscribe failures:</h3>
                <ul>
                    <li><strong>Expired link:</strong> Unsubscribe links expire after 30 days for security</li>
                    <li><strong>Already used:</strong> Each unsubscribe link can only be used once</li>
                    <li><strong>Invalid format:</strong> The link may have been corrupted when copied</li>
                    <li><strong>Technical issue:</strong> Temporary system problems (rare)</li>
                </ul>
            </div>

            <div class="email-content">
                <p><strong>Alternative ways to manage your email preferences:</strong></p>
                <ul>
                    <li>Log into your Collaboration Hub account and visit Settings → Notifications</li>
                    <li>Reply to any notification email with "UNSUBSCRIBE" in the subject line</li>
                    <li>Contact our support team directly for immediate assistance</li>
                </ul>
            </div>

            <div class="action-buttons">
                <a href="/support" class="primary-button">
                    Contact Support
                </a>
                <a href="/login" class="secondary-button">
                    Login to Account
                </a>
                <a href="/" class="secondary-button">
                    Return Home
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p class="email-footer-text">
                If you continue to experience issues, please contact our support team with the error details above.
                We're committed to helping you manage your email preferences effectively.
            </p>
            <div class="email-footer-links">
                <a href="/">Visit Collaboration Hub</a>
                <a href="/support">Support</a>
                <a href="/privacy">Privacy Policy</a>
            </div>
        </div>
    </div>
</body>
</html>
