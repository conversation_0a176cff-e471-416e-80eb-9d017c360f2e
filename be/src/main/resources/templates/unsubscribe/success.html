<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">Unsubscribed Successfully</title>
    <style th:replace="email/base :: email-styles"></style>
    <style>
        /* Additional styles for unsubscribe pages */
        .success-icon {
            font-size: 48px;
            color: #10b981;
            margin-bottom: 16px;
        }
        
        .success-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 24px 0;
            text-align: center;
        }
        
        .success-title {
            color: #065f46;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .success-description {
            color: #047857;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .info-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 16px;
            margin: 24px 0;
            font-size: 14px;
            color: #92400e;
            line-height: 1.5;
        }
        
        .action-buttons {
            text-align: center;
            margin: 32px 0;
        }
        
        .secondary-button {
            display: inline-block;
            background-color: #f3f4f6;
            color: #374151 !important;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            margin: 0 8px;
            border: 1px solid #d1d5db;
            transition: all 0.2s ease;
        }
        
        .secondary-button:hover {
            background-color: #e5e7eb;
            border-color: #9ca3af;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="email-logo">Collaboration Hub</h1>
            <p class="email-tagline">Streamline your content collaborations</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <div style="text-align: center;">
                <div class="success-icon">✓</div>
                <h2 class="email-greeting">Successfully Unsubscribed</h2>
            </div>
            
            <div class="success-message">
                <div class="success-title">You've been unsubscribed from notification emails</div>
                <div class="success-description">
                    We've disabled email notifications for <strong th:text="${email}">your email address</strong>.
                    You won't receive notification emails about posts, comments, mentions, or other collaboration activities.
                </div>
            </div>

            <div class="info-box">
                <strong>Important:</strong> You'll still receive critical emails such as:
                <ul style="margin: 8px 0 0 20px; padding: 0;">
                    <li>Collaboration hub invitations</li>
                    <li>Account security notifications</li>
                    <li>Invoice and billing emails</li>
                    <li>Password reset requests</li>
                </ul>
            </div>

            <div class="email-content">
                <p>If you change your mind, you can re-enable email notifications by:</p>
                <ul>
                    <li>Logging into your account and updating your notification preferences</li>
                    <li>Contacting our support team for assistance</li>
                </ul>
            </div>

            <div class="action-buttons">
                <a href="/" class="secondary-button">
                    Return to Collaboration Hub
                </a>
                <a href="/support" class="secondary-button">
                    Contact Support
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p class="email-footer-text">
                This action was completed on <span th:text="${#temporals.format(#temporals.createNow(), 'MMM dd, yyyy HH:mm')}">date and time</span>.
                If you didn't request this change, please contact our support team immediately.
            </p>
            <div class="email-footer-links">
                <a href="/">Visit Collaboration Hub</a>
                <a href="/support">Support</a>
                <a href="/privacy">Privacy Policy</a>
            </div>
        </div>
    </div>
</body>
</html>
