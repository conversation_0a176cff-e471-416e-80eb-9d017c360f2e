<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
  <meta charset="UTF-8" />
  <title th:text="'Invoice ' + ${invoice.invoiceNumber}">Invoice</title>
  <style>
      body {
          font-family: Arial, sans-serif;
          font-size: 10pt;
          color: #000;
          margin: 0;
          padding: 40px;
      }

      .header {
          border-bottom: 2px solid black;
          margin-bottom: 20px;
          padding-bottom: 10px;
      }

      .header-title {
          font-size: 24pt;
      }

      .section {
          margin-bottom: 20px;
      }

      .section-title {
          font-weight: bold;
          font-size: 10pt;
          margin-bottom: 6px;
      }

      .address-block {
          margin-bottom: 10px;
      }

      .invoice-metadata {
          margin-bottom: 20px;
      }

      .invoice-metadata div {
          margin: 2px 0;
      }

      .table-two-col {
          width: 100%;
          margin-bottom: 20px;
          border-collapse: collapse;
      }

      .table-two-col td {
          vertical-align: top;
          padding: 5px;
          width: 50%;
      }

      table.items {
          width: 100%;
          border-collapse: collapse;
          margin-top: 20px;
      }

      table.items th,
      table.items td {
          border: 1px solid black;
          padding: 8px;
          font-size: 9pt;
      }

      table.items th {
          background-color: #000;
          color: #fff;
          font-weight: bold;
      }

      .text-right {
          text-align: right;
      }

      .text-center {
          text-align: center;
      }

      .totals {
          width: 100%;
          margin-top: 20px;
          border-collapse: collapse;
      }

      .totals td {
          padding: 6px;
          font-size: 10pt;
      }

      .totals .label {
          text-align: right;
          font-weight: bold;
          width: 80%;
      }

      .totals .value {
          text-align: right;
          width: 20%;
      }

      .totals .final {
          font-size: 11pt;
          font-weight: bold;
          border-top: 2px solid black;
          border-bottom: 2px solid black;
      }

      .payment-details {
          margin-top: 30px;
          font-size: 9pt;
          margin-bottom: 30px;
      }

      .signature {
          margin-top: 40px;
          text-align: right;
      }

      .signature-line {
          border-bottom: 1px solid #000;
          width: 200px;
          height: 20px;
          margin-left: auto;
      }

      .signature-label {
          font-size: 9pt;
          text-align: center;
      }
  </style>
</head>
<body>
<div class="header">
  <div class="header-title">INVOICE</div>
</div>

<div class="invoice-metadata">
  <div><strong>Invoice No:</strong> <span th:text="${invoice.invoiceNumber}">#0001</span></div>
  <div><strong>Date:</strong> <span th:text="${formattedIssueDate}">11.02.2030</span></div>
  <div><strong>Due Date:</strong> <span th:text="${formattedDueDate}">11.03.2030</span></div>
</div>

<table class="table-two-col">
  <tr>
    <td>
      <div class="section-title">Recipient</div>
      <div class="address-block">
        <div th:text="${recipient.name}">Recipient Name</div>
        <div th:text="${recipient.address}">Recipient Address</div>
        <div>
          <span th:text="${recipient.city}">City</span>,
          <span th:text="${recipient.postalCode}">Postal Code</span>
        </div>
        <div th:if="${recipient.country}" th:text="${recipient.country}">Country</div>
        <div th:if="${recipient.vatNumber}">VAT: <span th:text="${recipient.vatNumber}">VAT123456</span></div>
        <div th:if="${recipient.registrationNumber}">Reg: <span th:text="${recipient.registrationNumber}">REG123456</span></div>
      </div>
    </td>
    <td>
      <div class="section-title">Issuer</div>
      <div class="address-block">
        <div th:text="${issuer.name}">Issuer Company</div>
        <div th:text="${issuer.address}">Issuer Address</div>
        <div>
          <span th:text="${issuer.city}">City</span>,
          <span th:text="${issuer.postalCode}">Postal Code</span>
        </div>
        <div th:if="${issuer.country}" th:text="${issuer.country}">Country</div>
        <div th:if="${issuer.vatNumber}">VAT: <span th:text="${issuer.vatNumber}">VAT123456</span></div>
        <div th:if="${issuer.registrationNumber}">Reg: <span th:text="${issuer.registrationNumber}">REG123456</span></div>
      </div>
    </td>
  </tr>
</table>

<table class="items">
  <thead>
  <tr>
    <th>Description</th>
    <th class="text-right">Unit Price</th>
    <th class="text-center">Qty</th>
    <th class="text-right">Total</th>
  </tr>
  </thead>
  <tbody>
  <tr th:each="item : ${items}">
    <td th:text="${item.description}">Service Description</td>
    <td class="text-right" th:text="${#numbers.formatDecimal(item.unitPrice, 1, 2)}">100.00</td>
    <td class="text-center" th:text="${item.quantity}">1</td>
    <td class="text-right" th:text="${#numbers.formatDecimal(item.lineTotal, 1, 2)}">100.00</td>
  </tr>
  </tbody>
</table>

<table class="totals">
  <tr>
    <td class="label">Subtotal</td>
    <td class="value" th:text="${formattedSubtotal}">€400.00</td>
  </tr>
  <tr>
    <td class="label">VAT</td>
    <td class="value" th:text="${formattedTotalVat}">€21.00</td>
  </tr>
  <tr class="final">
    <td class="label">Total</td>
    <td class="value" th:text="${formattedTotalAmount}">€440.00</td>
  </tr>
</table>

<div class="payment-details" th:if="${bankDetails != null and (bankDetails.accountName != null or bankDetails.iban != null or bankDetails.bic != null)}">
  <div class="section-title">Payment Information</div>
  <div th:if="${bankDetails.accountName}">Account Name: <span th:text="${bankDetails.accountName}">Name</span></div>
  <div th:if="${bankDetails.iban}">IBAN: <span th:text="${bankDetails.iban}">IBAN</span></div>
  <div th:if="${bankDetails.bic}">BIC: <span th:text="${bankDetails.bic}">BIC</span></div>
  <div th:if="${bankDetails.bankName}">Bank: <span th:text="${bankDetails.bankName}">Bank Name</span></div>
</div>

<div th:if="${invoice.notes != null and !#strings.isEmpty(invoice.notes)}">
  <div class="section-title">Notes</div>
  <div th:text="${invoice.notes}">Additional notes or payment terms...</div>
</div>
</body>
</html>
