<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
  <meta charset="UTF-8">
  <title th:text="'Invoice ' + ${invoice.invoiceNumber}">Invoice</title>
  <style>
      body {
          font-family: Arial, sans-serif;
          font-size: 10pt;
          color: #000;
          margin: 0;
          padding: 40px;
      }

      .header {
          border-bottom: 2px solid black;
          margin-bottom: 40px;
          padding-bottom: 20px;
      }

      .header-title {
          font-size: 32pt;
          letter-spacing: 8px;
      }

      .section {
          margin-bottom: 30px;
      }

      .section-title {
          font-weight: bold;
          text-transform: uppercase;
          margin-bottom: 8px;
      }

      .address-block {
          margin-bottom: 20px;
      }

      .invoice-metadata {
          text-align: left;
          margin-bottom: 40px;
      }

      .invoice-metadata div {
          margin: 4px 0;
      }

      .flex-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
      }

      table.items {
          width: 100%;
          border-collapse: collapse;
          margin-top: 30px;
      }

      table.items th,
      table.items td {
          border: 1px solid black;
          padding: 10px;
          font-size: 9pt;
      }

      table.items th {
          background-color: black;
          color: white;
          text-transform: uppercase;
          font-weight: bold;
      }

      .text-right {
          text-align: right;
      }

      .text-center {
          text-align: center;
      }

      .totals {
          width: 300px;
          margin-left: auto;
          margin-top: 30px;
      }

      .totals td {
          padding: 8px;
          font-size: 10pt;
      }

      .totals .label {
          text-align: right;
          font-weight: bold;
      }

      .totals .value {
          text-align: right;
      }

      .totals .final {
          font-size: 12pt;
          font-weight: bold;
          border-top: 2px solid black;
          border-bottom: 2px solid black;
      }

      .payment-details {
          margin-top: 50px;
          font-size: 9pt;
          margin-bottom: 50px;
      }

      .signature {
          margin-top: 60px;
          text-align: right;
      }

      .signature-line {
          border-bottom: 1px solid #000;
          width: 200px;
          height: 40px;
          margin-left: auto;
      }

      .signature-label {
          font-size: 9pt;
          text-align: center;
      }
  </style>
</head>
<body>
<div class="header">
  <div class="header-title">INVOICE</div>
</div>

<div class="invoice-metadata">
  <div><strong>Invoice No:</strong> <span th:text="${invoice.invoiceNumber}">#0001</span></div>
  <div><strong>Date:</strong> <span th:text="${formattedIssueDate}">11.02.2030</span></div>
  <div><strong>Due Date:</strong> <span th:text="${formattedDueDate}">11.03.2030</span></div>
</div>

<div class="flex-container">
  <div>
    <div class="section-title">Recipient</div>
    <div class="address-block">
      <div th:text="${recipient.name}">Recipient Name</div>
      <div th:text="${recipient.address}">Recipient Address</div>
      <div>
        <span th:text="${recipient.city}">City</span>,
        <span th:text="${recipient.postalCode}">Postal Code</span>
      </div>
    </div>
  </div>

  <div>
    <div class="section-title">Issuer</div>
    <div class="address-block">
      <div th:text="${issuer.name}">Issuer Company</div>
      <div>Account Name: <span th:text="${bankDetails.accountName}">Bank Account Name</span></div>
      <div>IBAN: <span th:text="${bankDetails.iban}">IBAN</span></div>
    </div>
  </div>

</div>

<table class="items">
  <thead>
  <tr>
    <th>Description</th>
    <th class="text-right">Unit Price</th>
    <th class="text-center">Qty</th>
    <th class="text-right">Total</th>
  </tr>
  </thead>
  <tbody>
  <tr th:each="item : ${items}">
    <td th:text="${item.description}">Service Description</td>
    <td class="text-right" th:text="${#numbers.formatDecimal(item.unitPrice, 1, 2)}">100.00</td>
    <td class="text-center" th:text="${item.quantity}">1</td>
    <td class="text-right" th:text="${#numbers.formatDecimal(item.lineTotal, 1, 2)}">100.00</td>
  </tr>
  </tbody>
</table>

<table class="totals">
  <tr>
    <td class="label">Subtotal</td>
    <td class="value" th:text="${formattedSubtotal}">€400.00</td>
  </tr>
  <tr>
    <td class="label">VAT</td>
    <td class="value" th:text="${formattedTotalVat}">€21.00</td>
  </tr>
  <tr class="final">
    <td class="label">Total</td>
    <td class="value" th:text="${formattedTotalAmount}">€440.00</td>
  </tr>
</table>

<div class="payment-details">
  <div class="section-title">Payment Information</div>
  <div>Account Name: <span th:text="${bankDetails.accountName}">Name</span></div>
  <div>IBAN: <span th:text="${bankDetails.iban}">IBAN</span></div>
  <div>BIC: <span th:text="${bankDetails.bic}">BIC</span></div>
</div>

<div>
  <div class="section-title">Notes</div>
  <div th:text="${invoice.notes}">Additional notes or payment terms...</div>
</div>
</body>
</html>
