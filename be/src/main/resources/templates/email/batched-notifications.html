<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'Collaboration Hub - ' + ${totalNotifications} + ' New Notifications'">Collaboration Hub - New Notifications</title>
    <style th:replace="email/base :: email-styles"></style>
    <style>
        /* Additional styles for batched notifications */
        .notification-summary {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: center;
        }
        
        .notification-group {
            margin: 24px 0;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .notification-group-header {
            background: #f1f5f9;
            padding: 12px 16px;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #334155;
        }
        
        .notification-item {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            position: relative;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-item.high-priority {
            border-left: 4px solid #f59e0b;
            background: #fffbeb;
        }
        
        .notification-item.urgent {
            border-left: 4px solid #ef4444;
            background: #fef2f2;
        }
        
        .notification-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
            font-size: 14px;
        }
        
        .notification-message {
            color: #64748b;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .notification-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #94a3b8;
        }
        
        .notification-time {
            font-weight: 500;
        }
        
        .priority-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .priority-high {
            background: #fef3c7;
            color: #92400e;
        }
        
        .priority-urgent {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .view-all-button {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
        
        .view-all-button:hover {
            background: #2563eb;
        }
        
        .batch-info {
            background: #f8fafc;
            padding: 12px;
            border-radius: 6px;
            font-size: 12px;
            color: #64748b;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="email-logo">Collaboration Hub</h1>
            <p class="email-tagline">Streamline your content collaborations</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <h2 class="email-greeting" th:text="'Hi ' + ${userName} + '!'">Hi there!</h2>
            
            <!-- Notification Summary -->
            <div class="notification-summary">
                <h3 th:text="${totalNotifications == 1 ? '1 new notification' : totalNotifications + ' new notifications'}">
                    New notifications
                </h3>
                <p th:text="'You have received ' + ${totalNotifications} + ' notification' + (${totalNotifications > 1} ? 's' : '') + ' across ' + ${notificationTypes} + ' categor' + (${notificationTypes > 1} ? 'ies' : 'y')">
                    You have received notifications across multiple categories
                </p>
            </div>

            <!-- Notification Groups -->
            <div th:each="group : ${notificationGroups}" class="notification-group">
                <div class="notification-group-header">
                    <span th:text="${group.displayName}">Notification Type</span>
                    <span th:text="'(' + ${group.count} + ')'"> (count)</span>
                </div>
                
                <div th:each="notification : ${group.notifications}" 
                     class="notification-item"
                     th:classappend="${notification.urgency.name() == 'HIGH'} ? 'high-priority' : (${notification.urgency.name() == 'URGENT'} ? 'urgent' : '')">
                    
                    <div class="notification-title" th:text="${notification.title}">
                        Notification Title
                    </div>
                    
                    <div class="notification-message" th:text="${notification.message}">
                        Notification message content goes here.
                    </div>
                    
                    <div class="notification-meta">
                        <span class="notification-time" th:text="${notification.time}">12:34</span>
                        <span th:if="${notification.highPriority}" 
                              class="priority-badge"
                              th:classappend="${notification.urgency.name() == 'HIGH'} ? 'priority-high' : 'priority-urgent'"
                              th:text="${notification.urgency.name()}">
                            HIGH
                        </span>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 32px 0;">
                <a th:href="${viewAllNotificationsUrl}" class="view-all-button">
                    View All Notifications
                </a>
            </div>

            <!-- Unsubscribe Section -->
            <div style="text-align: center; margin: 24px 0; padding-top: 20px; border-top: 1px solid #e2e8f0;">
                <p style="font-size: 13px; color: #64748b; margin-bottom: 12px;">
                    Don't want to receive these notification emails?
                </p>
                <a th:href="${unsubscribeUrl}"
                   style="color: #64748b; text-decoration: underline; font-size: 13px; font-weight: 500;">
                    Unsubscribe from notifications
                </a>
                <p style="font-size: 11px; color: #94a3b8; margin-top: 8px; line-height: 1.4;">
                    Note: You'll still receive important emails like invitations and account security notifications.
                </p>
            </div>

            <!-- Batch Information -->
            <div class="batch-info">
                <p>
                    This email contains notifications from <span th:text="${batchTime}">time</span> on <span th:text="${batchDate}">date</span>.
                    <br>
                    To reduce email volume, we group your notifications and send them together.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p>
                You're receiving this email because you have notifications enabled in your 
                <a href="/app/settings/notifications">notification preferences</a>.
            </p>
            <p>
                <strong>Collaboration Hub</strong><br>
                Streamline your content collaborations
            </p>
        </div>
    </div>
</body>
</html>
