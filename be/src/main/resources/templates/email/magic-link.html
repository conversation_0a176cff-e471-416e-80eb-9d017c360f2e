<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'Access Your Collaboration Hub - ' + ${hubName}">Access Your Collaboration Hub</title>
    <style th:replace="email/base :: email-styles"></style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="email-logo" th:text="${accountName}">Collaboration Hub</h1>
            <p class="email-tagline">Streamline your content collaborations</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <h2 class="email-greeting">You're invited to collaborate!</h2>
            
            <div class="email-content">
                <p th:if="${hasInviter}">
                    <strong th:text="${inviterName}"><PERSON></strong> has invited you to collaborate on
                    <strong th:text="${hubName}">the collaboration hub</strong><span th:if="${hasRole}"> as a <strong th:text="${participantRole}">Reviewer</strong></span>.
                </p>
                <p th:unless="${hasInviter}">
                    You've been invited to collaborate on <strong th:text="${hubName}">the collaboration hub</strong><span th:if="${hasRole}"> as a <strong th:text="${participantRole}">Reviewer</strong></span>.
                </p>

                <p th:if="${hasDescription}" th:text="${hubDescription}" style="color: #6b7280; font-style: italic; margin: 16px 0;">
                    Project description goes here.
                </p>

                <p>Click the button below to access the collaboration hub and start working on the project:</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a th:href="${magicUrl}" class="email-button">
                    Access Collaboration Hub
                </a>
            </div>

            <div class="email-content">
                <p>Or copy and paste this link into your browser:</p>
                <div class="email-link" th:text="${magicUrl}">magic-link</div>
            </div>

            <div class="security-notice">
                <strong>Security Notice:</strong> This access link will expire within 24 hours for security reasons.
                If you weren't expecting this invitation<span th:if="${hasInviter}"> from <span th:text="${inviterName}">the inviter</span></span>, please ignore this email.
            </div>

            <div class="email-content">
                <p><strong>What you can do in the collaboration hub:</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>Review and provide feedback on content</li>
                    <li>Participate in project discussions</li>
                    <li>Upload and share files</li>
                    <li>Track project progress</li>
                </ul>
                
                <p>We're excited to have you as part of this collaboration!</p>
                
                <p style="margin-top: 30px;">
                    Best regards,<br>
                    <strong th:if="${hasInviter}">
                        <span th:text="${inviterName}">John Doe</span><br>
                        <span th:text="${accountName}">Collaboration Hub</span> Team
                    </strong>
                    <strong th:unless="${hasInviter}">
                        The <span th:text="${accountName}">Collaboration Hub</span> Team
                    </strong>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p class="email-footer-text">
                This email was sent by <span th:text="${accountName}">Collaboration Hub</span>.
                <span th:if="${hasInviter}">If you have any questions about this invitation, you can reply to this email or contact <span th:text="${inviterName}">the inviter</span>.</span>
                <span th:unless="${hasInviter}">If you have any questions, please contact our support team.</span>
            </p>
            <div class="email-footer-links">
                <a href="#" th:href="${frontendUrl}">Visit Website</a>
                <a href="#" th:href="${frontendUrl + '/support'}">Support</a>
                <a href="#" th:href="${frontendUrl + '/privacy'}">Privacy Policy</a>
            </div>
        </div>
    </div>
</body>
</html>
