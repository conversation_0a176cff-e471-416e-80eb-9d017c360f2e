<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'Invoice ' + ${invoice.invoiceNumber}">Invoice</title>
    <style th:replace="email/base :: email-styles"></style>
    <style>
        /* Invoice-specific styles */
        .invoice-summary {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .invoice-number {
            font-size: 18px;
            font-weight: 700;
            color: #111827;
            letter-spacing: -0.025em;
        }

        .invoice-status {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-draft { background-color: #f3f4f6; color: #374151; border: 1px solid #d1d5db; }
        .status-sent { background-color: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .status-overdue { background-color: #fee2e2; color: #dc2626; border: 1px solid #fca5a5; }
        .status-paid { background-color: #d1fae5; color: #059669; border: 1px solid #6ee7b7; }
        
        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detail-label {
            font-weight: 600;
            color: #6b7280;
            font-size: 14px;
        }

        .detail-value {
            color: #111827;
            font-weight: 500;
            font-size: 14px;
        }

        .total-amount {
            background-color: #111827;
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 24px 0;
            border: 1px solid #111827;
        }

        .total-amount .amount {
            font-size: 28px;
            font-weight: 700;
            display: block;
            letter-spacing: -0.025em;
        }

        .total-amount .label {
            font-size: 14px;
            opacity: 0.8;
            font-weight: 500;
        }
        
        .overdue-notice {
            background-color: #fee2e2;
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 16px;
            margin: 24px 0;
            text-align: center;
        }

        .overdue-notice .title {
            font-weight: 700;
            color: #dc2626;
            font-size: 16px;
            margin-bottom: 8px;
        }

        .overdue-notice .message {
            color: #991b1b;
            line-height: 1.5;
        }

        .copy-notice {
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: center;
            font-style: italic;
            color: #374151;
            font-weight: 500;
        }

        .attachment-notice {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 16px;
            margin: 24px 0;
            display: flex;
            align-items: center;
        }

        .attachment-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: #059669;
        }
        
        .payment-info {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 24px 0;
        }

        .payment-info h4 {
            margin: 0 0 16px 0;
            color: #111827;
            font-weight: 600;
            font-size: 16px;
        }

        .bank-details {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="email-logo">Collaboration Hub</h1>
            <p class="email-tagline">Professional Invoice Management</p>
        </div>

        <!-- Copy Notice -->
        <div class="copy-notice" th:if="${isCopy}">
            📋 This is a copy of the invoice for your records
        </div>

        <!-- Body -->
        <div class="email-body">
            <h2 class="email-greeting" th:text="${isCopy ? 'Invoice Copy Attached' : 'New Invoice Available'}">New Invoice Available</h2>
            
            <div class="email-content">
                <p th:text="${isCopy ? 'You are receiving a copy of this invoice for your records.' : 'Please find your invoice attached to this email.'}">
                    Please find your invoice attached to this email.
                </p>
            </div>

            <!-- Overdue Notice -->
            <div class="overdue-notice" th:if="${isOverdue}">
                <div class="title">⚠️ PAYMENT OVERDUE</div>
                <div class="message" th:text="'This invoice is overdue by ' + ${-daysUntilDue} + ' days. Please arrange payment immediately.'">
                    This invoice is overdue. Please arrange payment immediately.
                </div>
            </div>

            <!-- Invoice Summary -->
            <div class="invoice-summary">
                <div class="invoice-header">
                    <div class="invoice-number" th:text="'Invoice ' + ${invoice.invoiceNumber}">Invoice #INV-001</div>
                    <div class="invoice-status" th:class="'status-' + ${invoice.status}" th:text="${invoice.status}">sent</div>
                </div>
                
                <div class="invoice-details">
                    <div class="detail-item">
                        <span class="detail-label">Issue Date:</span>
                        <span class="detail-value" th:text="${#temporals.format(invoice.issueDate, 'dd/MM/yyyy')}">01/01/2024</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Due Date:</span>
                        <span class="detail-value" th:text="${#temporals.format(invoice.dueDate, 'dd/MM/yyyy')}">31/01/2024</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">From:</span>
                        <span class="detail-value" th:text="${issuer.name}">Company Name</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">To:</span>
                        <span class="detail-value" th:text="${recipient.name}">Client Name</span>
                    </div>
                </div>
                
                <div class="total-amount">
                    <span class="label">Total Amount Due</span>
                    <span class="amount" th:text="${formattedTotalAmount}">€121.00</span>
                </div>
            </div>

            <!-- Attachment Notice -->
            <div class="attachment-notice">
                <svg class="attachment-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <strong>PDF Invoice Attached</strong><br>
                    <span th:text="'Invoice_' + ${invoice.invoiceNumber} + '.pdf'">Invoice_INV-001.pdf</span>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="payment-info" th:if="${!isCopy}">
                <h4>💳 Payment Information</h4>
                <div class="bank-details">
                    <strong>Account Name:</strong> <span th:text="${bankDetails.accountName}">Account Name</span><br>
                    <strong>Bank:</strong> <span th:text="${bankDetails.bankName}">Bank Name</span><br>
                    <strong>IBAN:</strong> <span th:text="${bankDetails.iban}">IBAN</span><br>
                    <strong>BIC/SWIFT:</strong> <span th:text="${bankDetails.bic}">BIC</span><br>
                    <strong>Reference:</strong> <span th:text="${invoice.invoiceNumber}">INV-001</span>
                </div>
            </div>

            <!-- Notes -->
            <div class="email-content" th:if="${invoice.notes}">
                <h4>📝 Additional Notes:</h4>
                <p th:text="${invoice.notes}">Additional notes or payment terms...</p>
            </div>

            <!-- Due Date Reminder -->
            <div class="email-content" th:if="${!isOverdue and daysUntilDue != null and daysUntilDue <= 7}">
                <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin: 24px 0; color: #92400e; font-weight: 500;">
                    <strong>⏰ Payment Reminder:</strong> This invoice is due in
                    <span th:text="${daysUntilDue}">X</span> day<span th:if="${daysUntilDue != 1}">s</span>.
                </div>
            </div>

            <div class="email-content">
                <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
                
                <p style="margin-top: 30px;">
                    Best regards,<br>
                    <strong th:text="${issuer.name}">Company Name</strong><br>
                    <span th:if="${issuer.email}" th:text="${issuer.email}"><EMAIL></span><br>
                    <span th:if="${issuer.phone}" th:text="${issuer.phone}">****** 567 8900</span>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p class="email-footer-text">This email was sent from Collaboration Hub's invoice management system.</p>
            <div class="email-footer-links">
                <a th:href="${frontendUrl}">Visit Collaboration Hub</a>
                <a th:href="${frontendUrl + '/support'}">Support</a>
            </div>
        </div>
    </div>
</body>
</html>
