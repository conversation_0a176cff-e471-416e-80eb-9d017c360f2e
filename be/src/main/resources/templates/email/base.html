<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${subject}">Email</title>
    <style th:fragment="email-styles">
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
        }

        /* Base styles */
        body {
            margin: 0;
            padding: 0;
            width: 100% !important;
            min-width: 100%;
            background-color: #f9fafb;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #111827;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .email-header {
            background-color: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            padding: 32px 30px;
            text-align: center;
        }

        .email-logo {
            color: #111827;
            font-size: 24px;
            font-weight: 700;
            text-decoration: none;
            margin: 0;
            letter-spacing: -0.025em;
        }

        .email-tagline {
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            margin: 6px 0 0 0;
        }

        .email-body {
            padding: 40px 30px;
        }

        .email-greeting {
            font-size: 20px;
            font-weight: 700;
            color: #111827;
            margin: 0 0 24px 0;
            letter-spacing: -0.025em;
        }

        .email-content {
            font-size: 16px;
            line-height: 1.7;
            color: #374151;
            margin: 0 0 24px 0;
        }

        .email-button {
            display: inline-block;
            background-color: #111827;
            color: #ffffff !important;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            margin: 24px 0;
            transition: all 0.2s ease;
            border: 1px solid #111827;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .email-button:hover {
            background-color: #1f2937;
            border-color: #1f2937;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .email-link {
            color: #111827;
            text-decoration: none;
            word-break: break-all;
            font-size: 13px;
            background-color: #f3f4f6;
            padding: 12px 16px;
            border-radius: 8px;
            display: block;
            margin: 16px 0;
            border: 1px solid #e5e7eb;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        .email-footer {
            background-color: #f9fafb;
            padding: 32px 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .email-footer-text {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 16px 0;
            line-height: 1.5;
        }

        .email-footer-links {
            font-size: 13px;
            color: #9ca3af;
        }

        .email-footer-links a {
            color: #374151;
            text-decoration: none;
            margin: 0 12px;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .email-footer-links a:hover {
            color: #111827;
        }

        .security-notice {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 24px 0;
            font-size: 14px;
            color: #92400e;
            line-height: 1.5;
        }

        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
                border-radius: 0 !important;
            }
            .email-header {
                padding: 24px 20px !important;
            }
            .email-body {
                padding: 32px 20px !important;
            }
            .email-footer {
                padding: 24px 20px !important;
            }
            .email-button {
                display: block !important;
                width: 100% !important;
                box-sizing: border-box;
                padding: 16px 24px !important;
            }
            .email-greeting {
                font-size: 18px !important;
            }
            .email-logo {
                font-size: 20px !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="email-logo">Collaboration Hub</h1>
            <p class="email-tagline">Streamline your content collaborations</p>
        </div>

        <!-- Body -->
        <div class="email-body" th:fragment="email-template">
            <div th:insert="${contentFragment}"></div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p class="email-footer-text">
                This email was sent by Collaboration Hub. If you have any questions, please contact our support team.
            </p>
            <div class="email-footer-links">
                <a href="#" th:href="${frontendUrl}">Visit Website</a>
                <a href="#" th:href="${frontendUrl + '/support'}">Support</a>
                <a href="#" th:href="${frontendUrl + '/privacy'}">Privacy Policy</a>
            </div>
        </div>
    </div>
</body>
</html>
