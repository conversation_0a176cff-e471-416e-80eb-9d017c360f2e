package com.collabhub.be.exception.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * Unified error response structure for all API exceptions.
 * Ensures consistent error format across the entire application.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {

    @NotNull
    private ErrorInfo error;

    public ErrorResponse(String code, String message) {
        this.error = new ErrorInfo(code, message);
    }

    public ErrorResponse(String code, String message, List<ErrorDetail> details) {
        this.error = new ErrorInfo(code, message, details);
    }

    public ErrorInfo getError() {
        return error;
    }

    public void setError(ErrorInfo error) {
        this.error = error;
    }

    /**
     * Error information container.
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ErrorInfo {
        @NotNull
        private String code;
        @NotNull
        private String message;
        private List<ErrorDetail> details;

        public ErrorInfo(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public ErrorInfo(String code, String message, List<ErrorDetail> details) {
            this.code = code;
            this.message = message;
            this.details = details;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public List<ErrorDetail> getDetails() {
            return details;
        }

        public void setDetails(List<ErrorDetail> details) {
            this.details = details;
        }

        @Override
        public String toString() {
            return "ErrorInfo{" +
                    "code='" + code + '\'' +
                    ", message='" + message + '\'' +
                    ", details=" + details +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "ErrorResponse{" +
                "error=" + error +
                '}';
    }
}
