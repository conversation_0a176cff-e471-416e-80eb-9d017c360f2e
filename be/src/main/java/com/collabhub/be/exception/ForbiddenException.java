package com.collabhub.be.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception for HTTP 403 Forbidden errors.
 * Used for authorization failures and access denied scenarios.
 */
@ResponseStatus(HttpStatus.FORBIDDEN)
public class ForbiddenException extends ApiException {

    public ForbiddenException(ErrorCode errorCode, String message) {
        super(errorCode, message, HttpStatus.FORBIDDEN);
    }

    public ForbiddenException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause, HttpStatus.FORBIDDEN);
    }

    /**
     * Convenience constructor for access denied scenarios.
     */
    public ForbiddenException(String message) {
        super(ErrorCode.ACCESS_DENIED, message, HttpStatus.FORBIDDEN);
    }

    /**
     * Convenience constructor for access denied scenarios with cause.
     */
    public ForbiddenException(String message, Throwable cause) {
        super(ErrorCode.ACCESS_DENIED, message, cause, HttpStatus.FORBIDDEN);
    }
}
