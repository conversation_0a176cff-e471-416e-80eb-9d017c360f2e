package com.collabhub.be.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception for HTTP 400 Bad Request errors.
 * Used for validation errors, malformed requests, and other client-side errors.
 */
@ResponseStatus(HttpStatus.BAD_REQUEST)
public class BadRequestException extends ApiException {

    public BadRequestException(ErrorCode errorCode, String message) {
        super(errorCode, message, HttpStatus.BAD_REQUEST);
    }

    public BadRequestException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause, HttpStatus.BAD_REQUEST);
    }

    /**
     * Convenience constructor for common bad request scenarios.
     */
    public BadRequestException(String message) {
        super(ErrorCode.BAD_REQUEST, message, HttpStatus.BAD_REQUEST);
    }

    /**
     * Convenience constructor for common bad request scenarios with cause.
     */
    public BadRequestException(String message, Throwable cause) {
        super(ErrorCode.BAD_REQUEST, message, cause, HttpStatus.BAD_REQUEST);
    }
}
