package com.collabhub.be.exception;

import org.springframework.http.HttpStatus;

/**
 * Base exception for custom application errors.
 * Provides consistent handling with HTTP status codes and error codes for frontend i18n.
 */
public abstract class ApiException extends RuntimeException {

    private final ErrorCode errorCode;
    private final HttpStatus httpStatus;

    public ApiException(ErrorCode errorCode, String message) {
        this(errorCode, message, HttpStatus.BAD_REQUEST);
    }

    public ApiException(ErrorCode errorCode, String message, HttpStatus httpStatus) {
        super(message);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
    }

    public ApiException(ErrorCode errorCode, String message, Throwable cause) {
        this(errorCode, message, cause, HttpStatus.BAD_REQUEST);
    }

    public ApiException(ErrorCode errorCode, String message, Throwable cause, HttpStatus httpStatus) {
        super(message, cause);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

    public HttpStatus getHttpStatus() {
        return httpStatus;
    }

    @Override
    public String toString() {
        return "ApiException{" +
                "errorCode=" + errorCode +
                ", httpStatus=" + httpStatus +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
