package com.collabhub.be.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception for HTTP 500 Internal Server Error.
 * Used for unexpected server-side errors and system failures.
 */
@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class InternalServerErrorException extends ApiException {

    public InternalServerErrorException(ErrorCode errorCode, String message) {
        super(errorCode, message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public InternalServerErrorException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Convenience constructor for internal server errors.
     */
    public InternalServerErrorException(String message) {
        super(ErrorCode.INTERNAL_SERVER_ERROR, message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Convenience constructor for internal server errors with cause.
     */
    public InternalServerErrorException(String message, Throwable cause) {
        super(ErrorCode.INTERNAL_SERVER_ERROR, message, cause, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
