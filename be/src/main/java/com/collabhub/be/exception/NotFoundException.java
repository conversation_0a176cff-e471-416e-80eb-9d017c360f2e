package com.collabhub.be.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception for HTTP 404 Not Found errors.
 * Used when requested resources cannot be found.
 */
@ResponseStatus(HttpStatus.NOT_FOUND)
public class NotFoundException extends ApiException {

    public NotFoundException(ErrorCode errorCode, String message) {
        super(errorCode, message, HttpStatus.NOT_FOUND);
    }

    public NotFoundException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause, HttpStatus.NOT_FOUND);
    }

    /**
     * Convenience constructor for resource not found scenarios.
     */
    public NotFoundException(String message) {
        super(ErrorCode.RESOURCE_NOT_FOUND, message, HttpStatus.NOT_FOUND);
    }

    /**
     * Convenience constructor for resource not found scenarios with cause.
     */
    public NotFoundException(String message, Throwable cause) {
        super(ErrorCode.RESOURCE_NOT_FOUND, message, cause, HttpStatus.NOT_FOUND);
    }
}
