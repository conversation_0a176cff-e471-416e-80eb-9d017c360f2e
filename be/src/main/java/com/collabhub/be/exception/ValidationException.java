package com.collabhub.be.exception;

import com.collabhub.be.exception.dto.ErrorDetail;
import org.springframework.http.HttpStatus;
import java.util.List;

/**
 * Validation exception with field-level error details.
 * Used for business logic validation errors.
 */
public class ValidationException extends ApiException {

    private final List<ErrorDetail> errorDetails;

    public ValidationException(String message, List<ErrorDetail> errorDetails) {
        super(ErrorCode.VALIDATION_FAILED, message, HttpStatus.BAD_REQUEST);
        this.errorDetails = errorDetails;
    }

    public ValidationException(String message, List<ErrorDetail> errorDetails, Throwable cause) {
        super(ErrorCode.VALIDATION_FAILED, message, cause, HttpStatus.BAD_REQUEST);
        this.errorDetails = errorDetails;
    }

    public List<ErrorDetail> getErrorDetails() {
        return errorDetails;
    }

    @Override
    public String toString() {
        return "ValidationException{" +
                "errorDetails=" + errorDetails +
                ", errorCode=" + getErrorCode() +
                ", httpStatus=" + getHttpStatus() +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
