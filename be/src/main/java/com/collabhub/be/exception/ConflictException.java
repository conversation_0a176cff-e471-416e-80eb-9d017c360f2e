package com.collabhub.be.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception for HTTP 409 Conflict errors.
 * Used for resource conflicts, duplicate entries, and business rule violations.
 */
@ResponseStatus(HttpStatus.CONFLICT)
public class ConflictException extends ApiException {

    public ConflictException(ErrorCode errorCode, String message) {
        super(errorCode, message, HttpStatus.CONFLICT);
    }

    public ConflictException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause, HttpStatus.CONFLICT);
    }

    /**
     * Convenience constructor for resource conflict scenarios.
     */
    public ConflictException(String message) {
        super(ErrorCode.RESOURCE_ALREADY_EXISTS, message, HttpStatus.CONFLICT);
    }

    /**
     * Convenience constructor for resource conflict scenarios with cause.
     */
    public ConflictException(String message, Throwable cause) {
        super(ErrorCode.RESOURCE_ALREADY_EXISTS, message, cause, HttpStatus.CONFLICT);
    }
}
