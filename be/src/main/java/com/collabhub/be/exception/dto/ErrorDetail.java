package com.collabhub.be.exception.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

import java.util.Map;

/**
 * Field-level error detail for validation responses.
 * Contains specific information about validation failures on individual fields.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorDetail {

    @NotNull
    private String field;
    @NotNull
    private String code;
    @NotNull
    private String message;

    private Map<String, Object> params;

    public ErrorDetail(String field, String code, String message) {
        this.field = field;
        this.code = code;
        this.message = message;
    }

    public ErrorDetail(String field, String code, String message, Map<String, Object> params) {
        this.field = field;
        this.code = code;
        this.message = message;
        this.params = params;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    @Override
    public String toString() {
        return "ErrorDetail{" +
                "field='" + field + '\'' +
                ", code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", params=" + params +
                '}';
    }
}
