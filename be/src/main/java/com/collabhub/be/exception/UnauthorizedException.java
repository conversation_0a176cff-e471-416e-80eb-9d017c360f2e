package com.collabhub.be.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception for HTTP 401 Unauthorized errors.
 * Used for authentication failures, invalid credentials, and expired tokens.
 */
@ResponseStatus(HttpStatus.UNAUTHORIZED)
public class UnauthorizedException extends ApiException {

    public UnauthorizedException(ErrorCode errorCode, String message) {
        super(errorCode, message, HttpStatus.UNAUTHORIZED);
    }

    public UnauthorizedException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause, HttpStatus.UNAUTHORIZED);
    }

    /**
     * Convenience constructor for authentication failures.
     */
    public UnauthorizedException(String message) {
        super(ErrorCode.AUTHENTICATION_FAILED, message, HttpStatus.UNAUTHORIZED);
    }

    /**
     * Convenience constructor for authentication failures with cause.
     */
    public UnauthorizedException(String message, Throwable cause) {
        super(ErrorCode.AUTHENTICATION_FAILED, message, cause, HttpStatus.UNAUTHORIZED);
    }
}
