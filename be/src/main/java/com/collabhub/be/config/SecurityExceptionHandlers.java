package com.collabhub.be.config;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.dto.ErrorResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Spring Security exception handlers providing unified error responses.
 * These handlers are invoked before requests reach the controller layer.
 */
@Component
public class SecurityExceptionHandlers {

    public SecurityExceptionHandlers() {
    }

    /**
     * Handles authentication failures (e.g., missing or invalid JWT tokens).
     */
    @Component
    public static class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

        private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationEntryPoint.class);
        private final ObjectMapper objectMapper;

        public CustomAuthenticationEntryPoint(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @Override
        public void commence(HttpServletRequest request, HttpServletResponse response,
                           AuthenticationException authException) throws IOException {

            logger.warn("Authentication failed for request {}: {}",
                       request.getRequestURI(), authException.getMessage());

            ErrorResponse errorResponse = new ErrorResponse(
                ErrorCode.AUTHENTICATION_FAILED.getCode(),
                "Authentication failed"
            );

            writeErrorResponse(response, errorResponse);
        }

        private void writeErrorResponse(HttpServletResponse response,
                                        ErrorResponse errorResponse) throws IOException {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());

            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            response.getWriter().write(jsonResponse);
            response.getWriter().flush();
        }
    }

    /**
     * Handles authorization failures (e.g., insufficient privileges).
     */
    @Component
    public static class CustomAccessDeniedHandler implements AccessDeniedHandler {

        private static final Logger logger = LoggerFactory.getLogger(CustomAccessDeniedHandler.class);
        private final ObjectMapper objectMapper;

        public CustomAccessDeniedHandler(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @Override
        public void handle(HttpServletRequest request, HttpServletResponse response,
                          AccessDeniedException accessDeniedException) throws IOException {

            logger.warn("Access denied for request {}: {}",
                       request.getRequestURI(), accessDeniedException.getMessage());

            ErrorResponse errorResponse = new ErrorResponse(
                ErrorCode.ACCESS_DENIED.getCode(),
                "Access denied"
            );

            writeErrorResponse(response, errorResponse);
        }

        private void writeErrorResponse(HttpServletResponse response,
                                        ErrorResponse errorResponse) throws IOException {
            response.setStatus(HttpStatus.FORBIDDEN.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());

            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            response.getWriter().write(jsonResponse);
            response.getWriter().flush();
        }
    }
}
