package com.collabhub.be.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.jooq.JSONB;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * Jackson configuration for custom serialization/deserialization.
 * Provides custom serializers for jOOQ types like JSONB.
 */
@Configuration
public class JacksonConfig {

    /**
     * Custom Jackson module for handling jOOQ JSONB type serialization.
     * This module provides serializers and deserializers for JSONB objects
     * to prevent "No serializer found for class org.jooq.JSONB" errors.
     *
     * @return configured Jackson module
     */
    @Bean
    public SimpleModule jooqJsonbModule() {
        SimpleModule module = new SimpleModule("JooqJsonbModule");
        
        // Add JSONB serializer
        module.addSerializer(JSONB.class, new JsonbSerializer());
        
        // Add JSONB deserializer
        module.addDeserializer(JSONB.class, new JsonbDeserializer());
        
        return module;
    }

    /**
     * Custom serializer for jOOQ JSONB type.
     * Converts JSONB objects to their JSON string representation.
     */
    public static class JsonbSerializer extends JsonSerializer<JSONB> {
        
        @Override
        public void serialize(JSONB value, JsonGenerator gen, SerializerProvider serializers) 
                throws IOException {
            if (value == null) {
                gen.writeNull();
            } else {
                // Write the JSON data as raw JSON
                gen.writeRawValue(value.data());
            }
        }
    }

    /**
     * Custom deserializer for jOOQ JSONB type.
     * Converts JSON strings back to JSONB objects.
     */
    public static class JsonbDeserializer extends JsonDeserializer<JSONB> {
        
        @Override
        public JSONB deserialize(JsonParser p, DeserializationContext ctxt) 
                throws IOException {
            String jsonString = p.readValueAsTree().toString();
            return JSONB.valueOf(jsonString);
        }
    }
}
