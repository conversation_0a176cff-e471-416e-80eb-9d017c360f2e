package com.collabhub.be.config;

import com.collabhub.be.modules.auth.model.Permission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Validates that all required configurations are properly set for production deployment.
 * Performs comprehensive checks on startup to ensure system integrity.
 */
@Component
public class ProductionReadinessValidator {

    private static final Logger logger = LoggerFactory.getLogger(ProductionReadinessValidator.class);

    private final Environment environment;

    public ProductionReadinessValidator(Environment environment) {
        this.environment = environment;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void validateProductionReadiness() {
        logger.info("Starting production readiness validation...");

        boolean allValid = true;

        // Validate database configuration
        allValid &= validateDatabaseConfig();

        // Validate S3 configuration
        allValid &= validateS3Config();

        // Validate JWT configuration
        allValid &= validateJwtConfig();

        // Validate permission constants
        allValid &= validatePermissions();

        // Validate logging configuration
        allValid &= validateLoggingConfig();

        // Validate security configuration
        allValid &= validateSecurityConfig();

        if (allValid) {
            logger.info("✅ Production readiness validation PASSED - System is ready for deployment");
        } else {
            logger.error("❌ Production readiness validation FAILED - System is NOT ready for production");
            throw new IllegalStateException("Production readiness validation failed. Check logs for details.");
        }
    }

    private boolean validateDatabaseConfig() {
        logger.debug("Validating database configuration...");
        
        String url = environment.getProperty("spring.datasource.url");
        String username = environment.getProperty("spring.datasource.username");
        String password = environment.getProperty("spring.datasource.password");

        if (url == null || url.trim().isEmpty()) {
            logger.error("Database URL is not configured");
            return false;
        }

        if (username == null || username.trim().isEmpty()) {
            logger.error("Database username is not configured");
            return false;
        }

        if (password == null || password.trim().isEmpty()) {
            logger.error("Database password is not configured");
            return false;
        }

        // Validate connection pool settings
        String maxPoolSize = environment.getProperty("spring.datasource.hikari.maximum-pool-size");
        if (maxPoolSize == null) {
            logger.warn("Database connection pool size not configured - using defaults");
        }

        logger.debug("✅ Database configuration is valid");
        return true;
    }

    private boolean validateS3Config() {
        logger.debug("Validating S3 configuration...");

        String region = environment.getProperty("app.s3.region");
        if (region == null || region.trim().isEmpty()) {
            logger.error("S3 region is not configured");
            return false;
        }

        String bucketName = environment.getProperty("app.s3.bucket-name");
        if (bucketName == null || bucketName.trim().isEmpty()) {
            logger.error("S3 bucket name is not configured");
            return false;
        }

        String accessKey = environment.getProperty("app.s3.access-key");
        if (accessKey == null || accessKey.trim().isEmpty()) {
            logger.error("S3 access key is not configured");
            return false;
        }

        String secretKey = environment.getProperty("app.s3.secret-key");
        if (secretKey == null || secretKey.trim().isEmpty()) {
            logger.error("S3 secret key is not configured");
            return false;
        }

        // Check for allowed MIME types
        String allowedImageTypes = environment.getProperty("app.s3.allowed-image-types");
        String allowedVideoTypes = environment.getProperty("app.s3.allowed-video-types");
        if ((allowedImageTypes == null || allowedImageTypes.trim().isEmpty()) &&
            (allowedVideoTypes == null || allowedVideoTypes.trim().isEmpty())) {
            logger.warn("S3 allowed MIME types not configured - this may cause security issues");
        }

        // Check max file size
        String maxFileSize = environment.getProperty("app.s3.max-file-size");
        if (maxFileSize == null || maxFileSize.trim().isEmpty()) {
            logger.warn("S3 max file size not configured - using default");
        }

        logger.debug("✅ S3 configuration is valid");
        return true;
    }

    private boolean validateJwtConfig() {
        logger.debug("Validating JWT configuration...");

        String issuer = environment.getProperty("app.auth.jwt.issuer");
        if (issuer == null || issuer.trim().isEmpty()) {
            logger.error("JWT issuer is not configured");
            return false;
        }

        String audience = environment.getProperty("app.auth.jwt.audience");
        if (audience == null || audience.trim().isEmpty()) {
            logger.error("JWT audience is not configured");
            return false;
        }

        String signatureAlgorithm = environment.getProperty("app.auth.jwt.signature-algorithm");
        if (signatureAlgorithm == null || signatureAlgorithm.trim().isEmpty()) {
            logger.error("JWT signature algorithm is not configured");
            return false;
        }

        String accessTokenTtl = environment.getProperty("app.auth.jwt.access-token-ttl");
        if (accessTokenTtl == null || accessTokenTtl.trim().isEmpty()) {
            logger.error("JWT access token TTL is not configured");
            return false;
        }

        // Validate refresh token configuration
        String refreshTokenTtl = environment.getProperty("app.auth.refresh-token.ttl");
        if (refreshTokenTtl == null || refreshTokenTtl.trim().isEmpty()) {
            logger.error("Refresh token TTL is not configured");
            return false;
        }

        logger.debug("✅ JWT configuration is valid");
        return true;
    }

    private boolean validatePermissions() {
        logger.debug("Validating permission constants...");

        try {
            // Validate that all required permissions exist
            List<Permission> requiredPermissions = Arrays.asList(
                Permission.POST_READ,
                Permission.POST_WRITE,
                Permission.POST_UPDATE,
                Permission.POST_DELETE,
                Permission.HUB_PARTICIPANT_READ,
                Permission.HUB_PARTICIPANT_INVITE,
                Permission.HUB_PARTICIPANT_MANAGE,
                Permission.BRIEF_READ,
                Permission.BRIEF_WRITE,
                Permission.BRIEF_UPDATE,
                Permission.BRIEF_DELETE
            );

            for (Permission permission : requiredPermissions) {
                if (permission.getPermission() == null || permission.getPermission().trim().isEmpty()) {
                    logger.error("Permission {} has invalid permission string", permission);
                    return false;
                }
            }

            logger.debug("✅ Permission constants are valid");
            return true;
        } catch (Exception e) {
            logger.error("Failed to validate permissions: {}", e.getMessage());
            return false;
        }
    }

    private boolean validateLoggingConfig() {
        logger.debug("Validating logging configuration...");

        String logLevel = environment.getProperty("logging.level.com.collabhub");
        if (logLevel == null) {
            logger.warn("Application log level not explicitly configured");
        }

        String rootLogLevel = environment.getProperty("logging.level.root");
        if ("DEBUG".equalsIgnoreCase(rootLogLevel)) {
            logger.warn("Root log level is set to DEBUG - this may impact performance in production");
        }

        logger.debug("✅ Logging configuration is acceptable");
        return true;
    }

    private boolean validateSecurityConfig() {
        logger.debug("Validating security configuration...");

        // Check if we're running in production profile
        String[] activeProfiles = environment.getActiveProfiles();
        boolean isProduction = Arrays.asList(activeProfiles).contains("prod");

        if (isProduction) {
            // Additional production-specific validations
            String corsOrigins = environment.getProperty("app.cors.allowed-origins");
            if (corsOrigins == null || corsOrigins.contains("*")) {
                logger.error("CORS is not properly configured for production - wildcard origins detected");
                return false;
            }

            String httpsOnly = environment.getProperty("server.ssl.enabled");
            if (!"true".equals(httpsOnly)) {
                logger.warn("HTTPS is not enabled - this is not recommended for production");
            }
        }

        logger.debug("✅ Security configuration is valid");
        return true;
    }
}
