package com.collabhub.be.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * Configuration for S3 services.
 * Provides additional beans and configuration for the refactored S3 services.
 */
@Configuration
@EnableAsync
public class S3ServiceConfig {

    /**
     * Thread pool executor for S3 multipart upload operations.
     */
    @Bean(name = "s3MultipartExecutor")
    @ConditionalOnMissingBean(name = "s3MultipartExecutor")
    public Executor s3MultipartExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("S3Multipart-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * Thread pool executor for S3 async operations.
     */
    @Bean(name = "s3AsyncExecutor")
    @ConditionalOnMissingBean(name = "s3AsyncExecutor")
    public Executor s3AsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("S3Async-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
