package com.collabhub.be.config;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * Configuration properties for S3 storage.
 * Supports both AWS S3 and S3-compatible storage like MinIO.
 */
@ConfigurationProperties(prefix = "app.s3")
@Validated
public class S3Properties {

    /**
     * S3 endpoint URL. For AWS S3, this can be null to use default.
     * For MinIO or other S3-compatible storage, specify the endpoint.
     */
    private String endpoint;

    /**
     * AWS access key ID or MinIO access key.
     */
    @NotBlank
    private String accessKey;

    /**
     * AWS secret access key or MinIO secret key.
     */
    @NotBlank
    private String secretKey;

    /**
     * AWS region.
     */
    @NotBlank
    private String region = "us-east-1";

    /**
     * Single shared bucket name for all accounts.
     * Files are organized using folder structure: {bucketName}/{accountId}/{resourceType}/{filename}
     */
    @NotBlank
    private String bucketName = "collabhub";

    /**
     * Maximum file size for uploads (default 100MB).
     */
    @NotBlank
    private String maxFileSize = "100MB";

    /**
     * Maximum file size for images (default 10MB).
     */
    private long maxImageSizeBytes = 10 * 1024 * 1024; // 10MB

    /**
     * Maximum file size for videos (default 100MB).
     */
    private long maxVideoSizeBytes = 100 * 1024 * 1024; // 100MB

    /**
     * Allowed image MIME types.
     */
    @NotNull
    private List<String> allowedImageTypes = List.of(
            "image/jpeg", "image/png", "image/gif", "image/webp"
    );

    /**
     * Allowed video MIME types.
     */
    @NotNull
    private List<String> allowedVideoTypes = List.of(
            "video/mp4", "video/quicktime", "video/x-msvideo"
    );

    /**
     * Allowed file extensions (security measure).
     */
    @NotNull
    private List<String> allowedExtensions = List.of(
            ".jpg", ".jpeg", ".png", ".gif", ".webp",
            ".mp4", ".mov", ".avi"
    );

    /**
     * Blocked file extensions for security.
     */
    @NotNull
    private List<String> blockedExtensions = List.of(
            ".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js",
            ".jar", ".php", ".asp", ".jsp", ".sh", ".ps1", ".py", ".rb"
    );

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(String maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public List<String> getAllowedImageTypes() {
        return allowedImageTypes;
    }

    public void setAllowedImageTypes(List<String> allowedImageTypes) {
        this.allowedImageTypes = allowedImageTypes;
    }

    public List<String> getAllowedVideoTypes() {
        return allowedVideoTypes;
    }

    public void setAllowedVideoTypes(List<String> allowedVideoTypes) {
        this.allowedVideoTypes = allowedVideoTypes;
    }

    public long getMaxImageSizeBytes() {
        return maxImageSizeBytes;
    }

    public void setMaxImageSizeBytes(long maxImageSizeBytes) {
        this.maxImageSizeBytes = maxImageSizeBytes;
    }

    public long getMaxVideoSizeBytes() {
        return maxVideoSizeBytes;
    }

    public void setMaxVideoSizeBytes(long maxVideoSizeBytes) {
        this.maxVideoSizeBytes = maxVideoSizeBytes;
    }

    public List<String> getAllowedExtensions() {
        return allowedExtensions;
    }

    public void setAllowedExtensions(List<String> allowedExtensions) {
        this.allowedExtensions = allowedExtensions;
    }

    public List<String> getBlockedExtensions() {
        return blockedExtensions;
    }

    public void setBlockedExtensions(List<String> blockedExtensions) {
        this.blockedExtensions = blockedExtensions;
    }

    /**
     * Gets the S3 key prefix for a specific account and resource type.
     * Format: {accountId}/{resourceType}/
     */
    public String getKeyPrefix(Long accountId, String resourceType) {
        return accountId + "/" + resourceType + "/";
    }

    /**
     * Resource type constants for organizing files in S3.
     */
    public static class ResourceType {
        public static final String POSTS = "posts";
        public static final String INVOICES = "invoices";
        public static final String AVATARS = "avatars";
        public static final String DOCUMENTS = "documents";
    }

    /**
     * Gets all allowed MIME types (images + videos).
     */
    public List<String> getAllowedMimeTypes() {
        List<String> allTypes = new ArrayList<>();
        allTypes.addAll(allowedImageTypes);
        allTypes.addAll(allowedVideoTypes);
        return allTypes;
    }

    /**
     * Checks if a MIME type is allowed.
     */
    public boolean isAllowedMimeType(String mimeType) {
        return allowedImageTypes.contains(mimeType) || allowedVideoTypes.contains(mimeType);
    }

    /**
     * Checks if a MIME type is an image.
     */
    public boolean isImageType(String mimeType) {
        return allowedImageTypes.contains(mimeType);
    }

    /**
     * Checks if a MIME type is a video.
     */
    public boolean isVideoType(String mimeType) {
        return allowedVideoTypes.contains(mimeType);
    }

    /**
     * Validates file extension for security.
     */
    public boolean isAllowedExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return false;
        }

        String extension = getFileExtension(filename).toLowerCase();
        return allowedExtensions.contains(extension) && !blockedExtensions.contains(extension);
    }

    /**
     * Checks if file extension is blocked for security reasons.
     */
    public boolean isBlockedExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return false;
        }

        String extension = getFileExtension(filename).toLowerCase();
        return blockedExtensions.contains(extension);
    }

    /**
     * Gets the maximum file size based on content type.
     */
    public long getMaxFileSizeForType(String contentType) {
        if (contentType == null) {
            return maxVideoSizeBytes; // Default to larger limit
        }

        if (isImageType(contentType)) {
            return maxImageSizeBytes;
        } else if (isVideoType(contentType)) {
            return maxVideoSizeBytes;
        }

        return maxVideoSizeBytes; // Default to larger limit
    }

    /**
     * Extracts file extension from filename.
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    /**
     * Sanitizes filename to prevent directory traversal and other attacks.
     */
    public String sanitizeFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "unnamed_file";
        }

        // Remove path separators and dangerous characters
        String sanitized = filename.replaceAll("[/\\\\:*?\"<>|]", "_")
                                  .replaceAll("\\.\\.", "_")
                                  .replaceAll("^\\.", "_")
                                  .trim();

        // Limit filename length
        if (sanitized.length() > 255) {
            String extension = getFileExtension(sanitized);
            String nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf('.'));
            sanitized = nameWithoutExt.substring(0, 255 - extension.length()) + extension;
        }

        return sanitized.isEmpty() ? "unnamed_file" : sanitized;
    }
}
