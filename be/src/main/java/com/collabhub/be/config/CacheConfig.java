package com.collabhub.be.config;

import com.collabhub.be.service.s3.constants.S3Constants;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.time.Duration;
import java.util.List;

/**
 * Cache configuration for the application.
 * Configures cache managers with appropriate TTL settings for different cache types.
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Cache manager with TTL configuration for presigned URLs and other cached data.
     * Uses Caffeine cache implementation for better performance and features.
     * Implements content-type-aware caching with appropriate TTL for security.
     */
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // Configure default cache settings - conservative TTL for security
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(2000) // Increased for better performance
                .expireAfterWrite(S3Constants.PRESIGNED_URL_CACHE_TTL) // 5 minutes - safe for 10min URLs
                .recordStats());

        // Pre-configure specific caches with custom settings
        cacheManager.setCacheNames(List.of("presignedDownloadUrls", "presignedUploadUrls"));

        return cacheManager;
    }


}
