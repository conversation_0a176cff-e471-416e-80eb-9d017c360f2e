package com.collabhub.be.config;

import com.collabhub.be.modules.auth.service.JwtClaimsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

import java.util.List;

/**
 * WebSocket configuration for real-time chat functionality.
 * Configures STOMP messaging with JWT-based authentication.
 */
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketConfig.class);

    private final JwtDecoder jwtDecoder;
    private final JwtClaimsService jwtClaimsService;

    public WebSocketConfig(JwtDecoder jwtDecoder, JwtClaimsService jwtClaimsService) {
        this.jwtDecoder = jwtDecoder;
        this.jwtClaimsService = jwtClaimsService;
    }

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // Enable simple broker for destinations prefixed with /topic and /queue
        config.enableSimpleBroker("/topic", "/queue");
        
        // Set application destination prefix for messages bound for @MessageMapping methods
        config.setApplicationDestinationPrefixes("/app");
        
        // Set user destination prefix for private messages
        config.setUserDestinationPrefix("/user");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // Register STOMP endpoint for WebSocket connections
        registry.addEndpoint("/ws/chat")
                .setAllowedOriginPatterns("*") // Configure properly for production
                .withSockJS(); // Enable SockJS fallback
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        registration.interceptors(new ChannelInterceptor() {
            @Override
            public Message<?> preSend(Message<?> message, MessageChannel channel) {
                StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
                
                if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand())) {
                    // Extract JWT token from Authorization header
                    String authHeader = accessor.getFirstNativeHeader("Authorization");
                    if (authHeader != null && authHeader.startsWith("Bearer ")) {
                        String token = authHeader.substring(7);
                        
                        try {
                            // Decode and validate JWT
                            Jwt jwt = jwtDecoder.decode(token);
                            
                            // Extract user context from JWT
                            var userContext = jwtClaimsService.extractUserContext(jwt);
                            
                            // Create authentication object
                            List<SimpleGrantedAuthority> authorities = userContext.getPermissions().stream()
                                    .map(permission -> new SimpleGrantedAuthority(permission.getPermission()))
                                    .toList();
                            
                            Authentication auth = new UsernamePasswordAuthenticationToken(
                                    userContext, null, authorities);
                            
                            // Set authentication in STOMP session
                            accessor.setUser(auth);
                            
                            logger.debug("WebSocket authentication successful for user: {}", 
                                    userContext.getUserId());
                            
                        } catch (JwtException e) {
                            logger.warn("WebSocket authentication failed: {}", e.getMessage());
                            throw new IllegalArgumentException("Invalid JWT token", e);
                        }
                    } else {
                        logger.warn("WebSocket connection attempted without valid Authorization header");
                        throw new IllegalArgumentException("Missing or invalid Authorization header");
                    }
                }
                
                return message;
            }
        });
    }
}
