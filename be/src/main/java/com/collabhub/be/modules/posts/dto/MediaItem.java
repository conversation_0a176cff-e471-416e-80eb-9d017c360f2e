package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Represents a media item (image or video) in a post.
 */
public class MediaItem {

    private String url;
    private String type; // "image" or "video"
    private Long size;
    private Integer duration; // For videos, in seconds
    private Integer width;
    private Integer height;

    @JsonProperty("mime_type")
    private String mimeType;

    public MediaItem() {}

    public MediaItem(String url, String type, Long size, String mimeType) {
        this.url = url;
        this.type = type;
        this.size = size;
        this.mimeType = mimeType;
    }

    public MediaItem(String url, String type, Long size, String mimeType, Integer duration, Integer width, Integer height) {
        this.url = url;
        this.type = type;
        this.size = size;
        this.mimeType = mimeType;
        this.duration = duration;
        this.width = width;
        this.height = height;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    @Override
    public String toString() {
        return "MediaItem{" +
                "url='" + url + '\'' +
                ", type='" + type + '\'' +
                ", size=" + size +
                ", duration=" + duration +
                ", width=" + width +
                ", height=" + height +
                ", mimeType='" + mimeType + '\'' +
                '}';
    }
}
