package com.collabhub.be.modules.brands.service;

import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.brands.converter.BrandContactConverter;
import com.collabhub.be.modules.brands.converter.BrandConverter;
import com.collabhub.be.modules.brands.converter.BrandListItemConverter;
import com.collabhub.be.modules.brands.dto.*;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.modules.brands.repository.BrandContactRepositoryImpl;
import com.collabhub.be.modules.brands.repository.BrandRepositoryImpl;
import org.jooq.generated.tables.pojos.Brand;
import org.jooq.generated.tables.pojos.BrandContact;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for managing brands and their contacts.
 * Handles business logic, validation, and multi-tenancy.
 */
@Service
public class BrandService {

    private static final Logger logger = LoggerFactory.getLogger(BrandService.class);

    // Pagination constants
    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_PAGE_SIZE = 100;

    private final BrandRepositoryImpl brandRepository;
    private final BrandContactRepositoryImpl brandContactRepository;
    private final BrandConverter brandConverter;
    private final BrandContactConverter brandContactConverter;
    private final BrandListItemConverter brandListItemConverter;

    public BrandService(BrandRepositoryImpl brandRepository,
                       BrandContactRepositoryImpl brandContactRepository,
                       BrandConverter brandConverter,
                       BrandContactConverter brandContactConverter,
                       BrandListItemConverter brandListItemConverter) {
        this.brandRepository = brandRepository;
        this.brandContactRepository = brandContactRepository;
        this.brandConverter = brandConverter;
        this.brandContactConverter = brandContactConverter;
        this.brandListItemConverter = brandListItemConverter;
    }

    /**
     * Creates a new brand with associated contacts.
     *
     * @param request the brand creation request
     * @param accountId the account ID for multi-tenancy
     * @return the created brand response
     */
    @Transactional
    public BrandResponse createBrand(BrandCreateRequest request, Long accountId) {
        logger.info("Creating brand '{}' for account {}", request.getName(), accountId);

        // Check if brand name already exists
        if (brandRepository.existsByNameAndAccountId(request.getName(), accountId, null)) {
            throw new ConflictException(ErrorCode.RESOURCE_ALREADY_EXISTS, 
                "Brand with name '" + request.getName() + "' already exists");
        }

        // Create brand
        Brand brand = brandConverter.toBrand(request, accountId);
        brandRepository.insert(brand);

        // Create contacts if provided
        List<BrandContactResponse> contactResponses = new ArrayList<>();
        if (request.getContacts() != null && !request.getContacts().isEmpty()) {
            contactResponses = createBrandContacts(brand.getId(), accountId, request.getContacts());
        }

        logger.info("Successfully created brand with ID {} for account {}", brand.getId(), accountId);
        return brandConverter.toResponse(brand, contactResponses);
    }

    /**
     * Retrieves brands with filtering and pagination for the current account.
     * Uses bulk loading to avoid N+1 queries when loading contacts.
     *
     * @param accountId the account ID for multi-tenancy
     * @param nameFilter optional name filter (case-insensitive partial match)
     * @param pageRequest pagination information (0-based page numbers)
     * @return paginated brand responses
     */
    @Transactional(readOnly = true)
    public PageResponse<BrandResponse> getBrands(Long accountId, String nameFilter, PageRequest pageRequest) {
        logger.debug("Retrieving brands for account {} with filter '{}', page {}, size {}",
                    accountId, nameFilter, pageRequest.getPage(), pageRequest.getSize());

        long totalCount = brandRepository.countBrandsWithFilter(accountId, nameFilter);
        List<Brand> brands = brandRepository.findBrandsWithPagination(
                accountId, nameFilter, pageRequest.getOffset(), pageRequest.getSize());

        List<BrandResponse> brandResponses = convertToBrandResponsesBulk(brands, accountId);

        logger.debug("Retrieved {} brands out of {} total for account {}",
                    brandResponses.size(), totalCount, accountId);

        return PageResponse.of(brandResponses, pageRequest, totalCount);
    }

    /**
     * Retrieves brands with filtering and pagination for the current account (optimized for list views).
     * This method returns lightweight DTOs without contacts to improve performance.
     *
     * @param accountId the account ID for multi-tenancy
     * @param nameFilter optional name filter (case-insensitive partial match)
     * @param pageRequest pagination information (0-based page numbers)
     * @return paginated lightweight brand list items
     */
    @Transactional(readOnly = true)
    public PageResponse<BrandListItemDto> getBrandListItems(Long accountId, String nameFilter, PageRequest pageRequest) {
        logger.debug("Retrieving brand list items for account {} with filter '{}', page {}, size {}",
                    accountId, nameFilter, pageRequest.getPage(), pageRequest.getSize());

        // Get total count for pagination metadata
        long totalCount = brandRepository.countBrandsWithFilter(accountId, nameFilter);

        // Get brands for current page
        List<Brand> brands = brandRepository.findBrandsWithPagination(
                accountId, nameFilter, pageRequest.getOffset(), pageRequest.getSize());

        // Convert to lightweight DTOs (no contacts fetching)
        List<BrandListItemDto> brandListItems = brandListItemConverter.toListItems(brands);

        logger.debug("Retrieved {} brand list items out of {} total for account {}",
                    brandListItems.size(), totalCount, accountId);

        return PageResponse.of(brandListItems, pageRequest, totalCount);
    }

    /**
     * Retrieves a specific brand by ID.
     *
     * @param id the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return the brand response
     */
    @Transactional(readOnly = true)
    public BrandResponse getBrandById(Long id, Long accountId) {
        logger.debug("Retrieving brand {} for account {}", id, accountId);

        Brand brand = brandRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, 
                    "Brand not found with ID: " + id));

        List<BrandContactResponse> contacts = getBrandContactResponses(id, accountId);
        return brandConverter.toResponse(brand, contacts);
    }

    /**
     * Updates an existing brand and its contacts.
     *
     * @param id the brand ID
     * @param request the brand update request
     * @param accountId the account ID for multi-tenancy
     * @return the updated brand response
     */
    @Transactional
    public BrandResponse updateBrand(Long id, BrandUpdateRequest request, Long accountId) {
        logger.info("Updating brand {} for account {}", id, accountId);

        // Find existing brand
        Brand existingBrand = brandRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, 
                    "Brand not found with ID: " + id));

        // Check if brand name already exists (excluding current brand)
        if (brandRepository.existsByNameAndAccountId(request.getName(), accountId, id)) {
            throw new ConflictException(ErrorCode.RESOURCE_ALREADY_EXISTS, 
                "Brand with name '" + request.getName() + "' already exists");
        }

        // Update brand
        Brand updatedBrand = brandConverter.updateBrand(existingBrand, request);
        brandRepository.update(updatedBrand);

        // Update contacts
        List<BrandContactResponse> contactResponses = updateBrandContacts(id, accountId, request.getContacts());

        logger.info("Successfully updated brand {} for account {}", id, accountId);
        return brandConverter.toResponse(updatedBrand, contactResponses);
    }

    /**
     * Soft deletes a brand and all its contacts.
     *
     * @param id the brand ID
     * @param accountId the account ID for multi-tenancy
     */
    @Transactional
    public void deleteBrand(Long id, Long accountId) {
        logger.info("Soft deleting brand {} for account {}", id, accountId);

        // Verify brand exists and belongs to account
        if (brandRepository.findByIdAndAccountId(id, accountId).isEmpty()) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, 
                "Brand not found with ID: " + id);
        }

        // Soft delete brand
        boolean deleted = brandRepository.softDeleteByIdAndAccountId(id, accountId);
        if (!deleted) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, 
                "Brand not found with ID: " + id);
        }

        logger.info("Successfully soft deleted brand {} for account {}", id, accountId);
    }

    /**
     * Retrieves contacts for a specific brand.
     *
     * @param brandId the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return list of brand contact responses
     */
    @Transactional(readOnly = true)
    public List<BrandContactResponse> getBrandContacts(Long brandId, Long accountId) {
        logger.debug("Retrieving contacts for brand {} for account {}", brandId, accountId);

        // Verify brand exists and belongs to account
        brandRepository.findByIdAndAccountId(brandId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    "Brand not found with ID: " + brandId));

        return getBrandContactResponses(brandId, accountId);
    }

    /**
     * Converts a list of Brand entities to response DTOs with bulk loading to avoid N+1 queries.
     */
    private List<BrandResponse> convertToBrandResponsesBulk(List<Brand> brands, Long accountId) {
        if (brands.isEmpty()) {
            return List.of();
        }

        // Bulk load contacts for all brands to avoid N+1 queries
        List<Long> brandIds = brands.stream()
                .map(Brand::getId)
                .toList();

        Map<Long, List<BrandContact>> contactMap = brandContactRepository.findContactsByBrandIds(brandIds, accountId);

        // Convert to response DTOs
        return brands.stream()
                .map(brand -> {
                    List<BrandContact> contacts = contactMap.getOrDefault(brand.getId(), List.of());
                    List<BrandContactResponse> contactResponses = contacts.stream()
                            .map(brandContactConverter::toResponse)
                            .toList();
                    return brandConverter.toResponse(brand, contactResponses);
                })
                .toList();
    }

    /**
     * Helper method to get brand contact responses.
     * This method has N+1 query issue - use convertToBrandResponsesBulk for lists.
     */
    private List<BrandContactResponse> getBrandContactResponses(Long brandId, Long accountId) {
        List<BrandContact> contacts = brandContactRepository.findByBrandIdAndAccountId(brandId, accountId);
        return contacts.stream()
                .map(brandContactConverter::toResponse)
                .collect(Collectors.toList());
    }

    /**
     * Helper method to create brand contacts.
     */
    private List<BrandContactResponse> createBrandContacts(Long brandId, Long accountId,
                                                         List<BrandContactRequest> contactRequests) {
        List<BrandContactResponse> responses = new ArrayList<>();

        for (BrandContactRequest contactRequest : contactRequests) {
            validateContactEmailUnique(contactRequest.getEmail(), brandId, accountId, null);

            BrandContact contact = brandContactConverter.toBrandContact(contactRequest, brandId, accountId);
            brandContactRepository.insert(contact);
            responses.add(brandContactConverter.toResponse(contact));
        }

        return responses;
    }

    /**
     * Validates that a contact email is unique within a brand.
     */
    private void validateContactEmailUnique(String email, Long brandId, Long accountId, Long excludeId) {
        if (brandContactRepository.existsByEmailAndBrandIdAndAccountId(email, brandId, accountId, excludeId)) {
            throw new ConflictException(ErrorCode.RESOURCE_ALREADY_EXISTS,
                "Contact with email '" + email + "' already exists for this brand");
        }
    }

    /**
     * Helper method to update brand contacts.
     */
    private List<BrandContactResponse> updateBrandContacts(
            Long brandId, Long accountId, List<BrandContactRequest> contactRequests) {

        Map<Long, BrandContact> existingMap = getExistingContactsMap(brandId, accountId);

        if (contactRequests == null || contactRequests.isEmpty()) {
            deleteAllContacts(existingMap, brandId, accountId);
            return List.of();
        }

        List<BrandContactResponse> responses = processContactRequests(
                contactRequests, existingMap, brandId, accountId);

        deleteRemainingContacts(existingMap, brandId, accountId);

        return responses;
    }

    /**
     * Gets existing contacts mapped by ID.
     */
    private Map<Long, BrandContact> getExistingContactsMap(Long brandId, Long accountId) {
        List<BrandContact> existingContacts = brandContactRepository
                .findByBrandIdAndAccountId(brandId, accountId);
        return existingContacts.stream()
                .collect(Collectors.toMap(BrandContact::getId, contact -> contact));
    }

    /**
     * Deletes all contacts for a brand.
     */
    private void deleteAllContacts(Map<Long, BrandContact> existingMap, Long brandId, Long accountId) {
        for (BrandContact contact : existingMap.values()) {
            brandContactRepository.deleteByIdAndBrandIdAndAccountId(contact.getId(), brandId, accountId);
        }
    }

    /**
     * Processes contact requests (create or update).
     */
    private List<BrandContactResponse> processContactRequests(
            List<BrandContactRequest> contactRequests, Map<Long, BrandContact> existingMap,
            Long brandId, Long accountId) {

        List<BrandContactResponse> responses = new ArrayList<>();

        for (BrandContactRequest request : contactRequests) {
            Long contactId = request.getId();
            boolean isUpdate = contactId != null && existingMap.containsKey(contactId);

            validateContactEmailUnique(request.getEmail(), brandId, accountId,
                    isUpdate ? contactId : null);

            if (isUpdate) {
                BrandContactResponse response = updateExistingContact(request, existingMap, contactId);
                responses.add(response);
                existingMap.remove(contactId);
            } else {
                BrandContactResponse response = createNewContact(request, brandId, accountId);
                responses.add(response);
            }
        }

        return responses;
    }

    /**
     * Updates an existing contact.
     */
    private BrandContactResponse updateExistingContact(BrandContactRequest request,
                                                     Map<Long, BrandContact> existingMap, Long contactId) {
        BrandContact existing = existingMap.get(contactId);
        BrandContact updated = brandContactConverter.updateBrandContact(existing, request);
        brandContactRepository.update(updated);
        return brandContactConverter.toResponse(updated);
    }

    /**
     * Creates a new contact.
     */
    private BrandContactResponse createNewContact(BrandContactRequest request, Long brandId, Long accountId) {
        BrandContact newContact = brandContactConverter.toBrandContact(request, brandId, accountId);
        brandContactRepository.insert(newContact);
        return brandContactConverter.toResponse(newContact);
    }

    /**
     * Deletes contacts that were not included in the update request.
     */
    private void deleteRemainingContacts(Map<Long, BrandContact> existingMap, Long brandId, Long accountId) {
        for (BrandContact toDelete : existingMap.values()) {
            brandContactRepository.deleteByIdAndBrandIdAndAccountId(toDelete.getId(), brandId, accountId);
        }
    }
}
