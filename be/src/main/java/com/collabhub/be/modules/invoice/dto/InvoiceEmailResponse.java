package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

/**
 * Response DTO for invoice email sending operations.
 * Contains information about the success status and delivery details.
 */
public class InvoiceEmailResponse {

    @NotNull
    private Boolean success;

    @NotNull
    private String message;

    @JsonProperty("recipients_count")
    private Integer recipientsCount;

    @JsonProperty("sent_at")
    private java.time.LocalDateTime sentAt;

    public InvoiceEmailResponse() {
        this.sentAt = java.time.LocalDateTime.now();
    }

    public InvoiceEmailResponse(Boolean success, String message, Integer recipientsCount) {
        this.success = success;
        this.message = message;
        this.recipientsCount = recipientsCount;
        this.sentAt = java.time.LocalDateTime.now();
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getRecipientsCount() {
        return recipientsCount;
    }

    public void setRecipientsCount(Integer recipientsCount) {
        this.recipientsCount = recipientsCount;
    }

    public java.time.LocalDateTime getSentAt() {
        return sentAt;
    }

    public void setSentAt(java.time.LocalDateTime sentAt) {
        this.sentAt = sentAt;
    }

    @Override
    public String toString() {
        return "InvoiceEmailResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", recipientsCount=" + recipientsCount +
                ", sentAt=" + sentAt +
                '}';
    }
}
