package com.collabhub.be.modules.posts.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Arrays;
import java.util.List;

/**
 * Enum representing available post filters.
 * Used for filtering posts in collaboration hubs based on user relationship to posts.
 */
@Schema(enumAsRef = true, description = "Available post filter options")
public enum PostFilter {
    ALL("all"),
    ASSIGNED_TO_ME("assigned_to_me"),
    CREATED_BY_ME("created_by_me"),
    NEEDS_REVIEW("needs_review"),
    MY_PENDING("my_pending"),
    MY_APPROVED("my_approved"),
    MY_REWORK("my_rework"),
    REVIEWED_BY_ME("reviewed_by_me");

    private final String value;

    PostFilter(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Returns all available filter values as strings.
     */
    public static List<String> getAllValues() {
        return Arrays.stream(PostFilter.values())
                .map(PostFilter::getValue)
                .toList();
    }

    /**
     * Creates a PostFilter from a string value.
     */
    public static PostFilter fromString(String value) {
        if (value == null) {
            return ALL; // Default fallback
        }
        
        for (PostFilter filter : PostFilter.values()) {
            if (filter.getValue().equalsIgnoreCase(value)) {
                return filter;
            }
        }
        
        return ALL; // Default fallback for unknown filters
    }

    @Override
    public String toString() {
        return value;
    }
}
