package com.collabhub.be.modules.collaborationhub.converter;

import com.collabhub.be.modules.collaborationhub.dto.HubParticipantResponse;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Converter class for mapping between HubParticipant DTOs and jOOQ POJOs.
 * Handles conversion between different representations of hub participant data.
 */
@Component
public class HubParticipantConverter {

    /**
     * Creates a new hub participant POJO for an internal user.
     *
     * @param hubId the hub ID
     * @param userId the user ID
     * @param email the user email
     * @param role the participant role
     * @return the jOOQ POJO ready for database insertion
     */
    public HubParticipant createInternalParticipant(Long hubId, Long userId, String email, String userDisplayName, HubParticipantRole role) {
        HubParticipant participant = new HubParticipant();
        participant.setHubId(hubId);
        participant.setUserId(userId);
        participant.setEmail(email);
        participant.setName(userDisplayName);
        participant.setRole(role);
        participant.setIsExternal(false);
        participant.setInvitedAt(LocalDateTime.now());
        participant.setJoinedAt(LocalDateTime.now()); // Internal users join immediately
        participant.setCreatedAt(LocalDateTime.now());
        
        return participant;
    }

    /**
     * Converts a jOOQ POJO to a response DTO for API responses.
     *
     * @param participant the jOOQ POJO
     * @param userName the user name (from User table or external name)
     * @return the response DTO
     */
    public HubParticipantResponse toResponse(HubParticipant participant, String userName) {
        if (participant == null) {
            return null;
        }

        String status = determineParticipantStatus(participant);
        return new HubParticipantResponse(
                participant.getId(),
                participant.getUserId(),
                participant.getEmail(),
                userName,
                participant.getRole(),
                participant.getIsExternal(),
                participant.getInvitedAt(),
                participant.getJoinedAt(),
                status
        );
    }

    /**
     * Determines participant status based on their state.
     *
     * @param participant the participant
     * @return status string ("active", "pending", "removed")
     */
    private String determineParticipantStatus(HubParticipant participant) {
        if (participant.getRemovedAt() != null) {
            return "removed";
        }
        if (participant.getIsExternal() && participant.getJoinedAt() == null) {
            return "pending";
        }
        return "active";
    }

}
