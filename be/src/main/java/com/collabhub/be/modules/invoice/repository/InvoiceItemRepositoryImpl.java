package com.collabhub.be.modules.invoice.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.InvoiceItemDao;
import org.jooq.generated.tables.pojos.InvoiceItem;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.jooq.generated.Tables.INVOICE_ITEM;

/**
 * Repository for InvoiceItem entity using jOOQ for database operations.
 * Provides type-safe database operations for invoice line items.
 */
@Repository
public class InvoiceItemRepositoryImpl extends InvoiceItemDao {

    private final DSLContext dsl;

    public InvoiceItemRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all invoice items for a specific invoice.
     *
     * @param invoiceId the invoice ID
     * @return list of invoice items ordered by sort_order
     */
    public List<InvoiceItem> findByInvoiceId(Long invoiceId) {
        return dsl.select()
                .from(INVOICE_ITEM)
                .where(INVOICE_ITEM.INVOICE_ID.eq(invoiceId))
                .orderBy(INVOICE_ITEM.SORT_ORDER.asc(), INVOICE_ITEM.ID.asc())
                .fetchInto(InvoiceItem.class);
    }

    /**
     * Deletes all invoice items for a specific invoice.
     * Used when updating invoice items - delete all and re-insert.
     *
     * @param invoiceId the invoice ID
     * @return number of deleted items
     */
    public int deleteByInvoiceId(Long invoiceId) {
        return dsl.deleteFrom(INVOICE_ITEM)
                .where(INVOICE_ITEM.INVOICE_ID.eq(invoiceId))
                .execute();
    }

    /**
     * Batch inserts invoice items.
     *
     * @param items the list of invoice items to insert
     */
    @Transactional
    public void batchInsert(List<InvoiceItem> items) {
        if (items == null || items.isEmpty()) {
            return;
        }

        this.insert(items);
    }
}
