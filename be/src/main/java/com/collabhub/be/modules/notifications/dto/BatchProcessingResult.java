package com.collabhub.be.modules.notifications.dto;

/**
 * Enumeration representing the outcome of batch notification processing operations.
 * 
 * <p>This enum provides strongly-typed results for batch processing operations,
 * enabling clear error handling and retry logic based on the specific type of
 * outcome encountered during processing.</p>
 * 
 * <h3>Usage in Batch Processing:</h3>
 * <pre>
 * BatchProcessingResult result = processBatch(notifications);
 * switch (result) {
 *     case SUCCESS -> logSuccess(notifications.size());
 *     case PARTIAL_SUCCESS -> handlePartialFailure(result);
 *     case TEMPORARY_FAILURE -> scheduleRetry(notifications);
 *     case PERMANENT_FAILURE -> moveToDeadLetter(notifications);
 * }
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public enum BatchProcessingResult {
    
    /**
     * All notifications in the batch were successfully processed and delivered.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>100% success rate for the batch</li>
     *   <li>All emails were sent successfully</li>
     *   <li>No retry required</li>
     *   <li>Notifications can be marked as SENT</li>
     * </ul>
     * 
     * <p><strong>Next Actions:</strong> Mark all notifications as SENT, update metrics</p>
     */
    SUCCESS("All notifications processed successfully"),
    
    /**
     * Some notifications were processed successfully, others failed.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>Mixed success/failure within the batch</li>
     *   <li>Successful notifications should be marked as SENT</li>
     *   <li>Failed notifications should be evaluated for retry</li>
     *   <li>Requires individual notification status handling</li>
     * </ul>
     * 
     * <p><strong>Next Actions:</strong> Process individual results, retry failed notifications</p>
     */
    PARTIAL_SUCCESS("Some notifications processed successfully, others failed"),
    
    /**
     * Batch processing failed due to temporary issues that may resolve with retry.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>Network connectivity issues</li>
     *   <li>Email service temporary unavailability</li>
     *   <li>Rate limiting from external services</li>
     *   <li>Database connection timeouts</li>
     * </ul>
     * 
     * <p><strong>Next Actions:</strong> Schedule retry with exponential backoff</p>
     */
    TEMPORARY_FAILURE("Processing failed due to temporary issues, retry recommended"),
    
    /**
     * Batch processing failed due to permanent issues that won't resolve with retry.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>Invalid email addresses</li>
     *   <li>Authentication failures</li>
     *   <li>Configuration errors</li>
     *   <li>Data validation failures</li>
     * </ul>
     * 
     * <p><strong>Next Actions:</strong> Log error, move to dead letter queue, alert administrators</p>
     */
    PERMANENT_FAILURE("Processing failed due to permanent issues, retry not recommended"),
    
    /**
     * Batch processing was skipped due to business logic conditions.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>User preferences disabled email notifications</li>
     *   <li>Batch window not yet reached</li>
     *   <li>Duplicate processing prevention</li>
     *   <li>System maintenance mode</li>
     * </ul>
     * 
     * <p><strong>Next Actions:</strong> Log skip reason, no retry needed</p>
     */
    SKIPPED("Processing was skipped due to business logic conditions"),
    
    /**
     * Batch processing encountered an unexpected error requiring investigation.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>Unexpected exceptions or system errors</li>
     *   <li>Unknown failure modes</li>
     *   <li>Requires manual investigation</li>
     *   <li>May indicate system bugs or infrastructure issues</li>
     * </ul>
     * 
     * <p><strong>Next Actions:</strong> Alert administrators, investigate root cause, limited retry</p>
     */
    UNKNOWN_ERROR("Processing encountered an unexpected error requiring investigation");
    
    private final String description;
    
    /**
     * Constructs a BatchProcessingResult with the specified description.
     * 
     * @param description human-readable description of this result type
     */
    BatchProcessingResult(String description) {
        this.description = description;
    }
    
    /**
     * Returns a human-readable description of this processing result.
     * 
     * @return descriptive text explaining what this result means
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Checks if this result indicates a successful outcome.
     * 
     * @return true if the result is SUCCESS, false otherwise
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * Checks if this result indicates a complete failure.
     * 
     * @return true if the result is TEMPORARY_FAILURE, PERMANENT_FAILURE, or UNKNOWN_ERROR
     */
    public boolean isFailure() {
        return this == TEMPORARY_FAILURE || this == PERMANENT_FAILURE || this == UNKNOWN_ERROR;
    }
    
    /**
     * Checks if this result indicates a partial success with some failures.
     * 
     * @return true if the result is PARTIAL_SUCCESS, false otherwise
     */
    public boolean isPartialSuccess() {
        return this == PARTIAL_SUCCESS;
    }
    
    /**
     * Checks if this result indicates the operation was skipped.
     * 
     * @return true if the result is SKIPPED, false otherwise
     */
    public boolean isSkipped() {
        return this == SKIPPED;
    }
    
    /**
     * Checks if this result indicates a retry should be attempted.
     * 
     * <p>Retry is recommended for temporary failures and unknown errors (with limits),
     * but not for permanent failures, skipped operations, or successful operations.</p>
     * 
     * @return true if retry is recommended, false otherwise
     */
    public boolean shouldRetry() {
        return this == TEMPORARY_FAILURE || this == UNKNOWN_ERROR;
    }
    
    /**
     * Checks if this result requires administrative attention.
     * 
     * @return true if administrators should be alerted, false otherwise
     */
    public boolean requiresAdminAttention() {
        return this == PERMANENT_FAILURE || this == UNKNOWN_ERROR;
    }
    
    /**
     * Gets the appropriate log level for this result type.
     * 
     * @return recommended log level as a string
     */
    public String getLogLevel() {
        return switch (this) {
            case SUCCESS -> "INFO";
            case PARTIAL_SUCCESS -> "WARN";
            case TEMPORARY_FAILURE -> "WARN";
            case PERMANENT_FAILURE -> "ERROR";
            case SKIPPED -> "DEBUG";
            case UNKNOWN_ERROR -> "ERROR";
        };
    }
}
