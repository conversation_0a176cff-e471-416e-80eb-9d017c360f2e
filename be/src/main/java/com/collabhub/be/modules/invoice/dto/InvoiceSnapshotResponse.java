package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

/**
 * Response DTOs for invoice snapshots.
 * These provide type-safe access to snapshot data with original entity IDs.
 */
public class InvoiceSnapshotResponse {

    /**
     * Response DTO for issuer snapshot data.
     */
    public static class IssuerSnapshotResponse {
        
        @NotNull
        private Long id;
        
        @NotNull
        private String name;
        
        private String address;
        
        private String city;
        
        @JsonProperty("postal_code")
        private String postalCode;
        
        private String country;
        
        @JsonProperty("vat_number")
        private String vatNumber;
        
        @JsonProperty("registration_number")
        private String registrationNumber;
        
        private String phone;
        
        private String email;
        
        private String website;

        public IssuerSnapshotResponse() {
        }

        public IssuerSnapshotResponse(Long id, String name, String address, String city, String postalCode,
                                    String country, String vatNumber, String registrationNumber, 
                                    String phone, String email, String website) {
            this.id = id;
            this.name = name;
            this.address = address;
            this.city = city;
            this.postalCode = postalCode;
            this.country = country;
            this.vatNumber = vatNumber;
            this.registrationNumber = registrationNumber;
            this.phone = phone;
            this.email = email;
            this.website = website;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        
        public String getPostalCode() { return postalCode; }
        public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
        
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
        
        public String getVatNumber() { return vatNumber; }
        public void setVatNumber(String vatNumber) { this.vatNumber = vatNumber; }
        
        public String getRegistrationNumber() { return registrationNumber; }
        public void setRegistrationNumber(String registrationNumber) { this.registrationNumber = registrationNumber; }
        
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getWebsite() { return website; }
        public void setWebsite(String website) { this.website = website; }
    }

    /**
     * Response DTO for recipient snapshot data.
     */
    public static class RecipientSnapshotResponse {
        
        @NotNull
        private Long id;
        
        @NotNull
        private String name;
        
        private String address;
        
        private String city;
        
        @JsonProperty("postal_code")
        private String postalCode;
        
        private String country;

        @JsonProperty("vat_number")
        private String vatNumber;

        @JsonProperty("registration_number")
        private String registrationNumber;

        private String email;

        private String phone;

        public RecipientSnapshotResponse() {
        }

        public RecipientSnapshotResponse(Long id, String name, String address, String city, String postalCode,
                                       String country, String vatNumber, String registrationNumber,
                                       String email, String phone) {
            this.id = id;
            this.name = name;
            this.address = address;
            this.city = city;
            this.postalCode = postalCode;
            this.country = country;
            this.vatNumber = vatNumber;
            this.registrationNumber = registrationNumber;
            this.email = email;
            this.phone = phone;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        
        public String getPostalCode() { return postalCode; }
        public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
        
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }

        public String getVatNumber() { return vatNumber; }
        public void setVatNumber(String vatNumber) { this.vatNumber = vatNumber; }

        public String getRegistrationNumber() { return registrationNumber; }
        public void setRegistrationNumber(String registrationNumber) { this.registrationNumber = registrationNumber; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
    }

    /**
     * Response DTO for bank details snapshot data.
     */
    public static class BankDetailsSnapshotResponse {
        
        @NotNull
        private Long id;
        
        @NotNull
        @JsonProperty("account_name")
        private String accountName;
        
        @NotNull
        @JsonProperty("bank_name")
        private String bankName;
        
        @NotNull
        private String iban;
        
        private String bic;

        public BankDetailsSnapshotResponse() {
        }

        public BankDetailsSnapshotResponse(Long id, String accountName, String bankName, String iban, String bic) {
            this.id = id;
            this.accountName = accountName;
            this.bankName = bankName;
            this.iban = iban;
            this.bic = bic;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getAccountName() { return accountName; }
        public void setAccountName(String accountName) { this.accountName = accountName; }
        
        public String getBankName() { return bankName; }
        public void setBankName(String bankName) { this.bankName = bankName; }
        
        public String getIban() { return iban; }
        public void setIban(String iban) { this.iban = iban; }
        
        public String getBic() { return bic; }
        public void setBic(String bic) { this.bic = bic; }
    }
}
