package com.collabhub.be.modules.auth.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.UnauthorizedException;
import com.collabhub.be.modules.auth.constants.JwtClaims;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.model.Role;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Service;

/**
 * Extracts and validates JWT claims into a structured {@link UserContext}.
 * Handles both internal users (identified by user ID) and external participants.
 */
@Service
public class JwtClaimsService {

    private static final Logger logger = LoggerFactory.getLogger(JwtClaimsService.class);

    // Constants
    private static final String NO_AUTHENTICATION_MESSAGE = "No valid authentication found in security context";
    private static final String INVALID_AUTHENTICATION_TYPE_MESSAGE = "Invalid authentication type, JWT required";

    // Log Messages
    private static final String EXTRACTING_CURRENT_USER_LOG = "Extracting current user from security context";
    private static final String CURRENT_USER_EXTRACTED_LOG = "Current user extracted: {}";

    /**
     * Gets the current authenticated user from the security context.
     * Extracts JWT from SecurityContext and converts to UserContext.
     *
     * @return the current user context
     * @throws UnauthorizedException if no valid authentication found
     */
    public UserContext getCurrentUser() {
        logger.debug(EXTRACTING_CURRENT_USER_LOG);

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new UnauthorizedException(ErrorCode.AUTHENTICATION_REQUIRED, NO_AUTHENTICATION_MESSAGE);
        }

        if (authentication instanceof JwtAuthenticationToken jwtAuth) {
            Jwt jwt = jwtAuth.getToken();
            UserContext userContext = extractUserContext(jwt);
            logger.debug(CURRENT_USER_EXTRACTED_LOG, userContext.getEmail());
            return userContext;
        }

        throw new UnauthorizedException(ErrorCode.AUTHENTICATION_REQUIRED, INVALID_AUTHENTICATION_TYPE_MESSAGE);
    }
    /**
     * Extracts {@link UserContext} from the provided {@link Jwt}.
     *
     * @param jwt the JWT token to extract from
     * @return parsed and validated UserContext
     * @throws BadRequestException if required claims are missing or malformed
     */
    public UserContext extractUserContext(Jwt jwt) {
        if (jwt == null) {
            throw new BadRequestException(ErrorCode.AUTHENTICATION_FAILED, "JWT token is required");
        }

        String subject = getRequiredClaim(jwt, JwtClaims.SUBJECT, "subject");
        boolean isExternal = subject.startsWith(JwtClaims.EXTERNAL_USER_SUBJECT_PREFIX);

        String email = getRequiredClaim(jwt, JwtClaims.EMAIL, "email");
        String displayName = getRequiredClaim(jwt, JwtClaims.DISPLAY_NAME, "display name");
        String roleString = getRequiredClaim(jwt, JwtClaims.ROLE, "role");

        Role role = parseRole(roleString);

        if (isExternal) {
            logger.debug("Extracted external user context for email: {}", email);
            return new UserContext(email, displayName, role);
        }

        long userId = parseUserId(subject);
        Long accountId = jwt.getClaim(JwtClaims.ACCOUNT_ID);
        if (accountId == null) {
            throw new BadRequestException(ErrorCode.TOKEN_INVALID, "Missing or invalid account ID claim for internal user");
        }

        logger.debug("Extracted internal user context: userId={}, accountId={}", userId, accountId);
        return new UserContext(userId, email, displayName, role, accountId);
    }

    private String getRequiredClaim(Jwt jwt, String claimName, String humanReadableName) {
        String value = jwt.getClaimAsString(claimName);
        if (value == null || value.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.TOKEN_INVALID, "Missing or invalid " + humanReadableName + " claim");
        }
        return value;
    }

    private Role parseRole(String roleClaim) {
        try {
            return Role.valueOf(roleClaim);
        } catch (IllegalArgumentException e) {
            throw new BadRequestException(ErrorCode.TOKEN_INVALID, "Invalid role: " + roleClaim);
        }
    }

    private long parseUserId(String subject) {
        try {
            return Long.parseLong(subject);
        } catch (NumberFormatException e) {
            logger.warn("Invalid user ID format in subject: {}", subject);
            throw new BadRequestException(ErrorCode.TOKEN_INVALID, "Invalid user ID format in token");
        }
    }
}
