package com.collabhub.be.modules.posts.service;

import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.converter.PostCommentConverter;
import com.collabhub.be.modules.posts.dto.PostCommentCreateRequest;
import com.collabhub.be.modules.posts.util.PostParticipantUtil;
import com.collabhub.be.modules.chat.service.MentionService;
import com.collabhub.be.modules.chat.dto.MentionDto;
import com.collabhub.be.modules.posts.dto.PostCommentUpdateRequest;
import com.collabhub.be.modules.posts.dto.PostCommentResponse;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.posts.dto.PostCommentListResponse;
import com.collabhub.be.modules.posts.repository.PostCommentRepositoryImpl;
import com.collabhub.be.modules.posts.repository.PostRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.service.CollabHubPermissionService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.service.NotificationDispatcherService;
import com.collabhub.be.modules.notifications.service.NotificationTranslationService;
import com.collabhub.be.modules.notifications.util.NotificationRecipientUtils;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ForbiddenException;
import org.jooq.generated.tables.pojos.PostComment;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for managing post comments.
 * Handles CRUD operations for comments with permission-based access control.
 * Supports both internal users and external participants without account IDs.
 */
@Service
public class PostCommentService {

    private static final Logger logger = LoggerFactory.getLogger(PostCommentService.class);

    private final PostCommentRepositoryImpl commentRepository;
    private final PostRepositoryImpl postRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final PostCommentConverter commentConverter;
    private final PostPermissionService postPermissionService;
    private final CollabHubPermissionService collabHubPermissionService;
    private final PostParticipantUtil participantUtil;
    private final MentionService mentionService;
    private final JwtClaimsService jwtClaimsService;
    private final NotificationDispatcherService notificationDispatcherService;
    private final NotificationTranslationService notificationTranslationService;

    public PostCommentService(PostCommentRepositoryImpl commentRepository,
                             PostRepositoryImpl postRepository,
                             HubParticipantRepositoryImpl participantRepository,
                             CollaborationHubRepositoryImpl hubRepository,
                             PostCommentConverter commentConverter,
                             PostPermissionService postPermissionService,
                             CollabHubPermissionService collabHubPermissionService,
                             PostParticipantUtil participantUtil,
                             MentionService mentionService,
                             JwtClaimsService jwtClaimsService,
                             NotificationDispatcherService notificationDispatcherService,
                             NotificationTranslationService notificationTranslationService) {
        this.commentRepository = commentRepository;
        this.postRepository = postRepository;
        this.participantRepository = participantRepository;
        this.hubRepository = hubRepository;
        this.commentConverter = commentConverter;
        this.postPermissionService = postPermissionService;
        this.collabHubPermissionService = collabHubPermissionService;
        this.participantUtil = participantUtil;
        this.mentionService = mentionService;
        this.jwtClaimsService = jwtClaimsService;
        this.notificationDispatcherService = notificationDispatcherService;
        this.notificationTranslationService = notificationTranslationService;
    }

    /**
     * Creates a new comment on a post.
     * Works for both internal users and external participants.
     */
    @Transactional
    public PostCommentResponse createComment(Long postId, PostCommentCreateRequest request) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Creating comment on post {} by user {}", postId, userContext.getUserId());

        Post post = findPostAndValidateAccess(postId);
        validateCanCommentOnPost(post);
        HubParticipant participant = getParticipantForUser(post.getHubId(), userContext);

        PostComment comment = commentConverter.toPostComment(request, postId, participant.getId());
        commentRepository.insert(comment);

        // Process mentions in the comment content
        List<MentionDto> mentions = mentionService.parseMentions(request.getContent(), post.getHubId());
        logger.debug("Processed {} mentions in comment {}", mentions.size(), comment.getId());

        // Send notifications
        sendCommentNotifications(post, participant, comment.getId(), request.getContent(), mentions);

        logger.debug("Successfully created comment {} on post {}", comment.getId(), postId);
        return buildCommentResponseWithMentions(comment, participant, userContext, mentions);
    }

    /**
     * Updates an existing comment.
     * Works for both internal users and external participants.
     */
    @Transactional
    public PostCommentResponse updateComment(Long commentId, PostCommentUpdateRequest request) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Updating comment {} by user {}", commentId, userContext.getUserId());

        PostComment comment = findCommentAndValidateAccess(commentId);
        Post post = findPostAndValidateAccess(comment.getPostId());
        validateCanEditComment(comment, post, userContext);

        boolean updated = commentRepository.updateCommentContent(commentId, request.getContent());
        if (!updated) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.COMMENT_NOT_FOUND_MESSAGE);
        }

        // Fetch updated comment
        Long accountId = extractAccountIdFromHub(post.getHubId());
        comment = commentRepository.findByIdWithAccountValidation(commentId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.COMMENT_NOT_FOUND_MESSAGE));

        HubParticipant participant = participantRepository.findById(comment.getParticipantId());

        // Process mentions in the updated comment content
        List<MentionDto> mentions = mentionService.parseMentions(request.getContent(), post.getHubId());
        logger.debug("Processed {} mentions in updated comment {}", mentions.size(), commentId);

        logger.debug("Successfully updated comment {}", commentId);
        return buildCommentResponseWithMentions(comment, participant, userContext, mentions);
    }

    /**
     * Deletes a comment.
     * Works for both internal users and external participants.
     */
    @Transactional
    public void deleteComment(Long commentId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Deleting comment {} by user {}", commentId, userContext.getUserId());

        PostComment comment = findCommentAndValidateAccess(commentId);
        Post post = findPostAndValidateAccess(comment.getPostId());
        validateCanDeleteComment(comment, post, userContext);

        boolean deleted = commentRepository.deleteComment(commentId);
        if (!deleted) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.COMMENT_NOT_FOUND_MESSAGE);
        }

        logger.debug("Successfully deleted comment {}", commentId);
    }

    /**
     * Retrieves a specific comment by ID.
     * Works for both internal users and external participants.
     */
    @Transactional(readOnly = true)
    public PostCommentResponse getComment(Long commentId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Retrieving comment {} for user {}", commentId, userContext.getUserId());

        PostComment comment = findCommentAndValidateAccess(commentId);
        Post post = findPostAndValidateAccess(comment.getPostId());
        postPermissionService.validateCanViewPost(post.getId());

        HubParticipant participant = participantRepository.findById(comment.getParticipantId());

        // Process mentions in the comment content
        List<MentionDto> mentions = mentionService.parseMentions(comment.getContent(), post.getHubId());

        return buildCommentResponseWithMentions(comment, participant, userContext, mentions);
    }

    /**
     * Retrieves comments for a post with pagination.
     * Uses 0-based pagination and works for both internal users and external participants.
     */
    @Transactional(readOnly = true)
    public PostCommentListResponse getCommentsForPost(Long postId, PageRequest pageRequest) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Retrieving comments for post {} (page {}, size {}) for user {}",
                    postId, pageRequest.getPage(), pageRequest.getSize(), userContext.getUserId());

        Post post = findPostAndValidateAccess(postId);
        postPermissionService.validateCanViewPost(postId);

        Long accountId = extractAccountIdFromHub(post.getHubId());
        List<PostComment> comments = commentRepository.findByPostIdWithPagination(
                postId, accountId, pageRequest.getOffset(), pageRequest.getSize());
        long totalCount = commentRepository.countByPostId(postId, accountId);

        // Bulk load participant information to avoid N+1 queries
        Map<Long, HubParticipant> participantMap = loadParticipantsForComments(comments);

        List<PostCommentListResponse.PostCommentItem> commentItems = comments.stream()
                .map(comment -> {
                    HubParticipant participant = participantMap.get(comment.getParticipantId());
                    PostCommentListResponse.CommentAuthor author = participantUtil.createListCommentAuthor(participant);

                    boolean canEdit = canUserEditComment(comment, participant, userContext);
                    boolean canDelete = canUserDeleteComment(comment, participant, userContext);

                    // Process mentions for each comment
                    List<MentionDto> mentions = mentionService.parseMentions(comment.getContent(), post.getHubId());

                    return commentConverter.toPostCommentListItemWithMentions(comment, author, canEdit, canDelete, mentions);
                })
                .collect(Collectors.toList());

        return commentConverter.createCommentListResponse(commentItems, totalCount, pageRequest.getSize(), pageRequest.getPage());
    }

    // Private helper methods

    /**
     * Finds a post and validates that the current user can access it.
     * Works for both internal users and external participants.
     */
    private Post findPostAndValidateAccess(Long postId) {
        // First, try to find the post without account validation
        Post post = postRepository.findById(postId);
        if (post == null) {
            logger.warn("Post not found: ID={}", postId);
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE);
        }

        // Validate that user can access the hub containing this post
        collabHubPermissionService.validateCanParticipantAccessHubContent(post.getHubId());

        return post;
    }

    /**
     * Finds a comment and validates that the current user can access it.
     * Works for both internal users and external participants.
     */
    private PostComment findCommentAndValidateAccess(Long commentId) {
        // First, find the comment
        PostComment comment = commentRepository.findById(commentId);
        if (comment == null) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.COMMENT_NOT_FOUND_MESSAGE);
        }

        // Then validate access to the post containing this comment
        findPostAndValidateAccess(comment.getPostId());

        return comment;
    }

    private void validateCanCommentOnPost(Post post) {
        // Validate hub access first
        collabHubPermissionService.validateCanParticipantAccessHubContent(post.getHubId());
        // Then validate post-specific comment permissions
        if (!postPermissionService.canUserCommentOnPost(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, "Cannot comment on this post");
        }
    }

    private void validateCanEditComment(PostComment comment, Post post, UserContext userContext) {
        // For now, only comment author can edit (implement proper logic later)
        HubParticipant participant = participantRepository.findByHubIdAndEmail(post.getHubId(), userContext.getEmail());
        if (participant == null || !comment.getParticipantId().equals(participant.getId())) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, "Cannot edit this comment");
        }
    }

    private void validateCanDeleteComment(PostComment comment, Post post, UserContext userContext) {
        // For now, only comment author can delete (implement proper logic later)
        HubParticipant participant = participantRepository.findByHubIdAndEmail(post.getHubId(), userContext.getEmail());
        if (participant == null || !comment.getParticipantId().equals(participant.getId())) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, "Cannot delete this comment");
        }
    }

    private HubParticipant getParticipantForUser(Long hubId, UserContext userContext) {
        return participantRepository.findByHubIdAndEmail(hubId, userContext.getEmail());
    }

    /**
     * Checks if user can edit a comment (only comment author can edit).
     */
    private boolean canUserEditComment(PostComment comment, HubParticipant participant, UserContext userContext) {
        return participant != null && comment.getParticipantId().equals(participant.getId());
    }

    /**
     * Checks if user can delete a comment (only comment author can delete).
     */
    private boolean canUserDeleteComment(PostComment comment, HubParticipant participant, UserContext userContext) {
        return participant != null && comment.getParticipantId().equals(participant.getId());
    }

    private PostCommentResponse buildCommentResponse(PostComment comment, HubParticipant participant, UserContext userContext) {
        PostCommentResponse.CommentAuthor author = participantUtil.createCommentAuthor(participant);

        boolean canEdit = canUserEditComment(comment, participant, userContext);
        boolean canDelete = canUserDeleteComment(comment, participant, userContext);
        PostCommentResponse.CommentPermissions permissions = commentConverter.createCommentPermissions(canEdit, canDelete);

        return commentConverter.toPostCommentResponse(comment, author, permissions);
    }

    private PostCommentResponse buildCommentResponseWithMentions(PostComment comment, HubParticipant participant,
                                                               UserContext userContext, List<MentionDto> mentions) {
        PostCommentResponse.CommentAuthor author = participantUtil.createCommentAuthor(participant);

        boolean canEdit = canUserEditComment(comment, participant, userContext);
        boolean canDelete = canUserDeleteComment(comment, participant, userContext);
        PostCommentResponse.CommentPermissions permissions = commentConverter.createCommentPermissions(canEdit, canDelete);

        return commentConverter.toPostCommentResponseWithMentions(comment, author, permissions, mentions);
    }

    private Map<Long, HubParticipant> loadParticipantsForComments(List<PostComment> comments) {
        List<Long> participantIds = comments.stream()
                .map(PostComment::getParticipantId)
                .distinct()
                .collect(Collectors.toList());

        return participantUtil.bulkLoadHubParticipants(participantIds);
    }

    /**
     * Sends notifications for comment creation (comment added and mentions).
     * Uses centralized notification system with internationalization support.
     */
    private void sendCommentNotifications(Post post, HubParticipant commenter, Long commentId, String commentContent, List<MentionDto> mentions) {
        try {
            // Send comment mention notifications (both internal and external)
            if (!mentions.isEmpty()) {
                List<NotificationRecipient> mentionRecipients = new ArrayList<>();

                for (MentionDto mention : mentions) {
                    // Skip self-mentions
                    if (mention.getParticipantId().equals(commenter.getId())) {
                        continue;
                    }

                    // Get participant and convert to NotificationRecipient
                    HubParticipant mentionedParticipant = participantRepository.findById(mention.getParticipantId());
                    if (mentionedParticipant != null) {
                        NotificationRecipient recipient = convertHubParticipantToRecipient(mentionedParticipant);
                        if (recipient != null) {
                            mentionRecipients.add(recipient);
                        }
                    }
                }

                // Send mention notifications using centralized system
                if (!mentionRecipients.isEmpty()) {
                    String postTitle = post.getCaption() != null && !post.getCaption().trim().isEmpty()
                            ? post.getCaption()
                            : "Post #" + post.getId();

                    sendCommentMentionNotifications(mentionRecipients, post.getHubId(), post.getId(),
                                                  commentId, postTitle, commenter.getName(), commentContent);
                }
            }

            // Send comment added notification to post creator and other commenters
            sendCommentAddedNotification(post, commenter, commentId, commentContent);

        } catch (Exception e) {
            logger.warn("Failed to send comment notifications for post {}: {}", post.getId(), e.getMessage());
        }
    }

    /**
     * Sends comment added notification to relevant users (post creator, other commenters).
     */
    private void sendCommentAddedNotification(Post post, HubParticipant commenter, Long commentId, String commentContent) {
        try {
            // Get post creator
            HubParticipant postCreator = participantRepository.findById(post.getCreatorParticipantId());
            List<HubParticipant> notifyParticipants = new ArrayList<>();

            // Add post creator if they are not the commenter
            if (postCreator != null && !postCreator.getId().equals(commenter.getId())) {
                notifyParticipants.add(postCreator);
            }

            // Add other commenters who have previously commented on this post
            List<HubParticipant> otherCommenters = findOtherCommentersOnPost(post.getId(), commenter.getId());
            for (HubParticipant otherCommenter : otherCommenters) {
                // Avoid duplicates (post creator might also be a commenter)
                if (notifyParticipants.stream().noneMatch(p -> p.getId().equals(otherCommenter.getId()))) {
                    notifyParticipants.add(otherCommenter);
                }
            }

            if (!notifyParticipants.isEmpty()) {
                String postTitle = post.getCaption() != null && !post.getCaption().trim().isEmpty()
                        ? post.getCaption()
                        : "Post #" + post.getId();

                sendCommentAddedNotifications(post.getHubId(), post.getId(), commentId,
                                             postTitle, commenter.getName(), commentContent, notifyParticipants);
            }

        } catch (Exception e) {
            logger.warn("Failed to send comment added notification for post {}: {}", post.getId(), e.getMessage());
        }
    }



    /**
     * Extracts account ID from hub for multi-tenancy support.
     * This allows both internal users and external participants to work with comments.
     */
    private Long extractAccountIdFromHub(Long hubId) {
        Long accountId = hubRepository.getAccountIdForHub(hubId);
        if (accountId == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Unable to determine account for hub: " + hubId);
        }
        return accountId;
    }

    /**
     * Converts a HubParticipant to a NotificationRecipient for the unified system.
     */
    private NotificationRecipient convertHubParticipantToRecipient(HubParticipant participant) {
        try {
            return NotificationRecipientUtils.fromHubParticipant(participant);
        } catch (Exception e) {
            logger.warn("Failed to convert hub participant {} to notification recipient: {}",
                       participant.getId(), e.getMessage());
            return null;
        }
    }

    /**
     * Sends comment mention notifications using the unified notification system.
     */
    private void sendCommentMentionNotifications(List<NotificationRecipient> recipients, Long hubId, Long postId,
                                               Long commentId, String postTitle, String commenterName, String commentContent) {
        try {
            // Create entity references for deep linking
            com.collabhub.be.modules.notifications.service.NotificationStorageService.EntityReferences entityReferences =
                com.collabhub.be.modules.notifications.service.NotificationStorageService.EntityReferences.comment(hubId, postId, commentId);

            // Create metadata with comment context
            com.collabhub.be.modules.notifications.dto.NotificationMetadata metadata =
                com.collabhub.be.modules.notifications.dto.NotificationMetadata.builder()
                    .actorName(commenterName)
                    .targetTitle(postTitle)
                    .commentPreview(truncateContent(commentContent))
                    .build();

            // Get localized notification content
            Locale defaultLocale = Locale.forLanguageTag("english");
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("commenterName", commenterName);
            parameters.put("postTitle", postTitle);
            parameters.put("commentPreview", truncateContent(commentContent));

            String title = notificationTranslationService.getNotificationTitle(
                com.collabhub.be.modules.notifications.dto.NotificationType.COMMENT_MENTION, defaultLocale);
            String message = notificationTranslationService.getNotificationMessage(
                com.collabhub.be.modules.notifications.dto.NotificationType.COMMENT_MENTION, defaultLocale, parameters);

            // Send notification using unified system
            notificationDispatcherService.dispatchMixedNotification(
                com.collabhub.be.modules.notifications.dto.NotificationType.COMMENT_MENTION,
                title,
                message,
                recipients,
                entityReferences,
                metadata
            );

        } catch (Exception e) {
            logger.error("Failed to send comment mention notifications: {}", e.getMessage(), e);
        }
    }

    /**
     * Sends comment added notifications using the unified notification system.
     */
    private void sendCommentAddedNotifications(Long hubId, Long postId, Long commentId, String postTitle,
                                             String commenterName, String commentContent, List<HubParticipant> notifyParticipants) {
        try {
            if (notifyParticipants.isEmpty()) {
                return;
            }

            // Create entity references for deep linking
            com.collabhub.be.modules.notifications.service.NotificationStorageService.EntityReferences entityReferences =
                com.collabhub.be.modules.notifications.service.NotificationStorageService.EntityReferences.comment(hubId, postId, commentId);

            // Create metadata with comment context
            com.collabhub.be.modules.notifications.dto.NotificationMetadata metadata =
                com.collabhub.be.modules.notifications.dto.NotificationMetadata.builder()
                    .actorName(commenterName)
                    .targetTitle(postTitle)
                    .commentPreview(truncateContent(commentContent))
                    .build();

            // Get localized notification content
            Locale defaultLocale = Locale.forLanguageTag("english");
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("commenterName", commenterName);
            parameters.put("postTitle", postTitle);
            parameters.put("commentPreview", truncateContent(commentContent));

            String title = notificationTranslationService.getNotificationTitle(
                com.collabhub.be.modules.notifications.dto.NotificationType.COMMENT_ADDED, defaultLocale);
            String message = notificationTranslationService.getNotificationMessage(
                com.collabhub.be.modules.notifications.dto.NotificationType.COMMENT_ADDED, defaultLocale, parameters);

            // Send notification using unified system with hub participants
            notificationDispatcherService.dispatchToHubParticipants(
                com.collabhub.be.modules.notifications.dto.NotificationType.COMMENT_ADDED,
                title,
                message,
                notifyParticipants,
                entityReferences,
                metadata
            );

        } catch (Exception e) {
            logger.error("Failed to send comment added notifications: {}", e.getMessage(), e);
        }
    }

    /**
     * Truncates content for notification preview.
     */
    private String truncateContent(String content) {
        if (content == null) return "";
        return content.length() > 100 ? content.substring(0, 97) + "..." : content;
    }

    /**
     * Finds other participants who have previously commented on the same post.
     * Excludes the current commenter to avoid self-notification.
     */
    private List<HubParticipant> findOtherCommentersOnPost(Long postId, Long currentCommenterId) {
        try {
            // Get distinct participant IDs who have commented on this post (excluding current commenter)
            List<Long> commenterIds = commentRepository.findDistinctCommentersByPostId(postId, currentCommenterId);

            if (commenterIds.isEmpty()) {
                return new ArrayList<>();
            }

            // Bulk load participant information to avoid N+1 queries
            return participantUtil.bulkLoadHubParticipants(commenterIds).values()
                    .stream()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.warn("Failed to find other commenters for post {}: {}", postId, e.getMessage());
            return new ArrayList<>();
        }
    }
}
