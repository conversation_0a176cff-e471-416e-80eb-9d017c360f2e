package com.collabhub.be.modules.brands.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request DTO for updating an existing brand.
 * Includes brand information and associated contacts.
 */
public class BrandUpdateRequest {

    @NotBlank(message = "Brand name is required")
    @Size(max = 255, message = "Brand name cannot exceed 255 characters")
    private String name;

    @NotBlank(message = "Company name is required")
    @Size(max = 255, message = "Company name cannot exceed 255 characters")
    @JsonProperty("company_name")
    private String companyName;

    @Size(max = 500, message = "Address street cannot exceed 500 characters")
    @JsonProperty("address_street")
    private String addressStreet;

    @Size(max = 100, message = "Address city cannot exceed 100 characters")
    @JsonProperty("address_city")
    private String addressCity;

    @Size(max = 20, message = "Address postal code cannot exceed 20 characters")
    @JsonProperty("address_postal_code")
    private String addressPostalCode;

    @Size(max = 100, message = "Address country cannot exceed 100 characters")
    @JsonProperty("address_country")
    private String addressCountry;

    @Size(max = 50, message = "VAT number cannot exceed 50 characters")
    @JsonProperty("vat_number")
    private String vatNumber;

    @Size(max = 100, message = "Registration number cannot exceed 100 characters")
    @JsonProperty("registration_number")
    private String registrationNumber;

    @Size(max = 50, message = "Phone cannot exceed 50 characters")
    private String phone;

    @Email(message = "Email must be a valid email address")
    @Size(max = 255, message = "Email cannot exceed 255 characters")
    private String email;

    @Size(max = 500, message = "Website cannot exceed 500 characters")
    private String website;

    @Valid
    private List<BrandContactRequest> contacts;

    public BrandUpdateRequest() {
    }

    public BrandUpdateRequest(String name, String companyName, String addressStreet, String addressCity,
                            String addressPostalCode, String addressCountry, String vatNumber,
                            String registrationNumber, String phone, String email, String website,
                            List<BrandContactRequest> contacts) {
        this.name = name;
        this.companyName = companyName;
        this.addressStreet = addressStreet;
        this.addressCity = addressCity;
        this.addressPostalCode = addressPostalCode;
        this.addressCountry = addressCountry;
        this.vatNumber = vatNumber;
        this.registrationNumber = registrationNumber;
        this.phone = phone;
        this.email = email;
        this.website = website;
        this.contacts = contacts;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getAddressStreet() {
        return addressStreet;
    }

    public void setAddressStreet(String addressStreet) {
        this.addressStreet = addressStreet;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressPostalCode() {
        return addressPostalCode;
    }

    public void setAddressPostalCode(String addressPostalCode) {
        this.addressPostalCode = addressPostalCode;
    }

    public String getAddressCountry() {
        return addressCountry;
    }

    public void setAddressCountry(String addressCountry) {
        this.addressCountry = addressCountry;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public List<BrandContactRequest> getContacts() {
        return contacts;
    }

    public void setContacts(List<BrandContactRequest> contacts) {
        this.contacts = contacts;
    }

    @Override
    public String toString() {
        return "BrandUpdateRequest{" +
                "name='" + name + '\'' +
                ", companyName='" + companyName + '\'' +
                ", addressStreet='" + addressStreet + '\'' +
                ", addressCity='" + addressCity + '\'' +
                ", addressPostalCode='" + addressPostalCode + '\'' +
                ", addressCountry='" + addressCountry + '\'' +
                ", vatNumber='" + vatNumber + '\'' +
                ", registrationNumber='" + registrationNumber + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", website='" + website + '\'' +
                ", contacts=" + contacts +
                '}';
    }
}
