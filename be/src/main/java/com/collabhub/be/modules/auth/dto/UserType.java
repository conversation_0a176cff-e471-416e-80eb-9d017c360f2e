package com.collabhub.be.modules.auth.dto;

/**
 * Enum representing the type of user in the system.
 * Maps to the database UserTypeEnum for consistency.
 */
public enum UserType {
    /**
     * Internal users with full account access (userId and accountId present).
     */
    INTERNAL,

    /**
     * External participants with hub-scoped access only (userId and accountId null).
     */
    EXTERNAL_PARTICIPANT,

    /**
     * External accountants with invoice export capabilities (future use).
     */
    EXTERNAL_ACCOUNTANT;

    /**
     * Legacy mapping for backward compatibility.
     */
    public static final UserType EXTERNAL = EXTERNAL_PARTICIPANT;
}
