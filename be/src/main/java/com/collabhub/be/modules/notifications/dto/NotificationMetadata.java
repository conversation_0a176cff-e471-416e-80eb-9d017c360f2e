package com.collabhub.be.modules.notifications.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.Objects;
import java.util.Optional;

/**
 * Strongly-typed metadata container for notifications.
 * 
 * <p>This record provides type-safe storage for notification metadata, replacing
 * the previous {@code Map<String, Object>} approach with compile-time safety and
 * clear data contracts. All fields are optional and immutable.</p>
 * 
 * <h3>Usage Examples:</h3>
 * <pre>
 * // Create metadata for a comment mention
 * NotificationMetadata metadata = NotificationMetadata.builder()
 *     .actorName("John Doe")
 *     .targetTitle("Summer Campaign Post")
 *     .actionContext("mentioned you in a comment")
 *     .deepLinkPath("/app/posts/123#comment-456")
 *     .build();
 * 
 * // Create metadata for a hub invitation
 * NotificationMetadata metadata = NotificationMetadata.builder()
 *     .actor<PERSON><PERSON>("<PERSON>")
 *     .targetTitle("Brand Partnership Hub")
 *     .invitationRole("Content Creator")
 *     .build();
 * </pre>
 * 
 * @param actorName The name of the user who triggered this notification (e.g., "John Doe")
 * @param targetTitle The title of the target entity (e.g., post title, hub name, brief title)
 * @param actionContext Additional context about the action (e.g., "mentioned you", "assigned you as reviewer")
 * @param deepLinkPath The application path for deep linking to the relevant content
 * @param invitationRole The role being offered in hub invitations (e.g., "Content Creator", "Reviewer")
 * @param reviewStatus The status of a review (e.g., "approved", "rejected", "needs changes")
 * @param commentPreview A preview of comment content (truncated to 100 characters)
 * @param urgencyReason Explanation for why this notification has high/urgent priority
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Structured metadata for notifications providing type-safe context information")
public record NotificationMetadata(
    
    @JsonProperty("actor_name")
    @Schema(description = "Name of the user who triggered this notification", example = "John Doe")
    @Size(max = 100, message = "Actor name must not exceed 100 characters")
    String actorName,
    
    @JsonProperty("target_title")
    @Schema(description = "Title of the target entity (post, hub, brief, etc.)", example = "Summer Campaign Post")
    @Size(max = 200, message = "Target title must not exceed 200 characters")
    String targetTitle,
    
    @JsonProperty("action_context")
    @Schema(description = "Additional context about the action performed", example = "mentioned you in a comment")
    @Size(max = 150, message = "Action context must not exceed 150 characters")
    String actionContext,
    
    @JsonProperty("deep_link_path")
    @Schema(description = "Application path for deep linking to relevant content", example = "/app/posts/123#comment-456")
    @Size(max = 500, message = "Deep link path must not exceed 500 characters")
    String deepLinkPath,
    
    @JsonProperty("invitation_role")
    @Schema(description = "Role being offered in hub invitations", example = "Content Creator")
    @Size(max = 50, message = "Invitation role must not exceed 50 characters")
    String invitationRole,
    
    @JsonProperty("review_status")
    @Schema(description = "Status of a review", example = "approved", allowableValues = {"approved", "rejected", "needs_changes"})
    @Size(max = 20, message = "Review status must not exceed 20 characters")
    String reviewStatus,
    
    @JsonProperty("comment_preview")
    @Schema(description = "Preview of comment content", example = "This looks great! I think we should...")
    @Size(max = 100, message = "Comment preview must not exceed 100 characters")
    String commentPreview,
    
    @JsonProperty("urgency_reason")
    @Schema(description = "Explanation for high/urgent priority notifications", example = "Direct mention requires immediate attention")
    @Size(max = 200, message = "Urgency reason must not exceed 200 characters")
    String urgencyReason
) {
    
    /**
     * Creates an empty NotificationMetadata instance.
     * 
     * @return a NotificationMetadata with all fields set to null
     */
    public static NotificationMetadata empty() {
        return new NotificationMetadata(null, null, null, null, null, null, null, null);
    }
    
    /**
     * Creates a builder for constructing NotificationMetadata instances.
     * 
     * @return a new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * Gets the actor name if present.
     * 
     * @return Optional containing the actor name, or empty if not set
     */
    public Optional<String> getActorName() {
        return Optional.ofNullable(actorName);
    }
    
    /**
     * Gets the target title if present.
     * 
     * @return Optional containing the target title, or empty if not set
     */
    public Optional<String> getTargetTitle() {
        return Optional.ofNullable(targetTitle);
    }
    
    /**
     * Gets the action context if present.
     * 
     * @return Optional containing the action context, or empty if not set
     */
    public Optional<String> getActionContext() {
        return Optional.ofNullable(actionContext);
    }
    
    /**
     * Gets the deep link path if present.
     * 
     * @return Optional containing the deep link path, or empty if not set
     */
    public Optional<String> getDeepLinkPath() {
        return Optional.ofNullable(deepLinkPath);
    }
    
    /**
     * Checks if this metadata contains any non-null values.
     * 
     * @return true if at least one field is non-null, false if all fields are null
     */
    public boolean hasContent() {
        return actorName != null || targetTitle != null || actionContext != null || 
               deepLinkPath != null || invitationRole != null || reviewStatus != null || 
               commentPreview != null || urgencyReason != null;
    }
    
    /**
     * Builder class for constructing NotificationMetadata instances.
     * 
     * <p>Provides a fluent API for building metadata with validation and null safety.</p>
     */
    public static class Builder {
        private String actorName;
        private String targetTitle;
        private String actionContext;
        private String deepLinkPath;
        private String invitationRole;
        private String reviewStatus;
        private String commentPreview;
        private String urgencyReason;
        
        private Builder() {}
        
        /**
         * Sets the actor name (the user who triggered the notification).
         * 
         * @param actorName the actor name, will be trimmed and validated
         * @return this builder for method chaining
         * @throws IllegalArgumentException if actorName exceeds 100 characters
         */
        public Builder actorName(String actorName) {
            if (actorName != null) {
                actorName = actorName.trim();
                if (actorName.length() > 100) {
                    throw new IllegalArgumentException("Actor name must not exceed 100 characters");
                }
                this.actorName = actorName.isEmpty() ? null : actorName;
            }
            return this;
        }
        
        /**
         * Sets the target title (title of the entity being acted upon).
         * 
         * @param targetTitle the target title, will be trimmed and validated
         * @return this builder for method chaining
         * @throws IllegalArgumentException if targetTitle exceeds 200 characters
         */
        public Builder targetTitle(String targetTitle) {
            if (targetTitle != null) {
                targetTitle = targetTitle.trim();
                if (targetTitle.length() > 200) {
                    throw new IllegalArgumentException("Target title must not exceed 200 characters");
                }
                this.targetTitle = targetTitle.isEmpty() ? null : targetTitle;
            }
            return this;
        }
        
        /**
         * Sets the action context (description of what happened).
         * 
         * @param actionContext the action context, will be trimmed and validated
         * @return this builder for method chaining
         * @throws IllegalArgumentException if actionContext exceeds 150 characters
         */
        public Builder actionContext(String actionContext) {
            if (actionContext != null) {
                actionContext = actionContext.trim();
                if (actionContext.length() > 150) {
                    throw new IllegalArgumentException("Action context must not exceed 150 characters");
                }
                this.actionContext = actionContext.isEmpty() ? null : actionContext;
            }
            return this;
        }
        
        /**
         * Sets the deep link path for navigation.
         * 
         * @param deepLinkPath the deep link path, will be trimmed and validated
         * @return this builder for method chaining
         * @throws IllegalArgumentException if deepLinkPath exceeds 500 characters
         */
        public Builder deepLinkPath(String deepLinkPath) {
            if (deepLinkPath != null) {
                deepLinkPath = deepLinkPath.trim();
                if (deepLinkPath.length() > 500) {
                    throw new IllegalArgumentException("Deep link path must not exceed 500 characters");
                }
                this.deepLinkPath = deepLinkPath.isEmpty() ? null : deepLinkPath;
            }
            return this;
        }
        
        /**
         * Sets the invitation role for hub invitations.
         * 
         * @param invitationRole the role being offered
         * @return this builder for method chaining
         */
        public Builder invitationRole(String invitationRole) {
            if (invitationRole != null) {
                invitationRole = invitationRole.trim();
                this.invitationRole = invitationRole.isEmpty() ? null : invitationRole;
            }
            return this;
        }
        
        /**
         * Sets the review status for review notifications.
         * 
         * @param reviewStatus the review status
         * @return this builder for method chaining
         */
        public Builder reviewStatus(String reviewStatus) {
            if (reviewStatus != null) {
                reviewStatus = reviewStatus.trim();
                this.reviewStatus = reviewStatus.isEmpty() ? null : reviewStatus;
            }
            return this;
        }
        
        /**
         * Sets a preview of comment content.
         * 
         * @param commentPreview the comment preview, will be truncated to 100 characters
         * @return this builder for method chaining
         */
        public Builder commentPreview(String commentPreview) {
            if (commentPreview != null) {
                commentPreview = commentPreview.trim();
                if (commentPreview.length() > 100) {
                    commentPreview = commentPreview.substring(0, 97) + "...";
                }
                this.commentPreview = commentPreview.isEmpty() ? null : commentPreview;
            }
            return this;
        }
        
        /**
         * Sets the urgency reason for high-priority notifications.
         * 
         * @param urgencyReason explanation for urgency
         * @return this builder for method chaining
         */
        public Builder urgencyReason(String urgencyReason) {
            if (urgencyReason != null) {
                urgencyReason = urgencyReason.trim();
                this.urgencyReason = urgencyReason.isEmpty() ? null : urgencyReason;
            }
            return this;
        }
        
        /**
         * Builds the NotificationMetadata instance.
         * 
         * @return a new NotificationMetadata with the configured values
         */
        public NotificationMetadata build() {
            return new NotificationMetadata(
                actorName, targetTitle, actionContext, deepLinkPath,
                invitationRole, reviewStatus, commentPreview, urgencyReason
            );
        }
    }
}
