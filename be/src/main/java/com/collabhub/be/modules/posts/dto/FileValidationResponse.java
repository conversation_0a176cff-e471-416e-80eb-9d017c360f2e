package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Response DTO for file validation operations.
 * Used to return validation results for uploaded files.
 */
public class FileValidationResponse {

    @NotNull(message = "Validation result is required")
    private Boolean valid;

    @JsonProperty("file_url")
    @NotBlank(message = "File URL is required")
    private String fileUrl;

    @NotBlank(message = "Message is required")
    private String message;

    @JsonProperty("file_size")
    private Long fileSize;

    @JsonProperty("content_type")
    private String contentType;

    @JsonProperty("validation_details")
    private ValidationDetails validationDetails;

    public FileValidationResponse() {}

    public FileValidationResponse(Boolean valid, String fileUrl, String message) {
        this.valid = valid;
        this.fileUrl = fileUrl;
        this.message = message;
    }

    public FileValidationResponse(Boolean valid, String fileUrl, String message, 
                                 Long fileSize, String contentType, ValidationDetails validationDetails) {
        this.valid = valid;
        this.fileUrl = fileUrl;
        this.message = message;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.validationDetails = validationDetails;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public ValidationDetails getValidationDetails() {
        return validationDetails;
    }

    public void setValidationDetails(ValidationDetails validationDetails) {
        this.validationDetails = validationDetails;
    }

    @Override
    public String toString() {
        return "FileValidationResponse{" +
                "valid=" + valid +
                ", fileUrl='" + fileUrl + '\'' +
                ", message='" + message + '\'' +
                ", fileSize=" + fileSize +
                ", contentType='" + contentType + '\'' +
                ", validationDetails=" + validationDetails +
                '}';
    }

    /**
     * Nested class for detailed validation information.
     */
    public static class ValidationDetails {
        @JsonProperty("size_check_passed")
        private Boolean sizeCheckPassed;

        @JsonProperty("content_type_check_passed")
        private Boolean contentTypeCheckPassed;

        @JsonProperty("file_exists")
        private Boolean fileExists;

        @JsonProperty("max_size_limit")
        private Long maxSizeLimit;

        @JsonProperty("expected_content_type")
        private String expectedContentType;

        public ValidationDetails() {}

        public ValidationDetails(Boolean sizeCheckPassed, Boolean contentTypeCheckPassed, 
                               Boolean fileExists, Long maxSizeLimit, String expectedContentType) {
            this.sizeCheckPassed = sizeCheckPassed;
            this.contentTypeCheckPassed = contentTypeCheckPassed;
            this.fileExists = fileExists;
            this.maxSizeLimit = maxSizeLimit;
            this.expectedContentType = expectedContentType;
        }

        public Boolean getSizeCheckPassed() {
            return sizeCheckPassed;
        }

        public void setSizeCheckPassed(Boolean sizeCheckPassed) {
            this.sizeCheckPassed = sizeCheckPassed;
        }

        public Boolean getContentTypeCheckPassed() {
            return contentTypeCheckPassed;
        }

        public void setContentTypeCheckPassed(Boolean contentTypeCheckPassed) {
            this.contentTypeCheckPassed = contentTypeCheckPassed;
        }

        public Boolean getFileExists() {
            return fileExists;
        }

        public void setFileExists(Boolean fileExists) {
            this.fileExists = fileExists;
        }

        public Long getMaxSizeLimit() {
            return maxSizeLimit;
        }

        public void setMaxSizeLimit(Long maxSizeLimit) {
            this.maxSizeLimit = maxSizeLimit;
        }

        public String getExpectedContentType() {
            return expectedContentType;
        }

        public void setExpectedContentType(String expectedContentType) {
            this.expectedContentType = expectedContentType;
        }

        @Override
        public String toString() {
            return "ValidationDetails{" +
                    "sizeCheckPassed=" + sizeCheckPassed +
                    ", contentTypeCheckPassed=" + contentTypeCheckPassed +
                    ", fileExists=" + fileExists +
                    ", maxSizeLimit=" + maxSizeLimit +
                    ", expectedContentType='" + expectedContentType + '\'' +
                    '}';
        }
    }
}
