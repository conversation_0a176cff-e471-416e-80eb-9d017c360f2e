package com.collabhub.be.modules.auth.dto;

import jakarta.validation.constraints.NotBlank;

/**
 * Request DTO for refresh token operations.
 */
public class RefreshTokenRequest {

    @NotBlank(message = "Refresh token is required")
    private String refreshToken;

    public RefreshTokenRequest() {
    }

    public RefreshTokenRequest(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    @Override
    public String toString() {
        return "RefreshTokenRequest{" +
               "refreshToken='[PROTECTED]'" +
               '}';
    }
}
