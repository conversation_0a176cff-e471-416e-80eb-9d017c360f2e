package com.collabhub.be.modules.auth.config;

import com.collabhub.be.modules.auth.constants.JwtClaims;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import org.jooq.generated.tables.pojos.User;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.NimbusJwtEncoder;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.List;
import java.util.UUID;

/**
 * Spring Authorization Server configuration for JWT token generation and validation.
 * Configures OAuth2 clients, JWT customization, and token settings.
 */
@Configuration
@EnableConfigurationProperties(AuthProperties.class)
public class AuthorizationServerConfig {

    private final AuthProperties authProperties;

    public AuthorizationServerConfig(AuthProperties authProperties) {
        this.authProperties = authProperties;
    }

    /**
     * Password encoder using BCrypt for OWASP compliance.
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12); // Higher strength for better security
    }


    /**
     * JWT token customizer to add custom claims to access tokens.
     */
    @Bean
    public OAuth2TokenCustomizer<JwtEncodingContext> jwtTokenCustomizer() {
        return context -> {
            if (context.getPrincipal().getPrincipal() instanceof User user) {
                // Add custom claims to JWT
                context.getClaims()
                        .claim(JwtClaims.SUBJECT, user.getId().toString())
                        .claim(JwtClaims.EMAIL, user.getEmail())
                        .claim(JwtClaims.DISPLAY_NAME, user.getDisplayName())
                        .claim(JwtClaims.ROLE, user.getRole())
                        .claim(JwtClaims.ACCOUNT_ID, user.getAccountId())
                        .claim(JwtClaims.INTERNAL, user.getInternal())
                        .claim(JwtClaims.AUDIENCE, List.of(authProperties.getJwt().getAudience()))
                        .claim(JwtClaims.ISSUER, authProperties.getJwt().getIssuer());
            }
        };
    }

    /**
     * JWK source for JWT signing and verification.
     * In production, this should use externally managed keys.
     */
    @Bean
    public JWKSource<SecurityContext> jwkSource() {
        KeyPair keyPair = generateRsaKey();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        
        RSAKey rsaKey = new RSAKey.Builder(publicKey)
                .privateKey(privateKey)
                .keyID(UUID.randomUUID().toString())
                .build();
        
        JWKSet jwkSet = new JWKSet(rsaKey);
        return new ImmutableJWKSet<>(jwkSet);
    }

    /**
     * JWT decoder for validating incoming JWT tokens.
     */
    @Bean
    public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
        return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
    }

    /**
     * JWT encoder for creating JWT tokens.
     */
    @Bean
    public JwtEncoder jwtEncoder(JWKSource<SecurityContext> jwkSource) {
        return new NimbusJwtEncoder(jwkSource);
    }


    /**
     * Generates an RSA key pair for JWT signing.
     * In production, use externally managed keys or HSM.
     */
    private static KeyPair generateRsaKey() {
        KeyPair keyPair;
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048);
            keyPair = keyPairGenerator.generateKeyPair();
        } catch (Exception ex) {
            throw new IllegalStateException("Failed to generate RSA key pair", ex);
        }
        return keyPair;
    }
}
