package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import org.jooq.generated.enums.InvoiceStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Lightweight response DTO for invoice list operations.
 * Contains only essential fields needed for list views to optimize performance.
 */
public class InvoiceListItemDto {

    @NotNull
    private Long id;

    @NotNull
    @JsonProperty("invoice_number")
    private String invoiceNumber;

    @NotNull
    private InvoiceStatus status;

    @NotNull
    @JsonProperty("issue_date")
    private LocalDate issueDate;

    @NotNull
    @JsonProperty("due_date")
    private LocalDate dueDate;

    @NotNull
    private BigDecimal subtotal;

    @NotNull
    @JsonProperty("total_vat")
    private BigDecimal totalVat;

    @NotNull
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;

    @NotNull
    private String currency;

    // Recipient brand ID (the company/brand the invoice is sent to)
    @JsonProperty("recipient_id")
    private Long recipientId;

    // Recipient brand information (populated via bulk query)
    @JsonProperty("recipient_name")
    private String recipientName;

    // Calculated fields
    @JsonProperty("days_until_due")
    private Integer daysUntilDue;

    @JsonProperty("is_overdue")
    private Boolean isOverdue;

    public InvoiceListItemDto() {
    }

    public InvoiceListItemDto(Long id, String invoiceNumber, InvoiceStatus status, LocalDate issueDate,
                             LocalDate dueDate, BigDecimal subtotal, BigDecimal totalVat, BigDecimal totalAmount,
                             String currency, Long recipientId, String recipientName) {
        this.id = id;
        this.invoiceNumber = invoiceNumber;
        this.status = status;
        this.issueDate = issueDate;
        this.dueDate = dueDate;
        this.subtotal = subtotal;
        this.totalVat = totalVat;
        this.totalAmount = totalAmount;
        this.currency = currency;
        this.recipientId = recipientId;
        this.recipientName = recipientName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public InvoiceStatus getStatus() {
        return status;
    }

    public void setStatus(InvoiceStatus status) {
        this.status = status;
    }

    public LocalDate getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDate issueDate) {
        this.issueDate = issueDate;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public BigDecimal getTotalVat() {
        return totalVat;
    }

    public void setTotalVat(BigDecimal totalVat) {
        this.totalVat = totalVat;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Long getRecipientId() {
        return recipientId;
    }

    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }

    public String getRecipientName() {
        return recipientName;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public Integer getDaysUntilDue() {
        return daysUntilDue;
    }

    public void setDaysUntilDue(Integer daysUntilDue) {
        this.daysUntilDue = daysUntilDue;
    }

    public Boolean getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(Boolean isOverdue) {
        this.isOverdue = isOverdue;
    }

    @Override
    public String toString() {
        return "InvoiceListItemDto{" +
                "id=" + id +
                ", invoiceNumber='" + invoiceNumber + '\'' +
                ", status=" + status +
                ", issueDate=" + issueDate +
                ", dueDate=" + dueDate +
                ", subtotal=" + subtotal +
                ", totalVat=" + totalVat +
                ", totalAmount=" + totalAmount +
                ", currency='" + currency + '\'' +
                ", recipientId=" + recipientId +
                ", recipientName='" + recipientName + '\'' +
                ", daysUntilDue=" + daysUntilDue +
                ", isOverdue=" + isOverdue +
                '}';
    }
}
