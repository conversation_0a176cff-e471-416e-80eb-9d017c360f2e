package com.collabhub.be.modules.notifications.dto;

import org.jooq.generated.enums.NotificationUrgencyEnum;

/**
 * Enum representing the urgency levels of notifications.
 * Determines batching behavior and delivery timing.
 * Maps to the database notification_urgency_enum for consistency.
 */
public enum NotificationUrgency {
    /**
     * Low priority notifications that can wait for batching.
     * Examples: General comments, non-critical updates.
     * Batching window: Full window (3 minutes default).
     */
    LOW(NotificationUrgencyEnum.LOW),

    /**
     * Normal priority notifications with standard batching.
     * Examples: Post reviews, brief updates, general assignments.
     * Batching window: Standard window (3 minutes default).
     */
    NORMAL(NotificationUrgencyEnum.NORMAL),

    /**
     * High priority notifications with shorter batching window.
     * Examples: Direct mentions, reviewer assignments, hub invitations.
     * Batching window: Reduced window (1 minute).
     */
    HIGH(NotificationUrgencyEnum.HIGH),

    /**
     * Urgent notifications that bypass batching entirely.
     * Examples: Security alerts, account issues, critical system notifications.
     * Batching window: Immediate delivery.
     */
    URGENT(NotificationUrgencyEnum.URGENT);

    private final NotificationUrgencyEnum jooqEnum;

    NotificationUrgency(NotificationUrgencyEnum jooqEnum) {
        this.jooqEnum = jooqEnum;
    }

    /**
     * Returns the corresponding jOOQ enum for database operations.
     */
    public NotificationUrgencyEnum toJooqEnum() {
        return jooqEnum;
    }

    /**
     * Converts from jOOQ enum to application enum.
     */
    public static NotificationUrgency fromJooqEnum(NotificationUrgencyEnum jooqEnum) {
        if (jooqEnum == null) {
            return NORMAL; // Default fallback
        }
        
        for (NotificationUrgency urgency : values()) {
            if (urgency.jooqEnum == jooqEnum) {
                return urgency;
            }
        }
        
        return NORMAL; // Fallback for unknown values
    }

    /**
     * Determines if this urgency level should bypass batching.
     */
    public boolean shouldBypassBatching() {
        return this == URGENT;
    }

    /**
     * Gets the batching window in minutes for this urgency level.
     * 
     * @param defaultWindowMinutes the default batching window
     * @return batching window in minutes
     */
    public int getBatchingWindowMinutes(int defaultWindowMinutes) {
        return switch (this) {
            case LOW -> defaultWindowMinutes;
            case NORMAL -> defaultWindowMinutes;
            case HIGH -> Math.max(1, defaultWindowMinutes / 3); // Reduced window
            case URGENT -> 0; // Immediate
        };
    }

    /**
     * Determines the default urgency for a notification type.
     * This provides sensible defaults that can be overridden when creating notifications.
     */
    public static NotificationUrgency getDefaultForType(NotificationType type) {
        return switch (type) {
            case INVITE_TO_HUB -> HIGH;           // Important invitations
            case ASSIGNED_AS_REVIEWER -> HIGH;    // Direct assignments
            case POST_REVIEWED -> NORMAL;         // Standard workflow
            case COMMENT_ADDED -> LOW;            // General activity
            case COMMENT_MENTION -> HIGH;         // Direct mentions
            case CHAT_MENTION -> HIGH;            // Direct mentions
            case CHAT_ADDED -> NORMAL;            // Channel additions
            case BRIEF_CREATED -> NORMAL;         // New briefs
            case BRIEF_UPDATED -> LOW;            // Brief changes
            case BRIEF_ASSIGNED -> HIGH;          // Direct assignments
        };
    }
}
