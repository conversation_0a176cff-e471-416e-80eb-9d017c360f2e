package com.collabhub.be.modules.chat.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.chat.dto.ChatChannelResponse;
import com.collabhub.be.modules.chat.dto.ChatChannelCreateRequest;
import com.collabhub.be.modules.chat.dto.ChatChannelParticipantRequest;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.modules.chat.service.ChatChannelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * REST controller for chat channel management.
 * Provides endpoints for listing and accessing chat channels.
 */
@RestController
@RequestMapping("/api/hubs/{hubId}/chats")
@Tag(name = "Chat Channels", description = "Chat channel management endpoints")
public class ChatChannelController {

    private static final Logger logger = LoggerFactory.getLogger(ChatChannelController.class);
    private static final int MAX_PAGE_SIZE = 100;
    private static final int DEFAULT_PAGE_SIZE = 20;

    private final ChatChannelService channelService;
    private final JwtClaimsService jwtClaimsService;

    public ChatChannelController(ChatChannelService channelService,
                               JwtClaimsService jwtClaimsService) {
        this.channelService = channelService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Lists all accessible chat channels for a hub with pagination.
     */
    @GetMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_CHANNEL_READ.permission)")
    @Operation(summary = "List chat channels",
               description = "Gets all chat channels accessible to the current user in the specified hub with pagination")
    public ResponseEntity<PageResponse<ChatChannelResponse>> getChannels(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Page number (0-based)")
            @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)")
            @RequestParam(value = "size", defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Listing chat channels for hub {} by user {} (page: {}, size: {})",
                   hubId, userContext.getUserId(), page, size);

        int validatedSize = Math.min(size, MAX_PAGE_SIZE);
        PageRequest pageRequest = PageRequest.of(page, validatedSize);

        PageResponse<ChatChannelResponse> response = channelService.getAccessibleChannelsByUser(
                hubId, userContext, pageRequest);

        logger.info("Successfully retrieved {} channels for hub {} by user {}",
                   response.getNumberOfElements(), hubId, userContext.getUserId());
        return ResponseEntity.ok(response);
    }

    /**
     * Gets details of a specific chat channel.
     */
    @GetMapping("/{channelId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_CHANNEL_READ.permission)")
    @Operation(summary = "Get chat channel details",
               description = "Gets details of a specific chat channel if the user has access")
    public ResponseEntity<ChatChannelResponse> getChannel(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Channel ID", required = true)
            @PathVariable Long channelId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Getting chat channel {} in hub {} by user {}", channelId, hubId, userContext.getUserId());

        ChatChannelResponse response = channelService.getAccessibleChannelByUser(hubId, channelId, userContext);

        logger.info("Successfully retrieved channel {} for user {}", channelId, userContext.getUserId());
        return ResponseEntity.ok(response);
    }

    /**
     * Creates a custom chat channel with selected participants.
     */
    @PostMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_WRITE.permission)")
    @Operation(summary = "Create custom chat channel",
               description = "Creates a new custom chat channel with selected participants")
    public ResponseEntity<ChatChannelResponse> createCustomChannel(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Valid @RequestBody ChatChannelCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Creating custom channel '{}' in hub {} by user {}",
                   request.getName(), hubId, userContext.getUserId());

        // Get participant ID for the user
        Long participantId = channelService.getParticipantIdByUserContext(hubId, userContext);

        ChatChannelResponse response = channelService.createCustomChannel(hubId, request, participantId);

        logger.info("Successfully created custom channel {} in hub {}", response.getId(), hubId);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Adds participants to a custom chat channel.
     */
    @PutMapping("/{channelId}/participants")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_WRITE.permission)")
    @Operation(summary = "Add participants to channel",
               description = "Adds participants to a custom chat channel")
    public ResponseEntity<Void> addParticipantsToChannel(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Channel ID", required = true)
            @PathVariable Long channelId,
            @Valid @RequestBody ChatChannelParticipantRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Adding {} participants to channel {} by user {}",
                   request.getParticipantIds().size(), channelId, userContext.getUserId());

        // Get participant ID for the user
        Long participantId = channelService.getParticipantIdByUserContext(hubId, userContext);

        channelService.addParticipantsToChannel(hubId, channelId, request, participantId);

        logger.info("Successfully added participants to channel {}", channelId);
        return ResponseEntity.ok().build();
    }

    /**
     * Removes participants from a custom chat channel.
     */
    @DeleteMapping("/{channelId}/participants")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_WRITE.permission)")
    @Operation(summary = "Remove participants from channel",
               description = "Removes participants from a custom chat channel")
    public ResponseEntity<Void> removeParticipantsFromChannel(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Channel ID", required = true)
            @PathVariable Long channelId,
            @Valid @RequestBody ChatChannelParticipantRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Removing {} participants from channel {} by user {}",
                   request.getParticipantIds().size(), channelId, userContext.getUserId());

        // Get participant ID for the user
        Long participantId = channelService.getParticipantIdByUserContext(hubId, userContext);

        channelService.removeParticipantsFromChannel(hubId, channelId, request, participantId);

        logger.info("Successfully removed participants from channel {}", channelId);
        return ResponseEntity.ok().build();
    }

    /**
     * Deletes a custom chat channel.
     */
    @DeleteMapping("/{channelId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_WRITE.permission)")
    @Operation(summary = "Delete custom chat channel",
               description = "Deletes a custom chat channel (only by creator or hub admin)")
    public ResponseEntity<Void> deleteCustomChannel(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Channel ID", required = true)
            @PathVariable Long channelId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Deleting channel {} in hub {} by user {}", channelId, hubId, userContext.getUserId());

        // Get participant ID for the user
        Long participantId = channelService.getParticipantIdByUserContext(hubId, userContext);

        channelService.deleteCustomChannel(channelId, participantId);

        logger.info("Successfully deleted channel {}", channelId);
        return ResponseEntity.ok().build();
    }
}
