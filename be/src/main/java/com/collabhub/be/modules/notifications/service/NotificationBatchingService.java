package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.config.NotificationBatchingProperties;
import com.collabhub.be.modules.notifications.dto.BatchQueueStatus;
import com.collabhub.be.modules.notifications.dto.ExternalUserRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.exception.NotificationBatchingException;
import com.collabhub.be.modules.notifications.repository.NotificationBatchQueueRepository;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.NotificationBatchQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Production-grade service for managing notification batching operations.
 *
 * <p>This service handles queuing notifications for batching and determining delivery
 * timing based on urgency levels and batching configuration. It uses strongly-typed
 * data structures and provides comprehensive validation and error handling.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Strongly-typed metadata using {@link NotificationMetadata}</li>
 *   <li>Urgency-based batching with {@link NotificationUrgency}</li>
 *   <li>Configurable batching windows and processing intervals</li>
 *   <li>Production-grade validation and null safety</li>
 *   <li>Comprehensive logging and monitoring support</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class NotificationBatchingService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationBatchingService.class);

    // Constants
    private static final int MAX_TITLE_LENGTH = 255;
    private static final int MAX_MESSAGE_LENGTH = 1000;
    private static final String QUEUE_DEBUG_MESSAGE = "Queuing {} notifications of type {} with urgency {} for batching";
    private static final String BYPASS_BATCHING_MESSAGE = "Bypassing batching for urgency {} or batching disabled";
    private static final String QUEUE_SUCCESS_MESSAGE = "Queued {} notifications for batching with window start: {}";
    private static final String QUEUE_SINGLE_DEBUG_MESSAGE = "Queued notification for user {} in batch window {}";

    private final NotificationBatchingProperties batchingProperties;
    private final NotificationBatchQueueRepository batchQueueRepository;

    public NotificationBatchingService(NotificationBatchingProperties batchingProperties,
                                     NotificationBatchQueueRepository batchQueueRepository) {
        this.batchingProperties = batchingProperties;
        this.batchQueueRepository = batchQueueRepository;
    }

    /**
     * Queues notifications for batching or determines immediate delivery using strongly-typed metadata.
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipientUserIds the user IDs to send notifications to (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level (must not be null)
     * @return set of user IDs that should receive immediate notifications (bypassing batching)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if queueing fails
     */
    @Transactional
    public Set<Long> queueNotificationsForBatching(@NotNull @Valid NotificationType type,
                                                  @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                                  @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                                  @NotEmpty Set<Long> recipientUserIds,
                                                  NotificationStorageService.EntityReferences entityReferences,
                                                  @Valid NotificationMetadata metadata,
                                                  @NotNull @Valid NotificationUrgency urgency) {

        logger.debug(QUEUE_DEBUG_MESSAGE, recipientUserIds.size(), type, urgency);

        validateQueueParameters(type, title, message, recipientUserIds, urgency);

        if (shouldBypassBatching(urgency)) {
            logger.debug(BYPASS_BATCHING_MESSAGE, urgency);
            return recipientUserIds; // Send immediately
        }

        LocalDateTime batchWindowStart = calculateBatchWindowStart(urgency);
        queueNotificationsInBatch(recipientUserIds, type, title, message, entityReferences, metadata, urgency, batchWindowStart);

        logger.info(QUEUE_SUCCESS_MESSAGE, recipientUserIds.size(), batchWindowStart);
        return Set.of(); // No immediate notifications
    }

    /**
     * Validates queue parameters for consistency and business rules.
     *
     * @param type the notification type
     * @param title the notification title
     * @param message the notification message
     * @param recipientUserIds the recipient user IDs
     * @param urgency the notification urgency
     * @throws IllegalArgumentException if validation fails
     */
    private void validateQueueParameters(@NotNull NotificationType type,
                                       @NotBlank String title,
                                       @NotBlank String message,
                                       @NotEmpty Set<Long> recipientUserIds,
                                       @NotNull NotificationUrgency urgency) {

        if (title.length() > MAX_TITLE_LENGTH) {
            throw new IllegalArgumentException("Title exceeds maximum length of " + MAX_TITLE_LENGTH + " characters");
        }

        if (message.length() > MAX_MESSAGE_LENGTH) {
            throw new IllegalArgumentException("Message exceeds maximum length of " + MAX_MESSAGE_LENGTH + " characters");
        }

        if (recipientUserIds.stream().anyMatch(id -> id == null || id <= 0)) {
            throw new IllegalArgumentException("All recipient user IDs must be positive");
        }
    }

    /**
     * Determines if batching should be bypassed based on configuration and urgency.
     *
     * @param urgency the notification urgency
     * @return true if batching should be bypassed
     */
    private boolean shouldBypassBatching(@NotNull NotificationUrgency urgency) {
        boolean batchingDisabled = !batchingProperties.isEnabled();
        boolean urgentNotification = urgency.shouldBypassBatching();
        boolean shouldBypass = batchingDisabled || urgentNotification;

        logger.debug("Batching decision: enabled={}, urgency={}, shouldBypass={}",
                    batchingProperties.isEnabled(), urgency, shouldBypass);

        return shouldBypass;
    }

    /**
     * Queues all notifications in a batch with the same window start time.
     */
    private void queueNotificationsInBatch(Set<Long> recipientUserIds, NotificationType type, String title, String message,
                                         NotificationStorageService.EntityReferences entityReferences,
                                         NotificationMetadata metadata, NotificationUrgency urgency,
                                         LocalDateTime batchWindowStart) {
        for (Long userId : recipientUserIds) {
            queueSingleNotification(userId, type, title, message, entityReferences,
                                  metadata, urgency, batchWindowStart);
        }
    }

    /**
     * Calculates the batch window start time based on urgency and current time.
     *
     * @param urgency the notification urgency
     * @return the calculated batch window start time
     */
    private LocalDateTime calculateBatchWindowStart(@NotNull NotificationUrgency urgency) {
        LocalDateTime now = LocalDateTime.now();
        int windowMinutes = urgency.getBatchingWindowMinutes(batchingProperties.getWindowMinutes());

        if (windowMinutes <= 0) {
            return now; // Immediate processing
        }

        return calculateWindowBoundary(now, windowMinutes);
    }

    /**
     * Calculates the window boundary for batching.
     *
     * @param now the current time
     * @param windowMinutes the window size in minutes
     * @return the window boundary time
     */
    private LocalDateTime calculateWindowBoundary(LocalDateTime now, int windowMinutes) {
        // Round down to the nearest window boundary
        // For example, with 3-minute windows: 14:32 -> 14:30, 14:35 -> 14:33
        int currentMinute = now.getMinute();
        int windowStartMinute = (currentMinute / windowMinutes) * windowMinutes;

        return now.withMinute(windowStartMinute).withSecond(0).withNano(0);
    }

    /**
     * Queues a single notification for batching with strongly-typed metadata.
     *
     * @param userId the user ID to queue notification for
     * @param type the notification type
     * @param title the notification title
     * @param message the notification message
     * @param entityReferences optional entity references
     * @param metadata optional metadata
     * @param urgency the notification urgency
     * @param batchWindowStart the batch window start time
     * @throws NotificationBatchingException if queueing fails
     */
    private void queueSingleNotification(@NotNull Long userId, @NotNull NotificationType type,
                                       @NotBlank String title, @NotBlank String message,
                                       NotificationStorageService.EntityReferences entityReferences,
                                       NotificationMetadata metadata, @NotNull NotificationUrgency urgency,
                                       @NotNull LocalDateTime batchWindowStart) {

        try {
            NotificationBatchQueue queueEntry = createQueueEntry(userId, type, title, message, urgency, batchWindowStart);
            setEntityReferences(queueEntry, entityReferences);
            setMetadata(queueEntry, metadata);

            batchQueueRepository.insert(queueEntry);
            logger.debug(QUEUE_SINGLE_DEBUG_MESSAGE, userId, batchWindowStart);

        } catch (Exception e) {
            logger.error("Failed to queue notification for user {}: {}", userId, e.getMessage(), e);
            throw new NotificationBatchingException("Failed to queue notification for user " + userId, e);
        }
    }

    /**
     * Creates a basic queue entry with core notification data.
     */
    private NotificationBatchQueue createQueueEntry(Long userId, NotificationType type, String title, String message,
                                                   NotificationUrgency urgency, LocalDateTime batchWindowStart) {
        NotificationBatchQueue queueEntry = new NotificationBatchQueue();
        queueEntry.setUserId(userId);
        queueEntry.setNotificationType(type.toJooqEnum());
        queueEntry.setUrgency(urgency.toJooqEnum());
        queueEntry.setTitle(title);
        queueEntry.setMessage(message);
        queueEntry.setBatchWindowStart(batchWindowStart);
        queueEntry.setStatus(BatchQueueStatus.PENDING.name());
        queueEntry.setRetryCount(0);
        queueEntry.setCreatedAt(LocalDateTime.now());
        return queueEntry;
    }

    /**
     * Sets entity references on the queue entry if provided.
     */
    private void setEntityReferences(NotificationBatchQueue queueEntry,
                                   NotificationStorageService.EntityReferences entityReferences) {
        if (entityReferences != null) {
            queueEntry.setCollaborationHubId(entityReferences.getHubId());
            queueEntry.setPostId(entityReferences.getPostId());
            queueEntry.setCommentId(entityReferences.getCommentId());
            queueEntry.setChatChannelId(entityReferences.getChatChannelId());
            queueEntry.setBriefId(entityReferences.getBriefId());
        }
    }

    /**
     * Sets metadata on the queue entry if provided.
     */
    private void setMetadata(NotificationBatchQueue queueEntry, NotificationMetadata metadata) {
        if (metadata != null && metadata.hasContent()) {
            queueEntry.setMetadata(convertMetadataToJsonb(metadata));
        }
    }

    /**
     * Converts strongly-typed NotificationMetadata to JSONB format for database storage.
     *
     * @param metadata the strongly-typed metadata to convert
     * @return JSONB representation suitable for database storage
     */
    private JSONB convertMetadataToJsonb(NotificationMetadata metadata) {
        if (metadata == null || !metadata.hasContent()) {
            return null;
        }

        try {
            // Create a simple JSON representation of the metadata
            StringBuilder json = new StringBuilder("{");
            boolean first = true;

            if (metadata.actorName() != null) {
                json.append("\"actor_name\":\"").append(escapeJson(metadata.actorName())).append("\"");
                first = false;
            }

            if (metadata.targetTitle() != null) {
                if (!first) json.append(",");
                json.append("\"target_title\":\"").append(escapeJson(metadata.targetTitle())).append("\"");
                first = false;
            }

            if (metadata.actionContext() != null) {
                if (!first) json.append(",");
                json.append("\"action_context\":\"").append(escapeJson(metadata.actionContext())).append("\"");
                first = false;
            }

            if (metadata.deepLinkPath() != null) {
                if (!first) json.append(",");
                json.append("\"deep_link_path\":\"").append(escapeJson(metadata.deepLinkPath())).append("\"");
                first = false;
            }

            if (metadata.invitationRole() != null) {
                if (!first) json.append(",");
                json.append("\"invitation_role\":\"").append(escapeJson(metadata.invitationRole())).append("\"");
                first = false;
            }

            if (metadata.reviewStatus() != null) {
                if (!first) json.append(",");
                json.append("\"review_status\":\"").append(escapeJson(metadata.reviewStatus())).append("\"");
                first = false;
            }

            if (metadata.commentPreview() != null) {
                if (!first) json.append(",");
                json.append("\"comment_preview\":\"").append(escapeJson(metadata.commentPreview())).append("\"");
                first = false;
            }

            if (metadata.urgencyReason() != null) {
                if (!first) json.append(",");
                json.append("\"urgency_reason\":\"").append(escapeJson(metadata.urgencyReason())).append("\"");
            }

            json.append("}");
            return JSONB.valueOf(json.toString());

        } catch (Exception e) {
            logger.error("Failed to serialize notification metadata: {}", e.getMessage(), e);
            return null; // Graceful degradation - notification will work without metadata
        }
    }

    /**
     * Escapes JSON string values to prevent injection and ensure valid JSON.
     */
    private String escapeJson(String value) {
        if (value == null) return "";
        return value.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }

    // ========================================
    // EXTERNAL USER BATCHING METHODS
    // ========================================

    /**
     * Queues notifications for external users with batching support.
     *
     * <p>This method provides feature parity with internal user batching,
     * supporting urgency-based batching decisions and proper error handling.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipients the external user recipients (must not be empty)
     * @param entityReferences optional entity references for email context
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level (must not be null)
     * @return list of recipients that should receive immediate notifications (bypassing batching)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if queueing fails
     */
    @Transactional
    public List<ExternalUserRecipient> queueExternalNotificationsForBatching(
            @NotNull @Valid NotificationType type,
            @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
            @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
            @NotEmpty List<ExternalUserRecipient> recipients,
            NotificationStorageService.EntityReferences entityReferences,
            @Valid NotificationMetadata metadata,
            @NotNull @Valid NotificationUrgency urgency) {

        logger.debug(QUEUE_DEBUG_MESSAGE, recipients.size(), type, urgency);

        validateExternalQueueParameters(type, title, message, recipients, urgency);

        if (shouldBypassBatching(urgency)) {
            logger.debug(BYPASS_BATCHING_MESSAGE, urgency);
            return recipients; // Send immediately
        }

        LocalDateTime batchWindowStart = calculateBatchWindowStart(urgency);
        queueExternalNotificationsInBatch(recipients, type, title, message, entityReferences, metadata, urgency, batchWindowStart);

        logger.info(QUEUE_SUCCESS_MESSAGE, recipients.size(), batchWindowStart);
        return List.of(); // No immediate notifications
    }

    /**
     * Queues external notifications in a batch with the same window start time.
     */
    private void queueExternalNotificationsInBatch(List<ExternalUserRecipient> recipients,
                                                  NotificationType type, String title, String message,
                                                  NotificationStorageService.EntityReferences entityReferences,
                                                  NotificationMetadata metadata, NotificationUrgency urgency,
                                                  LocalDateTime batchWindowStart) {
        for (ExternalUserRecipient recipient : recipients) {
            queueSingleExternalNotification(recipient, type, title, message, entityReferences,
                                           metadata, urgency, batchWindowStart);
        }
    }

    /**
     * Queues a single notification for an external user with comprehensive validation.
     */
    private void queueSingleExternalNotification(@NotNull ExternalUserRecipient recipient,
                                                @NotNull NotificationType type,
                                                @NotBlank String title,
                                                @NotBlank String message,
                                                NotificationStorageService.EntityReferences entityReferences,
                                                NotificationMetadata metadata,
                                                @NotNull NotificationUrgency urgency,
                                                @NotNull LocalDateTime batchWindowStart) {

        try {
            NotificationBatchQueue queueEntry = createExternalQueueEntry(recipient, type, title, message, urgency, batchWindowStart);
            setEntityReferences(queueEntry, entityReferences);
            setMetadata(queueEntry, metadata);

            batchQueueRepository.insert(queueEntry);
            logger.debug(QUEUE_SINGLE_DEBUG_MESSAGE, recipient.getEmail(), batchWindowStart);

        } catch (Exception e) {
            logger.error("Failed to queue notification for external user {}: {}", recipient.getEmail(), e.getMessage(), e);
            throw new NotificationBatchingException("Failed to queue notification for external user " + recipient.getEmail(), e);
        }
    }

    /**
     * Creates a basic queue entry for an external user.
     */
    private NotificationBatchQueue createExternalQueueEntry(ExternalUserRecipient recipient,
                                                           NotificationType type, String title, String message,
                                                           NotificationUrgency urgency, LocalDateTime batchWindowStart) {
        NotificationBatchQueue queueEntry = new NotificationBatchQueue();
        queueEntry.setUserId(null); // External user
        queueEntry.setEmail(recipient.getEmail());
        queueEntry.setNotificationType(type.toJooqEnum());
        queueEntry.setUrgency(urgency.toJooqEnum());
        queueEntry.setTitle(title);
        queueEntry.setMessage(message);
        queueEntry.setBatchWindowStart(batchWindowStart);
        queueEntry.setStatus(BatchQueueStatus.PENDING.name());
        queueEntry.setRetryCount(0);
        queueEntry.setCreatedAt(LocalDateTime.now());
        return queueEntry;
    }

    /**
     * Validates external queue parameters for consistency and business rules.
     */
    private void validateExternalQueueParameters(@NotNull NotificationType type,
                                               @NotBlank String title,
                                               @NotBlank String message,
                                               @NotEmpty List<ExternalUserRecipient> recipients,
                                               @NotNull NotificationUrgency urgency) {

        validateQueueParameters(type, title, message, Set.of(), urgency); // Basic validation

        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipients list cannot be empty");
        }

        // Validate all external recipients
        for (int i = 0; i < recipients.size(); i++) {
            ExternalUserRecipient recipient = recipients.get(i);
            if (recipient == null) {
                throw new IllegalArgumentException("Recipient at index " + i + " is null");
            }

            try {
                recipient.validate();
            } catch (Exception e) {
                throw new IllegalArgumentException("Recipient at index " + i + " is invalid: " + e.getMessage(), e);
            }
        }
    }

    /**
     * Checks if batching is currently enabled.
     */
    public boolean isBatchingEnabled() {
        return batchingProperties.isEnabled();
    }

    /**
     * Gets the current batching window in minutes.
     */
    public int getBatchingWindowMinutes() {
        return batchingProperties.getWindowMinutes();
    }

    /**
     * Gets statistics about the current batch queue.
     */
    public Map<String, Integer> getBatchQueueStatistics() {
        return batchQueueRepository.getBatchQueueStatistics();
    }
}
