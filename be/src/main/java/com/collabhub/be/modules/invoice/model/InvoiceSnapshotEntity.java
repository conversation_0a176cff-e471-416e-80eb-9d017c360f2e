package com.collabhub.be.modules.invoice.model;

import org.jooq.JSONB;

import java.time.LocalDateTime;

/**
 * Entity representing a deduplicated invoice snapshot.
 * This entity stores snapshots of issuer, recipient, and bank details
 * with hash-based deduplication to reduce storage space.
 */
public class InvoiceSnapshotEntity {

    /**
     * Enum for snapshot entity types.
     */
    public enum EntityType {
        ISSUER,
        RECIPIENT,
        BANK_DETAILS
    }

    private Long id;
    private String hash;
    private EntityType entityType;
    private JSONB snapshotData;
    private LocalDateTime createdAt;

    public InvoiceSnapshotEntity() {
    }

    public InvoiceSnapshotEntity(String hash, EntityType entityType, JSONB snapshotData) {
        this.hash = hash;
        this.entityType = entityType;
        this.snapshotData = snapshotData;
        this.createdAt = LocalDateTime.now();
    }

    public InvoiceSnapshotEntity(Long id, String hash, EntityType entityType, JSONB snapshotData, LocalDateTime createdAt) {
        this.id = id;
        this.hash = hash;
        this.entityType = entityType;
        this.snapshotData = snapshotData;
        this.createdAt = createdAt;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public EntityType getEntityType() {
        return entityType;
    }

    public void setEntityType(EntityType entityType) {
        this.entityType = entityType;
    }

    public JSONB getSnapshotData() {
        return snapshotData;
    }

    public void setSnapshotData(JSONB snapshotData) {
        this.snapshotData = snapshotData;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        InvoiceSnapshotEntity that = (InvoiceSnapshotEntity) obj;
        
        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (hash != null ? !hash.equals(that.hash) : that.hash != null) return false;
        if (entityType != that.entityType) return false;
        if (snapshotData != null ? !snapshotData.equals(that.snapshotData) : that.snapshotData != null) return false;
        return createdAt != null ? createdAt.equals(that.createdAt) : that.createdAt == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (hash != null ? hash.hashCode() : 0);
        result = 31 * result + (entityType != null ? entityType.hashCode() : 0);
        result = 31 * result + (snapshotData != null ? snapshotData.hashCode() : 0);
        result = 31 * result + (createdAt != null ? createdAt.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "InvoiceSnapshotEntity{" +
                "id=" + id +
                ", hash='" + hash + '\'' +
                ", entityType=" + entityType +
                ", snapshotData=" + snapshotData +
                ", createdAt=" + createdAt +
                '}';
    }
}
