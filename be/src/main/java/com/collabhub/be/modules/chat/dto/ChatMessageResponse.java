package com.collabhub.be.modules.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for chat message with sender details, mentions, and attachments.
 */
public class ChatMessageResponse {

    private Long id;

    private String content;

    private ChatParticipantDto sender;

    private List<MentionDto> mentions;

    private List<AttachmentDto> attachments;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    @JsonProperty("edited_at")
    private LocalDateTime editedAt;

    @JsonProperty("is_edited")
    private Boolean isEdited;

    public ChatMessageResponse() {}

    public ChatMessageResponse(Long id, String content, ChatParticipantDto sender, 
                             List<MentionDto> mentions, List<AttachmentDto> attachments,
                             LocalDateTime createdAt, LocalDateTime updatedAt, LocalDateTime editedAt) {
        this.id = id;
        this.content = content;
        this.sender = sender;
        this.mentions = mentions;
        this.attachments = attachments;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.editedAt = editedAt;
        this.isEdited = editedAt != null;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public ChatParticipantDto getSender() {
        return sender;
    }

    public void setSender(ChatParticipantDto sender) {
        this.sender = sender;
    }

    public List<MentionDto> getMentions() {
        return mentions;
    }

    public void setMentions(List<MentionDto> mentions) {
        this.mentions = mentions;
    }

    public List<AttachmentDto> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<AttachmentDto> attachments) {
        this.attachments = attachments;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getEditedAt() {
        return editedAt;
    }

    public void setEditedAt(LocalDateTime editedAt) {
        this.editedAt = editedAt;
        this.isEdited = editedAt != null;
    }

    public Boolean getIsEdited() {
        return isEdited;
    }

    public void setIsEdited(Boolean isEdited) {
        this.isEdited = isEdited;
    }
}
