package com.collabhub.be.modules.notifications.constants;

/**
 * Production-grade constants for notification-related operations.
 *
 * <p>This class centralizes all constants used throughout the notification system,
 * providing a single source of truth for configuration values, validation limits,
 * error messages, and other system-wide constants.</p>
 *
 * <h3>Constant Categories:</h3>
 * <ul>
 *   <li><strong>Validation:</strong> Field length limits, request size limits</li>
 *   <li><strong>Defaults:</strong> Default values for user preferences and settings</li>
 *   <li><strong>Limits:</strong> System limits for performance and security</li>
 *   <li><strong>Messages:</strong> Error messages and log message templates</li>
 *   <li><strong>Database:</strong> Field constraints and table limits</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class NotificationConstants {

    private NotificationConstants() {
        throw new UnsupportedOperationException("Constants class cannot be instantiated");
    }

    /**
     * Default settings for notification system.
     */
    public static final class Defaults {

        /**
         * Default enabled state for new notification preferences.
         */
        public static final boolean NOTIFICATION_ENABLED = true;

        /**
         * Default enabled state for new notification preferences (alias for compatibility).
         */
        public static final boolean DEFAULT_NOTIFICATION_ENABLED = true;

        /**
         * Default page size for notification pagination.
         */
        public static final int PAGE_SIZE = 20;

        /**
         * Default maximum page size to prevent performance issues.
         */
        public static final int MAX_PAGE_SIZE = 100;

        /**
         * Default notification retention period in days.
         */
        public static final int RETENTION_DAYS = 30;

        private Defaults() {}
    }

    /**
     * Validation constants for input validation and security.
     */
    public static final class Validation {

        /**
         * Maximum number of preferences that can be updated in a single request.
         */
        public static final int MAX_PREFERENCES_PER_REQUEST = 50;

        /**
         * Maximum length for notification titles.
         */
        public static final int MAX_TITLE_LENGTH = 255;

        /**
         * Maximum length for notification messages.
         */
        public static final int MAX_MESSAGE_LENGTH = 1000;

        /**
         * Maximum length for error messages in batch queue.
         */
        public static final int MAX_ERROR_MESSAGE_LENGTH = 500;

        /**
         * Maximum length for metadata field values.
         */
        public static final int MAX_METADATA_FIELD_LENGTH = 200;

        /**
         * Maximum page number to prevent excessive database queries.
         */
        public static final int MAX_PAGE_NUMBER = 1000;

        /**
         * Minimum page size for pagination.
         */
        public static final int MIN_PAGE_SIZE = 1;

        private Validation() {}
    }

    /**
     * System limits for performance and resource management.
     */
    public static final class Limits {

        /**
         * Maximum number of notifications to process in a single batch.
         */
        public static final int MAX_BATCH_SIZE = 1000;

        /**
         * Maximum number of notifications to delete in a single cleanup operation.
         */
        public static final int MAX_CLEANUP_BATCH_SIZE = 10000;

        /**
         * Maximum number of retry attempts for failed operations.
         */
        public static final int MAX_RETRY_ATTEMPTS = 3;

        /**
         * Maximum number of notifications to retrieve in a single query.
         */
        public static final int MAX_QUERY_LIMIT = 1000;

        private Limits() {}
    }

    /**
     * Error messages for consistent error handling.
     */
    public static final class ErrorMessages {



        /**
         * Error message for notification not found.
         */
        public static final String NOTIFICATION_NOT_FOUND = "Notification not found or not accessible";

        /**
         * Error message for preference not found.
         */
        public static final String PREFERENCE_NOT_FOUND = "Notification preference not found";

        /**
         * Error message for user not found.
         */
        public static final String USER_NOT_FOUND = "User not found";

        /**
         * Error message for invalid pagination parameters.
         */
        public static final String INVALID_PAGINATION = "Invalid pagination parameters";

        /**
         * Error message for storage operation failures.
         */
        public static final String STORAGE_FAILED = "Failed to store notification";

        /**
         * Error message for batching operation failures.
         */
        public static final String BATCHING_FAILED = "Failed to process notification batch";

        private ErrorMessages() {}
    }

    /**
     * Log message templates for consistent logging.
     */
    public static final class LogMessages {

        /**
         * Log message for successful preference retrieval.
         */
        public static final String PREFERENCES_RETRIEVED = "Retrieved {} notification preferences for user {}";

        /**
         * Log message for successful preference retrieval (alias for compatibility).
         */
        public static final String PREFERENCES_RETRIEVED_LOG = "Retrieved {} notification preferences for user {}";

        /**
         * Log message for successful preference updates.
         */
        public static final String PREFERENCES_UPDATED = "Updated {} notification preferences for user {}";

        /**
         * Log message for successful preference updates (alias for compatibility).
         */
        public static final String PREFERENCES_UPDATED_LOG = "Updated {} notification preferences for user {}";

        /**
         * Log message for preference creation.
         */
        public static final String PREFERENCE_CREATED = "Created notification preference: user={}, type={}, channel={}, enabled={}";

        /**
         * Log message for preference updates.
         */
        public static final String PREFERENCE_UPDATED = "Updated notification preference: user={}, type={}, channel={}, enabled={}";

        /**
         * Log message for preference updates (alias for compatibility).
         */
        public static final String PREFERENCE_UPDATED_LOG = "Updated notification preference: user={}, type={}, channel={}, enabled={}";

        /**
         * Log message for notification creation.
         */
        public static final String NOTIFICATION_CREATED = "Created notification: user={}, type={}, title={}";

        /**
         * Log message for notification marked as read.
         */
        public static final String NOTIFICATION_READ = "Marked notification as read: id={}, user={}";

        /**
         * Log message for bulk operations.
         */
        public static final String BULK_OPERATION_COMPLETED = "Completed bulk operation: {} items processed";

        private LogMessages() {}
    }

    /**
     * Database-related constants.
     */
    public static final class Database {

        /**
         * Name of the notification table.
         */
        public static final String NOTIFICATION_TABLE = "notification";

        /**
         * Name of the notification preference table.
         */
        public static final String PREFERENCE_TABLE = "notification_preference";

        /**
         * Name of the batch queue table.
         */
        public static final String BATCH_QUEUE_TABLE = "notification_batch_queue";

        /**
         * Default batch size for database operations.
         */
        public static final int DEFAULT_BATCH_SIZE = 1000;

        private Database() {}
    }

    /**
     * Code quality constants for development guidelines.
     */
    public static final class CodeQuality {

        /**
         * Maximum recommended lines per method.
         */
        public static final int MAX_METHOD_LINES = 30;

        /**
         * Recommended lines per method for optimal readability.
         */
        public static final int RECOMMENDED_METHOD_LINES = 20;

        /**
         * Maximum recommended lines per controller endpoint.
         */
        public static final int MAX_CONTROLLER_ENDPOINT_LINES = 15;

        private CodeQuality() {}
    }
}
