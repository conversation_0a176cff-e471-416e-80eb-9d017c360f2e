package com.collabhub.be.modules.chat.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.chat.dto.*;
import com.collabhub.be.modules.chat.service.ChatMessageService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for chat message management.
 * Provides endpoints for sending, retrieving, editing, and deleting chat messages.
 */
@RestController
@RequestMapping("/api/chats/{channelId}/messages")
@Tag(name = "Chat Messages", description = "Chat message management endpoints")
public class ChatMessageController {

    private static final Logger logger = LoggerFactory.getLogger(ChatMessageController.class);

    private final ChatMessageService messageService;
    private final JwtClaimsService jwtClaimsService;

    public ChatMessageController(ChatMessageService messageService,
                               JwtClaimsService jwtClaimsService) {
        this.messageService = messageService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Gets messages from a chat channel with pagination for infinite scroll.
     */
    @GetMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_READ.permission)")
    @Operation(summary = "Get chat messages",
               description = "Gets messages from a chat channel with pagination support for infinite scroll")
    public ResponseEntity<ChatMessageListResponse> getMessages(
            @Parameter(description = "Channel ID", required = true)
            @PathVariable Long channelId,
            @Parameter(description = "Maximum number of messages to return (default: 50, max: 100)")
            @RequestParam(value = "limit", required = false) Integer limit,
            @Parameter(description = "Get messages before this message ID (for pagination)")
            @RequestParam(value = "before", required = false) Long beforeMessageId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Getting messages from channel {} by user {}", channelId, userContext.getUserId());

        ChatMessageListResponse response = messageService.getMessages(
                channelId, userContext.getEmail(), limit, beforeMessageId);

        logger.info("Successfully retrieved {} messages from channel {} for user {}",
                   response.getMessages().size(), channelId, userContext.getUserId());
        return ResponseEntity.ok(response);
    }

    /**
     * Sends a new message to a chat channel.
     */
    @PostMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_WRITE.permission)")
    @Operation(summary = "Send chat message",
               description = "Sends a new message to the specified chat channel")
    public ResponseEntity<ChatMessageResponse> sendMessage(
            @Parameter(description = "Channel ID", required = true)
            @PathVariable Long channelId,
            @Valid @RequestBody ChatMessageRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Sending message to channel {} by user {}", channelId, userContext.getUserId());

        ChatMessageResponse response = messageService.sendMessage(channelId, userContext, request);

        logger.info("Successfully sent message {} to channel {} by user {}",
                   response.getId(), channelId, userContext.getUserId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Updates an existing chat message.
     */
    @PutMapping("/{messageId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_WRITE.permission)")
    @Operation(summary = "Update chat message",
               description = "Updates an existing chat message (only by the message author)")
    public ResponseEntity<ChatMessageResponse> updateMessage(
            @Parameter(description = "Channel ID", required = true)
            @PathVariable Long channelId,
            @Parameter(description = "Message ID", required = true)
            @PathVariable Long messageId,
            @Valid @RequestBody ChatMessageUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Updating message {} in channel {} by user {}", messageId, channelId, userContext.getUserId());

        ChatMessageResponse response = messageService.updateMessage(
                channelId, messageId, userContext.getEmail(), request);

        logger.info("Successfully updated message {} in channel {} by user {}",
                   messageId, channelId, userContext.getUserId());
        return ResponseEntity.ok(response);
    }

    /**
     * Deletes a chat message.
     */
    @DeleteMapping("/{messageId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CHAT_WRITE.permission)")
    @Operation(summary = "Delete chat message",
               description = "Deletes a chat message (only by the message author)")
    public ResponseEntity<Void> deleteMessage(
            @Parameter(description = "Channel ID", required = true)
            @PathVariable Long channelId,
            @Parameter(description = "Message ID", required = true)
            @PathVariable Long messageId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Deleting message {} in channel {} by user {}", messageId, channelId, userContext.getUserId());

        messageService.deleteMessage(channelId, messageId, userContext.getEmail());

        logger.info("Successfully deleted message {} in channel {} by user {}",
                   messageId, channelId, userContext.getUserId());
        return ResponseEntity.noContent().build();
    }
}
