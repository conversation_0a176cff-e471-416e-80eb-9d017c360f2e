package com.collabhub.be.modules.auth.service;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.UnauthorizedException;
import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.constants.JwtClaims;
import com.collabhub.be.modules.auth.dto.RedirectContext;
import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.auth.service.CustomUserDetailsService.CustomUserPrincipal;
import com.collabhub.be.modules.auth.service.ExternalUserMagicLinkService;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;

import org.jooq.generated.tables.pojos.RefreshToken;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoderParameters;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotBlank;
import java.time.Instant;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;

/**
 * Main authentication service handling login, token generation, and refresh operations.
 * Implements OWASP security best practices for token management.
 */
@Service
@Transactional
public class AuthenticationService {

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationService.class);

    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final RefreshTokenService refreshTokenService;
    private final JwtEncoder jwtEncoder;
    private final AuthProperties authProperties;
    private final ExternalUserMagicLinkService magicLinkService;
    private final HubParticipantRepositoryImpl participantRepository;


    public AuthenticationService(AuthenticationManager authenticationManager,
                               UserRepository userRepository,
                               RefreshTokenService refreshTokenService,
                               JwtEncoder jwtEncoder,
                               AuthProperties authProperties,
                               ExternalUserMagicLinkService magicLinkService,
                               HubParticipantRepositoryImpl participantRepository) {
        this.authenticationManager = authenticationManager;
        this.userRepository = userRepository;
        this.refreshTokenService = refreshTokenService;
        this.jwtEncoder = jwtEncoder;
        this.authProperties = authProperties;
        this.magicLinkService = magicLinkService;
        this.participantRepository = participantRepository;
    }

    /**
     * Authenticates a user and returns access and refresh tokens.
     *
     * @param email the user's email
     * @param password the user's password
     * @param userAgent the client user agent
     * @return authentication result with tokens
     */
    public AuthenticationResult authenticate(String email, String password,
                                           String userAgent) {
        try {
            logger.debug("Attempting authentication for email: {}", email);

            // Authenticate with Spring Security
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(email, password)
            );

            CustomUserPrincipal principal = (CustomUserPrincipal) authentication.getPrincipal();
            User user = principal.user();

            // Generate tokens
            String accessToken = generateAccessToken(user);
            RefreshToken refreshToken = refreshTokenService.createInternalUserRefreshToken(
                    user.getId(), userAgent);

            logger.info("Successful authentication for user {} (account {})", 
                       user.getId(), user.getAccountId());

            return new AuthenticationResult(accessToken, refreshToken.getToken(), user);

        } catch (AuthenticationException e) {
            logger.warn("Authentication failed for email {}: {}", email, e.getMessage());
            throw new UnauthorizedException(
                    ErrorCode.AUTHENTICATION_FAILED,
                    "Invalid email or password"
            );
        }
    }

    /**
     * Authenticates an external user via magic link.
     *
     * @param token the magic link token
     * @param userAgent the client user agent
     * @return authentication result with tokens
     */
    @Transactional
    public AuthenticationResult authenticateWithMagicLink(@NotBlank String token, String userAgent) {
        logger.info("Authenticating external user with magic link");

        // Validate magic link and get user information
        ExternalUserMagicLinkService.MagicLinkValidationResult validationResult =
                magicLinkService.validateMagicLinkToken(token);

        String email = validationResult.email();
        RedirectContext redirectContext = validationResult.redirectContext();

        // Generate tokens for external user
        String accessToken = generateExternalUserAccessToken(email);
        RefreshToken refreshToken = refreshTokenService.createExternalUserRefreshToken(email, userAgent);

        // Create user representation
        User externalUser = createExternalUserRepresentation(email);

        logger.info("Successful magic link authentication for email: {} with redirect: {}",
                   email, redirectContext != null ? redirectContext.getTargetUrl() : "none");

        return new AuthenticationResult(accessToken, refreshToken.getToken(), externalUser, redirectContext);
    }

    /**
     * Refreshes an access token using a refresh token.
     * Automatically detects and handles both internal and external user tokens.
     *
     * @param refreshTokenString the refresh token
     * @param userAgent the client user agent
     * @return new authentication result with rotated tokens
     */
    public AuthenticationResult refreshToken(String refreshTokenString,
                                           String userAgent) {
        logger.debug("Attempting token refresh");

        // Validate refresh token
        Optional<RefreshToken> refreshTokenOpt = refreshTokenService.validateRefreshToken(refreshTokenString);
        if (refreshTokenOpt.isEmpty()) {
            logger.warn("Invalid refresh token provided");
            throw new UnauthorizedException(
                    ErrorCode.AUTHENTICATION_FAILED,
                    "Invalid or expired refresh token"
            );
        }

        RefreshToken refreshToken = refreshTokenOpt.get();

        // Check if this is an external user token
        if (refreshTokenService.isExternalUserToken(refreshToken)) {
            logger.debug("Detected external user refresh token, processing external user refresh");
            return refreshExternalUserToken(refreshToken, userAgent);
        }

        // Handle internal user token refresh
        return refreshInternalUserToken(refreshToken, userAgent);
    }

    /**
     * Refreshes tokens for an external user.
     */
    private AuthenticationResult refreshExternalUserToken(RefreshToken refreshToken, String userAgent) {
        String email = refreshTokenService.getExternalUserEmail(refreshToken);

        // Validate that external user still has access to collaboration hubs
        validateExternalUserHubAccess(email);

        // Generate new access token for external user
        String accessToken = generateExternalUserAccessToken(email);

        // Rotate refresh token
        RefreshToken newRefreshToken = refreshTokenService.rotateRefreshToken(refreshToken, userAgent);

        // Create minimal user representation for external user
        User externalUser = createExternalUserRepresentation(email);

        logger.info("Successful external user token refresh for email: {}", email);

        return new AuthenticationResult(accessToken, newRefreshToken.getToken(), externalUser);
    }

    /**
     * Refreshes tokens for an internal user.
     */
    private AuthenticationResult refreshInternalUserToken(RefreshToken refreshToken, String userAgent) {
        // Get user
        User user = Optional.ofNullable(userRepository.findById(refreshToken.getUserId()))
                .orElseThrow(() -> {
                    logger.error("User not found for refresh token: {}", refreshToken.getUserId());
                    return new UnauthorizedException(
                            ErrorCode.AUTHENTICATION_FAILED,
                            "User not found"
                    );
                });

        if (!user.getEnabled()) {
            logger.warn("Attempted token refresh for disabled user: {}", user.getId());
            refreshTokenService.revokeAllTokensForUser(user.getId());
            throw new UnauthorizedException(
                    ErrorCode.AUTHENTICATION_FAILED,
                    "User account is disabled"
            );
        }

        // Generate new access token
        String accessToken = generateAccessToken(user);

        // Rotate refresh token if enabled
        RefreshToken newRefreshToken = refreshTokenService.rotateRefreshToken(
                refreshToken, userAgent);

        logger.info("Successful token refresh for user {} (account {})", 
                   user.getId(), user.getAccountId());

        return new AuthenticationResult(accessToken, newRefreshToken.getToken(), user);
    }



    /**
     * Extracts display name from email address.
     */
    private String extractDisplayNameFromEmail(String email) {
        if (email == null || !email.contains("@")) {
            return "External User";
        }
        String localPart = email.substring(0, email.indexOf("@"));
        // Capitalize first letter and replace dots/underscores with spaces
        return localPart.substring(0, 1).toUpperCase() +
               localPart.substring(1).replace(".", " ").replace("_", " ");
    }

    /**
     * Logs out a user by revoking their refresh token.
     *
     * @param refreshTokenString the refresh token to revoke
     */
    public void logout(String refreshTokenString) {
        logger.debug("Processing logout request");

        Optional<RefreshToken> refreshTokenOpt = refreshTokenService.validateRefreshToken(refreshTokenString);
        if (refreshTokenOpt.isPresent()) {
            RefreshToken refreshToken = refreshTokenOpt.get();
            refreshTokenService.revokeRefreshToken(refreshToken);
            logger.info("User {} logged out successfully", refreshToken.getUserId());
        } else {
            logger.warn("Logout attempted with invalid refresh token");
        }
    }

    /**
     * Logs out a user from all devices by revoking all their refresh tokens.
     * 
     * @param userId the user ID
     */
    public void logoutFromAllDevices(Long userId) {
        refreshTokenService.revokeAllTokensForUser(userId);
        logger.info("User {} logged out from all devices", userId);
    }

    /**
     * Generates a JWT access token for a user.
     * 
     * @param user the user
     * @return the JWT access token
     */
    private String generateAccessToken(User user) {
        Instant now = Instant.now();
        Instant expiry = now.plus(authProperties.getJwt().getAccessTokenTtl());

        JwtClaimsSet claims = JwtClaimsSet.builder()
                .issuer(authProperties.getJwt().getIssuer())
                .audience(Collections.singletonList(authProperties.getJwt().getAudience()))
                .subject(user.getId().toString())
                .issuedAt(now)
                .expiresAt(expiry)
                .notBefore(now)
                .id(UUID.randomUUID().toString()) // jti claim
                .claim(JwtClaims.EMAIL, user.getEmail())
                .claim(JwtClaims.DISPLAY_NAME, user.getDisplayName())
                .claim(JwtClaims.ROLE, user.getRole())
                .claim(JwtClaims.ACCOUNT_ID, user.getAccountId())
                .claim(JwtClaims.INTERNAL, user.getInternal())
                .build();

        return jwtEncoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    /**
     * Generates a JWT access token for an external user.
     *
     * @param email the external user's email
     * @return the JWT access token
     */
    private String generateExternalUserAccessToken(String email) {
        Instant now = Instant.now();
        Instant expiry = now.plus(authProperties.getJwt().getAccessTokenTtl());
        String subject = JwtClaims.EXTERNAL_USER_SUBJECT_PREFIX + email;

        JwtClaimsSet claims = JwtClaimsSet.builder()
                .issuer(authProperties.getJwt().getIssuer())
                .audience(Collections.singletonList(authProperties.getJwt().getAudience()))
                .subject(subject)
                .issuedAt(now)
                .expiresAt(expiry)
                .notBefore(now)
                .id(UUID.randomUUID().toString()) // jti claim
                .claim(JwtClaims.EMAIL, email)
                .claim(JwtClaims.DISPLAY_NAME, extractDisplayNameFromEmail(email))
                .claim(JwtClaims.ROLE, Role.EXTERNAL_PARTICIPANT.name())
                .claim(JwtClaims.INTERNAL, false)
                .build();

        return jwtEncoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    /**
     * Creates a minimal User representation for external users.
     * External users don't have full User records, so we create a representation for API consistency.
     */
    private User createExternalUserRepresentation(String email) {
        User externalUser = new User();
        externalUser.setId(0L); // Special ID for external users
        externalUser.setEmail(email);
        externalUser.setDisplayName(extractDisplayNameFromEmail(email));
        externalUser.setRole(Role.EXTERNAL_PARTICIPANT.name());
        externalUser.setInternal(false); // Mark as external user
        externalUser.setEnabled(true);
        return externalUser;
    }

    /**
     * Validates that an external user still has access to at least one collaboration hub.
     * External users without any active hub participation should not be able to refresh tokens.
     */
    private void validateExternalUserHubAccess(@NotBlank String email) {
        boolean hasActiveParticipation = participantRepository.hasActiveParticipationByEmail(email);

        if (!hasActiveParticipation) {
            logger.warn("External user {} attempted token refresh but has no active hub participation", email);
            throw new UnauthorizedException(
                    ErrorCode.AUTHENTICATION_FAILED,
                    "External user access has been revoked or expired"
            );
        }

        logger.debug("External user {} has active hub participation, allowing token refresh", email);
    }

    /**
         * Result of authentication containing tokens, user information, and redirect context.
         */
        public record AuthenticationResult(
                String accessToken,
                String refreshToken,
                User user,
                RedirectContext redirectContext
        ) {

            /**
             * Constructor for backward compatibility without redirect context.
             */
            public AuthenticationResult(String accessToken, String refreshToken, User user) {
                this(accessToken, refreshToken, user, null);
            }
        }
}
