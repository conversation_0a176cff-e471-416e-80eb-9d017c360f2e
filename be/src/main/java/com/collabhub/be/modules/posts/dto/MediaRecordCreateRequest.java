package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * Request DTO for creating media records after presigned URL uploads.
 * Used to register uploaded files in the media table.
 */
public class MediaRecordCreateRequest {

    @NotBlank(message = "File URL is required")
    @JsonProperty("file_url")
    private String fileUrl;

    @NotBlank(message = "Filename is required")
    private String filename;

    @NotNull(message = "File size is required")
    @Positive(message = "File size must be positive")
    @JsonProperty("file_size")
    private Long fileSize;

    @NotBlank(message = "MIME type is required")
    @JsonProperty("mime_type")
    private String mimeType;

    public MediaRecordCreateRequest() {}

    public MediaRecordCreateRequest(String fileUrl, String filename, Long fileSize, String mimeType) {
        this.fileUrl = fileUrl;
        this.filename = filename;
        this.fileSize = fileSize;
        this.mimeType = mimeType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    @Override
    public String toString() {
        return "MediaRecordCreateRequest{" +
                "fileUrl='" + fileUrl + '\'' +
                ", filename='" + filename + '\'' +
                ", fileSize=" + fileSize +
                ", mimeType='" + mimeType + '\'' +
                '}';
    }
}
