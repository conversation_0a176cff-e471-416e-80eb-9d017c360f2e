package com.collabhub.be.modules.companies.converter;

import com.collabhub.be.modules.companies.dto.AccountCompanyCreateRequest;
import com.collabhub.be.modules.companies.dto.AccountCompanyResponse;
import com.collabhub.be.modules.companies.dto.AccountCompanyUpdateRequest;
import org.jooq.generated.tables.pojos.AccountCompany;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Converter class for mapping between Account Company DTOs and jOOQ POJOs.
 * Handles conversion between different representations of account company data.
 */
@Component
public class AccountCompanyConverter {

    /**
     * Converts a create request DTO to a jOOQ POJO for database insertion.
     *
     * @param request the create request DTO
     * @param accountId the account ID for multi-tenancy
     * @return the jOOQ POJO ready for database insertion
     */
    public AccountCompany toAccountCompany(AccountCompanyCreateRequest request, Long accountId) {
        if (request == null) {
            return null;
        }

        AccountCompany accountCompany = new AccountCompany();
        accountCompany.setAccountId(accountId);
        accountCompany.setCompanyName(request.getCompanyName());
        accountCompany.setAddressStreet(request.getAddressStreet());
        accountCompany.setAddressCity(request.getAddressCity());
        accountCompany.setAddressPostalCode(request.getAddressPostalCode());
        accountCompany.setAddressCountry(request.getAddressCountry());
        accountCompany.setVatNumber(request.getVatNumber());
        accountCompany.setRegistrationNumber(request.getRegistrationNumber());
        accountCompany.setPhone(request.getPhone());
        accountCompany.setEmail(request.getEmail());
        accountCompany.setWebsite(request.getWebsite());
        accountCompany.setCreatedAt(LocalDateTime.now());
        accountCompany.setUpdatedAt(LocalDateTime.now());

        return accountCompany;
    }

    /**
     * Updates an existing jOOQ POJO with data from an update request DTO.
     * Only updates fields that are not null in the request.
     *
     * @param existing the existing jOOQ POJO
     * @param request the update request DTO
     * @return the updated jOOQ POJO
     */
    public AccountCompany updateAccountCompany(AccountCompany existing, AccountCompanyUpdateRequest request) {
        if (existing == null || request == null) {
            return existing;
        }

        if (request.getCompanyName() != null) {
            existing.setCompanyName(request.getCompanyName());
        }
        if (request.getAddressStreet() != null) {
            existing.setAddressStreet(request.getAddressStreet());
        }
        if (request.getAddressCity() != null) {
            existing.setAddressCity(request.getAddressCity());
        }
        if (request.getAddressPostalCode() != null) {
            existing.setAddressPostalCode(request.getAddressPostalCode());
        }
        if (request.getAddressCountry() != null) {
            existing.setAddressCountry(request.getAddressCountry());
        }
        if (request.getVatNumber() != null) {
            existing.setVatNumber(request.getVatNumber());
        }
        if (request.getRegistrationNumber() != null) {
            existing.setRegistrationNumber(request.getRegistrationNumber());
        }
        if (request.getPhone() != null) {
            existing.setPhone(request.getPhone());
        }
        if (request.getEmail() != null) {
            existing.setEmail(request.getEmail());
        }
        if (request.getWebsite() != null) {
            existing.setWebsite(request.getWebsite());
        }

        existing.setUpdatedAt(LocalDateTime.now());

        return existing;
    }

    /**
     * Converts a jOOQ POJO to a response DTO for API responses.
     *
     * @param accountCompany the jOOQ POJO
     * @return the response DTO
     */
    public AccountCompanyResponse toResponse(AccountCompany accountCompany) {
        if (accountCompany == null) {
            return null;
        }

        return new AccountCompanyResponse(
                accountCompany.getId(),
                accountCompany.getAccountId(),
                accountCompany.getCompanyName(),
                accountCompany.getAddressStreet(),
                accountCompany.getAddressCity(),
                accountCompany.getAddressPostalCode(),
                accountCompany.getAddressCountry(),
                accountCompany.getVatNumber(),
                accountCompany.getRegistrationNumber(),
                accountCompany.getPhone(),
                accountCompany.getEmail(),
                accountCompany.getWebsite(),
                accountCompany.getCreatedAt(),
                accountCompany.getUpdatedAt()
        );
    }
}
