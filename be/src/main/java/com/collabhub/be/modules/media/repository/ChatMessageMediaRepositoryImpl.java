package com.collabhub.be.modules.media.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.ChatMessageMediaDao;
import org.jooq.generated.tables.pojos.ChatMessageMedia;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static org.jooq.generated.Tables.*;
import org.jooq.impl.DSL;

/**
 * Repository for ChatMessageMedia junction table using jOOQ for database operations.
 * Manages the many-to-many relationship between chat messages and media files.
 */
@Repository
public class ChatMessageMediaRepositoryImpl extends ChatMessageMediaDao {

    private final DSLContext dsl;

    public ChatMessageMediaRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Creates media associations for a chat message with proper ordering.
     */
    public void createChatMessageMediaAssociations(Long chatMessageId, List<Long> mediaIds) {
        if (mediaIds == null || mediaIds.isEmpty()) {
            return;
        }

        // Delete existing associations first
        deleteByChatMessageId(chatMessageId);

        // Create new associations with display order
        for (int i = 0; i < mediaIds.size(); i++) {
            ChatMessageMedia chatMessageMedia = new ChatMessageMedia();
            chatMessageMedia.setChatMessageId(chatMessageId);
            chatMessageMedia.setMediaId(mediaIds.get(i));
            chatMessageMedia.setDisplayOrder(i);
            chatMessageMedia.setCreatedAt(LocalDateTime.now());
            
            insert(chatMessageMedia);
        }
    }

    /**
     * Updates media associations for a chat message, maintaining order.
     */
    public void updateChatMessageMediaAssociations(Long chatMessageId, List<Long> mediaIds) {
        createChatMessageMediaAssociations(chatMessageId, mediaIds);
    }

    /**
     * Finds all media IDs associated with a chat message in display order.
     */
    public List<Long> findMediaIdsByChatMessageId(Long chatMessageId) {
        return dsl.select(CHAT_MESSAGE_MEDIA.MEDIA_ID)
                .from(CHAT_MESSAGE_MEDIA)
                .where(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID.eq(chatMessageId))
                .orderBy(CHAT_MESSAGE_MEDIA.DISPLAY_ORDER.asc())
                .fetchInto(Long.class);
    }

    /**
     * Finds all chat message-media associations for a specific message.
     */
    public List<ChatMessageMedia> findByChatMessageId(Long chatMessageId) {
        return dsl.selectFrom(CHAT_MESSAGE_MEDIA)
                .where(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID.eq(chatMessageId))
                .orderBy(CHAT_MESSAGE_MEDIA.DISPLAY_ORDER.asc())
                .fetchInto(ChatMessageMedia.class);
    }

    /**
     * Deletes all media associations for a chat message.
     */
    public void deleteByChatMessageId(Long chatMessageId) {
        dsl.deleteFrom(CHAT_MESSAGE_MEDIA)
                .where(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID.eq(chatMessageId))
                .execute();
    }

    /**
     * Deletes a specific media association.
     */
    public boolean deleteChatMessageMediaAssociation(Long chatMessageId, Long mediaId) {
        int deleted = dsl.deleteFrom(CHAT_MESSAGE_MEDIA)
                .where(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID.eq(chatMessageId))
                .and(CHAT_MESSAGE_MEDIA.MEDIA_ID.eq(mediaId))
                .execute();

        return deleted > 0;
    }

    /**
     * Checks if a media file is associated with any chat messages.
     */
    public boolean isMediaUsedInChatMessages(Long mediaId) {
        return dsl.selectCount()
                .from(CHAT_MESSAGE_MEDIA)
                .where(CHAT_MESSAGE_MEDIA.MEDIA_ID.eq(mediaId))
                .fetchOne(0, Integer.class) > 0;
    }

    /**
     * Finds all chat messages that use a specific media file.
     */
    public List<Long> findChatMessageIdsByMediaId(Long mediaId) {
        return dsl.select(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID)
                .from(CHAT_MESSAGE_MEDIA)
                .where(CHAT_MESSAGE_MEDIA.MEDIA_ID.eq(mediaId))
                .fetchInto(Long.class);
    }

    /**
     * Gets the maximum display order for a chat message (for adding new media).
     */
    public int getMaxDisplayOrderForChatMessage(Long chatMessageId) {
        Integer maxOrder = dsl.select(DSL.max(CHAT_MESSAGE_MEDIA.DISPLAY_ORDER))
                .from(CHAT_MESSAGE_MEDIA)
                .where(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID.eq(chatMessageId))
                .fetchOne(0, Integer.class);

        return maxOrder != null ? maxOrder : -1;
    }

    /**
     * Adds a single media file to a chat message with the next display order.
     */
    public void addMediaToChatMessage(Long chatMessageId, Long mediaId) {
        int nextOrder = getMaxDisplayOrderForChatMessage(chatMessageId) + 1;
        
        ChatMessageMedia chatMessageMedia = new ChatMessageMedia();
        chatMessageMedia.setChatMessageId(chatMessageId);
        chatMessageMedia.setMediaId(mediaId);
        chatMessageMedia.setDisplayOrder(nextOrder);
        chatMessageMedia.setCreatedAt(LocalDateTime.now());
        
        insert(chatMessageMedia);
    }
}
