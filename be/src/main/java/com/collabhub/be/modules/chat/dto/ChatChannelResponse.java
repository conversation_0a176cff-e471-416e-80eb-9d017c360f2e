package com.collabhub.be.modules.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.ChatChannelScope;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for chat channel details.
 */
public class ChatChannelResponse {

    private Long id;

    private String name;

    private String description;

    private ChatChannelScope scope;

    @JsonProperty("created_by_participant_id")
    private Long createdByParticipantId;

    @JsonProperty("participant_count")
    private Long participantCount;

    @JsonProperty("participants")
    private List<ChatParticipantDto> participants;

    @JsonProperty("unread_count")
    private Long unreadCount;

    @JsonProperty("last_message")
    private ChatMessageResponse lastMessage;

    @JsonProperty("can_access")
    private Boolean canAccess;

    @JsonProperty("can_write")
    private Boolean canWrite;

    @JsonProperty("can_manage")
    private Boolean canManage;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("last_activity_at")
    private LocalDateTime lastActivityAt;

    public ChatChannelResponse() {}

    public ChatChannelResponse(Long id, String name, String description, ChatChannelScope scope,
                             Long createdByParticipantId, Long participantCount,
                             List<ChatParticipantDto> participants, Long unreadCount,
                             ChatMessageResponse lastMessage, Boolean canAccess, Boolean canWrite, Boolean canManage,
                             LocalDateTime createdAt, LocalDateTime lastActivityAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.scope = scope;
        this.createdByParticipantId = createdByParticipantId;
        this.participantCount = participantCount;
        this.participants = participants;
        this.unreadCount = unreadCount;
        this.lastMessage = lastMessage;
        this.canAccess = canAccess;
        this.canWrite = canWrite;
        this.canManage = canManage;
        this.createdAt = createdAt;
        this.lastActivityAt = lastActivityAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ChatChannelScope getScope() {
        return scope;
    }

    public void setScope(ChatChannelScope scope) {
        this.scope = scope;
    }

    public Long getParticipantCount() {
        return participantCount;
    }

    public void setParticipantCount(Long participantCount) {
        this.participantCount = participantCount;
    }

    public List<ChatParticipantDto> getParticipants() {
        return participants;
    }

    public void setParticipants(List<ChatParticipantDto> participants) {
        this.participants = participants;
    }

    public Long getCreatedByParticipantId() {
        return createdByParticipantId;
    }

    public void setCreatedByParticipantId(Long createdByParticipantId) {
        this.createdByParticipantId = createdByParticipantId;
    }

    public Long getUnreadCount() {
        return unreadCount;
    }

    public void setUnreadCount(Long unreadCount) {
        this.unreadCount = unreadCount;
    }

    public ChatMessageResponse getLastMessage() {
        return lastMessage;
    }

    public void setLastMessage(ChatMessageResponse lastMessage) {
        this.lastMessage = lastMessage;
    }

    public Boolean getCanAccess() {
        return canAccess;
    }

    public void setCanAccess(Boolean canAccess) {
        this.canAccess = canAccess;
    }

    public Boolean getCanWrite() {
        return canWrite;
    }

    public void setCanWrite(Boolean canWrite) {
        this.canWrite = canWrite;
    }

    public Boolean getCanManage() {
        return canManage;
    }

    public void setCanManage(Boolean canManage) {
        this.canManage = canManage;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getLastActivityAt() {
        return lastActivityAt;
    }

    public void setLastActivityAt(LocalDateTime lastActivityAt) {
        this.lastActivityAt = lastActivityAt;
    }
}
