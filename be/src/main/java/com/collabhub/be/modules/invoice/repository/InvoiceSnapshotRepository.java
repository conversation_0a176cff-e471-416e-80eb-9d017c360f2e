package com.collabhub.be.modules.invoice.repository;

import com.collabhub.be.modules.invoice.model.InvoiceSnapshotEntity;
import org.jooq.JSONB;

import java.util.Optional;

/**
 * Repository interface for InvoiceSnapshot operations.
 * Provides methods for storing and retrieving deduplicated snapshots.
 */
public interface InvoiceSnapshotRepository {

    /**
     * Finds a snapshot by its hash.
     *
     * @param hash the SHA-256 hash of the snapshot
     * @return optional containing the snapshot if found
     */
    Optional<InvoiceSnapshotEntity> findByHash(String hash);

    /**
     * Saves a new snapshot or returns existing one if hash already exists.
     * This method handles deduplication by checking if a snapshot with the same hash exists.
     *
     * @param snapshot the snapshot entity to save
     * @return the saved or existing snapshot entity
     */
    InvoiceSnapshotEntity saveOrGetExisting(InvoiceSnapshotEntity snapshot);

    /**
     * Creates a new snapshot with the given parameters.
     * This method will fail if a snapshot with the same hash already exists.
     *
     * @param hash the SHA-256 hash
     * @param entityType the entity type
     * @param snapshotData the JSON snapshot data
     * @return the created snapshot entity
     */
    InvoiceSnapshotEntity create(String hash, InvoiceSnapshotEntity.EntityType entityType, JSONB snapshotData);

    /**
     * Checks if a snapshot with the given hash exists.
     *
     * @param hash the SHA-256 hash
     * @return true if snapshot exists, false otherwise
     */
    boolean existsByHash(String hash);
}
