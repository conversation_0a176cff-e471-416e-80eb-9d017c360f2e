package com.collabhub.be.modules.notifications.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * Configuration properties for notification batching.
 * Controls batching behavior, timing, and retry logic.
 */
@Component
@ConfigurationProperties(prefix = "app.notifications.batching")
@Validated
public class NotificationBatchingProperties {

    /**
     * Whether notification batching is enabled.
     * When disabled, all notifications are sent immediately.
     */
    private boolean enabled = true;

    /**
     * Batching window in minutes.
     * Notifications within this window are grouped together.
     */
    @NotNull
    @Min(1)
    private Integer windowMinutes = 3;

    /**
     * Batch processing interval in seconds.
     * How often the batch processor runs to send accumulated notifications.
     */
    @NotNull
    @Min(30)
    private Integer processIntervalSeconds = 180; // 3 minutes

    /**
     * Maximum number of notifications to include in a single batch email.
     * Prevents emails from becoming too large.
     */
    @NotNull
    @Min(1)
    private Integer maxBatchSize = 50;

    /**
     * Maximum number of retry attempts for failed batch deliveries.
     */
    @NotNull
    @Min(0)
    private Integer maxRetryAttempts = 3;

    /**
     * Delay in minutes between retry attempts.
     */
    @NotNull
    @Min(1)
    private Integer retryDelayMinutes = 5;

    /**
     * Retention period in hours for processed batch queue entries.
     * Entries older than this are cleaned up.
     */
    @NotNull
    @Min(1)
    private Integer cleanupRetentionHours = 24;

    /**
     * Lock timeout in minutes for distributed batch processing.
     * Prevents stuck locks from blocking processing.
     */
    @NotNull
    @Min(1)
    private Integer lockTimeoutMinutes = 10;

    // Getters and setters

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getWindowMinutes() {
        return windowMinutes;
    }

    public void setWindowMinutes(Integer windowMinutes) {
        this.windowMinutes = windowMinutes;
    }

    public Integer getProcessIntervalSeconds() {
        return processIntervalSeconds;
    }

    public void setProcessIntervalSeconds(Integer processIntervalSeconds) {
        this.processIntervalSeconds = processIntervalSeconds;
    }

    public Integer getMaxBatchSize() {
        return maxBatchSize;
    }

    public void setMaxBatchSize(Integer maxBatchSize) {
        this.maxBatchSize = maxBatchSize;
    }

    public Integer getMaxRetryAttempts() {
        return maxRetryAttempts;
    }

    public void setMaxRetryAttempts(Integer maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }

    public Integer getRetryDelayMinutes() {
        return retryDelayMinutes;
    }

    public void setRetryDelayMinutes(Integer retryDelayMinutes) {
        this.retryDelayMinutes = retryDelayMinutes;
    }

    public Integer getCleanupRetentionHours() {
        return cleanupRetentionHours;
    }

    public void setCleanupRetentionHours(Integer cleanupRetentionHours) {
        this.cleanupRetentionHours = cleanupRetentionHours;
    }

    public Integer getLockTimeoutMinutes() {
        return lockTimeoutMinutes;
    }

    public void setLockTimeoutMinutes(Integer lockTimeoutMinutes) {
        this.lockTimeoutMinutes = lockTimeoutMinutes;
    }

    /**
     * Gets the processing interval as a cron expression for @Scheduled.
     * Converts seconds to a cron expression that runs every N seconds.
     */
    public String getProcessIntervalCron() {
        // For intervals less than 60 seconds, use seconds field
        if (processIntervalSeconds < 60) {
            return String.format("*/%d * * * * *", processIntervalSeconds);
        }
        
        // For intervals of 60+ seconds, convert to minutes
        int minutes = processIntervalSeconds / 60;
        if (minutes == 1) {
            return "0 * * * * *"; // Every minute
        }
        
        return String.format("0 */%d * * * *", minutes);
    }
}
