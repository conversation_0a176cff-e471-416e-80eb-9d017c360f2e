package com.collabhub.be.modules.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request DTO for adding or removing participants from a chat channel.
 */
public class ChatChannelParticipantRequest {

    @NotNull(message = "Participant IDs are required")
    @Size(min = 1, message = "At least one participant must be specified")
    @JsonProperty("participant_ids")
    private List<Long> participantIds;

    public ChatChannelParticipantRequest() {}

    public ChatChannelParticipantRequest(List<Long> participantIds) {
        this.participantIds = participantIds;
    }

    public List<Long> getParticipantIds() {
        return participantIds;
    }

    public void setParticipantIds(List<Long> participantIds) {
        this.participantIds = participantIds;
    }

    @Override
    public String toString() {
        return "ChatChannelParticipantRequest{" +
                "participantIds=" + participantIds +
                '}';
    }
}
