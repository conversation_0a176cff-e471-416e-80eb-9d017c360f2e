package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.ReviewStatus;

import java.time.LocalDateTime;

/**
 * Response DTO for post review operations.
 */
public class PostReviewResponse {

    @JsonProperty("post_id")
    private Long postId;

    @JsonProperty("reviewer_id")
    private Long reviewerId;

    @JsonProperty("reviewer_name")
    private String reviewerName;

    @JsonProperty("reviewer_email")
    private String reviewerEmail;

    private ReviewStatus status;

    @JsonProperty("review_notes")
    private String reviewNotes;

    @JsonProperty("assigned_at")
    private LocalDateTime assignedAt;

    @JsonProperty("reviewed_at")
    private LocalDateTime reviewedAt;

    public PostReviewResponse() {}

    public PostReviewResponse(Long postId, Long reviewerId, String reviewerName, String reviewerEmail,
                             ReviewStatus status, String reviewNotes, LocalDateTime assignedAt, LocalDateTime reviewedAt) {
        this.postId = postId;
        this.reviewerId = reviewerId;
        this.reviewerName = reviewerName;
        this.reviewerEmail = reviewerEmail;
        this.status = status;
        this.reviewNotes = reviewNotes;
        this.assignedAt = assignedAt;
        this.reviewedAt = reviewedAt;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getReviewerId() {
        return reviewerId;
    }

    public void setReviewerId(Long reviewerId) {
        this.reviewerId = reviewerId;
    }

    public String getReviewerName() {
        return reviewerName;
    }

    public void setReviewerName(String reviewerName) {
        this.reviewerName = reviewerName;
    }

    public String getReviewerEmail() {
        return reviewerEmail;
    }

    public void setReviewerEmail(String reviewerEmail) {
        this.reviewerEmail = reviewerEmail;
    }

    public ReviewStatus getStatus() {
        return status;
    }

    public void setStatus(ReviewStatus status) {
        this.status = status;
    }

    public String getReviewNotes() {
        return reviewNotes;
    }

    public void setReviewNotes(String reviewNotes) {
        this.reviewNotes = reviewNotes;
    }

    public LocalDateTime getAssignedAt() {
        return assignedAt;
    }

    public void setAssignedAt(LocalDateTime assignedAt) {
        this.assignedAt = assignedAt;
    }

    public LocalDateTime getReviewedAt() {
        return reviewedAt;
    }

    public void setReviewedAt(LocalDateTime reviewedAt) {
        this.reviewedAt = reviewedAt;
    }

    @Override
    public String toString() {
        return "PostReviewResponse{" +
                "postId=" + postId +
                ", reviewerId=" + reviewerId +
                ", reviewerName='" + reviewerName + '\'' +
                ", reviewerEmail='" + reviewerEmail + '\'' +
                ", status=" + status +
                ", reviewNotes='" + reviewNotes + '\'' +
                ", assignedAt=" + assignedAt +
                ", reviewedAt=" + reviewedAt +
                '}';
    }
}
