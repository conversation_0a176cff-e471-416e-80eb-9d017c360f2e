package com.collabhub.be.modules.posts.service;

import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.repository.PostRepositoryImpl;
import com.collabhub.be.modules.posts.repository.PostReviewerRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.ErrorCode;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.enums.HubParticipantRole;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for managing post permissions and access control.
 * Centralizes all permission-related logic for posts and reviews.
 *
 * Handles permissions for:
 * - Viewing posts: App ADMIN OR hub participant
 * - Editing posts: App ADMIN OR hub ADMIN OR post creator
 * - Reviewing posts: Only assigned reviewers
 * - Creating posts: App ADMIN OR hub roles (ADMIN, CONTENT_CREATOR, REVIEWER_CREATOR)
 * - Assigning reviewers: Only hub roles (ADMIN, REVIEWER, REVIEWER_CREATOR)
 *
 * Access Control:
 * - Internal users (with accountId) can have app-level ADMIN override within their account only
 * - External users (without accountId) are checked via hub participation by email
 * - Multi-tenant isolation: ADMIN from account X cannot access account Y content
 */
@Service
@Validated
public class PostPermissionService {

    private static final Logger logger = LoggerFactory.getLogger(PostPermissionService.class);

    private final PostRepositoryImpl postRepository;
    private final PostReviewerRepositoryImpl postReviewerRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final JwtClaimsService jwtClaimsService;

    public PostPermissionService(PostRepositoryImpl postRepository,
                               PostReviewerRepositoryImpl postReviewerRepository,
                               HubParticipantRepositoryImpl participantRepository,
                               JwtClaimsService jwtClaimsService) {
        this.postRepository = postRepository;
        this.postReviewerRepository = postReviewerRepository;
        this.participantRepository = participantRepository;
        this.jwtClaimsService = jwtClaimsService;
    }

    // ========================================
    // Validation Methods (Throwing)
    // ========================================

    /**
     * Validates that the current user can view a specific post.
     * App ADMIN can view posts within their account, otherwise checks hub participation.
     *
     * @param postId the post ID to validate access for
     * @throws ForbiddenException if user cannot view the post
     */
    @Transactional(readOnly = true)
    public void validateCanViewPost(@NotNull Long postId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();

        // For internal users, always use account validation (including ADMIN)
        // For external users, use basic post lookup
        Post post = userContext.isExternalUser()
            ? postRepository.findById(postId)
            : postRepository.findByIdWithAccountValidation(postId, userContext.getAccountId())
                    .orElse(null);

        if (post == null) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }

        if (!canUserViewPost(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }
    }

    /**
     * Validates that the current user can edit a specific post.
     * Throws exception if edit access is denied.
     *
     * @param post the post to validate edit access for
     * @throws ForbiddenException if user cannot edit the post
     */
    @Transactional(readOnly = true)
    public void validateCanEditPost(@Valid @NotNull Post post) {
        if (!canUserEditPost(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.CANNOT_EDIT_POST_MESSAGE);
        }
    }

    /**
     * Validates that the current user can edit a specific post by ID.
     * Throws exception if edit access is denied.
     *
     * @param postId the post ID to validate edit access for
     * @throws ForbiddenException if user cannot edit the post
     */
    @Transactional(readOnly = true)
    public void validateCanEditPost(@NotNull Long postId) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }
        validateCanEditPost(post);
    }

    /**
     * Validates that the current user can review a specific post.
     * Throws exception if review access is denied.
     *
     * @param post the post to validate review access for
     * @throws ForbiddenException if user cannot review the post
     */
    @Transactional(readOnly = true)
    public void validateCanReviewPost(@Valid @NotNull Post post) {
        if (!canUserReviewPost(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.CANNOT_REVIEW_POST_MESSAGE);
        }
    }

    /**
     * Validates that the current user can create posts in a hub.
     * App ADMIN (within same account) can create posts in any hub, otherwise checks hub role.
     *
     * @param hubId the hub ID to validate creation access for
     * @throws ForbiddenException if user cannot create posts
     */
    @Transactional(readOnly = true)
    public void validateCanCreatePost(@NotNull Long hubId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();

        // For internal users, verify hub belongs to their account and check ADMIN role
        if (userContext.isInternalUser()) {
            // This will validate account ownership and throw if hub doesn't belong to user's account
            HubParticipant participant = getParticipantForHub(hubId, userContext);

            // App ADMIN within same account can create posts in any hub of their account
            if (userContext.getRole() == Role.ADMIN) {
                return;
            }

            // For non-admin internal users, check hub participation and role
            if (participant == null || participant.getRemovedAt() != null) {
                throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                    PostConstants.CANNOT_CREATE_POSTS_MESSAGE);
            }

            if (!canRoleCreatePosts(participant.getRole())) {
                throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                    PostConstants.CANNOT_CREATE_POSTS_MESSAGE + ": " + participant.getRole());
            }
        } else {
            // For external users, check hub participation and role
            HubParticipant participant = getParticipantForHub(hubId, userContext);
            if (participant == null || participant.getRemovedAt() != null) {
                throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                    PostConstants.CANNOT_CREATE_POSTS_MESSAGE);
            }

            if (!canRoleCreatePosts(participant.getRole())) {
                throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                    PostConstants.CANNOT_CREATE_POSTS_MESSAGE + ": " + participant.getRole());
            }
        }
    }

    // ========================================
    // Permission Check Methods (Non-throwing)
    // ========================================

    /**
     * Checks if a user can edit a specific post.
     * App ADMIN (within same account), hub ADMIN, or post creator can edit.
     *
     * @param post the post to check edit permissions for
     * @return true if user can edit the post, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserEditPost(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();

        // For internal users, verify the post belongs to their account first
        if (userContext.isInternalUser()) {
            // Use account-validated lookup to ensure multi-tenant isolation
            Post validatedPost = postRepository.findByIdWithAccountValidation(post.getId(), userContext.getAccountId())
                    .orElse(null);
            if (validatedPost == null) {
                return false; // Post doesn't belong to user's account
            }

            // App ADMIN within same account can edit any post
            if (userContext.getRole() == Role.ADMIN) {
                return true;
            }
        }

        try {
            HubParticipant participant = getParticipantForPost(post, userContext);
            if (participant == null || participant.getRemovedAt() != null) {
                return false;
            }

            return isPostCreator(post, participant) || isHubAdmin(participant);
        } catch (Exception e) {
            logger.warn("Error checking edit permissions for post {} and user {}: {}",
                       post.getId(), userContext.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Checks if a user can review a specific post.
     * Only assigned reviewers and admins can review.
     *
     * @param post the post to check review permissions for
     * @return true if user can review the post, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserReviewPost(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        try {
            HubParticipant participant = getParticipantForPost(post, userContext);
            if (participant == null || participant.getRemovedAt() != null) {
                return false;
            }

            return postReviewerRepository.isParticipantAssignedAsReviewer(post.getId(), participant.getId());
        } catch (Exception e) {
            logger.warn("Error checking review permissions for post {} and user {}: {}",
                       post.getId(), userContext.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Checks if a user can comment on a specific post.
     * Anyone who can view a post can also comment on it.
     *
     * @param post the post to check comment permissions for
     * @return true if user can comment on the post, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserCommentOnPost(@Valid @NotNull Post post) {
        return canUserViewPost(post);
    }

    /**
     * Checks if a user can view a specific post.
     * App ADMIN (within same account) or hub participants can view posts.
     *
     * @param post the post to check view permissions for
     * @return true if user can view the post, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserViewPost(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();

        // For internal users, verify the post belongs to their account first
        if (userContext.isInternalUser()) {
            Post validatedPost = postRepository.findByIdWithAccountValidation(post.getId(), userContext.getAccountId())
                    .orElse(null);
            if (validatedPost == null) {
                return false; // Post doesn't belong to user's account
            }

            // App ADMIN within same account can view any post
            if (userContext.getRole() == Role.ADMIN) {
                return true;
            }
        }

        try {
            HubParticipant participant = getParticipantForPost(post, userContext);
            return participant != null && participant.getRemovedAt() == null;
        } catch (Exception e) {
            logger.warn("Error checking view permissions for post {} and user {}: {}",
                       post.getId(), userContext.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Checks if a role can create posts.
     *
     * @param role the hub participant role to check
     * @return true if role can create posts, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canRoleCreatePosts(@NotNull HubParticipantRole role) {
        return role == HubParticipantRole.admin ||
               role == HubParticipantRole.content_creator ||
               role == HubParticipantRole.reviewer_creator;
    }

    /**
     * Bulk calculates permissions for multiple posts.
     *
     * @param posts the list of posts to calculate permissions for
     * @return map of post ID to permissions
     */
    @Transactional(readOnly = true)
    public Map<Long, PostListPermissions> calculateBulkPostPermissions(@Valid @NotNull List<Post> posts) {
        Map<Long, PostListPermissions> permissionsMap = new HashMap<>();

        for (Post post : posts) {
            boolean canEdit = canUserEditPost(post);
            boolean canReview = canUserReviewPost(post);
            boolean canComment = canUserCommentOnPost(post);

            permissionsMap.put(post.getId(), new PostListPermissions(canEdit, canReview, canComment));
        }

        return permissionsMap;
    }

    /**
     * Record to hold post list permissions.
     */
    public record PostListPermissions(boolean canEdit, boolean canReview, boolean canComment) {}

    /**
     * Validates if a participant can be assigned as a reviewer.
     *
     * @param participantId the participant ID to validate
     * @param hubId the hub ID to validate against
     * @return true if participant can be a reviewer, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean isValidReviewer(@NotNull Long participantId, @NotNull Long hubId) {
        HubParticipant reviewer = participantRepository.findById(participantId);

        if (!isValidParticipant(reviewer, hubId)) {
            logger.warn("Invalid reviewer ID {} for hub {}", participantId, hubId);
            return false;
        }

        if (!hasReviewerRole(reviewer)) {
            logger.warn("Reviewer {} does not have review permissions", participantId);
            return false;
        }

        return true;
    }

    // ========================================
    // Private Helper Methods
    // ========================================



    /**
     * Gets participant for a post by hub ID and email.
     */
    private HubParticipant getParticipantForPost(Post post, UserContext userContext) {
        return participantRepository.findByHubIdAndEmail(post.getHubId(), userContext.getEmail());
    }

    /**
     * Gets participant for a hub by hub ID and email with account validation.
     * For internal users, validates that the hub belongs to their account.
     * For external users, performs basic hub participation lookup.
     */
    private HubParticipant getParticipantForHub(Long hubId, UserContext userContext) {
        Long accountId = userContext.isInternalUser() ? userContext.getAccountId() : null;
        return participantRepository.validateHubAccessWithParticipant(hubId, accountId, userContext.getEmail());
    }

    /**
     * Checks if participant is the post creator.
     */
    private boolean isPostCreator(Post post, HubParticipant participant) {
        return post.getCreatorParticipantId().equals(participant.getId());
    }

    /**
     * Checks if participant is a hub admin.
     */
    private boolean isHubAdmin(HubParticipant participant) {
        return participant.getRole() == HubParticipantRole.admin;
    }

    /**
     * Validates if participant is valid for the hub.
     */
    private boolean isValidParticipant(HubParticipant participant, Long hubId) {
        return participant != null &&
               participant.getHubId().equals(hubId) &&
               participant.getRemovedAt() == null;
    }

    /**
     * Checks if participant has reviewer role.
     */
    private boolean hasReviewerRole(HubParticipant participant) {
        return participant.getRole() == HubParticipantRole.admin ||
               participant.getRole() == HubParticipantRole.reviewer ||
               participant.getRole() == HubParticipantRole.reviewer_creator;
    }

}
