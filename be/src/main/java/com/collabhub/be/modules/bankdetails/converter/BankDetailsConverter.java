package com.collabhub.be.modules.bankdetails.converter;

import com.collabhub.be.modules.bankdetails.dto.BankDetailsCreateRequest;
import com.collabhub.be.modules.bankdetails.dto.BankDetailsResponse;
import com.collabhub.be.modules.bankdetails.dto.BankDetailsUpdateRequest;
import org.jooq.generated.tables.pojos.BankDetails;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Converter for mapping between bank details DTOs and jOOQ POJOs.
 * Handles conversion logic for create, update, and response operations.
 */
@Component
public class BankDetailsConverter {

    /**
     * Converts a create request DTO to a jOOQ POJO for database insertion.
     *
     * @param request the create request DTO
     * @param accountId the account ID for multi-tenancy
     * @return the jOOQ POJO ready for database insertion
     */
    public BankDetails toBankDetails(BankDetailsCreateRequest request, Long accountId) {
        if (request == null) {
            return null;
        }

        BankDetails bankDetails = new BankDetails();
        bankDetails.setAccountId(accountId);
        bankDetails.setName(request.getName());
        bankDetails.setBankName(request.getBankName());
        bankDetails.setIban(request.getIban());
        bankDetails.setBicswift(request.getBicSwift());
        bankDetails.setCreatedAt(LocalDateTime.now());
        bankDetails.setUpdatedAt(LocalDateTime.now());

        return bankDetails;
    }

    /**
     * Updates an existing jOOQ POJO with data from an update request DTO.
     *
     * @param existingBankDetails the existing bank details entity
     * @param request the update request DTO
     * @return the updated jOOQ POJO
     */
    public BankDetails updateBankDetails(BankDetails existingBankDetails, BankDetailsUpdateRequest request) {
        if (existingBankDetails == null || request == null) {
            return existingBankDetails;
        }

        // Update only non-null fields from the request
        if (request.getName() != null) {
            existingBankDetails.setName(request.getName());
        }
        if (request.getBankName() != null) {
            existingBankDetails.setBankName(request.getBankName());
        }
        if (request.getIban() != null) {
            existingBankDetails.setIban(request.getIban());
        }
        if (request.getBicSwift() != null) {
            existingBankDetails.setBicswift(request.getBicSwift());
        }

        // Always update the timestamp
        existingBankDetails.setUpdatedAt(LocalDateTime.now());

        return existingBankDetails;
    }

    /**
     * Converts a jOOQ POJO to a response DTO for API responses.
     *
     * @param bankDetails the jOOQ POJO
     * @return the response DTO
     */
    public BankDetailsResponse toResponse(BankDetails bankDetails) {
        if (bankDetails == null) {
            return null;
        }

        return new BankDetailsResponse(
                bankDetails.getId(),
                bankDetails.getAccountId(),
                bankDetails.getName(),
                bankDetails.getBankName(),
                bankDetails.getIban(),
                bankDetails.getBicswift(),
                bankDetails.getCreatedAt(),
                bankDetails.getUpdatedAt()
        );
    }
}
