package com.collabhub.be.modules.notifications.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Custom pagination response DTO for notification queries.
 * Follows the same pattern as other modules in the codebase.
 */
@Schema(description = "Paginated response for notifications")
public class NotificationPageResponse {

    @JsonProperty("content")
    @Schema(description = "List of notifications for the current page")
    private final List<NotificationResponse> content;

    @JsonProperty("page")
    @Schema(description = "Current page number (0-based)", example = "0")
    private final int page;

    @JsonProperty("size")
    @Schema(description = "Page size", example = "20")
    private final int size;

    @JsonProperty("total_elements")
    @Schema(description = "Total number of notifications", example = "150")
    private final long totalElements;

    @JsonProperty("total_pages")
    @Schema(description = "Total number of pages", example = "8")
    private final int totalPages;

    @JsonProperty("first")
    @Schema(description = "Whether this is the first page", example = "true")
    private final boolean first;

    @JsonProperty("last")
    @Schema(description = "Whether this is the last page", example = "false")
    private final boolean last;

    @JsonProperty("has_next")
    @Schema(description = "Whether there is a next page", example = "true")
    private final boolean hasNext;

    @JsonProperty("has_previous")
    @Schema(description = "Whether there is a previous page", example = "false")
    private final boolean hasPrevious;

    @JsonProperty("number_of_elements")
    @Schema(description = "Number of elements in current page", example = "20")
    private final int numberOfElements;

    public NotificationPageResponse(List<NotificationResponse> content, NotificationPageRequest pageRequest, long totalElements) {
        this.content = content != null ? content : List.of();
        this.page = pageRequest.getPage();
        this.size = pageRequest.getSize();
        this.totalElements = totalElements;
        this.totalPages = (int) Math.ceil((double) totalElements / pageRequest.getSize());
        this.first = pageRequest.getPage() == 0;
        this.last = pageRequest.getPage() >= (totalPages - 1);
        this.hasNext = !last;
        this.hasPrevious = !first;
        this.numberOfElements = this.content.size();
    }

    public List<NotificationResponse> getContent() {
        return content;
    }

    public int getPage() {
        return page;
    }

    public int getSize() {
        return size;
    }

    public long getTotalElements() {
        return totalElements;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public boolean isFirst() {
        return first;
    }

    public boolean isLast() {
        return last;
    }

    public boolean hasNext() {
        return hasNext;
    }

    public boolean hasPrevious() {
        return hasPrevious;
    }

    public int getNumberOfElements() {
        return numberOfElements;
    }

    /**
     * Creates an empty page response.
     * @param pageRequest the page request
     * @return empty page response
     */
    public static NotificationPageResponse empty(NotificationPageRequest pageRequest) {
        return new NotificationPageResponse(List.of(), pageRequest, 0);
    }

    /**
     * Creates a page response with content.
     * @param content the page content
     * @param pageRequest the page request
     * @param totalElements the total number of elements
     * @return page response with content
     */
    public static NotificationPageResponse of(List<NotificationResponse> content, NotificationPageRequest pageRequest, long totalElements) {
        return new NotificationPageResponse(content, pageRequest, totalElements);
    }

    @Override
    public String toString() {
        return "NotificationPageResponse{" +
                "page=" + page +
                ", size=" + size +
                ", totalElements=" + totalElements +
                ", totalPages=" + totalPages +
                ", numberOfElements=" + numberOfElements +
                '}';
    }
}
