package com.collabhub.be.modules.invoice.converter;

import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.invoice.dto.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.generated.enums.InvoiceStatus;
import org.jooq.generated.enums.RecipientSource;
import org.jooq.generated.enums.RecipientType;
import org.jooq.generated.tables.pojos.Invoice;
import org.jooq.generated.tables.pojos.InvoiceItem;
import org.jooq.generated.tables.pojos.InvoiceRecipient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * Converter class for mapping between Invoice DTOs and jOOQ POJOs.
 * Handles conversion between different representations of invoice data.
 */
@Component
public class InvoiceConverter {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceConverter.class);
    private final ObjectMapper objectMapper;

    public InvoiceConverter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Converts a create request DTO to a jOOQ POJO for database insertion.
     *
     * @param request the create request DTO
     * @param accountId the account ID for multi-tenancy
     * @return the jOOQ POJO ready for database insertion
     */
    public Invoice toInvoice(InvoiceCreateRequest request, Long accountId) {
        if (request == null) {
            return null;
        }

        Invoice invoice = new Invoice();
        invoice.setAccountId(accountId);
        invoice.setInvoiceNumber(request.getInvoiceNumber());
        invoice.setStatus(InvoiceStatus.draft);
        invoice.setTemplate(request.getTemplate() != null ? request.getTemplate() : InvoiceConstants.DEFAULT_TEMPLATE);
        invoice.setCurrency(request.getCurrency() != null ? request.getCurrency() : InvoiceConstants.DEFAULT_CURRENCY);
        invoice.setIssueDate(request.getIssueDate());
        invoice.setDueDate(request.getDueDate());
        invoice.setNotes(request.getNotes());
        invoice.setCreatedAt(LocalDateTime.now());
        invoice.setUpdatedAt(LocalDateTime.now());

        // Snapshots will be set by InvoiceSnapshotService
        // Totals will be calculated from items

        return invoice;
    }

    /**
     * Converts invoice items from request DTOs to jOOQ POJOs.
     *
     * @param itemRequests the item request DTOs
     * @param invoiceId the invoice ID
     * @return list of invoice item POJOs
     */
    public List<InvoiceItem> toInvoiceItems(List<InvoiceItemRequest> itemRequests, Long invoiceId) {
        if (itemRequests == null) {
            return List.of();
        }

        return itemRequests.stream()
                .map(request -> toInvoiceItem(request, invoiceId))
                .toList();
    }

    /**
     * Converts a single invoice item request to POJO.
     *
     * @param request the item request
     * @param invoiceId the invoice ID
     * @return the invoice item POJO
     */
    private InvoiceItem toInvoiceItem(InvoiceItemRequest request, Long invoiceId) {
        InvoiceItem item = new InvoiceItem();
        item.setInvoiceId(invoiceId);
        item.setDescription(request.getDescription());
        item.setQuantity(request.getQuantity() != null ? request.getQuantity() : BigDecimal.ONE);
        item.setUnitPrice(request.getUnitPrice());
        item.setVatRate(request.getVatRate() != null ? request.getVatRate() : BigDecimal.ZERO);
        
        // Calculate line total and VAT amount
        BigDecimal lineTotal = item.getQuantity().multiply(item.getUnitPrice());
        BigDecimal vatAmount = lineTotal.multiply(item.getVatRate());
        
        item.setLineTotal(lineTotal);
        item.setVatAmount(vatAmount);
        item.setSortOrder(request.getSortOrder() != null ? request.getSortOrder() : 0);
        item.setCreatedAt(LocalDateTime.now());

        return item;
    }

    /**
     * Converts invoice recipients from request DTOs to jOOQ POJOs.
     *
     * @param recipientRequests the recipient request DTOs
     * @param invoiceId the invoice ID
     * @return list of invoice recipient POJOs
     */
    public List<InvoiceRecipient> toInvoiceRecipients(List<InvoiceRecipientRequest> recipientRequests, Long invoiceId) {
        if (recipientRequests == null) {
            return List.of();
        }

        return recipientRequests.stream()
                .map(request -> toInvoiceRecipient(request, invoiceId))
                .toList();
    }

    /**
     * Converts a single invoice recipient request to POJO.
     *
     * @param request the recipient request
     * @param invoiceId the invoice ID
     * @return the invoice recipient POJO
     */
    private InvoiceRecipient toInvoiceRecipient(InvoiceRecipientRequest request, Long invoiceId) {
        InvoiceRecipient recipient = new InvoiceRecipient();
        recipient.setInvoiceId(invoiceId);
        recipient.setEmail(request.getEmail());
        recipient.setType(request.getType() != null ? request.getType() : RecipientType.original);
        recipient.setSource(request.getSource() != null ? request.getSource() : RecipientSource.manual);
        recipient.setBrandContactId(request.getBrandContactId());
        recipient.setSendCount(0);
        recipient.setCreatedAt(LocalDateTime.now());

        return recipient;
    }

    /**
     * Converts a complete invoice with items and recipients to response DTO.
     *
     * @param invoice the invoice entity
     * @param items the invoice items
     * @param recipients the invoice recipients
     * @return the complete invoice response
     */
    public InvoiceResponse toResponse(Invoice invoice, List<InvoiceItem> items, List<InvoiceRecipient> recipients) {
        if (invoice == null) {
            return null;
        }

        // Convert items and recipients
        List<InvoiceItemResponse> itemResponses = items != null ?
                items.stream().map(this::toInvoiceItemResponse).toList() : List.of();
        List<InvoiceRecipientResponse> recipientResponses = recipients != null ?
                recipients.stream().map(this::toInvoiceRecipientResponse).toList() : List.of();

        // Calculate derived fields
        Integer daysUntilDue = calculateDaysUntilDue(invoice.getDueDate());
        Boolean isOverdue = invoice.getDueDate().isBefore(LocalDate.now()) &&
                           !InvoiceStatus.paid.equals(invoice.getStatus());

        InvoiceResponse response = new InvoiceResponse(
                invoice.getId(),
                invoice.getAccountId(),
                invoice.getInvoiceNumber(),
                invoice.getStatus(),
                invoice.getTemplate(),
                invoice.getCurrency(),
                invoice.getIssueDate(),
                invoice.getDueDate(),
                invoice.getNotes(),
                invoice.getSubtotal(),
                invoice.getTotalVat(),
                invoice.getTotalAmount(),
                invoice.getCreatedAt(),
                invoice.getUpdatedAt(),
                invoice.getIssuerId(),
                invoice.getRecipientId(),
                invoice.getBankDetailsId(),
                invoice.getIssuerSnapshotHash(),
                invoice.getRecipientSnapshotHash(),
                invoice.getBankDetailsSnapshotHash(),
                itemResponses,
                recipientResponses
        );

        response.setDaysUntilDue(daysUntilDue);
        response.setIsOverdue(isOverdue);

        return response;
    }

    /**
     * Converts an invoice item to response DTO.
     *
     * @param item the invoice item
     * @return the item response
     */
    private InvoiceItemResponse toInvoiceItemResponse(InvoiceItem item) {
        return new InvoiceItemResponse(
                item.getId(),
                item.getDescription(),
                item.getQuantity(),
                item.getUnitPrice(),
                item.getVatRate(),
                item.getLineTotal(),
                item.getVatAmount(),
                item.getSortOrder()
        );
    }

    /**
     * Converts an invoice recipient to response DTO.
     *
     * @param recipient the invoice recipient
     * @return the recipient response
     */
    private InvoiceRecipientResponse toInvoiceRecipientResponse(InvoiceRecipient recipient) {
        return new InvoiceRecipientResponse(
                recipient.getId(),
                recipient.getEmail(),
                recipient.getType(),
                recipient.getSource(),
                recipient.getBrandContactId(),
                recipient.getFirstSentAt(),
                recipient.getLastSentAt(),
                recipient.getSendCount()
        );
    }

    /**
     * Updates an existing invoice entity with data from an update request.
     *
     * @param invoice the existing invoice entity
     * @param request the update request
     */
    public void updateInvoiceFromRequest(Invoice invoice, InvoiceUpdateRequest request) {
        if (invoice == null || request == null) {
            return;
        }

        if (request.getIssuerId() != null) {
            invoice.setIssuerId(request.getIssuerId());
        }

        if (request.getRecipientId() != null) {
            invoice.setRecipientId(request.getRecipientId());
        }

        if (request.getBankDetailsId() != null) {
            invoice.setBankDetailsId(request.getBankDetailsId());
        }

        if (request.getInvoiceNumber() != null) {
            invoice.setInvoiceNumber(request.getInvoiceNumber());
        }

        if (request.getCurrency() != null) {
            invoice.setCurrency(request.getCurrency());
        }

        if (request.getIssueDate() != null) {
            invoice.setIssueDate(request.getIssueDate());
        }

        if (request.getDueDate() != null) {
            invoice.setDueDate(request.getDueDate());
        }

        if (request.getNotes() != null) {
            invoice.setNotes(request.getNotes());
        }

        if (request.getTemplate() != null) {
            invoice.setTemplate(request.getTemplate());
        }

        invoice.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * Calculates days until due date.
     *
     * @param dueDate the due date
     * @return days until due (negative if overdue)
     */
    private Integer calculateDaysUntilDue(LocalDate dueDate) {
        if (dueDate == null) {
            return null;
        }
        return (int) ChronoUnit.DAYS.between(LocalDate.now(), dueDate);
    }
}
