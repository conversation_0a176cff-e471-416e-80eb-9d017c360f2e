package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * Response DTO for file upload operations.
 */
public class FileUploadResponse {

    @NotBlank(message = "File URL is required")
    private String url;

    @NotBlank(message = "Filename is required")
    private String filename;

    @NotNull(message = "File size is required")
    @Positive(message = "File size must be positive")
    private Long size;

    @JsonProperty("mime_type")
    @NotBlank(message = "MIME type is required")
    private String mimeType;

    @NotNull(message = "File type is required")
    private FileType type;

    public FileUploadResponse() {}

    public FileUploadResponse(String url, String filename, Long size, String mimeType, FileType type) {
        this.url = url;
        this.filename = filename;
        this.size = size;
        this.mimeType = mimeType;
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "FileUploadResponse{" +
                "url='" + url + '\'' +
                ", filename='" + filename + '\'' +
                ", size=" + size +
                ", mimeType='" + mimeType + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
