package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for updating an existing collaboration hub.
 * Only allows updating name and description - brand cannot be changed.
 */
public class CollaborationHubUpdateRequest {

    @NotBlank(message = "Hub name is required")
    @Size(max = 255, message = "Hub name must not exceed 255 characters")
    private String name;

    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;

    public CollaborationHubUpdateRequest() {}

    public CollaborationHubUpdateRequest(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "CollaborationHubUpdateRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
