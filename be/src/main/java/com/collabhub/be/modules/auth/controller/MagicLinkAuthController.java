package com.collabhub.be.modules.auth.controller;

import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.dto.AuthenticationResponse;
import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.service.AuthenticationService;
import com.collabhub.be.modules.auth.util.CookieUtil;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * REST controller for magic link authentication.
 * Handles external user authentication via magic links for collaboration hub access.
 * Generates JWT tokens and sets refresh token cookies following the same pattern as regular authentication.
 */
@RestController
@RequestMapping("/api/auth/magic-link")
@Tag(name = "Magic Link Authentication", description = "External user authentication via magic links")
public class MagicLinkAuthController {

    private static final Logger logger = LoggerFactory.getLogger(MagicLinkAuthController.class);

    private final AuthenticationService authenticationService;
    private final CookieUtil cookieUtil;
    private final AuthProperties authProperties;

    public MagicLinkAuthController(AuthenticationService authenticationService,
                                   CookieUtil cookieUtil, AuthProperties authProperties) {
        this.authenticationService = authenticationService;
        this.cookieUtil = cookieUtil;
        this.authProperties = authProperties;
    }

    /**
     * Authenticates an external user via magic link token.
     * Generates JWT access token and sets refresh token cookie.
     */
    @PostMapping("/authenticate")
    @Operation(summary = "Authenticate with magic link", 
               description = "Authenticates external user via magic link token and returns access token")
    public ResponseEntity<AuthenticationResponse> authenticateWithMagicLink(
            @Parameter(description = "Magic link token", required = true)
            @RequestParam("token") String token,
            HttpServletRequest request,
            HttpServletResponse response) {

        if (token == null || token.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.VALIDATION_FAILED, "Magic link token is required");
        }

        logger.info("Authenticating external participant with magic link");

        // Extract user agent for refresh token
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            userAgent = "Unknown";
        }

        // Authenticate and generate tokens
        AuthenticationService.AuthenticationResult authResult =
                authenticationService.authenticateWithMagicLink(token, userAgent);

        // Set refresh token cookie
        cookieUtil.addRefreshTokenCookie(response, authResult.refreshToken());

        // Create response
        AuthenticationResponse authResponse = createAuthenticationResponse(authResult);

        logger.info("Successfully authenticated external user via magic link: {}", 
                   authResult.user().getEmail());

        return ResponseEntity.ok(authResponse);
    }

    private AuthenticationResponse createAuthenticationResponse(AuthenticationService.AuthenticationResult result) {
        User user = result.user();
        Role role = Role.fromString(user.getRole());

        AuthenticationResponse.UserInfo userInfo = new AuthenticationResponse.UserInfo(
                user.getId(),
                user.getEmail(),
                user.getDisplayName(),
                role,
                role.getPermissions(),
                user.getAccountId(),
                user.getInternal()
        );

        long expiresInSeconds = authProperties.getJwt().getAccessTokenTtl().toSeconds();

        return new AuthenticationResponse(
                result.accessToken(),
                expiresInSeconds,
                userInfo,
                result.redirectContext()
        );
    }
}
