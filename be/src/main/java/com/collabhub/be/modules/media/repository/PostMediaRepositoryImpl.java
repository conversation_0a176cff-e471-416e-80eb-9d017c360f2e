package com.collabhub.be.modules.media.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.PostMediaDao;
import org.jooq.generated.tables.pojos.PostMedia;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.jooq.generated.Tables.*;

/**
 * Repository for PostMedia junction table using jOOQ for database operations.
 * Manages the many-to-many relationship between posts and media files.
 */
@Repository
@Transactional
public class PostMediaRepositoryImpl extends PostMediaDao {

    private final DSLContext dsl;

    public PostMediaRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Creates media associations for a post with proper ordering.
     */
    public void createPostMediaAssociations(Long postId, List<Long> mediaIds) {
        if (mediaIds == null || mediaIds.isEmpty()) {
            return;
        }

        // Delete existing associations first
        deleteByPostId(postId);

        // Create new associations with display order
        for (int i = 0; i < mediaIds.size(); i++) {
            PostMedia postMedia = new PostMedia();
            postMedia.setPostId(postId);
            postMedia.setMediaId(mediaIds.get(i));
            postMedia.setDisplayOrder(i);
            postMedia.setCreatedAt(LocalDateTime.now());
            
            insert(postMedia);
        }
    }

    /**
     * Updates media associations for a post, maintaining order.
     */
    public void updatePostMediaAssociations(Long postId, List<Long> mediaIds) {
        createPostMediaAssociations(postId, mediaIds);
    }

    /**
     * Finds all post-media associations for a specific post.
     */
    public List<PostMedia> findByPostId(Long postId) {
        return dsl.selectFrom(POST_MEDIA)
                .where(POST_MEDIA.POST_ID.eq(postId))
                .orderBy(POST_MEDIA.DISPLAY_ORDER.asc())
                .fetchInto(PostMedia.class);
    }

    /**
     * Deletes all media associations for a post.
     */
    public void deleteByPostId(Long postId) {
        dsl.deleteFrom(POST_MEDIA)
                .where(POST_MEDIA.POST_ID.eq(postId))
                .execute();
    }

}
