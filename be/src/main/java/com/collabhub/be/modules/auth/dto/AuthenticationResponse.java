package com.collabhub.be.modules.auth.dto;

import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.model.Permission;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import java.util.Set;

/**
 * Response DTO for authentication operations containing only access token and user information.
 * Refresh token is handled via HTTP-only cookies for security.
 */
public class AuthenticationResponse {

    @JsonProperty("access_token")
    @NotNull
    private String accessToken;

    @JsonProperty("token_type")
    @NotNull
    private String tokenType = "Bearer";

    @JsonProperty("expires_in")
    @NotNull
    private long expiresIn;

    @NotNull
    private UserInfo user;

    @JsonProperty("redirect_context")
    private RedirectContext redirectContext;

    public AuthenticationResponse() {
    }

    public AuthenticationResponse(String accessToken, long expiresIn, UserInfo user) {
        this.accessToken = accessToken;
        this.expiresIn = expiresIn;
        this.user = user;
    }

    public AuthenticationResponse(String accessToken, long expiresIn, UserInfo user, RedirectContext redirectContext) {
        this.accessToken = accessToken;
        this.expiresIn = expiresIn;
        this.user = user;
        this.redirectContext = redirectContext;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public UserInfo getUser() {
        return user;
    }

    public void setUser(UserInfo user) {
        this.user = user;
    }

    public RedirectContext getRedirectContext() {
        return redirectContext;
    }

    public void setRedirectContext(RedirectContext redirectContext) {
        this.redirectContext = redirectContext;
    }

    @Override
    public String toString() {
        return "AuthenticationResponse{" +
               "accessToken='[PROTECTED]'" +
               ", tokenType='" + tokenType + '\'' +
               ", expiresIn=" + expiresIn +
               ", user=" + user +
               ", redirectContext=" + redirectContext +
               '}';
    }

    public static class UserInfo {
        @NotNull
        private Long id;
        @NotNull
        private String email;
        
        @JsonProperty("display_name")
        @NotNull
        private String displayName;

        @NotNull
        private Role role;

        @NotNull
        private Set<Permission> permissions;

        @JsonProperty("account_id")
        @NotNull
        private Long accountId;

        @NotNull
        private boolean internal;

        public UserInfo() {
        }

        public UserInfo(Long id, String email, String displayName, Role role, Set<Permission> permissions, Long accountId, boolean internal) {
            this.id = id;
            this.email = email;
            this.displayName = displayName;
            this.role = role;
            this.permissions = permissions;
            this.accountId = accountId;
            this.internal = internal;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getDisplayName() {
            return displayName;
        }

        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }

        public Role getRole() {
            return role;
        }

        public void setRole(Role role) {
            this.role = role;
        }

        public Set<Permission> getPermissions() {
            return permissions;
        }

        public void setPermissions(Set<Permission> permissions) {
            this.permissions = permissions;
        }

        public Long getAccountId() {
            return accountId;
        }

        public void setAccountId(Long accountId) {
            this.accountId = accountId;
        }

        public boolean isInternal() {
            return internal;
        }

        public void setInternal(boolean internal) {
            this.internal = internal;
        }

        @Override
        public String toString() {
            return "UserInfo{" +
                   "id=" + id +
                   ", email='" + email + '\'' +
                   ", displayName='" + displayName + '\'' +
                   ", role=" + role +
                   ", permissions=" + permissions +
                   ", accountId=" + accountId +
                   ", internal=" + internal +
                   '}';
        }
    }
}
