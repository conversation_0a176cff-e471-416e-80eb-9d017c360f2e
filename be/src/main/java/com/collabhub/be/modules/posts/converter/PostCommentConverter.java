package com.collabhub.be.modules.posts.converter;

import com.collabhub.be.modules.posts.dto.PostCommentCreateRequest;
import com.collabhub.be.modules.posts.dto.PostCommentResponse;
import com.collabhub.be.modules.posts.dto.PostCommentListResponse;
import com.collabhub.be.modules.chat.dto.MentionDto;
import org.jooq.generated.tables.pojos.PostComment;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Converter for PostComment entities and DTOs.
 * Handles conversion between jOOQ POJOs and API DTOs.
 */
@Component
public class PostCommentConverter {

    /**
     * Converts a create request DTO to a jOOQ POJO for database insertion.
     */
    public PostComment toPostComment(PostCommentCreateRequest request, Long postId, Long participantId) {
        if (request == null) {
            return null;
        }

        PostComment comment = new PostComment();
        comment.setPostId(postId);
        comment.setParticipantId(participantId);
        comment.setContent(request.getContent());
        comment.setCreatedAt(LocalDateTime.now());
        comment.setUpdatedAt(LocalDateTime.now());

        return comment;
    }

    /**
     * Converts a PostComment POJO to a response DTO.
     */
    public PostCommentResponse toPostCommentResponse(PostComment comment, 
                                                    PostCommentResponse.CommentAuthor author,
                                                    PostCommentResponse.CommentPermissions permissions) {
        if (comment == null) {
            return null;
        }

        return new PostCommentResponse(
                comment.getId(),
                comment.getPostId(),
                comment.getContent(),
                author,
                comment.getCreatedAt(),
                comment.getUpdatedAt(),
                permissions
        );
    }

    /**
     * Converts a PostComment POJO to a response DTO with mentions.
     */
    public PostCommentResponse toPostCommentResponseWithMentions(PostComment comment,
                                                               PostCommentResponse.CommentAuthor author,
                                                               PostCommentResponse.CommentPermissions permissions,
                                                               List<MentionDto> mentions) {
        if (comment == null) {
            return null;
        }

        return new PostCommentResponse(
                comment.getId(),
                comment.getPostId(),
                comment.getContent(),
                author,
                comment.getCreatedAt(),
                comment.getUpdatedAt(),
                permissions,
                mentions
        );
    }

    /**
     * Creates a CommentAuthor DTO.
     */
    public PostCommentResponse.CommentAuthor createCommentAuthor(Long id, String name, String email) {
        return new PostCommentResponse.CommentAuthor(id, name, email);
    }

    /**
     * Creates a CommentAuthor DTO for list items.
     */
    public PostCommentListResponse.CommentAuthor createListCommentAuthor(Long id, String name, String email) {
        return new PostCommentListResponse.CommentAuthor(id, name, email);
    }

    /**
     * Creates CommentPermissions DTO.
     */
    public PostCommentResponse.CommentPermissions createCommentPermissions(boolean canEdit, boolean canDelete) {
        return new PostCommentResponse.CommentPermissions(canEdit, canDelete);
    }

    /**
     * Converts a PostComment POJO to a list item DTO.
     */
    public PostCommentListResponse.PostCommentItem toPostCommentListItem(PostComment comment,
                                                                        PostCommentListResponse.CommentAuthor author,
                                                                        boolean canEdit, boolean canDelete) {
        if (comment == null) {
            return null;
        }

        return new PostCommentListResponse.PostCommentItem(
                comment.getId(),
                comment.getContent(),
                author,
                comment.getCreatedAt(),
                comment.getUpdatedAt(),
                canEdit,
                canDelete
        );
    }

    /**
     * Converts a PostComment POJO to a list item DTO with mentions.
     */
    public PostCommentListResponse.PostCommentItem toPostCommentListItemWithMentions(PostComment comment,
                                                                                    PostCommentListResponse.CommentAuthor author,
                                                                                    boolean canEdit,
                                                                                    boolean canDelete,
                                                                                    List<MentionDto> mentions) {
        if (comment == null) {
            return null;
        }

        return new PostCommentListResponse.PostCommentItem(
                comment.getId(),
                comment.getContent(),
                author,
                comment.getCreatedAt(),
                comment.getUpdatedAt(),
                canEdit,
                canDelete,
                mentions
        );
    }

    /**
     * Creates a paginated comment list response.
     */
    public PostCommentListResponse createCommentListResponse(
            java.util.List<PostCommentListResponse.PostCommentItem> comments,
            long totalCount, int pageSize, int currentPage) {
        
        int totalPages = (int) Math.ceil((double) totalCount / pageSize);
        boolean hasNext = (currentPage + 1) < totalPages;
        boolean hasPrevious = currentPage > 0;

        return new PostCommentListResponse(
                comments,
                totalCount,
                pageSize,
                currentPage,
                totalPages,
                hasNext,
                hasPrevious
        );
    }
}
