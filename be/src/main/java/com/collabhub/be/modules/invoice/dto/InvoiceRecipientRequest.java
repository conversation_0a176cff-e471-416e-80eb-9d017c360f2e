package com.collabhub.be.modules.invoice.dto;

import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.jooq.generated.enums.RecipientSource;
import org.jooq.generated.enums.RecipientType;

/**
 * Request DTO for invoice recipients.
 * Used when creating or updating invoice recipients.
 */
public class InvoiceRecipientRequest {

    @NotBlank(message = InvoiceConstants.EMAIL_REQUIRED)
    @Email(message = InvoiceConstants.INVALID_EMAIL_MESSAGE)
    @Size(max = InvoiceConstants.EMAIL_MAX_LENGTH, message = InvoiceConstants.EMAIL_SIZE_MESSAGE)
    private String email;

    @NotNull(message = InvoiceConstants.RECIPIENT_TYPE_REQUIRED)
    private RecipientType type;

    @NotNull(message = InvoiceConstants.RECIPIENT_SOURCE_REQUIRED)
    private RecipientSource source;

    @JsonProperty("brand_contact_id")
    private Long brandContactId;

    public InvoiceRecipientRequest() {
    }

    public InvoiceRecipientRequest(String email, RecipientType type, RecipientSource source, Long brandContactId) {
        this.email = email;
        this.type = type;
        this.source = source;
        this.brandContactId = brandContactId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public RecipientType getType() {
        return type;
    }

    public void setType(RecipientType type) {
        this.type = type;
    }

    public RecipientSource getSource() {
        return source;
    }

    public void setSource(RecipientSource source) {
        this.source = source;
    }

    public Long getBrandContactId() {
        return brandContactId;
    }

    public void setBrandContactId(Long brandContactId) {
        this.brandContactId = brandContactId;
    }

    @Override
    public String toString() {
        return "InvoiceRecipientRequest{" +
                "email='" + email + '\'' +
                ", type=" + type +
                ", source=" + source +
                ", brandContactId=" + brandContactId +
                '}';
    }
}
