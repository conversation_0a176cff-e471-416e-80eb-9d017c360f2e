package com.collabhub.be.modules.bankdetails.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.bankdetails.converter.BankDetailsConverter;
import com.collabhub.be.modules.bankdetails.dto.BankDetailsCreateRequest;
import com.collabhub.be.modules.bankdetails.dto.BankDetailsResponse;
import com.collabhub.be.modules.bankdetails.dto.BankDetailsUpdateRequest;
import com.collabhub.be.modules.bankdetails.repository.BankDetailsRepositoryImpl;
import org.jooq.generated.tables.pojos.BankDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for managing bank details operations.
 * Handles business logic, validation, and coordinates with repository layer.
 */
@Service
public class BankDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(BankDetailsService.class);

    // Validation constants
    private static final int MAX_NAME_LENGTH = 255;
    private static final int MAX_BANK_NAME_LENGTH = 255;
    private static final int MIN_IBAN_LENGTH = 15;
    private static final int MAX_IBAN_LENGTH = 34;
    private static final int MIN_BIC_SWIFT_LENGTH = 8;
    private static final int MAX_BIC_SWIFT_LENGTH = 11;

    private final BankDetailsRepositoryImpl bankDetailsRepository;
    private final BankDetailsConverter bankDetailsConverter;

    public BankDetailsService(BankDetailsRepositoryImpl bankDetailsRepository,
                            BankDetailsConverter bankDetailsConverter) {
        this.bankDetailsRepository = bankDetailsRepository;
        this.bankDetailsConverter = bankDetailsConverter;
    }

    /**
     * Retrieves all active bank details for a specific account.
     *
     * @param accountId the account ID for multi-tenancy
     * @return list of bank details responses
     */
    @Transactional(readOnly = true)
    public List<BankDetailsResponse> getAllBankDetails(Long accountId) {
        logger.debug("Retrieving all bank details for account: {}", accountId);

        List<BankDetails> bankDetailsList = bankDetailsRepository.findAllByAccountId(accountId);

        List<BankDetailsResponse> responses = bankDetailsList.stream()
                .map(bankDetailsConverter::toResponse)
                .collect(Collectors.toList());

        logger.debug("Successfully retrieved {} bank details for account: {}", responses.size(), accountId);
        return responses;
    }

    /**
     * Retrieves a specific bank detail by ID and account ID.
     *
     * @param id the bank detail ID
     * @param accountId the account ID for multi-tenancy
     * @return the bank detail response
     * @throws NotFoundException if bank detail not found or access denied
     */
    @Transactional(readOnly = true)
    public BankDetailsResponse getBankDetailsById(Long id, Long accountId) {
        logger.debug("Retrieving bank detail with ID: {} for account: {}", id, accountId);

        BankDetails bankDetails = bankDetailsRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> {
                    logger.warn("Bank detail not found: ID={}, accountId={}", id, accountId);
                    return new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                            "Bank detail not found or access denied");
                });

        BankDetailsResponse response = bankDetailsConverter.toResponse(bankDetails);
        logger.debug("Successfully retrieved bank detail: {} for account: {}", response.getName(), accountId);
        return response;
    }

    /**
     * Creates a new bank detail.
     *
     * @param request the create request
     * @param accountId the account ID for multi-tenancy
     * @return the created bank detail response
     * @throws BadRequestException if validation fails
     */
    @Transactional
    public BankDetailsResponse createBankDetails(BankDetailsCreateRequest request, Long accountId) {
        logger.debug("Creating new bank detail for account: {}", accountId);

        // Convert request to entity
        BankDetails bankDetails = bankDetailsConverter.toBankDetails(request, accountId);

        // Create the bank detail
        bankDetailsRepository.insert(bankDetails);

        logger.info("Successfully created bank detail: {} for account: {}",
                bankDetails.getName(), accountId);

        return bankDetailsConverter.toResponse(bankDetails);
    }

    /**
     * Updates an existing bank detail.
     *
     * @param id the bank detail ID
     * @param request the update request
     * @param accountId the account ID for multi-tenancy
     * @return the updated bank detail response
     * @throws NotFoundException if bank detail not found or access denied
     * @throws BadRequestException if validation fails
     */
    @Transactional
    public BankDetailsResponse updateBankDetails(Long id, BankDetailsUpdateRequest request, Long accountId) {
        logger.debug("Updating bank detail with ID: {} for account: {}", id, accountId);

        // Find existing bank detail
        BankDetails existingBankDetails = bankDetailsRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> {
                    logger.warn("Bank detail not found for update: ID={}, accountId={}", id, accountId);
                    return new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                            "Bank detail not found or access denied");
                });

        // Validate name if provided
        if (request.getName() != null && request.getName().trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.FIELD_REQUIRED, "Name cannot be empty");
        }

        // Update the bank detail
        BankDetails updatedBankDetails = bankDetailsConverter.updateBankDetails(existingBankDetails, request);
        bankDetailsRepository.update(updatedBankDetails);

        logger.info("Successfully updated bank detail: {} for account: {}",
                updatedBankDetails.getName(), accountId);

        return bankDetailsConverter.toResponse(updatedBankDetails);
    }

    /**
     * Soft deletes a bank detail.
     *
     * @param id the bank detail ID
     * @param accountId the account ID for multi-tenancy
     * @throws NotFoundException if bank detail not found or access denied
     */
    @Transactional
    public void deleteBankDetails(Long id, Long accountId) {
        logger.debug("Deleting bank detail with ID: {} for account: {}", id, accountId);

        boolean deleted = bankDetailsRepository.softDelete(id, accountId);
        if (!deleted) {
            logger.warn("Bank detail not found for deletion: ID={}, accountId={}", id, accountId);
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    "Bank detail not found or access denied");
        }

        logger.info("Successfully deleted bank detail with ID: {} for account: {}", id, accountId);
    }
}
