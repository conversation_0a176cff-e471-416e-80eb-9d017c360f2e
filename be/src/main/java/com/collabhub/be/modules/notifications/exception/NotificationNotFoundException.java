package com.collabhub.be.modules.notifications.exception;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;

/**
 * Exception thrown when a requested notification is not found or not accessible to the user.
 *
 * <p>This exception is typically thrown when:</p>
 * <ul>
 *   <li>A notification with the specified ID does not exist</li>
 *   <li>A notification exists but does not belong to the current user</li>
 *   <li>A notification has been deleted or is no longer accessible</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public class NotificationNotFoundException extends NotFoundException {

    private static final String DEFAULT_MESSAGE = "Notification not found or not accessible";

    /**
     * Constructs a new NotificationNotFoundException with a default message.
     */
    public NotificationNotFoundException() {
        super(ErrorCode.NOTIFICATION_NOT_FOUND, DEFAULT_MESSAGE);
    }

    /**
     * Constructs a new NotificationNotFoundException with the specified detail message.
     *
     * @param message the detail message
     */
    public NotificationNotFoundException(String message) {
        super(ErrorCode.NOTIFICATION_NOT_FOUND, message);
    }

    /**
     * Constructs a new NotificationNotFoundException with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public NotificationNotFoundException(String message, Throwable cause) {
        super(ErrorCode.NOTIFICATION_NOT_FOUND, message, cause);
    }

    /**
     * Constructs a new NotificationNotFoundException with the specified cause.
     *
     * @param cause the cause
     */
    public NotificationNotFoundException(Throwable cause) {
        super(ErrorCode.NOTIFICATION_NOT_FOUND, DEFAULT_MESSAGE, cause);
    }
}
