package com.collabhub.be.modules.invoice.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.invoice.dto.InvoiceItemRequest;
import org.jooq.generated.tables.pojos.Invoice;
import org.jooq.generated.tables.pojos.InvoiceItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Service for handling invoice calculations.
 * Provides accurate monetary calculations with proper rounding and validation.
 */
@Service
public class InvoiceCalculationService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceCalculationService.class);
    
    // Constants for validation
    private static final BigDecimal MAX_QUANTITY = new BigDecimal("999999.999");
    private static final BigDecimal MAX_UNIT_PRICE = new BigDecimal("999999999.99");
    private static final BigDecimal MAX_VAT_RATE = BigDecimal.ONE; // 100%
    private static final BigDecimal MAX_TOTAL_AMOUNT = new BigDecimal("999999999999.99");

    /**
     * Calculates line total for an invoice item.
     * Formula: quantity * unit_price
     *
     * @param quantity the quantity
     * @param unitPrice the unit price
     * @return the calculated line total
     * @throws BadRequestException if values are invalid
     */
    public BigDecimal calculateLineTotal(BigDecimal quantity, BigDecimal unitPrice) {
        validateCalculationInputs(quantity, unitPrice);

        return quantity.multiply(unitPrice).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Calculates VAT amount for a line total.
     * Formula: line_total * vat_rate
     *
     * @param lineTotal the line total
     * @param vatRate the VAT rate (0-1)
     * @return the calculated VAT amount
     * @throws BadRequestException if values are invalid
     */
    public BigDecimal calculateVatAmount(BigDecimal lineTotal, BigDecimal vatRate) {
        if (lineTotal == null || vatRate == null) {
            return BigDecimal.ZERO;
        }

        validateVatRate(vatRate);

        return lineTotal.multiply(vatRate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Calculates totals for an invoice based on its items.
     *
     * @param items the invoice items
     * @return calculation result containing subtotal, total VAT, and total amount
     * @throws BadRequestException if calculations result in invalid amounts
     */
    public InvoiceCalculationResult calculateInvoiceTotals(List<InvoiceItem> items) {
        logger.debug("Calculating invoice totals for {} items", items != null ? items.size() : 0);

        if (items == null || items.isEmpty()) {
            return new InvoiceCalculationResult(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }

        BigDecimal subtotal = BigDecimal.ZERO;
        BigDecimal totalVat = BigDecimal.ZERO;

        for (InvoiceItem item : items) {
            if (item.getLineTotal() != null) {
                subtotal = subtotal.add(item.getLineTotal());
            }
            if (item.getVatAmount() != null) {
                totalVat = totalVat.add(item.getVatAmount());
            }
        }

        BigDecimal totalAmount = subtotal.add(totalVat);

        // Validate final amounts
        validateFinalAmounts(subtotal, totalVat, totalAmount);

        logger.debug("Calculated totals - Subtotal: {}, VAT: {}, Total: {}", subtotal, totalVat, totalAmount);

        return new InvoiceCalculationResult(subtotal, totalVat, totalAmount);
    }

    /**
     * Calculates totals from item requests (before creating invoice items).
     *
     * @param itemRequests the invoice item requests
     * @return calculation result containing subtotal, total VAT, and total amount
     * @throws BadRequestException if calculations result in invalid amounts
     */
    public InvoiceCalculationResult calculateTotalsFromRequests(List<InvoiceItemRequest> itemRequests) {
        logger.debug("Calculating totals from {} item requests", itemRequests != null ? itemRequests.size() : 0);

        if (itemRequests == null || itemRequests.isEmpty()) {
            return new InvoiceCalculationResult(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }

        BigDecimal subtotal = BigDecimal.ZERO;
        BigDecimal totalVat = BigDecimal.ZERO;

        for (InvoiceItemRequest request : itemRequests) {
            BigDecimal lineTotal = calculateLineTotal(request.getQuantity(), request.getUnitPrice());
            BigDecimal vatAmount = calculateVatAmount(lineTotal, request.getVatRate());

            subtotal = subtotal.add(lineTotal);
            totalVat = totalVat.add(vatAmount);
        }

        BigDecimal totalAmount = subtotal.add(totalVat);

        // Validate final amounts
        validateFinalAmounts(subtotal, totalVat, totalAmount);

        logger.debug("Calculated totals from requests - Subtotal: {}, VAT: {}, Total: {}", subtotal, totalVat, totalAmount);

        return new InvoiceCalculationResult(subtotal, totalVat, totalAmount);
    }

    /**
     * Updates invoice with calculated totals.
     *
     * @param invoice the invoice to update
     * @param calculationResult the calculation result
     */
    public void updateInvoiceWithTotals(Invoice invoice, InvoiceCalculationResult calculationResult) {
        if (invoice == null || calculationResult == null) {
            return;
        }

        invoice.setSubtotal(calculationResult.getSubtotal());
        invoice.setTotalVat(calculationResult.getTotalVat());
        invoice.setTotalAmount(calculationResult.getTotalAmount());

        logger.debug("Updated invoice {} with calculated totals", invoice.getId());
    }

    /**
     * Validates that calculated amounts match stored amounts.
     * Used for data integrity checks.
     *
     * @param invoice the invoice to validate
     * @param items the invoice items
     * @return true if amounts are correctly calculated
     */
    public boolean validateInvoiceCalculations(Invoice invoice, List<InvoiceItem> items) {
        if (invoice == null) {
            return false;
        }

        try {
            InvoiceCalculationResult calculated = calculateInvoiceTotals(items);

            boolean subtotalMatches = invoice.getSubtotal().compareTo(calculated.getSubtotal()) == 0;
            boolean vatMatches = invoice.getTotalVat().compareTo(calculated.getTotalVat()) == 0;
            boolean totalMatches = invoice.getTotalAmount().compareTo(calculated.getTotalAmount()) == 0;

            boolean isValid = subtotalMatches && vatMatches && totalMatches;

            if (!isValid) {
                logger.warn("Invoice calculation validation failed for invoice {}: " +
                           "Expected subtotal={}, VAT={}, total={} but got subtotal={}, VAT={}, total={}",
                           invoice.getId(),
                           calculated.getSubtotal(), calculated.getTotalVat(), calculated.getTotalAmount(),
                           invoice.getSubtotal(), invoice.getTotalVat(), invoice.getTotalAmount());
            }

            return isValid;
        } catch (Exception e) {
            logger.error("Error validating invoice calculations for invoice {}: {}", invoice.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * Formats currency amount for display.
     *
     * @param amount the amount to format
     * @param currency the currency code
     * @return formatted currency string
     */
    public String formatCurrency(BigDecimal amount, String currency) {
        if (amount == null) {
            return "0.00";
        }

        String formattedAmount = amount.setScale(2, RoundingMode.HALF_UP).toString();
        
        // Add currency symbol based on currency code
        return switch (currency != null ? currency.toUpperCase() : InvoiceConstants.DEFAULT_CURRENCY) {
            case "USD" -> InvoiceConstants.USD_SYMBOL + formattedAmount;
            case "GBP" -> InvoiceConstants.GBP_SYMBOL + formattedAmount;
            case "EUR" -> InvoiceConstants.EUR_SYMBOL + formattedAmount;
            default -> formattedAmount + " " + currency;
        };
    }

    /**
     * Validates calculation inputs for safety.
     */
    private void validateCalculationInputs(BigDecimal quantity, BigDecimal unitPrice) {
        if (quantity == null || unitPrice == null) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "Quantity and unit price cannot be null");
        }

        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "Quantity must be greater than zero");
        }

        if (unitPrice.compareTo(BigDecimal.ZERO) < 0) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "Unit price cannot be negative");
        }

        if (quantity.compareTo(MAX_QUANTITY) > 0) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "Quantity exceeds maximum allowed value");
        }

        if (unitPrice.compareTo(MAX_UNIT_PRICE) > 0) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "Unit price exceeds maximum allowed value");
        }
    }

    /**
     * Validates VAT rate.
     */
    private void validateVatRate(BigDecimal vatRate) {
        if (vatRate.compareTo(BigDecimal.ZERO) < 0) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "VAT rate cannot be negative");
        }

        if (vatRate.compareTo(MAX_VAT_RATE) > 0) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "VAT rate cannot exceed 100%");
        }
    }

    /**
     * Validates final calculated amounts.
     */
    private void validateFinalAmounts(BigDecimal subtotal, BigDecimal totalVat, BigDecimal totalAmount) {
        if (totalAmount.compareTo(MAX_TOTAL_AMOUNT) > 0) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "Total amount exceeds maximum allowed value");
        }

        if (subtotal.compareTo(BigDecimal.ZERO) < 0 || totalVat.compareTo(BigDecimal.ZERO) < 0) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, "Calculated amounts cannot be negative");
        }
    }

    /**
     * Result class for invoice calculations.
     */
    public static class InvoiceCalculationResult {
        private final BigDecimal subtotal;
        private final BigDecimal totalVat;
        private final BigDecimal totalAmount;

        public InvoiceCalculationResult(BigDecimal subtotal, BigDecimal totalVat, BigDecimal totalAmount) {
            this.subtotal = subtotal;
            this.totalVat = totalVat;
            this.totalAmount = totalAmount;
        }

        public BigDecimal getSubtotal() { return subtotal; }
        public BigDecimal getTotalVat() { return totalVat; }
        public BigDecimal getTotalAmount() { return totalAmount; }

        @Override
        public String toString() {
            return "InvoiceCalculationResult{" +
                    "subtotal=" + subtotal +
                    ", totalVat=" + totalVat +
                    ", totalAmount=" + totalAmount +
                    '}';
        }
    }
}
