package com.collabhub.be.modules.posts.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.PostCommentDao;
import org.jooq.generated.tables.pojos.PostComment;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.jooq.generated.tables.PostComment.POST_COMMENT;
import static org.jooq.generated.tables.Post.POST;
import static org.jooq.generated.tables.CollaborationHub.COLLABORATION_HUB;

/**
 * Repository for PostComment entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy support.
 */
@Repository
@Transactional
public class PostCommentRepositoryImpl extends PostCommentDao {

    private final DSLContext dsl;

    public PostCommentRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds comments for a specific post with pagination, ordered by creation date (newest first).
     * Validates account access.
     */
    public List<PostComment> findByPostIdWithPagination(Long postId, Long accountId, int offset, int limit) {
        return dsl.select(POST_COMMENT)
                .from(POST_COMMENT)
                .join(POST).on(POST.ID.eq(POST_COMMENT.POST_ID))
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(POST.HUB_ID))
                .where(POST_COMMENT.POST_ID.eq(postId))
                .and(POST.DELETED_AT.isNull()) // Exclude comments on deleted posts
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .orderBy(POST_COMMENT.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(PostComment.class);
    }

    /**
     * Counts comments for a specific post.
     * Validates account access.
     */
    public long countByPostId(Long postId, Long accountId) {
        return dsl.selectCount()
                .from(POST_COMMENT)
                .join(POST).on(POST.ID.eq(POST_COMMENT.POST_ID))
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(POST.HUB_ID))
                .where(POST_COMMENT.POST_ID.eq(postId))
                .and(POST.DELETED_AT.isNull()) // Exclude comments on deleted posts
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .fetchOne(0, Long.class);
    }

    /**
     * Finds a comment by ID with account validation.
     */
    public Optional<PostComment> findByIdWithAccountValidation(Long commentId, Long accountId) {
        return dsl.select(POST_COMMENT.fields())
                .from(POST_COMMENT)
                .join(POST).on(POST.ID.eq(POST_COMMENT.POST_ID))
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(POST.HUB_ID))
                .where(POST_COMMENT.ID.eq(commentId))
                .and(POST.DELETED_AT.isNull()) // Exclude comments on deleted posts
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .fetchOptionalInto(PostComment.class);
    }

    /**
     * Deletes a comment permanently.
     *
     * @param commentId the comment ID
     * @return true if the comment was deleted, false if not found
     */
    public boolean deleteComment(Long commentId) {
        int deleted = dsl.deleteFrom(POST_COMMENT)
                .where(POST_COMMENT.ID.eq(commentId))
                .execute();

        return deleted > 0;
    }

    /**
     * Updates a comment's content and updated_at timestamp.
     *
     * @param commentId the comment ID
     * @param content the new content
     * @return true if the comment was updated, false if not found
     */
    public boolean updateCommentContent(Long commentId, String content) {
        int updated = dsl.update(POST_COMMENT)
                .set(POST_COMMENT.CONTENT, content)
                .set(POST_COMMENT.UPDATED_AT, org.jooq.impl.DSL.currentLocalDateTime())
                .where(POST_COMMENT.ID.eq(commentId))
                .execute();

        return updated > 0;
    }

    /**
     * Checks if a user is the author of a specific comment.
     */
    public boolean isCommentAuthor(Long commentId, Long participantId) {
        return dsl.selectCount()
                .from(POST_COMMENT)
                .where(POST_COMMENT.ID.eq(commentId))
                .and(POST_COMMENT.PARTICIPANT_ID.eq(participantId))
                .fetchOne(0, Integer.class) > 0;
    }

    /**
     * Bulk loads comment counts for multiple posts to avoid N+1 queries.
     * Returns a map of postId -> comment count.
     * Validates account access and excludes comments on deleted posts.
     */
    public Map<Long, Integer> bulkCountCommentsByPostIds(List<Long> postIds, Long accountId) {
        if (postIds.isEmpty()) {
            return Map.of();
        }

        return dsl.select(POST_COMMENT.POST_ID, org.jooq.impl.DSL.count())
                .from(POST_COMMENT)
                .join(POST).on(POST.ID.eq(POST_COMMENT.POST_ID))
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(POST.HUB_ID))
                .where(POST_COMMENT.POST_ID.in(postIds))
                .and(POST.DELETED_AT.isNull()) // Exclude comments on deleted posts
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .groupBy(POST_COMMENT.POST_ID)
                .fetchMap(POST_COMMENT.POST_ID, org.jooq.impl.DSL.count().coerce(Integer.class));
    }

    /**
     * Finds distinct participant IDs who have commented on a specific post.
     * Excludes the specified participant ID to avoid self-notification.
     *
     * @param postId the post ID to find commenters for
     * @param excludeParticipantId the participant ID to exclude from results
     * @return list of distinct participant IDs who have commented on the post
     */
    public List<Long> findDistinctCommentersByPostId(Long postId, Long excludeParticipantId) {
        return dsl.selectDistinct(POST_COMMENT.PARTICIPANT_ID)
                .from(POST_COMMENT)
                .join(POST).on(POST.ID.eq(POST_COMMENT.POST_ID))
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(POST.HUB_ID))
                .where(POST_COMMENT.POST_ID.eq(postId))
                .and(POST_COMMENT.PARTICIPANT_ID.ne(excludeParticipantId))
                .and(POST.DELETED_AT.isNull()) // Exclude comments on deleted posts
                .fetchInto(Long.class);
    }
}
