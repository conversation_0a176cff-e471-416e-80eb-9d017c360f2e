package com.collabhub.be.modules.bankdetails.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.BankDetailsDao;
import org.jooq.generated.tables.pojos.BankDetails;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.generated.tables.BankDetails.BANK_DETAILS;

/**
 * Repository for Bank Details entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy and soft deletion support.
 */
@Repository
public class BankDetailsRepositoryImpl extends BankDetailsDao {

    private final DSLContext dsl;

    public BankDetailsRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all active (not soft deleted) bank details for a specific account.
     *
     * @param accountId the account ID for multi-tenancy
     * @return list of active bank details
     */
    public List<BankDetails> findAllByAccountId(Long accountId) {
        return dsl.select()
                .from(BANK_DETAILS)
                .where(BANK_DETAILS.ACCOUNT_ID.eq(accountId))
                .and(BANK_DETAILS.DELETED_AT.isNull())
                .orderBy(BANK_DETAILS.NAME.asc())
                .fetchInto(BankDetails.class);
    }

    /**
     * Finds an active bank detail by ID and account ID.
     *
     * @param id the bank detail ID
     * @param accountId the account ID for multi-tenancy
     * @return optional containing the bank detail if found and active
     */
    public Optional<BankDetails> findByIdAndAccountId(Long id, Long accountId) {
        return dsl.select()
                .from(BANK_DETAILS)
                .where(BANK_DETAILS.ID.eq(id))
                .and(BANK_DETAILS.ACCOUNT_ID.eq(accountId))
                .and(BANK_DETAILS.DELETED_AT.isNull())
                .fetchOptionalInto(BankDetails.class);
    }

    /**
     * Soft deletes a bank detail by setting deleted_at timestamp.
     *
     * @param id the bank detail ID
     * @param accountId the account ID for multi-tenancy
     * @return true if the bank detail was soft deleted, false if not found
     */
    public boolean softDelete(Long id, Long accountId) {
        int rowsAffected = dsl.update(BANK_DETAILS)
                .set(BANK_DETAILS.DELETED_AT, LocalDateTime.now())
                .where(BANK_DETAILS.ID.eq(id))
                .and(BANK_DETAILS.ACCOUNT_ID.eq(accountId))
                .and(BANK_DETAILS.DELETED_AT.isNull())
                .execute();

        return rowsAffected > 0;
    }

    /**
     * Checks if bank details exist by ID and account ID.
     *
     * @param id the bank details ID
     * @param accountId the account ID for multi-tenancy
     * @return true if the bank details exist and are active, false otherwise
     */
    public boolean existsByIdAndAccountId(Long id, Long accountId) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(BANK_DETAILS)
                        .where(BANK_DETAILS.ID.eq(id))
                        .and(BANK_DETAILS.ACCOUNT_ID.eq(accountId))
                        .and(BANK_DETAILS.DELETED_AT.isNull())
        );
    }
}
