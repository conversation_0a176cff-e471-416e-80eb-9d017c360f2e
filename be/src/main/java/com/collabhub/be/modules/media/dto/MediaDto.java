package com.collabhub.be.modules.media.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

/**
 * DTO for media file information.
 * Represents a centralized media file with metadata.
 */
public class MediaDto {

    private Long id;

    @JsonProperty("account_id")
    private Long accountId;

    @JsonProperty("original_filename")
    private String originalFilename;

    @JsonProperty("file_size_bytes")
    private Long fileSizeBytes;

    @JsonProperty("file_extension")
    private String fileExtension;

    @JsonProperty("mime_type")
    private String mimeType;

    @JsonProperty("s3_key")
    private String s3Key;

    @JsonProperty("s3_bucket")
    private String s3Bucket;

    private String url; // Generated S3 URL for frontend consumption

    private String type; // "image" or "video" for frontend compatibility

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    @JsonProperty("deleted_at")
    private LocalDateTime deletedAt;

    public MediaDto() {}

    public MediaDto(Long id, Long accountId, String originalFilename, Long fileSizeBytes,
                   String fileExtension, String mimeType, String s3Key, String s3Bucket,
                   String url, String type, LocalDateTime createdAt, LocalDateTime updatedAt,
                   LocalDateTime deletedAt) {
        this.id = id;
        this.accountId = accountId;
        this.originalFilename = originalFilename;
        this.fileSizeBytes = fileSizeBytes;
        this.fileExtension = fileExtension;
        this.mimeType = mimeType;
        this.s3Key = s3Key;
        this.s3Bucket = s3Bucket;
        this.url = url;
        this.type = type;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.deletedAt = deletedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }

    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getS3Key() {
        return s3Key;
    }

    public void setS3Key(String s3Key) {
        this.s3Key = s3Key;
    }

    public String getS3Bucket() {
        return s3Bucket;
    }

    public void setS3Bucket(String s3Bucket) {
        this.s3Bucket = s3Bucket;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    public String toString() {
        return "MediaDto{" +
                "id=" + id +
                ", accountId=" + accountId +
                ", originalFilename='" + originalFilename + '\'' +
                ", fileSizeBytes=" + fileSizeBytes +
                ", fileExtension='" + fileExtension + '\'' +
                ", mimeType='" + mimeType + '\'' +
                ", s3Key='" + s3Key + '\'' +
                ", s3Bucket='" + s3Bucket + '\'' +
                ", url='" + url + '\'' +
                ", type='" + type + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", deletedAt=" + deletedAt +
                '}';
    }
}
