package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotBlank;

/**
 * Service for handling magic link authentication for external hub participants.
 * Validates magic link tokens and manages external user access to collaboration hubs.
 * Provides secure token validation and participant access management.
 */
@Service
public class MagicLinkAuthService {

    private static final Logger logger = LoggerFactory.getLogger(MagicLinkAuthService.class);

    // Constants
    private static final int TOKEN_LOG_LENGTH = 8;
    private static final String TOKEN_LOG_SUFFIX = "...";

    // Error Messages
    private static final String MAGIC_LINK_TOKEN_REQUIRED_MESSAGE = "Magic link token is required";
    private static final String INVALID_EXPIRED_TOKEN_MESSAGE = "Invalid or expired magic link token";

    // Log Messages
    private static final String VALIDATING_TOKEN_LOG = "Validating magic link token: {}";
    private static final String MARKED_PARTICIPANT_JOINED_LOG = "Marked participant {} as joined for first time";
    private static final String VALIDATED_TOKEN_SUCCESS_LOG = "Successfully validated magic link token for participant {} in hub {}";
    private static final String REVOKING_TOKEN_LOG = "Revoking magic link token for participant {}";
    private static final String REVOKED_TOKEN_SUCCESS_LOG = "Successfully revoked magic link token for participant {}";
    private static final String REVOKE_TOKEN_FAILED_LOG = "Failed to revoke magic link token for participant {}";

    private final HubParticipantRepositoryImpl participantRepository;

    public MagicLinkAuthService(HubParticipantRepositoryImpl participantRepository) {
        this.participantRepository = participantRepository;
    }

    /**
     * Validates a magic link token and returns participant information.
     * Marks the participant as joined if this is their first access.
     *
     * @param token the magic link token
     * @return participant information if token is valid
     * @throws NotFoundException if token is invalid or expired
     */
    @Transactional
    public MagicLinkValidationResult validateMagicLinkToken(@NotBlank String token) {
        logger.info(VALIDATING_TOKEN_LOG, truncateTokenForLogging(token));

        validateTokenInput(token);
        HubParticipant participant = findParticipantByToken(token);
        boolean isFirstAccess = markParticipantAsJoinedIfFirstAccess(participant);

        logger.info(VALIDATED_TOKEN_SUCCESS_LOG, participant.getId(), participant.getHubId());

        return createValidationResult(participant, isFirstAccess);
    }

    /**
     * Revokes a magic link token, preventing further access.
     *
     * @param participantId the participant ID
     * @return true if token was revoked successfully
     */
    @Transactional
    public boolean revokeMagicLinkToken(Long participantId) {
        logger.info(REVOKING_TOKEN_LOG, participantId);

        boolean revoked = participantRepository.revokeMagicLinkToken(participantId);
        logRevocationResult(participantId, revoked);

        return revoked;
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Validates token input.
     */
    private void validateTokenInput(String token) {
        if (token == null || token.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, MAGIC_LINK_TOKEN_REQUIRED_MESSAGE);
        }
    }

    /**
     * Finds participant by magic link token.
     */
    private HubParticipant findParticipantByToken(String token) {
        return participantRepository.findByMagicLinkToken(token)
                .orElseThrow(() -> new NotFoundException(ErrorCode.INVALID_TOKEN, INVALID_EXPIRED_TOKEN_MESSAGE));
    }

    /**
     * Marks participant as joined if this is their first access.
     */
    private boolean markParticipantAsJoinedIfFirstAccess(HubParticipant participant) {
        boolean isFirstAccess = participant.getJoinedAt() == null;
        if (isFirstAccess) {
            boolean updated = participantRepository.markParticipantAsJoined(participant.getId());
            if (updated) {
                logger.info(MARKED_PARTICIPANT_JOINED_LOG, participant.getId());
            }
        }
        return isFirstAccess;
    }

    /**
     * Creates validation result.
     */
    private MagicLinkValidationResult createValidationResult(HubParticipant participant, boolean isFirstAccess) {
        return new MagicLinkValidationResult(
                participant.getId(),
                participant.getHubId(),
                participant.getEmail(),
                participant.getRole(),
                isFirstAccess
        );
    }

    /**
     * Truncates token for safe logging.
     */
    private String truncateTokenForLogging(String token) {
        return token.substring(0, Math.min(TOKEN_LOG_LENGTH, token.length())) + TOKEN_LOG_SUFFIX;
    }

    /**
     * Logs revocation result.
     */
    private void logRevocationResult(Long participantId, boolean revoked) {
        if (revoked) {
            logger.info(REVOKED_TOKEN_SUCCESS_LOG, participantId);
        } else {
            logger.warn(REVOKE_TOKEN_FAILED_LOG, participantId);
        }
    }

    /**
     * Result of magic link token validation.
     */
    public static class MagicLinkValidationResult {
        private final Long participantId;
        private final Long hubId;
        private final String email;
        private final org.jooq.generated.enums.HubParticipantRole role;
        private final boolean isFirstAccess;

        public MagicLinkValidationResult(Long participantId, Long hubId, String email,
                                       org.jooq.generated.enums.HubParticipantRole role, boolean isFirstAccess) {
            this.participantId = participantId;
            this.hubId = hubId;
            this.email = email;
            this.role = role;
            this.isFirstAccess = isFirstAccess;
        }

        public Long getParticipantId() {
            return participantId;
        }

        public Long getHubId() {
            return hubId;
        }

        public String getEmail() {
            return email;
        }

        public org.jooq.generated.enums.HubParticipantRole getRole() {
            return role;
        }

        public boolean isFirstAccess() {
            return isFirstAccess;
        }

        @Override
        public String toString() {
            return "MagicLinkValidationResult{" +
                    "participantId=" + participantId +
                    ", hubId=" + hubId +
                    ", email='" + email + '\'' +
                    ", role=" + role +
                    ", isFirstAccess=" + isFirstAccess +
                    '}';
        }
    }
}
