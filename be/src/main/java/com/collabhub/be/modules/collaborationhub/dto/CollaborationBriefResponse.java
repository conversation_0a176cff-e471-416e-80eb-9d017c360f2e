package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Response DTO for collaboration brief details.
 * Contains complete brief information including metadata.
 * All briefs are visible to all participants within the hub.
 */
public class CollaborationBriefResponse {

    @NotNull
    private Long id;

    @NotNull
    private Long hubId;

    @NotNull
    private String title;

    private String body;

    @NotNull
    private Long createdByParticipantId;

    private String createdByParticipantName;

    @NotNull
    private LocalDateTime createdAt;

    @NotNull
    private LocalDateTime updatedAt;

    public CollaborationBriefResponse() {}

    public CollaborationBriefResponse(Long id, Long hubId, String title, String body,
                                      Long createdByParticipantId, String createdByParticipantName,
                                      LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.hubId = hubId;
        this.title = title;
        this.body = body;
        this.createdByParticipantId = createdByParticipantId;
        this.createdByParticipantName = createdByParticipantName;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getHubId() {
        return hubId;
    }

    public void setHubId(Long hubId) {
        this.hubId = hubId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Long getCreatedByParticipantId() {
        return createdByParticipantId;
    }

    public void setCreatedByParticipantId(Long createdByParticipantId) {
        this.createdByParticipantId = createdByParticipantId;
    }

    public String getCreatedByParticipantName() {
        return createdByParticipantName;
    }

    public void setCreatedByParticipantName(String createdByParticipantName) {
        this.createdByParticipantName = createdByParticipantName;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "CollaborationBriefResponse{" +
                "id=" + id +
                ", hubId=" + hubId +
                ", title='" + title + '\'' +
                ", body='" + (body != null ? body.substring(0, Math.min(body.length(), 50)) + "..." : null) + '\'' +
                ", createdByParticipantId=" + createdByParticipantId +
                ", createdByParticipantName='" + createdByParticipantName + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
