package com.collabhub.be.modules.brands.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for brand operations.
 * Contains all brand information including associated contacts and metadata.
 */
public class BrandResponse {

    @NotNull
    private Long id;

    @NotNull
    @JsonProperty("account_id")
    private Long accountId;

    @NotNull
    private String name;

    @NotNull
    @JsonProperty("company_name")
    private String companyName;

    @JsonProperty("address_street")
    private String addressStreet;

    @JsonProperty("address_city")
    private String addressCity;

    @JsonProperty("address_postal_code")
    private String addressPostalCode;

    @JsonProperty("address_country")
    private String addressCountry;

    @JsonProperty("vat_number")
    private String vatNumber;

    @JsonProperty("registration_number")
    private String registrationNumber;

    private String phone;

    private String email;

    private String website;

    @NotNull
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @NotNull
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    @NotNull
    private List<BrandContactResponse> contacts;

    public BrandResponse() {
    }

    public BrandResponse(Long id, Long accountId, String name, String companyName, String addressStreet,
                       String addressCity, String addressPostalCode, String addressCountry,
                       String vatNumber, String registrationNumber, String phone, String email,
                       String website, LocalDateTime createdAt, LocalDateTime updatedAt,
                       List<BrandContactResponse> contacts) {
        this.id = id;
        this.accountId = accountId;
        this.name = name;
        this.companyName = companyName;
        this.addressStreet = addressStreet;
        this.addressCity = addressCity;
        this.addressPostalCode = addressPostalCode;
        this.addressCountry = addressCountry;
        this.vatNumber = vatNumber;
        this.registrationNumber = registrationNumber;
        this.phone = phone;
        this.email = email;
        this.website = website;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.contacts = contacts;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getAddressStreet() {
        return addressStreet;
    }

    public void setAddressStreet(String addressStreet) {
        this.addressStreet = addressStreet;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressPostalCode() {
        return addressPostalCode;
    }

    public void setAddressPostalCode(String addressPostalCode) {
        this.addressPostalCode = addressPostalCode;
    }

    public String getAddressCountry() {
        return addressCountry;
    }

    public void setAddressCountry(String addressCountry) {
        this.addressCountry = addressCountry;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<BrandContactResponse> getContacts() {
        return contacts;
    }

    public void setContacts(List<BrandContactResponse> contacts) {
        this.contacts = contacts;
    }

    @Override
    public String toString() {
        return "BrandResponse{" +
                "id=" + id +
                ", accountId=" + accountId +
                ", name='" + name + '\'' +
                ", companyName='" + companyName + '\'' +
                ", addressStreet='" + addressStreet + '\'' +
                ", addressCity='" + addressCity + '\'' +
                ", addressPostalCode='" + addressPostalCode + '\'' +
                ", addressCountry='" + addressCountry + '\'' +
                ", vatNumber='" + vatNumber + '\'' +
                ", registrationNumber='" + registrationNumber + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", website='" + website + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", contacts=" + contacts +
                '}';
    }
}
