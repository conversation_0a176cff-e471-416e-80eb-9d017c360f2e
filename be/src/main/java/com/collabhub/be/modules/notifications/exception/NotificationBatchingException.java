package com.collabhub.be.modules.notifications.exception;

/**
 * Base exception class for all notification batching related errors.
 * 
 * <p>This exception serves as the root of the notification batching exception hierarchy,
 * providing common functionality and ensuring consistent error handling across the
 * batching subsystem.</p>
 * 
 * <h3>Exception Hierarchy:</h3>
 * <pre>
 * NotificationBatchingException
 * ├── BatchProcessingException
 * │   ├── BatchLockException
 * │   ├── BatchQueueException
 * │   └── BatchDeliveryException
 * ├── NotificationValidationException
 * └── BatchConfigurationException
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public class NotificationBatchingException extends RuntimeException {
    
    /**
     * The error code associated with this exception for programmatic handling.
     */
    private final String errorCode;
    
    /**
     * Constructs a new NotificationBatchingException with the specified detail message.
     * 
     * @param message the detail message explaining the error
     */
    public NotificationBatchingException(String message) {
        super(message);
        this.errorCode = "BATCHING_ERROR";
    }
    
    /**
     * Constructs a new NotificationBatchingException with the specified detail message and cause.
     * 
     * @param message the detail message explaining the error
     * @param cause the underlying cause of this exception
     */
    public NotificationBatchingException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "BATCHING_ERROR";
    }
    
    /**
     * Constructs a new NotificationBatchingException with the specified detail message and error code.
     * 
     * @param message the detail message explaining the error
     * @param errorCode the error code for programmatic handling
     */
    public NotificationBatchingException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * Constructs a new NotificationBatchingException with the specified detail message, cause, and error code.
     * 
     * @param message the detail message explaining the error
     * @param cause the underlying cause of this exception
     * @param errorCode the error code for programmatic handling
     */
    public NotificationBatchingException(String message, Throwable cause, String errorCode) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    /**
     * Returns the error code associated with this exception.
     * 
     * <p>Error codes provide a programmatic way to handle different types of
     * batching errors without relying on exception class hierarchy or message parsing.</p>
     * 
     * @return the error code string
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * Returns a detailed error message including the error code.
     * 
     * @return formatted error message with error code
     */
    @Override
    public String toString() {
        return String.format("%s [%s]: %s", 
            getClass().getSimpleName(), 
            errorCode, 
            getMessage());
    }
}

/**
 * Exception thrown when batch processing operations fail.
 * 
 * <p>This exception covers errors that occur during the actual processing
 * of notification batches, including email delivery failures, database
 * errors, and external service communication issues.</p>
 */
class BatchProcessingException extends NotificationBatchingException {
    
    /**
     * The batch ID associated with the failed processing operation.
     */
    private final String batchId;
    
    /**
     * The number of notifications that were being processed when the error occurred.
     */
    private final int notificationCount;
    
    /**
     * Constructs a new BatchProcessingException.
     * 
     * @param message the detail message
     * @param batchId the ID of the batch that failed processing
     * @param notificationCount the number of notifications in the failed batch
     */
    public BatchProcessingException(String message, String batchId, int notificationCount) {
        super(message, "BATCH_PROCESSING_FAILED");
        this.batchId = batchId;
        this.notificationCount = notificationCount;
    }
    
    /**
     * Constructs a new BatchProcessingException with a cause.
     * 
     * @param message the detail message
     * @param cause the underlying cause
     * @param batchId the ID of the batch that failed processing
     * @param notificationCount the number of notifications in the failed batch
     */
    public BatchProcessingException(String message, Throwable cause, String batchId, int notificationCount) {
        super(message, cause, "BATCH_PROCESSING_FAILED");
        this.batchId = batchId;
        this.notificationCount = notificationCount;
    }
    
    /**
     * Returns the batch ID associated with this processing failure.
     * 
     * @return the batch ID
     */
    public String getBatchId() {
        return batchId;
    }
    
    /**
     * Returns the number of notifications in the failed batch.
     * 
     * @return the notification count
     */
    public int getNotificationCount() {
        return notificationCount;
    }
}

/**
 * Exception thrown when distributed lock operations fail.
 * 
 * <p>This exception is thrown when the batch processor cannot acquire
 * or manage distributed locks required for coordinating batch processing
 * across multiple application instances.</p>
 */
class BatchLockException extends BatchProcessingException {
    
    /**
     * The lock key that failed to be acquired or managed.
     */
    private final String lockKey;
    
    /**
     * Constructs a new BatchLockException.
     * 
     * @param message the detail message
     * @param lockKey the lock key that failed
     */
    public BatchLockException(String message, String lockKey) {
        super(message, "batch_lock_" + lockKey, 0);
        this.lockKey = lockKey;
    }
    
    /**
     * Constructs a new BatchLockException with a cause.
     * 
     * @param message the detail message
     * @param cause the underlying cause
     * @param lockKey the lock key that failed
     */
    public BatchLockException(String message, Throwable cause, String lockKey) {
        super(message, cause, "batch_lock_" + lockKey, 0);
        this.lockKey = lockKey;
    }
    
    /**
     * Returns the lock key associated with this failure.
     * 
     * @return the lock key
     */
    public String getLockKey() {
        return lockKey;
    }
}

/**
 * Exception thrown when batch queue operations fail.
 * 
 * <p>This exception covers errors related to queuing, dequeuing, and
 * managing notifications in the batch processing queue.</p>
 */
class BatchQueueException extends BatchProcessingException {
    
    /**
     * Constructs a new BatchQueueException.
     * 
     * @param message the detail message
     * @param notificationCount the number of notifications affected
     */
    public BatchQueueException(String message, int notificationCount) {
        super(message, "batch_queue_operation", notificationCount);
    }
    
    /**
     * Constructs a new BatchQueueException with a cause.
     * 
     * @param message the detail message
     * @param cause the underlying cause
     * @param notificationCount the number of notifications affected
     */
    public BatchQueueException(String message, Throwable cause, int notificationCount) {
        super(message, cause, "batch_queue_operation", notificationCount);
    }
}

/**
 * Exception thrown when email delivery operations fail.
 * 
 * <p>This exception is specific to failures in the email delivery
 * portion of batch processing, including SMTP errors, template
 * processing failures, and recipient validation issues.</p>
 */
class BatchDeliveryException extends BatchProcessingException {
    
    /**
     * The recipient user ID associated with the delivery failure.
     */
    private final Long recipientUserId;
    
    /**
     * Constructs a new BatchDeliveryException.
     * 
     * @param message the detail message
     * @param recipientUserId the user ID of the intended recipient
     * @param notificationCount the number of notifications that failed delivery
     */
    public BatchDeliveryException(String message, Long recipientUserId, int notificationCount) {
        super(message, "batch_delivery_user_" + recipientUserId, notificationCount);
        this.recipientUserId = recipientUserId;
    }
    
    /**
     * Constructs a new BatchDeliveryException with a cause.
     * 
     * @param message the detail message
     * @param cause the underlying cause
     * @param recipientUserId the user ID of the intended recipient
     * @param notificationCount the number of notifications that failed delivery
     */
    public BatchDeliveryException(String message, Throwable cause, Long recipientUserId, int notificationCount) {
        super(message, cause, "batch_delivery_user_" + recipientUserId, notificationCount);
        this.recipientUserId = recipientUserId;
    }
    
    /**
     * Returns the recipient user ID associated with this delivery failure.
     * 
     * @return the recipient user ID
     */
    public Long getRecipientUserId() {
        return recipientUserId;
    }
}

/**
 * Exception thrown when notification validation fails.
 * 
 * <p>This exception is thrown when notification data fails validation
 * before being queued for batch processing.</p>
 */
class NotificationValidationException extends NotificationBatchingException {
    
    /**
     * The field that failed validation.
     */
    private final String fieldName;
    
    /**
     * The invalid value that caused the validation failure.
     */
    private final Object invalidValue;
    
    /**
     * Constructs a new NotificationValidationException.
     * 
     * @param message the detail message
     * @param fieldName the field that failed validation
     * @param invalidValue the invalid value
     */
    public NotificationValidationException(String message, String fieldName, Object invalidValue) {
        super(message, "VALIDATION_FAILED");
        this.fieldName = fieldName;
        this.invalidValue = invalidValue;
    }
    
    /**
     * Returns the field name that failed validation.
     * 
     * @return the field name
     */
    public String getFieldName() {
        return fieldName;
    }
    
    /**
     * Returns the invalid value that caused the validation failure.
     * 
     * @return the invalid value
     */
    public Object getInvalidValue() {
        return invalidValue;
    }
}

/**
 * Exception thrown when batch processing configuration is invalid.
 * 
 * <p>This exception is thrown during application startup or configuration
 * changes when batch processing settings are invalid or incompatible.</p>
 */
class BatchConfigurationException extends NotificationBatchingException {
    
    /**
     * The configuration property that is invalid.
     */
    private final String propertyName;
    
    /**
     * Constructs a new BatchConfigurationException.
     * 
     * @param message the detail message
     * @param propertyName the configuration property that is invalid
     */
    public BatchConfigurationException(String message, String propertyName) {
        super(message, "CONFIGURATION_INVALID");
        this.propertyName = propertyName;
    }
    
    /**
     * Returns the configuration property name that is invalid.
     * 
     * @return the property name
     */
    public String getPropertyName() {
        return propertyName;
    }
}
