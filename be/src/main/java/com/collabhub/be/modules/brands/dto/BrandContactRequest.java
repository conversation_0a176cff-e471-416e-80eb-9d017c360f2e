package com.collabhub.be.modules.brands.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for brand contact operations.
 * Used for both create and update operations.
 */
public class BrandContactRequest {

    @JsonProperty("id")
    private Long id;

    @NotBlank(message = "Contact name is required")
    @Size(max = 255, message = "Contact name cannot exceed 255 characters")
    private String name;

    @NotBlank(message = "Contact email is required")
    @Email(message = "Contact email must be a valid email address")
    @Size(max = 255, message = "Contact email cannot exceed 255 characters")
    private String email;

    @Size(max = 2000, message = "Notes cannot exceed 2000 characters")
    private String notes;

    public BrandContactRequest() {
    }

    public BrandContactRequest(Long id, String name, String email, String notes) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.notes = notes;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Override
    public String toString() {
        return "BrandContactRequest{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", notes='" + notes + '\'' +
                '}';
    }
}
