package com.collabhub.be.modules.notifications.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

/**
 * Response DTO for notification preference data.
 */
public class NotificationPreferenceResponse {

    @NotNull
    private Long id;

    @JsonProperty("user_id")
    private Long userId; // Nullable for external participants

    @NotNull
    private String email;

    @Schema(enumAsRef = true)
    @NotNull
    private NotificationType type;

    @Schema(enumAsRef = true)
    @NotNull
    private NotificationChannel channel;

    @NotNull
    private Boolean enabled;

    public NotificationPreferenceResponse() {}

    public NotificationPreferenceResponse(Long id, Long userId, String email,
                                        NotificationType type, NotificationChannel channel, Boolean enabled) {
        this.id = id;
        this.userId = userId;
        this.email = email;
        this.type = type;
        this.channel = channel;
        this.enabled = enabled;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public NotificationType getType() {
        return type;
    }

    public void setType(NotificationType type) {
        this.type = type;
    }

    public NotificationChannel getChannel() {
        return channel;
    }

    public void setChannel(NotificationChannel channel) {
        this.channel = channel;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
