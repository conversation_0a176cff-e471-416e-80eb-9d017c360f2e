package com.collabhub.be.modules.chat.dto;

import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Response DTO for paginated list of chat messages.
 */
public class ChatMessageListResponse {

    private List<ChatMessageResponse> messages;

    private PageResponse<ChatMessageResponse> pagination;

    @JsonProperty("channel_info")
    private ChatChannelInfo channelInfo;

    @JsonProperty("has_more")
    private boolean hasMore;

    public ChatMessageListResponse() {}

    public ChatMessageListResponse(List<ChatMessageResponse> messages, PageResponse<ChatMessageResponse> pagination,
                                 ChatChannelInfo channelInfo, boolean hasMore) {
        this.messages = messages;
        this.pagination = pagination;
        this.channelInfo = channelInfo;
        this.hasMore = hasMore;
    }

    public List<ChatMessageResponse> getMessages() {
        return messages;
    }

    public void setMessages(List<ChatMessageResponse> messages) {
        this.messages = messages;
    }

    public PageResponse<ChatMessageResponse> getPagination() {
        return pagination;
    }

    public void setPagination(PageResponse<ChatMessageResponse> pagination) {
        this.pagination = pagination;
    }

    public boolean hasMore() {
        return hasMore;
    }

    public void setHasMore(boolean hasMore) {
        this.hasMore = hasMore;
    }

    public ChatChannelInfo getChannelInfo() {
        return channelInfo;
    }

    public void setChannelInfo(ChatChannelInfo channelInfo) {
        this.channelInfo = channelInfo;
    }

    /**
     * Nested DTO for basic channel information in message list response.
     */
    public static class ChatChannelInfo {
        private Long id;
        private String name;
        private org.jooq.generated.enums.ChatChannelScope scope;

        public ChatChannelInfo() {}

        public ChatChannelInfo(Long id, String name, org.jooq.generated.enums.ChatChannelScope scope) {
            this.id = id;
            this.name = name;
            this.scope = scope;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public org.jooq.generated.enums.ChatChannelScope getScope() {
            return scope;
        }

        public void setScope(org.jooq.generated.enums.ChatChannelScope scope) {
            this.scope = scope;
        }
    }
}
