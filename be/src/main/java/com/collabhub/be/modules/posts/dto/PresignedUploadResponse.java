package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Response DTO for presigned upload URL generation.
 * Contains the presigned URL for upload and metadata about the upload constraints.
 */
public class PresignedUploadResponse {

    @JsonProperty("presigned_url")
    private String presignedUrl;

    @JsonProperty("final_url")
    private String finalUrl;

    private String key;

    @JsonProperty("content_type")
    private String contentType;

    @JsonProperty("max_file_size")
    private Long maxFileSize;

    @JsonProperty("expires_in_minutes")
    private int expiresInMinutes = 15;

    public PresignedUploadResponse() {}

    public PresignedUploadResponse(String presignedUrl, String finalUrl, String key, 
                                 String contentType, Long maxFileSize) {
        this.presignedUrl = presignedUrl;
        this.finalUrl = finalUrl;
        this.key = key;
        this.contentType = contentType;
        this.maxFileSize = maxFileSize;
    }

    public String getPresignedUrl() {
        return presignedUrl;
    }

    public void setPresignedUrl(String presignedUrl) {
        this.presignedUrl = presignedUrl;
    }

    public String getFinalUrl() {
        return finalUrl;
    }

    public void setFinalUrl(String finalUrl) {
        this.finalUrl = finalUrl;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(Long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public int getExpiresInMinutes() {
        return expiresInMinutes;
    }

    public void setExpiresInMinutes(int expiresInMinutes) {
        this.expiresInMinutes = expiresInMinutes;
    }

    @Override
    public String toString() {
        return "PresignedUploadResponse{" +
                "presignedUrl='" + presignedUrl + '\'' +
                ", finalUrl='" + finalUrl + '\'' +
                ", key='" + key + '\'' +
                ", contentType='" + contentType + '\'' +
                ", maxFileSize=" + maxFileSize +
                ", expiresInMinutes=" + expiresInMinutes +
                '}';
    }
}
