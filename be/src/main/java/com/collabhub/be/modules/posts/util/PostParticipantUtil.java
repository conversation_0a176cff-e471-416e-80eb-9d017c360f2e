package com.collabhub.be.modules.posts.util;

import com.collabhub.be.modules.collaborationhub.model.ParticipantDetails;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.posts.converter.PostConverter;
import com.collabhub.be.modules.posts.converter.PostCommentConverter;
import com.collabhub.be.modules.posts.dto.PostResponse;
import com.collabhub.be.modules.posts.dto.PostListItemResponse;
import com.collabhub.be.modules.posts.dto.PostCommentResponse;
import com.collabhub.be.modules.posts.dto.PostCommentListResponse;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Utility class for handling participant-related operations in posts.
 * Centralizes participant detail fetching, name resolution, and conversion logic
 * to eliminate code duplication across post services.
 */
@Component
public class PostParticipantUtil {

    private static final Logger logger = LoggerFactory.getLogger(PostParticipantUtil.class);

    private static final String UNKNOWN_CREATOR_NAME = "Unknown Creator";
    private static final String UNKNOWN_EMAIL = "<EMAIL>";
    private static final String EMAIL_SEPARATOR = "@";

    private final HubParticipantRepositoryImpl participantRepository;
    private final PostConverter postConverter;
    private final PostCommentConverter commentConverter;

    public PostParticipantUtil(HubParticipantRepositoryImpl participantRepository,
                              PostConverter postConverter,
                              PostCommentConverter commentConverter) {
        this.participantRepository = participantRepository;
        this.postConverter = postConverter;
        this.commentConverter = commentConverter;
    }

    /**
     * Creates a PostCreator for post details with fallback handling.
     *
     * @param creatorParticipantId the creator participant ID
     * @return PostCreator with participant details or fallback data
     */
    public PostResponse.PostCreator createPostCreator(Long creatorParticipantId) {
        try {
            Map<Long, ParticipantDetails> creatorDetailsMap =
                    participantRepository.findParticipantDetailsByIds(List.of(creatorParticipantId));

            ParticipantDetails creatorDetails = creatorDetailsMap.get(creatorParticipantId);

            if (creatorDetails != null) {
                return postConverter.createPostCreator(
                        creatorDetails.getId(),
                        creatorDetails.getName(),
                        creatorDetails.getEmail());
            } else {
                return createPostCreatorFallback(creatorParticipantId);
            }
        } catch (Exception e) {
            logger.error("Unexpected error getting post creator {}: {}", creatorParticipantId, e.getMessage());
            return createPostCreatorFallback(creatorParticipantId);
        }
    }

    /**
     * Creates a PostCreator for list items from bulk-loaded data.
     *
     * @param creatorParticipantId the creator participant ID
     * @param creatorDetailsMap bulk-loaded participant details
     * @return PostListItemResponse.PostCreator with participant details or fallback data
     */
    public PostListItemResponse.PostCreator createListItemCreator(
            Long creatorParticipantId,
            Map<Long, ParticipantDetails> creatorDetailsMap) {

        ParticipantDetails creatorDetails = creatorDetailsMap.get(creatorParticipantId);

        if (creatorDetails != null) {
            return postConverter.createListItemCreator(
                    creatorDetails.getId(),
                    creatorDetails.getName(),
                    creatorDetails.getEmail());
        } else {
            logger.warn("Creator details not found for participant: {}", creatorParticipantId);
            return postConverter.createListItemCreator(
                    creatorParticipantId, UNKNOWN_CREATOR_NAME, UNKNOWN_EMAIL);
        }
    }

    /**
     * Creates a CommentAuthor for comment responses.
     *
     * @param participant the hub participant
     * @return CommentAuthor with participant details
     */
    public PostCommentResponse.CommentAuthor createCommentAuthor(HubParticipant participant) {
        return commentConverter.createCommentAuthor(
                participant.getId(),
                participant.getName(),
                participant.getEmail());
    }

    /**
     * Creates a CommentAuthor for comment list items.
     *
     * @param participant the hub participant
     * @return CommentAuthor for list items
     */
    public PostCommentListResponse.CommentAuthor createListCommentAuthor(HubParticipant participant) {
        return commentConverter.createListCommentAuthor(
                participant.getId(),
                participant.getName(),
                participant.getEmail());
    }

    /**
     * Resolves participant name with fallback logic.
     * Uses name if available, otherwise extracts from email, otherwise returns "Unknown".
     *
     * @param participantDetails the participant details
     * @return resolved name
     */
    public String resolveParticipantName(ParticipantDetails participantDetails) {
        if (participantDetails == null) {
            return UNKNOWN_CREATOR_NAME;
        }

        if (participantDetails.getName() != null && !participantDetails.getName().trim().isEmpty()) {
            return participantDetails.getName();
        }

        if (participantDetails.getEmail() != null && participantDetails.getEmail().contains(EMAIL_SEPARATOR)) {
            return participantDetails.getEmail().substring(0, participantDetails.getEmail().indexOf(EMAIL_SEPARATOR));
        }

        return UNKNOWN_CREATOR_NAME;
    }

    /**
     * Bulk loads hub participants for multiple participant IDs.
     *
     * @param participantIds list of participant IDs
     * @return map of participant ID to hub participant
     */
    public Map<Long, HubParticipant> bulkLoadHubParticipants(List<Long> participantIds) {
        if (participantIds.isEmpty()) {
            return Map.of();
        }

        return participantRepository.findByIds(participantIds).stream()
                .collect(java.util.stream.Collectors.toMap(HubParticipant::getId, participant -> participant));
    }

    /**
     * Converts a PostReviewer entity to PostResponse.PostReviewer DTO using bulk-loaded data.
     *
     * @param reviewerParticipantId the reviewer participant ID
     * @param reviewStatus the review status
     * @param participantDetailsMap bulk-loaded participant details
     * @return PostResponse.PostReviewer or null if participant not found
     */
    public PostResponse.PostReviewer convertToPostReviewer(
            Long reviewerParticipantId,
            org.jooq.generated.enums.ReviewStatus reviewStatus,
            Map<Long, ParticipantDetails> participantDetailsMap) {

        ParticipantDetails participantDetails = participantDetailsMap.get(reviewerParticipantId);

        if (participantDetails == null) {
            logger.warn("Participant details not found for reviewer: {}", reviewerParticipantId);
            return null;
        }

        String name = resolveParticipantName(participantDetails);

        return postConverter.createPostReviewer(
                reviewerParticipantId,
                name,
                participantDetails.getEmail(),
                reviewStatus
        );
    }

    /**
     * Converts a PostReviewer entity to PostResponse.PostReviewer DTO with detailed review information.
     *
     * @param postReviewer the PostReviewer entity with review details
     * @param participantDetailsMap bulk-loaded participant details
     * @return PostResponse.PostReviewer or null if participant not found
     */
    public PostResponse.PostReviewer convertToEnhancedPostReviewer(
            org.jooq.generated.tables.pojos.PostReviewer postReviewer,
            Map<Long, ParticipantDetails> participantDetailsMap) {

        ParticipantDetails participantDetails = participantDetailsMap.get(postReviewer.getParticipantId());

        if (participantDetails == null) {
            logger.warn("Participant details not found for reviewer: {}", postReviewer.getParticipantId());
            return null;
        }

        String name = resolveParticipantName(participantDetails);

        return postConverter.createEnhancedPostReviewer(
                postReviewer.getParticipantId(),
                name,
                participantDetails.getEmail(),
                postReviewer.getReviewStatus(),
                postReviewer.getReviewNotes(),
                postReviewer.getAssignedAt(),
                postReviewer.getReviewedAt()
        );
    }

    /**
     * Creates a fallback PostCreator for missing participant details.
     */
    private PostResponse.PostCreator createPostCreatorFallback(Long creatorParticipantId) {
        logger.warn("Creator participant not found: {}", creatorParticipantId);
        return postConverter.createPostCreator(
                creatorParticipantId, UNKNOWN_CREATOR_NAME, UNKNOWN_EMAIL);
    }
}
