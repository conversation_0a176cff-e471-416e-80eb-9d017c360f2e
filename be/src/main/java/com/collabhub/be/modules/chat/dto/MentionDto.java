package com.collabhub.be.modules.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

/**
 * DTO representing a participant mention in a chat message.
 */
public class MentionDto {

    @JsonProperty("participant_id")
    @NotNull
    private Long participantId;

    @NotNull
    private String name;

    private String email;

    @JsonProperty("is_external")
    private Boolean isExternal;

    public MentionDto() {}

    public MentionDto(Long participantId, String name, String email, Boolean isExternal) {
        this.participantId = participantId;
        this.name = name;
        this.email = email;
        this.isExternal = isExternal;
    }

    public Long getParticipantId() {
        return participantId;
    }

    public void setParticipantId(Long participantId) {
        this.participantId = participantId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getIsExternal() {
        return isExternal;
    }

    public void setIsExternal(Boolean isExternal) {
        this.isExternal = isExternal;
    }
}
