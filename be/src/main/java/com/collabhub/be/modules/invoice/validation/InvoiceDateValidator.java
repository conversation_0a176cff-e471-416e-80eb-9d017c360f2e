package com.collabhub.be.modules.invoice.validation;

import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.invoice.dto.InvoiceCreateRequest;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.time.LocalDate;

/**
 * Validator for invoice date constraints.
 * Validates cross-field date relationships.
 */
public class InvoiceDateValidator implements ConstraintValidator<ValidInvoiceDates, InvoiceCreateRequest> {

    @Override
    public void initialize(ValidInvoiceDates constraintAnnotation) {
        // No initialization needed
    }

    @Override
    public boolean isValid(InvoiceCreateRequest request, ConstraintValidatorContext context) {
        if (request == null) {
            return true; // Let other validators handle null checks
        }

        boolean isValid = true;
        LocalDate today = LocalDate.now();

        // Disable default constraint violation
        context.disableDefaultConstraintViolation();

        // Validate issue date is not in the future
        if (request.getIssueDate() != null && request.getIssueDate().isAfter(today)) {
            context.buildConstraintViolationWithTemplate(InvoiceConstants.ISSUE_DATE_FUTURE_MESSAGE)
                    .addPropertyNode("issue_date")
                    .addConstraintViolation();
            isValid = false;
        }

        // Validate due date is on or after issue date
        if (request.getIssueDate() != null && request.getDueDate() != null &&
            request.getDueDate().isBefore(request.getIssueDate())) {
            context.buildConstraintViolationWithTemplate(InvoiceConstants.DUE_DATE_BEFORE_ISSUE_MESSAGE)
                    .addPropertyNode("due_date")
                    .addConstraintViolation();
            isValid = false;
        }

        return isValid;
    }
}
