package com.collabhub.be.modules.collaborationhub.dto;

import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import org.jooq.generated.enums.HubParticipantRole;

import java.util.List;

/**
 * Response DTO for listing hub participants with pagination and filtering.
 * Extends the generic PageResponse to include participant-specific filters.
 */
public class HubParticipantListResponse extends PageResponse<HubParticipantResponse> {

    private ParticipantFilters filters;

    public HubParticipantListResponse() {
        super(List.of(), PageRequest.of(0), 0);
    }

    public HubParticipantListResponse(List<HubParticipantResponse> content, PageRequest pageRequest,
                                    long totalElements, ParticipantFilters filters) {
        super(content, pageRequest, totalElements);
        this.filters = filters;
    }

    public ParticipantFilters getFilters() {
        return filters;
    }

    public void setFilters(ParticipantFilters filters) {
        this.filters = filters;
    }

    /**
     * Nested class representing available filters for participants.
     */
    public static class ParticipantFilters {
        private List<String> availableStatuses;
        private List<HubParticipantRole> availableRoles;
        private String currentStatus;
        private HubParticipantRole currentRole;

        public ParticipantFilters() {}

        public ParticipantFilters(List<String> availableStatuses, List<HubParticipantRole> availableRoles,
                                String currentStatus, HubParticipantRole currentRole) {
            this.availableStatuses = availableStatuses;
            this.availableRoles = availableRoles;
            this.currentStatus = currentStatus;
            this.currentRole = currentRole;
        }

        public List<String> getAvailableStatuses() {
            return availableStatuses;
        }

        public void setAvailableStatuses(List<String> availableStatuses) {
            this.availableStatuses = availableStatuses;
        }

        public List<HubParticipantRole> getAvailableRoles() {
            return availableRoles;
        }

        public void setAvailableRoles(List<HubParticipantRole> availableRoles) {
            this.availableRoles = availableRoles;
        }

        public String getCurrentStatus() {
            return currentStatus;
        }

        public void setCurrentStatus(String currentStatus) {
            this.currentStatus = currentStatus;
        }

        public HubParticipantRole getCurrentRole() {
            return currentRole;
        }

        public void setCurrentRole(HubParticipantRole currentRole) {
            this.currentRole = currentRole;
        }
    }

    @Override
    public String toString() {
        return "HubParticipantListResponse{" +
                "filters=" + filters +
                ", content=" + getContent() +
                ", page=" + getPage() +
                ", size=" + getSize() +
                ", totalElements=" + getTotalElements() +
                '}';
    }
}
