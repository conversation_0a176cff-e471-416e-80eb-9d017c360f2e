package com.collabhub.be.modules.companies.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.companies.constants.CompanyConstants;
import com.collabhub.be.modules.companies.dto.AccountCompanyCreateRequest;
import com.collabhub.be.modules.companies.dto.AccountCompanyResponse;
import com.collabhub.be.modules.companies.dto.AccountCompanyUpdateRequest;
import com.collabhub.be.modules.companies.service.AccountCompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for Account Company operations.
 * Handles CRUD operations for account companies with multi-tenancy and permission-based authorization.
 */
@RestController
@RequestMapping("/api/account-companies")
@Tag(name = "Account Companies", description = "Account company management operations")
public class AccountCompanyController {

    private static final Logger logger = LoggerFactory.getLogger(AccountCompanyController.class);

    private final AccountCompanyService accountCompanyService;
    private final JwtClaimsService jwtClaimsService;

    public AccountCompanyController(AccountCompanyService accountCompanyService,
                                  JwtClaimsService jwtClaimsService) {
        this.accountCompanyService = accountCompanyService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Retrieves all active account companies for the current account.
     *
     * @param jwt the JWT token containing user information
     * @return list of account companies
     */
    @GetMapping
    @PreAuthorize("hasAuthority('company:read')")
    @Operation(summary = "Get all account companies", description = "Retrieves all active account companies for the current account")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved account companies"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<List<AccountCompanyResponse>> getAllAccountCompanies(
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving all account companies for account: {}", userContext.getAccountId());

        List<AccountCompanyResponse> companies = accountCompanyService.getAllAccountCompanies(userContext.getAccountId());

        logger.debug("Successfully retrieved {} account companies for account: {}", companies.size(), userContext.getAccountId());

        return ResponseEntity.ok(companies);
    }

    /**
     * Retrieves a specific account company by ID.
     *
     * @param id the company ID
     * @param jwt the JWT token containing user information
     * @return the account company
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('company:read')")
    @Operation(summary = "Get account company by ID", description = "Retrieves a specific account company by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved account company"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Account company not found")
    })
    public ResponseEntity<AccountCompanyResponse> getAccountCompanyById(
            @Parameter(description = "Account company ID") @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving account company with ID: {} for account: {}", id, userContext.getAccountId());

        AccountCompanyResponse company = accountCompanyService.getAccountCompanyById(id, userContext.getAccountId());

        logger.debug("Successfully retrieved account company: {} for account: {}", company.getCompanyName(), userContext.getAccountId());

        return ResponseEntity.ok(company);
    }

    /**
     * Creates a new account company.
     *
     * @param request the create request
     * @param jwt the JWT token containing user information
     * @return the created account company
     */
    @PostMapping
    @PreAuthorize("hasAuthority('company:write')")
    @Operation(summary = "Create account company", description = "Creates a new account company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Account company created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<AccountCompanyResponse> createAccountCompany(
            @Valid @RequestBody AccountCompanyCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Creating new account company for account: {}", userContext.getAccountId());

        AccountCompanyResponse createdCompany = accountCompanyService.createAccountCompany(request, userContext.getAccountId());

        logger.info("Successfully created account company: {} for account: {}",
                   createdCompany.getCompanyName(), userContext.getAccountId());

        return ResponseEntity.status(HttpStatus.CREATED).body(createdCompany);
    }

    /**
     * Updates an existing account company.
     *
     * @param id the company ID
     * @param request the update request
     * @param jwt the JWT token containing user information
     * @return the updated account company
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('company:write')")
    @Operation(summary = "Update account company", description = "Updates an existing account company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Account company updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Account company not found")
    })
    public ResponseEntity<AccountCompanyResponse> updateAccountCompany(
            @Parameter(description = "Account company ID") @PathVariable Long id,
            @Valid @RequestBody AccountCompanyUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Updating account company with ID: {} for account: {}", id, userContext.getAccountId());

        AccountCompanyResponse updatedCompany = accountCompanyService.updateAccountCompany(id, request, userContext.getAccountId());

        logger.info("Successfully updated account company: {} for account: {}",
                   updatedCompany.getCompanyName(), userContext.getAccountId());

        return ResponseEntity.ok(updatedCompany);
    }

    /**
     * Soft deletes an account company.
     *
     * @param id the company ID
     * @param jwt the JWT token containing user information
     * @return no content response
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('company:delete')")
    @Operation(summary = "Delete account company", description = "Soft deletes an account company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Account company deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Account company not found")
    })
    public ResponseEntity<Void> deleteAccountCompany(
            @Parameter(description = "Account company ID") @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Deleting account company with ID: {} for account: {}", id, userContext.getAccountId());

        accountCompanyService.deleteAccountCompany(id, userContext.getAccountId());

        logger.info("Successfully deleted account company with ID: {} for account: {}", id, userContext.getAccountId());

        return ResponseEntity.noContent().build();
    }
}
