package com.collabhub.be.modules.chat.service;

import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.chat.converter.ChatConverter;
import com.collabhub.be.modules.chat.dto.*;
import com.collabhub.be.modules.chat.repository.ChatChannelRepositoryImpl;
import com.collabhub.be.modules.chat.repository.ChatMessageRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.service.HubParticipantManagementService;
import com.collabhub.be.modules.media.service.MediaService;
import com.collabhub.be.modules.media.repository.ChatMessageMediaRepositoryImpl;
import com.collabhub.be.modules.media.dto.MediaDto;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.service.NotificationDispatcherService;
import com.collabhub.be.modules.notifications.util.NotificationRecipientUtils;
import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.ChatChannel;
import org.jooq.generated.tables.pojos.ChatMessage;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.Media;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing chat messages, including sending, editing, and retrieving messages.
 * Handles message CRUD operations, media attachments, and mention processing.
 */
@Service
@Validated
public class ChatMessageService {

    private static final Logger logger = LoggerFactory.getLogger(ChatMessageService.class);

    // Constants
    private static final int DEFAULT_MESSAGE_LIMIT = 50;
    private static final int MAX_MESSAGE_LIMIT = 100;
    private static final String MESSAGE_CONTENT_REQUIRED_MESSAGE = "Message must have either content or attachments";
    private static final String CHANNEL_ACCESS_DENIED_MESSAGE = "Access denied to chat channel";
    private static final String MESSAGE_NOT_FOUND_MESSAGE = "Message not found";
    private static final String EDIT_OWN_MESSAGES_ONLY_MESSAGE = "You can only edit your own messages";
    private static final String PARTICIPANT_NOT_FOUND_MESSAGE = "Participant not found";
    private static final String CHANNEL_NOT_FOUND_MESSAGE = "Chat channel not found";
    private static final String PARTICIPANT_NOT_FOUND_FOR_EMAIL_MESSAGE = "Participant not found for email: %s";
    private static final String INVALID_ATTACHMENT_MESSAGE = "Invalid or inaccessible attachment file: %s";
    private static final String ACCOUNT_ID_DETERMINATION_ERROR_MESSAGE = "Cannot determine account ID for hub: %s";
    private static final String UNKNOWN_FILE_NAME = "unknown_file";
    private static final String UNKNOWN_USER_NAME = "Unknown User";

    // File type constants
    private static final Map<String, String> FILE_EXTENSION_MIME_TYPES = Map.of(
        ".jpg", "image/jpeg",
        ".jpeg", "image/jpeg",
        ".png", "image/png",
        ".gif", "image/gif",
        ".mp4", "video/mp4",
        ".mov", "video/quicktime",
        ".avi", "video/x-msvideo"
    );
    private static final String DEFAULT_MIME_TYPE = "application/octet-stream";

    private final ChatMessageRepositoryImpl messageRepository;
    private final ChatChannelRepositoryImpl channelRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final ChatConverter chatConverter;
    private final MentionService mentionService;
    private final ChatChannelService channelService;
    private final MediaService mediaService;
    private final ChatMessageMediaRepositoryImpl chatMessageMediaRepository;
    private final JwtClaimsService jwtClaimsService;
    private final HubParticipantManagementService hubParticipantManagementService;
    private final NotificationDispatcherService notificationDispatcherService;

    public ChatMessageService(ChatMessageRepositoryImpl messageRepository,
                            ChatChannelRepositoryImpl channelRepository,
                            HubParticipantRepositoryImpl participantRepository,
                            CollaborationHubRepositoryImpl hubRepository,
                            ChatConverter chatConverter,
                            MentionService mentionService,
                            ChatChannelService channelService,
                            MediaService mediaService,
                            ChatMessageMediaRepositoryImpl chatMessageMediaRepository,
                            JwtClaimsService jwtClaimsService,
                            HubParticipantManagementService hubParticipantManagementService,
                            NotificationDispatcherService notificationDispatcherService) {
        this.messageRepository = messageRepository;
        this.channelRepository = channelRepository;
        this.participantRepository = participantRepository;
        this.hubRepository = hubRepository;
        this.chatConverter = chatConverter;
        this.mentionService = mentionService;
        this.channelService = channelService;
        this.mediaService = mediaService;
        this.chatMessageMediaRepository = chatMessageMediaRepository;
        this.jwtClaimsService = jwtClaimsService;
        this.hubParticipantManagementService = hubParticipantManagementService;
        this.notificationDispatcherService = notificationDispatcherService;
    }

    /**
     * Sends a new chat message to a channel by email.
     * For app-level ADMINs, automatically creates hub admin participant if not already present.
     *
     * @param channelId the channel ID
     * @param userContext the sender context
     * @param request the message request
     * @return the created message response
     */
    @Transactional
    public ChatMessageResponse sendMessage(@NotNull Long channelId,
                                           UserContext userContext,
                                          @Valid ChatMessageRequest request) {
        // Get channel to find hub ID
        ChatChannel channel = getChannelById(channelId);
        Long hubId = channel.getHubId();

        // Ensure participant exists (create if app-level ADMIN)
        HubParticipant participant = hubParticipantManagementService.ensureAdminParticipantExists(hubId, userContext);

        if (participant == null) {
            // User is not a participant and not an app-level ADMIN
            throw new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                    String.format("Participant not found for email: %s", userContext.getEmail()));
        }

        return sendMessage(channelId, participant.getId(), request);
    }

    /**
     * Sends a new chat message to a channel.
     *
     * @param channelId the channel ID
     * @param participantId the sender participant ID
     * @param request the message request
     * @return the created message response
     */
    @Transactional
    public ChatMessageResponse sendMessage(@NotNull Long channelId,
                                          @NotNull Long participantId,
                                          @Valid ChatMessageRequest request) {
        logger.info("Sending message to channel {} from participant {}", channelId, participantId);

        validateMessageRequest(request);
        validateChannelAccess(channelId, participantId);

        HubParticipant participant = getParticipantById(participantId);
        ChatChannel channel = getChannelById(channelId);

        return createMessageWithMedia(channelId, participantId, request, participant, channel);
    }

    /**
     * Validates that a message request has either content or attachments.
     */
    private void validateMessageRequest(ChatMessageRequest request) {
        boolean hasContent = hasValidContent(request.getContent());
        boolean hasAttachments = hasAttachments(request);

        if (!hasContent && !hasAttachments) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, MESSAGE_CONTENT_REQUIRED_MESSAGE);
        }
    }

    /**
     * Checks if content is valid (not null and not empty after trimming).
     */
    private boolean hasValidContent(String content) {
        return content != null && !content.trim().isEmpty();
    }

    /**
     * Validates that a participant has write access to a channel.
     */
    private void validateChannelAccess(Long channelId, Long participantId) {
        if (!channelService.canParticipantWriteToChannel(channelId, participantId)) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, CHANNEL_ACCESS_DENIED_MESSAGE);
        }
    }

    /**
     * Creates a message with media attachments.
     */
    private ChatMessageResponse createMessageWithMedia(Long channelId, Long participantId,
                                                     ChatMessageRequest request, HubParticipant participant,
                                                     ChatChannel channel) {
        List<MentionDto> mentions = parseMentionsFromContent(request.getContent(), channel.getHubId());
        List<Long> mediaIds = processAttachmentMedia(request, participant.getHubId());
        ChatMessage message = createMessageRecord(channelId, participantId, request.getContent(), mentions);

        associateMediaWithMessage(message.getId(), mediaIds);
        channelService.updateChannelActivity(channelId);

        // Send mention notifications
        sendChatMentionNotifications(mentions, channel, participant, request.getContent());

        ChatMessageResponse response = buildMessageResponse(message, mediaIds, participant);
        logMessageCreationSuccess(message.getId(), channelId, participantId);

        return response;
    }

    /**
     * Parses mentions from message content.
     */
    private List<MentionDto> parseMentionsFromContent(String content, Long hubId) {
        return mentionService.parseMentions(content, hubId);
    }

    /**
     * Creates message record in database.
     */
    private ChatMessage createMessageRecord(Long channelId, Long participantId, String content, List<MentionDto> mentions) {
        JSONB mentionsJsonb = chatConverter.mentionsToJsonb(mentions);
        return messageRepository.createMessage(channelId, participantId, content, mentionsJsonb);
    }

    /**
     * Associates media with message if media IDs exist.
     */
    private void associateMediaWithMessage(Long messageId, List<Long> mediaIds) {
        if (mediaIds != null && !mediaIds.isEmpty()) {
            chatMessageMediaRepository.createChatMessageMediaAssociations(messageId, mediaIds);
        }
    }

    /**
     * Builds message response with media and sender information.
     */
    private ChatMessageResponse buildMessageResponse(ChatMessage message, List<Long> mediaIds, HubParticipant participant) {
        List<Media> mediaList = loadMediaForMessage(message.getId(), mediaIds);
        ChatParticipantDto senderDto = chatConverter.toParticipantDto(participant);
        return chatConverter.toMessageResponse(message, mediaList, senderDto);
    }

    /**
     * Loads media for message if media IDs exist.
     */
    private List<Media> loadMediaForMessage(Long messageId, List<Long> mediaIds) {
        return (mediaIds != null && !mediaIds.isEmpty()) ?
            mediaService.findMediaByChatMessageId(messageId) : List.of();
    }

    /**
     * Logs successful message creation.
     */
    private void logMessageCreationSuccess(Long messageId, Long channelId, Long participantId) {
        logger.info("Successfully sent message {} to channel {} from participant {}",
                messageId, channelId, participantId);
    }

    /**
     * Gets messages from a channel with pagination for infinite scroll by email.
     *
     * @param channelId the channel ID
     * @param requesterEmail the requesting participant email
     * @param limit the maximum number of messages to return
     * @param beforeMessageId optional message ID for pagination
     * @return paginated message list
     */
    @Transactional(readOnly = true)
    public ChatMessageListResponse getMessages(@NotNull Long channelId,
                                              @NotBlank String requesterEmail,
                                              Integer limit,
                                              Long beforeMessageId) {
        Long participantId = resolveParticipantIdByEmail(requesterEmail);
        return getMessages(channelId, participantId, limit, beforeMessageId);
    }

    /**
     * Gets messages from a channel with pagination for infinite scroll.
     *
     * @param channelId the channel ID
     * @param participantId the requesting participant ID
     * @param limit the maximum number of messages to return
     * @param beforeMessageId optional message ID for pagination
     * @return paginated message list
     */
    @Transactional(readOnly = true)
    public ChatMessageListResponse getMessages(@NotNull Long channelId,
                                              @NotNull Long participantId,
                                              Integer limit,
                                              Long beforeMessageId) {
        logger.info("Getting messages from channel {} for participant {}", channelId, participantId);

        HubParticipant participant = getParticipantById(participantId);
        ChatChannel channel = validateChannelAccessForRetrieval(channelId, participantId);
        int messageLimit = validateAndSetLimit(limit);

        List<ChatMessage> messages = fetchMessagesWithPagination(channelId, messageLimit, beforeMessageId);
        List<ChatMessageResponse> messageResponses = convertToMessageResponsesBulk(messages);

        ChatMessageListResponse response = buildMessageListResponse(messages, messageResponses, channel, messageLimit);
        logMessageRetrievalSuccess(messageResponses.size(), channelId, participantId);

        return response;
    }

    /**
     * Validates channel access for message retrieval and returns the channel.
     */
    private ChatChannel validateChannelAccessForRetrieval(Long channelId, Long participantId) {
        Optional<ChatChannel> channel = channelRepository.findAccessibleChannelById(
                channelId, participantId);
        if (channel.isEmpty()) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, CHANNEL_ACCESS_DENIED_MESSAGE);
        }
        return channel.get();
    }

    /**
     * Fetches messages with pagination.
     */
    private List<ChatMessage> fetchMessagesWithPagination(Long channelId, int messageLimit, Long beforeMessageId) {
        return messageRepository.findMessagesByChannelWithPagination(channelId, messageLimit, beforeMessageId);
    }

    /**
     * Builds complete message list response.
     */
    private ChatMessageListResponse buildMessageListResponse(List<ChatMessage> messages,
                                                            List<ChatMessageResponse> messageResponses,
                                                            ChatChannel channel,
                                                            int messageLimit) {
        ChatMessageListResponse.ChatChannelInfo channelInfo = createChannelInfo(channel);
        boolean hasMore = messages.size() == messageLimit;

        Long totalMessages = messageRepository.countMessagesByChannel(channel.getId());
        PageRequest pageRequest = PageRequest.of(0, messageLimit);
        PageResponse<ChatMessageResponse> pageResponse = new PageResponse<>(messageResponses, pageRequest, totalMessages);

        return new ChatMessageListResponse(messageResponses, pageResponse, channelInfo, hasMore);
    }

    /**
     * Logs successful message retrieval.
     */
    private void logMessageRetrievalSuccess(int messageCount, Long channelId, Long participantId) {
        logger.info("Retrieved {} messages from channel {} for participant {}",
                messageCount, channelId, participantId);
    }

    /**
     * Validates and sets the message limit.
     */
    private int validateAndSetLimit(Integer limit) {
        return limit != null ? Math.min(limit, MAX_MESSAGE_LIMIT) : DEFAULT_MESSAGE_LIMIT;
    }

    /**
     * Creates channel info DTO.
     */
    private ChatMessageListResponse.ChatChannelInfo createChannelInfo(ChatChannel channel) {
        return new ChatMessageListResponse.ChatChannelInfo(
                channel.getId(),
                channel.getName(),
                channel.getScope());
    }

    /**
     * Updates an existing chat message by email.
     *
     * @param channelId the channel ID
     * @param messageId the message ID
     * @param authorEmail the participant email (must be message author)
     * @param request the update request
     * @return the updated message response
     */
    @Transactional
    public ChatMessageResponse updateMessage(@NotNull Long channelId,
                                            @NotNull Long messageId,
                                            @NotBlank String authorEmail,
                                            @Valid ChatMessageUpdateRequest request) {
        Long participantId = resolveParticipantIdByEmail(authorEmail);
        return updateMessage(channelId, messageId, participantId, request);
    }

    /**
     * Updates an existing chat message.
     *
     * @param channelId the channel ID
     * @param messageId the message ID
     * @param participantId the requesting participant ID
     * @param request the update request
     * @return the updated message response
     */
    @Transactional
    public ChatMessageResponse updateMessage(@NotNull Long channelId,
                                            @NotNull Long messageId,
                                            @NotNull Long participantId,
                                            @Valid ChatMessageUpdateRequest request) {
        logger.info("Updating message {} in channel {} by participant {}", messageId, channelId, participantId);

        validateMessageUpdateRequest(request);
        ChatMessage message = validateMessageOwnership(messageId, channelId, participantId);
        ChatChannel channel = getChannelById(channelId);

        return processMessageUpdate(messageId, channelId, request, channel);
    }

    /**
     * Validates that a message update request has either content or attachments.
     */
    private void validateMessageUpdateRequest(ChatMessageUpdateRequest request) {
        boolean hasContent = hasValidContent(request.getContent());
        boolean hasAttachments = hasAttachmentsUpdate(request);

        if (!hasContent && !hasAttachments) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, MESSAGE_CONTENT_REQUIRED_MESSAGE);
        }
    }

    /**
     * Validates that the message exists and belongs to the participant.
     */
    private ChatMessage validateMessageOwnership(Long messageId, Long channelId, Long participantId) {
        ChatMessage message = findMessageByIdAndChannel(messageId, channelId);
        validateMessageOwner(message, participantId);
        return message;
    }

    /**
     * Finds message by ID and channel.
     */
    private ChatMessage findMessageByIdAndChannel(Long messageId, Long channelId) {
        return messageRepository.findMessageByIdAndChannel(messageId, channelId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.CHAT_MESSAGE_NOT_FOUND, MESSAGE_NOT_FOUND_MESSAGE));
    }

    /**
     * Validates that the participant owns the message.
     */
    private void validateMessageOwner(ChatMessage message, Long participantId) {
        if (!message.getParticipantId().equals(participantId)) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, EDIT_OWN_MESSAGES_ONLY_MESSAGE);
        }
    }

    /**
     * Processes the message update with mentions and media.
     */
    private ChatMessageResponse processMessageUpdate(Long messageId, Long channelId,
                                                    ChatMessageUpdateRequest request, ChatChannel channel) {
        List<MentionDto> mentions = parseMentionsFromContent(request.getContent(), channel.getHubId());
        updateMessageMedia(messageId, request, channel.getHubId());
        updateMessageContent(messageId, request.getContent(), mentions);

        ChatMessage updatedMessage = findMessageByIdAndChannel(messageId, channelId);
        ChatMessageResponse response = convertToMessageResponse(updatedMessage);

        logMessageUpdateSuccess(messageId, channelId);
        return response;
    }

    /**
     * Updates message media associations if attachments are provided.
     */
    private void updateMessageMedia(Long messageId, ChatMessageUpdateRequest request, Long hubId) {
        if (request.getAttachmentUris() != null || request.getAttachments() != null) {
            List<Long> mediaIds = processAttachmentMediaFromUpdate(request, hubId);
            chatMessageMediaRepository.updateChatMessageMediaAssociations(messageId, mediaIds);
        }
    }

    /**
     * Updates message content and mentions.
     */
    private void updateMessageContent(Long messageId, String content, List<MentionDto> mentions) {
        JSONB mentionsJsonb = chatConverter.mentionsToJsonb(mentions);
        boolean updated = messageRepository.updateMessage(messageId, content, mentionsJsonb);

        if (!updated) {
            throw new NotFoundException(ErrorCode.CHAT_MESSAGE_NOT_FOUND, MESSAGE_NOT_FOUND_MESSAGE);
        }
    }

    /**
     * Logs successful message update.
     */
    private void logMessageUpdateSuccess(Long messageId, Long channelId) {
        logger.info("Successfully updated message {} in channel {}", messageId, channelId);
    }

    /**
     * Deletes a chat message by email.
     *
     * @param channelId the channel ID
     * @param messageId the message ID
     * @param authorEmail the participant email (must be message author)
     */
    @Transactional
    public void deleteMessage(@NotNull Long channelId,
                             @NotNull Long messageId,
                             @NotBlank String authorEmail) {
        Long participantId = resolveParticipantIdByEmail(authorEmail);
        deleteMessage(channelId, messageId, participantId);
    }

    /**
     * Deletes a chat message.
     *
     * @param channelId the channel ID
     * @param messageId the message ID
     * @param participantId the requesting participant ID
     */
    @Transactional
    public void deleteMessage(@NotNull Long channelId,
                             @NotNull Long messageId,
                             @NotNull Long participantId) {
        logger.info("Deleting message {} in channel {} by participant {}", messageId, channelId, participantId);

        validateMessageOwnership(messageId, channelId, participantId);
        performMessageDeletion(messageId);
        logMessageDeletionSuccess(messageId, channelId);
    }

    /**
     * Performs the actual message deletion.
     */
    private void performMessageDeletion(Long messageId) {
        boolean deleted = messageRepository.deleteMessage(messageId);
        if (!deleted) {
            throw new NotFoundException(ErrorCode.CHAT_MESSAGE_NOT_FOUND, MESSAGE_NOT_FOUND_MESSAGE);
        }
    }

    /**
     * Logs successful message deletion.
     */
    private void logMessageDeletionSuccess(Long messageId, Long channelId) {
        logger.info("Successfully deleted message {} in channel {}", messageId, channelId);
    }

    /**
     * Converts a ChatMessage entity to response DTO with sender details and media.
     * This method has N+1 query issue - use convertToMessageResponsesBulk for lists.
     */
    private ChatMessageResponse convertToMessageResponse(ChatMessage message) {
        HubParticipant sender = participantRepository.findById(message.getParticipantId());
        List<Media> mediaList = mediaService.findMediaByChatMessageId(message.getId());

        ChatParticipantDto senderDto = sender != null ? chatConverter.toParticipantDto(sender) : null;
        return chatConverter.toMessageResponse(message, mediaList, senderDto);
    }

    /**
     * Converts a list of ChatMessage entities to response DTOs with bulk loading to avoid N+1 queries.
     */
    private List<ChatMessageResponse> convertToMessageResponsesBulk(List<ChatMessage> messages) {
        if (messages.isEmpty()) {
            return List.of();
        }

        Map<Long, HubParticipant> participantMap = bulkLoadParticipants(messages);
        Map<Long, List<Media>> mediaMap = bulkLoadMedia(messages);

        return convertMessagesToResponses(messages, participantMap, mediaMap);
    }

    /**
     * Bulk loads participants to avoid N+1 queries.
     */
    private Map<Long, HubParticipant> bulkLoadParticipants(List<ChatMessage> messages) {
        List<Long> participantIds = messages.stream()
                .map(ChatMessage::getParticipantId)
                .distinct()
                .toList();

        return participantRepository.findByIds(participantIds)
                .stream()
                .collect(Collectors.toMap(HubParticipant::getId, p -> p));
    }

    /**
     * Bulk loads media for all messages.
     */
    private Map<Long, List<Media>> bulkLoadMedia(List<ChatMessage> messages) {
        List<Long> messageIds = messages.stream()
                .map(ChatMessage::getId)
                .toList();

        return mediaService.findMediaByChatMessageIds(messageIds);
    }

    /**
     * Converts messages to response DTOs using bulk-loaded data.
     */
    private List<ChatMessageResponse> convertMessagesToResponses(List<ChatMessage> messages,
                                                               Map<Long, HubParticipant> participantMap,
                                                               Map<Long, List<Media>> mediaMap) {
        return messages.stream()
                .map(message -> convertSingleMessageToResponse(message, participantMap, mediaMap))
                .toList();
    }

    /**
     * Converts single message to response using bulk-loaded data.
     */
    private ChatMessageResponse convertSingleMessageToResponse(ChatMessage message,
                                                              Map<Long, HubParticipant> participantMap,
                                                              Map<Long, List<Media>> mediaMap) {
        HubParticipant sender = participantMap.get(message.getParticipantId());
        List<Media> mediaList = mediaMap.getOrDefault(message.getId(), List.of());
        ChatParticipantDto senderDto = sender != null ? chatConverter.toParticipantDto(sender) : null;
        return chatConverter.toMessageResponse(message, mediaList, senderDto);
    }

    /**
     * Gets a participant by ID.
     */
    private HubParticipant getParticipantById(Long participantId) {
        return participantRepository.findOptionalById(participantId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                        PARTICIPANT_NOT_FOUND_MESSAGE));
    }

    /**
     * Gets a channel by ID.
     */
    private ChatChannel getChannelById(Long channelId) {
        ChatChannel channel = channelRepository.fetchOneById(channelId);
        if (channel == null) {
            throw new NotFoundException(ErrorCode.CHAT_CHANNEL_NOT_FOUND, CHANNEL_NOT_FOUND_MESSAGE);
        }
        return channel;
    }

    /**
     * Checks if a message request has attachments.
     */
    private boolean hasAttachments(ChatMessageRequest request) {
        return hasAttachmentsList(request.getAttachments()) || hasAttachmentUris(request.getAttachmentUris());
    }

    /**
     * Checks if a message update request has attachments.
     */
    private boolean hasAttachmentsUpdate(ChatMessageUpdateRequest request) {
        return hasAttachmentsList(request.getAttachments()) || hasAttachmentUris(request.getAttachmentUris());
    }

    /**
     * Checks if attachments list is not null and not empty.
     */
    private boolean hasAttachmentsList(List<AttachmentRequestDto> attachments) {
        return attachments != null && !attachments.isEmpty();
    }

    /**
     * Checks if attachment URIs list is not null and not empty.
     */
    private boolean hasAttachmentUris(List<String> attachmentUris) {
        return attachmentUris != null && !attachmentUris.isEmpty();
    }

    /**
     * Resolves participant ID by email address.
     *
     * @param email the participant email
     * @return the participant ID
     * @throws NotFoundException if participant not found
     */
    private Long resolveParticipantIdByEmail(String email) {
        Optional<HubParticipant> participant = participantRepository.findByEmail(email);
        return participant.map(HubParticipant::getId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                        String.format(PARTICIPANT_NOT_FOUND_FOR_EMAIL_MESSAGE, email)));
    }

    /**
     * Processes attachment media from a chat message request.
     */
    private List<Long> processAttachmentMedia(ChatMessageRequest request, Long hubId) {
        if (!hasAttachments(request)) {
            return List.of();
        }

        List<String> attachmentUris = extractAttachmentUris(request);
        return createMediaRecordsFromUris(attachmentUris, hubId);
    }

    /**
     * Processes attachment media from a chat message update request.
     */
    private List<Long> processAttachmentMediaFromUpdate(ChatMessageUpdateRequest request, Long hubId) {
        if (!hasAttachmentsUpdate(request)) {
            return List.of();
        }

        List<String> attachmentUris = extractAttachmentUrisFromUpdate(request);
        return createMediaRecordsFromUris(attachmentUris, hubId);
    }

    /**
     * Extracts attachment URIs from a chat message request.
     */
    private List<String> extractAttachmentUris(ChatMessageRequest request) {
        return extractUrisFromRequest(request.getAttachmentUris(), request.getAttachments());
    }

    /**
     * Extracts attachment URIs from a chat message update request.
     */
    private List<String> extractAttachmentUrisFromUpdate(ChatMessageUpdateRequest request) {
        return extractUrisFromRequest(request.getAttachmentUris(), request.getAttachments());
    }

    /**
     * Common method to extract URIs from request data.
     */
    private List<String> extractUrisFromRequest(List<String> attachmentUris, List<AttachmentRequestDto> attachments) {
        if (attachmentUris != null && !attachmentUris.isEmpty()) {
            return attachmentUris;
        } else if (attachments != null && !attachments.isEmpty()) {
            return attachments.stream()
                    .map(AttachmentRequestDto::getUrl)
                    .toList();
        }
        return List.of();
    }

    /**
     * Creates media records from attachment URIs.
     */
    private List<Long> createMediaRecordsFromUris(List<String> attachmentUris, Long hubId) {
        if (attachmentUris.isEmpty()) {
            return List.of();
        }

        Long accountId = extractAccountIdFromContext(hubId);
        return attachmentUris.stream()
                .map(uri -> createMediaRecordFromUri(uri, accountId))
                .toList();
    }

    /**
     * Creates a media record from a single URI.
     */
    private Long createMediaRecordFromUri(String uri, Long accountId) {
        try {
            MediaDto mediaDto = createMediaFromUri(uri, accountId);
            return mediaDto.getId();
        } catch (Exception e) {
            logger.error("Failed to create/validate media record for attachment: {}", uri, e);
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                String.format(INVALID_ATTACHMENT_MESSAGE, uri));
        }
    }

    /**
     * Creates media DTO from URI.
     */
    private MediaDto createMediaFromUri(String uri, Long accountId) {
        return mediaService.createMediaFromUrl(
                uri,
                extractFilenameFromUrl(uri),
                0L, // Size unknown from URL
                determineMimeTypeFromUrl(uri),
                accountId
        );
    }

    /**
     * Extracts account ID from hub context.
     */
    private Long extractAccountIdFromContext(Long hubId) {
        Long accountId = hubRepository.getAccountIdForHub(hubId);
        if (accountId == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                String.format(ACCOUNT_ID_DETERMINATION_ERROR_MESSAGE, hubId));
        }
        return accountId;
    }

    /**
     * Extracts filename from URL.
     */
    private String extractFilenameFromUrl(String url) {
        try {
            String[] parts = url.split("/");
            return parts[parts.length - 1];
        } catch (Exception e) {
            return UNKNOWN_FILE_NAME;
        }
    }

    /**
     * Sends chat mention notifications to mentioned users (both internal and external).
     * Uses centralized notification system with internationalization support.
     */
    private void sendChatMentionNotifications(List<MentionDto> mentions, ChatChannel channel,
                                            HubParticipant sender, String messageContent) {
        if (mentions.isEmpty() || messageContent == null) {
            return;
        }

        try {
            // Convert mentions to NotificationRecipients, excluding self-mentions
            List<NotificationRecipient> recipients = new ArrayList<>();

            for (MentionDto mention : mentions) {
                // Skip self-mentions
                if (mention.getParticipantId().equals(sender.getId())) {
                    continue;
                }

                // Get participant details and convert to NotificationRecipient
                HubParticipant mentionedParticipant = participantRepository.findById(mention.getParticipantId());
                if (mentionedParticipant != null) {
                    NotificationRecipient recipient = convertHubParticipantToRecipient(mentionedParticipant);
                    if (recipient != null) {
                        recipients.add(recipient);
                    }
                }
            }

            if (!recipients.isEmpty()) {
                sendChatMentionNotifications(recipients, channel, sender.getName(), messageContent);
                logger.info("Sent chat mention notifications for channel {} to {} recipients",
                           channel.getId(), recipients.size());
            }

        } catch (Exception e) {
            logger.warn("Failed to send chat mention notifications for channel {}: {}",
                       channel.getId(), e.getMessage());
        }
    }



    /**
     * Determines MIME type from URL extension using predefined mappings.
     */
    private String determineMimeTypeFromUrl(String url) {
        String lowerUrl = url.toLowerCase();

        return FILE_EXTENSION_MIME_TYPES.entrySet().stream()
                .filter(entry -> lowerUrl.endsWith(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(DEFAULT_MIME_TYPE);
    }

    /**
     * Converts a HubParticipant to a NotificationRecipient for the unified system.
     */
    private NotificationRecipient convertHubParticipantToRecipient(HubParticipant participant) {
        try {
            if (participant.getUserId() != null) {
                // Internal user - need to get user details
                // For now, we'll use a simplified approach
                return NotificationRecipientUtils.fromHubParticipant(participant);
            } else {
                // External user
                return NotificationRecipientUtils.fromHubParticipant(participant);
            }
        } catch (Exception e) {
            logger.warn("Failed to convert hub participant {} to notification recipient: {}",
                       participant.getId(), e.getMessage());
            return null;
        }
    }

    /**
     * Sends chat mention notifications using the unified notification system.
     */
    private void sendChatMentionNotifications(List<NotificationRecipient> recipients,
                                            ChatChannel channel, String senderName, String messageContent) {
        try {
            // Create entity references for deep linking
            com.collabhub.be.modules.notifications.service.NotificationStorageService.EntityReferences entityReferences =
                com.collabhub.be.modules.notifications.service.NotificationStorageService.EntityReferences.chat(channel.getHubId(), channel.getId());

            // Create metadata with chat context
            com.collabhub.be.modules.notifications.dto.NotificationMetadata metadata =
                com.collabhub.be.modules.notifications.dto.NotificationMetadata.builder()
                    .actorName(senderName)
                    .targetTitle(channel.getName())
                    .commentPreview(truncateMessage(messageContent))
                    .build();

            // Send notification using unified system
            notificationDispatcherService.dispatchMixedNotification(
                com.collabhub.be.modules.notifications.dto.NotificationType.CHAT_MENTION,
                "You were mentioned in " + channel.getName(),
                senderName + " mentioned you: " + truncateMessage(messageContent),
                recipients,
                entityReferences,
                metadata
            );

        } catch (Exception e) {
            logger.error("Failed to send chat mention notifications: {}", e.getMessage(), e);
        }
    }

    /**
     * Truncates message content for notification preview.
     */
    private String truncateMessage(String message) {
        if (message == null) return "";
        return message.length() > 100 ? message.substring(0, 97) + "..." : message;
    }
}
