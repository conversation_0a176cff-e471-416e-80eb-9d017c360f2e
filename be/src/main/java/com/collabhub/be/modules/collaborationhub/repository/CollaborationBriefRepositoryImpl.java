package com.collabhub.be.modules.collaborationhub.repository;

import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.generated.tables.daos.CollaborationBriefDao;
import org.jooq.generated.tables.pojos.CollaborationBrief;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static org.jooq.generated.tables.CollaborationBrief.COLLABORATION_BRIEF;
import static org.jooq.generated.tables.CollaborationHub.COLLABORATION_HUB;
import static org.jooq.generated.tables.HubParticipant.HUB_PARTICIPANT;
import static org.jooq.generated.tables.User.USER;

/**
 * Repository for CollaborationBrief entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy support.
 */
@Repository
public class CollaborationBriefRepositoryImpl extends CollaborationBriefDao {

    private final DSLContext dsl;

    public CollaborationBriefRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds briefs for a specific hub with pagination and filtering.
     *
     * @param hubId the hub ID
     * @param accountId the account ID for multi-tenancy
     * @param titleFilter optional title filter (case-insensitive partial match)
     * @param offset pagination offset
     * @param limit pagination limit
     * @return list of briefs matching the criteria
     */
    public List<CollaborationBrief> findBriefsWithPagination(Long hubId, Long accountId, String titleFilter, int offset, int limit) {
        var query = dsl.select(COLLABORATION_BRIEF.fields())
                .from(COLLABORATION_BRIEF)
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(COLLABORATION_BRIEF.HUB_ID))
                .where(COLLABORATION_BRIEF.HUB_ID.eq(hubId))
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId));

        // Add title filter if provided
        if (titleFilter != null && !titleFilter.trim().isEmpty()) {
            query = query.and(COLLABORATION_BRIEF.TITLE.containsIgnoreCase(titleFilter.trim()));
        }

        return query.orderBy(COLLABORATION_BRIEF.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(CollaborationBrief.class);
    }

    /**
     * Counts briefs for a specific hub with filtering.
     *
     * @param hubId the hub ID
     * @param accountId the account ID for multi-tenancy
     * @param titleFilter optional title filter (case-insensitive partial match)
     * @return total count of briefs matching the criteria
     */
    public long countBriefsWithFilter(Long hubId, Long accountId, String titleFilter) {
        var query = dsl.selectCount()
                .from(COLLABORATION_BRIEF)
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(COLLABORATION_BRIEF.HUB_ID))
                .where(COLLABORATION_BRIEF.HUB_ID.eq(hubId))
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId));

        // Add title filter if provided
        if (titleFilter != null && !titleFilter.trim().isEmpty()) {
            query = query.and(COLLABORATION_BRIEF.TITLE.containsIgnoreCase(titleFilter.trim()));
        }

        return query.fetchOne(0, Long.class);
    }

    /**
     * Finds a brief by ID with multi-tenancy validation.
     *
     * @param briefId the brief ID
     * @param hubId the hub ID
     * @param accountId the account ID for multi-tenancy
     * @return the brief if found and accessible
     */
    public Optional<CollaborationBrief> findByIdAndHubIdAndAccountId(Long briefId, Long hubId, Long accountId) {
        var result = dsl.select(COLLABORATION_BRIEF.fields())
                .from(COLLABORATION_BRIEF)
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(COLLABORATION_BRIEF.HUB_ID))
                .where(COLLABORATION_BRIEF.ID.eq(briefId))
                .and(COLLABORATION_BRIEF.HUB_ID.eq(hubId))
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .fetchOneInto(CollaborationBrief.class);

        return Optional.ofNullable(result);
    }

    /**
     * Finds briefs with creator participant information for list display.
     * This method joins with participant and user tables to get creator names.
     *
     * @param hubId the hub ID
     * @param accountId the account ID for multi-tenancy
     * @param titleFilter optional title filter
     * @param offset pagination offset
     * @param limit pagination limit
     * @return list of records with brief and creator information
     */
    public List<Record> findBriefsWithCreatorInfo(Long hubId, Long accountId, String titleFilter, int offset, int limit) {
        var query = dsl.select(
                        COLLABORATION_BRIEF.fields())
                .select(
                        HUB_PARTICIPANT.EMAIL.as("creator_email"),
                        USER.DISPLAY_NAME.as("creator_display_name")
                )
                .from(COLLABORATION_BRIEF)
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(COLLABORATION_BRIEF.HUB_ID))
                .join(HUB_PARTICIPANT).on(HUB_PARTICIPANT.ID.eq(COLLABORATION_BRIEF.CREATED_BY_PARTICIPANT_ID))
                .leftJoin(USER).on(USER.ID.eq(HUB_PARTICIPANT.USER_ID))
                .where(COLLABORATION_BRIEF.HUB_ID.eq(hubId))
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId));

        // Add title filter if provided
        if (titleFilter != null && !titleFilter.trim().isEmpty()) {
            query = query.and(COLLABORATION_BRIEF.TITLE.containsIgnoreCase(titleFilter.trim()));
        }

        return query.orderBy(COLLABORATION_BRIEF.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetch();
    }

    /**
     * Finds a brief with creator participant information.
     *
     * @param briefId the brief ID
     * @param hubId the hub ID
     * @return the record with brief and creator information
     */
    public Optional<Record> findBriefWithCreatorInfo(Long briefId, Long hubId) {
        var result = dsl.select(
                        COLLABORATION_BRIEF.fields())
                .select(
                        HUB_PARTICIPANT.EMAIL.as("creator_email"),
                        USER.DISPLAY_NAME.as("creator_display_name")
                )
                .from(COLLABORATION_BRIEF)
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(COLLABORATION_BRIEF.HUB_ID))
                .join(HUB_PARTICIPANT).on(HUB_PARTICIPANT.ID.eq(COLLABORATION_BRIEF.CREATED_BY_PARTICIPANT_ID))
                .leftJoin(USER).on(USER.ID.eq(HUB_PARTICIPANT.USER_ID))
                .where(COLLABORATION_BRIEF.ID.eq(briefId))
                .and(COLLABORATION_BRIEF.HUB_ID.eq(hubId))
                .fetchOne();

        return Optional.ofNullable(result);
    }


    /**
     * Gets the account ID for a hub.
     *
     * @param hubId the hub ID
     * @return the account ID
     */
    public Long getAccountIdByHubId(Long hubId) {
        return dsl.select(COLLABORATION_HUB.ACCOUNT_ID)
                .from(COLLABORATION_HUB)
                .where(COLLABORATION_HUB.ID.eq(hubId))
                .fetchOne(COLLABORATION_HUB.ACCOUNT_ID);
    }
}
