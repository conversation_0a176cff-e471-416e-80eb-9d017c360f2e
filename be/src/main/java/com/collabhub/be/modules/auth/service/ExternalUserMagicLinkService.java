package com.collabhub.be.modules.auth.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.dto.RedirectContext;
import com.collabhub.be.modules.auth.dto.RedirectType;
import com.collabhub.be.modules.auth.dto.HubAccessRedirectContext;
import com.collabhub.be.modules.auth.dto.GeneralAccessRedirectContext;
import com.collabhub.be.modules.auth.model.TokenType;
import com.collabhub.be.modules.auth.repository.VerificationTokenRepository;
import com.collabhub.be.util.JsonbUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.VerificationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.Optional;

/**
 * Service for managing email-based magic links for external users.
 * Handles creation, validation, and management of magic links that provide
 * access to multiple collaboration hubs for a single email address.
 */
@Service
public class ExternalUserMagicLinkService {

    private static final Logger logger = LoggerFactory.getLogger(ExternalUserMagicLinkService.class);

    // Constants
    private static final int TOKEN_LENGTH = 32;
    private static final int TOKEN_LOG_LENGTH = 8;
    private static final String TOKEN_LOG_SUFFIX = "...";

    // Error Messages
    private static final String EMAIL_REQUIRED_MESSAGE = "Email is required";
    private static final String ACCOUNT_ID_REQUIRED_MESSAGE = "Account ID is required";
    private static final String TOKEN_REQUIRED_MESSAGE = "Magic link token is required";
    private static final String INVALID_EXPIRED_TOKEN_MESSAGE = "Invalid or expired magic link token";
    private static final String NO_HUB_ACCESS_MESSAGE = "Email has no access to any collaboration hubs";

    // Log Messages
    private static final String CREATING_MAGIC_LINK_LOG = "Creating magic link for email {} in account {}";
    private static final String CREATED_MAGIC_LINK_LOG = "Created magic link for email {} in account {}, expires at {}";
    private static final String VALIDATING_TOKEN_LOG = "Validating magic link token: {}";
    private static final String VALIDATED_TOKEN_SUCCESS_LOG = "Successfully validated magic link token for email {} in account {}";
    private static final String REVOKING_TOKEN_LOG = "Revoking magic link token for email {} in account {}";
    private static final String UPDATING_JOINED_AT_LOG = "Updating joined_at for participant {} in hub {} via magic link";
    private static final String UPDATED_JOINED_AT_SUCCESS_LOG = "Successfully updated joined_at for participant {} in hub {}";
    private static final String UPDATED_JOINED_AT_FAILED_LOG = "Failed to update joined_at for participant {} in hub {}";

    private final VerificationTokenRepository verificationTokenRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final AuthProperties authProperties;
    private final ObjectMapper objectMapper;
    private final SecureRandom secureRandom;

    public ExternalUserMagicLinkService(VerificationTokenRepository verificationTokenRepository,
                                      HubParticipantRepositoryImpl participantRepository,
                                      AuthProperties authProperties,
                                      ObjectMapper objectMapper) {
        this.verificationTokenRepository = verificationTokenRepository;
        this.participantRepository = participantRepository;
        this.authProperties = authProperties;
        this.objectMapper = objectMapper;
        this.secureRandom = new SecureRandom();
    }

    /**
     * Creates a magic link token for an external user email.
     * One magic link provides access to all hubs the email is invited to within the account.
     *
     * @param email the external user's email address
     * @param accountId the account ID for multi-tenant isolation
     * @return the generated magic link token
     */
    @Transactional
    public String createMagicLinkForEmail(@NotBlank String email, @NotNull Long accountId) {
        return createMagicLinkForEmailWithRedirect(email, accountId, null);
    }

    /**
     * Creates a magic link token for an external user email with redirect context.
     * One magic link provides access to all hubs the email is invited to within the account.
     * This method always creates a new token and revokes existing ones.
     *
     * @param email the external user's email address
     * @param accountId the account ID for multi-tenant isolation
     * @param redirectContext the redirect context for post-authentication navigation
     * @return the generated magic link token
     */
    @Transactional
    public String createMagicLinkForEmailWithRedirect(@NotBlank String email, @NotNull Long accountId,
                                                     RedirectContext redirectContext) {
        logger.info(CREATING_MAGIC_LINK_LOG, email, accountId);

        validateInput(email, accountId);

        // Revoke any existing magic links for this email in this account
        revokeExistingMagicLinks(email, accountId);

        // Generate new token
        String token = generateSecureToken();
        LocalDateTime expiresAt = LocalDateTime.now()
                .plus(authProperties.getVerificationToken().getMagicLinkTtl());

        // Create verification token record with redirect context
        VerificationToken verificationToken = verificationTokenRepository.createExternalMagicLinkTokenWithRedirect(
                email, accountId, token, expiresAt, redirectContext);

        logger.info(CREATED_MAGIC_LINK_LOG, email, accountId, expiresAt);
        return token;
    }

    /**
     * Gets an existing active magic link token or creates a new one if none exists.
     * This method preserves existing tokens to prevent invalidating previously sent email links.
     *
     * @param email the external user's email address
     * @param accountId the account ID for multi-tenant isolation
     * @param redirectContext the redirect context for post-authentication navigation
     * @return the existing or newly generated magic link token
     */
    @Transactional
    public String getOrCreateMagicLinkForEmailWithRedirect(@NotBlank String email, @NotNull Long accountId,
                                                          RedirectContext redirectContext) {
        logger.info("Getting or creating magic link for email {} in account {}", email, accountId);

        validateInput(email, accountId);

        // First, try to find an existing active token
        Optional<VerificationToken> existingToken = verificationTokenRepository
                .findActiveExternalMagicLinkToken(email, accountId);

        if (existingToken.isPresent()) {
            logger.info("Reusing existing magic link token for email {} in account {}", email, accountId);
            return existingToken.get().getToken();
        }

        // No active token found, create a new one without revoking existing ones
        logger.info("No active token found, creating new magic link for email {} in account {}", email, accountId);

        String token = generateSecureToken();
        LocalDateTime expiresAt = LocalDateTime.now()
                .plus(authProperties.getVerificationToken().getMagicLinkTtl());

        // Create verification token record with redirect context
        VerificationToken verificationToken = verificationTokenRepository.createExternalMagicLinkTokenWithRedirect(
                email, accountId, token, expiresAt, redirectContext);

        logger.info("Created new magic link for email {} in account {}, expires at {}", email, accountId, expiresAt);
        return token;
    }

    /**
     * Validates a magic link token and returns hub access information.
     *
     * @param token the magic link token
     * @return validation result with email and hub access information
     */
    @Transactional
    public MagicLinkValidationResult validateMagicLinkToken(@NotBlank String token) {
        logger.info(VALIDATING_TOKEN_LOG, truncateTokenForLogging(token));

        validateTokenInput(token);
        VerificationToken verificationToken = findValidMagicLinkToken(token);
        List<HubParticipant> hubParticipants = findHubParticipantsForEmail(
                verificationToken.getEmail(), verificationToken.getAccountId());

        if (hubParticipants.isEmpty()) {
            throw new NotFoundException(ErrorCode.PARTICIPANT_NOT_FOUND, NO_HUB_ACCESS_MESSAGE);
        }

        // Extract redirect context from token
        RedirectContext redirectContext = extractRedirectContext(verificationToken, hubParticipants);

        // Update hub participant joined_at timestamp for first access
        updateHubParticipantJoinedAt(verificationToken.getEmail(), redirectContext, hubParticipants);

        // Mark token as used (first access tracking)
        markTokenAsUsed(verificationToken);

        logger.info(VALIDATED_TOKEN_SUCCESS_LOG, verificationToken.getEmail(), verificationToken.getAccountId());

        return new MagicLinkValidationResult(
                verificationToken.getEmail(),
                verificationToken.getAccountId(),
                redirectContext
        );
    }

    /**
     * Revokes magic link tokens for an email in an account.
     *
     * @param email the email address
     * @param accountId the account ID
     * @return true if tokens were revoked
     */
    @Transactional
    public boolean revokeMagicLinksForEmail(@NotBlank String email, @NotNull Long accountId) {
        logger.info(REVOKING_TOKEN_LOG, email, accountId);
        return revokeExistingMagicLinks(email, accountId);
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    private void validateInput(String email, Long accountId) {
        if (email == null || email.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, EMAIL_REQUIRED_MESSAGE);
        }
        if (accountId == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, ACCOUNT_ID_REQUIRED_MESSAGE);
        }
    }

    private void validateTokenInput(String token) {
        if (token == null || token.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, TOKEN_REQUIRED_MESSAGE);
        }
    }

    private String generateSecureToken() {
        byte[] tokenBytes = new byte[TOKEN_LENGTH];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }

    private boolean revokeExistingMagicLinks(String email, Long accountId) {
        return verificationTokenRepository.revokeExternalMagicLinkTokens(email, accountId);
    }

    private VerificationToken findValidMagicLinkToken(String token) {
        Optional<VerificationToken> tokenOpt = verificationTokenRepository
                .findByTokenAndType(token, TokenType.MAGIC_LINK);

        if (tokenOpt.isEmpty()) {
            throw new NotFoundException(ErrorCode.INVALID_TOKEN, INVALID_EXPIRED_TOKEN_MESSAGE);
        }

        VerificationToken verificationToken = tokenOpt.get();
        
        // Check if token is for external user (has email but no user_id)
        if (verificationToken.getEmail() == null) {
            throw new NotFoundException(ErrorCode.INVALID_TOKEN, INVALID_EXPIRED_TOKEN_MESSAGE);
        }

        return verificationToken;
    }

    private List<HubParticipant> findHubParticipantsForEmail(String email, Long accountId) {
        return participantRepository.findActiveParticipantsByEmailAndAccount(email);
    }

    /**
     * Extracts redirect context from verification token.
     * Determines the appropriate redirect based on token context and user's hub access.
     */
    private RedirectContext extractRedirectContext(VerificationToken token, List<HubParticipant> hubParticipants) {
        // Check if token has explicit redirect context
        if (token.getRedirectContext() != null) {
            try {
                return JsonbUtil.parseJsonb(token.getRedirectContext(), RedirectContext.class, objectMapper);
            } catch (Exception e) {
                logger.warn("Failed to parse redirect context from token for email {}: {}",
                           token.getEmail(), e.getMessage());
            }
        }

        // Check if token has redirect type specified
        if (token.getRedirectType() != null) {
            RedirectType redirectType = RedirectType.fromValue(token.getRedirectType());
            return createDefaultRedirectContext(redirectType, hubParticipants);
        }

        // Default: redirect to first available hub for backward compatibility
        if (!hubParticipants.isEmpty()) {
            HubParticipant firstHub = hubParticipants.get(0);
            return new HubAccessRedirectContext(firstHub.getHubId(), firstHub.getRole().name());
        }

        // Fallback to general access
        return new GeneralAccessRedirectContext("/dashboard");
    }

    /**
     * Creates default redirect context based on redirect type.
     */
    private RedirectContext createDefaultRedirectContext(RedirectType redirectType, List<HubParticipant> hubParticipants) {
        return switch (redirectType) {
            case HUB_ACCESS -> {
                if (!hubParticipants.isEmpty()) {
                    HubParticipant firstHub = hubParticipants.get(0);
                    yield new HubAccessRedirectContext(firstHub.getHubId(), firstHub.getRole().name());
                }
                yield new GeneralAccessRedirectContext("/dashboard");
            }
            case INVOICE_ACCESS -> new GeneralAccessRedirectContext("/invoices");
            case GENERAL_ACCESS -> new GeneralAccessRedirectContext("/dashboard");
        };
    }

    /**
     * Updates hub participant joined_at timestamp for first access to specific hubs.
     * This tracks when external participants first access each collaboration hub.
     *
     * Logic:
     * - If redirect context specifies a hub ID, update joined_at only for that hub
     * - If no specific hub context, update joined_at for all hubs where user hasn't joined yet
     * - Only updates participants where joined_at is currently null (first access)
     */
    private void updateHubParticipantJoinedAt(String email, RedirectContext redirectContext,
                                            List<HubParticipant> hubParticipants) {
        Long targetHubId = extractTargetHubId(redirectContext);

        if (targetHubId != null) {
            logger.debug("Magic link authentication for specific hub {}, updating joined_at for that hub only", targetHubId);
            updateJoinedAtForHub(email, targetHubId, hubParticipants);
        }
    }

    /**
     * Extracts the target hub ID from redirect context.
     */
    private Long extractTargetHubId(RedirectContext redirectContext) {
        if (redirectContext instanceof HubAccessRedirectContext hubContext) {
            return hubContext.getHubId();
        }
        return null;
    }

    /**
     * Updates joined_at timestamp for a specific hub if the participant hasn't joined yet.
     */
    private void updateJoinedAtForHub(String email, Long hubId, List<HubParticipant> hubParticipants) {
        HubParticipant targetParticipant = hubParticipants.stream()
                .filter(p -> p.getHubId().equals(hubId))
                .filter(p -> p.getJoinedAt() == null) // Only update if not already joined
                .findFirst()
                .orElse(null);

        if (targetParticipant != null) {
            logger.debug(UPDATING_JOINED_AT_LOG, targetParticipant.getId(), hubId);
            boolean updated = participantRepository.updateJoinedAt(targetParticipant.getId());
            if (updated) {
                logger.info(UPDATED_JOINED_AT_SUCCESS_LOG, targetParticipant.getId(), hubId);
            } else {
                logger.warn(UPDATED_JOINED_AT_FAILED_LOG, targetParticipant.getId(), hubId);
            }
        } else {
            logger.debug("No unjoined participant found for hub {} and email {}", hubId, email);
        }
    }

    private void markTokenAsUsed(VerificationToken token) {
        // This could be implemented if we want to track first usage
        // For now, we keep the token valid until expiration for reusability
    }

    private boolean isFirstAccess(List<HubParticipant> participants) {
        return participants.stream().anyMatch(p -> p.getJoinedAt() == null);
    }

    private String truncateTokenForLogging(String token) {
        return token.substring(0, Math.min(TOKEN_LOG_LENGTH, token.length())) + TOKEN_LOG_SUFFIX;
    }

    /**
     * Result of magic link token validation for external users.
     * Includes redirect context for automatic redirection after authentication.
     */
    public record MagicLinkValidationResult(
            String email,
            Long accountId,
            RedirectContext redirectContext
    ) {
    }
}
