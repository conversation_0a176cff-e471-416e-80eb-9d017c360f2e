package com.collabhub.be.modules.auth.model;

/**
 * Types of verification tokens supported by the system.
 * Used for email verification, magic links, and password reset functionality.
 */
public enum TokenType {
    /**
     * Token used for email verification during user registration.
     * Single-use token with 24-hour expiration.
     */
    EMAIL_VERIFICATION,
    
    /**
     * Token used for magic link authentication for external users.
     * Single-use token with configurable expiration.
     */
    MAGIC_LINK,
    
    /**
     * Token used for password reset functionality.
     * Single-use token with shorter expiration for security.
     */
    PASSWORD_RESET,

    /**
     * Token used for one-click email notification unsubscribe.
     * Single-use token with configurable expiration for security.
     */
    UNSUBSCRIBE_TOKEN;
    
    /**
     * Returns the token type name as a string for database storage.
     */
    public String getTypeName() {
        return this.name();
    }
    
    /**
     * Creates a TokenType from a string value.
     * 
     * @param typeName the token type name string
     * @return the corresponding TokenType enum
     * @throws IllegalArgumentException if the token type name is invalid
     */
    public static TokenType fromString(String typeName) {
        if (typeName == null || typeName.trim().isEmpty()) {
            throw new IllegalArgumentException("Token type name cannot be null or empty");
        }
        
        try {
            return TokenType.valueOf(typeName.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid token type: " + typeName, e);
        }
    }
}
