package com.collabhub.be.modules.auth.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Redirect context for general access.
 * Used when external users authenticate via magic link without specific context.
 * Redirects to a default page (dashboard, home, etc.).
 */
public class GeneralAccessRedirectContext extends RedirectContext {
    
    private final String defaultPage;
    
    @JsonCreator
    public GeneralAccessRedirectContext(@JsonProperty("defaultPage") String defaultPage) {
        this.defaultPage = defaultPage != null ? defaultPage : "/dashboard";
    }
    
    public String getDefaultPage() {
        return defaultPage;
    }
    
    @Override
    public RedirectType getType() {
        return RedirectType.GENERAL_ACCESS;
    }
    
    @Override
    public String getTargetUrl() {
        return defaultPage;
    }
    
    @Override
    public boolean isValid() {
        return defaultPage != null && !defaultPage.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "GeneralAccessRedirectContext{" +
                "defaultPage='" + defaultPage + '\'' +
                '}';
    }
}
