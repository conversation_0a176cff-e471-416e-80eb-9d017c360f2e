package com.collabhub.be.modules.bankdetails.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for creating a new bank detail.
 * Contains validation rules matching database constraints.
 */
public class BankDetailsCreateRequest {

    @NotBlank(message = "Name is required")
    @Size(max = 255, message = "Name must not exceed 255 characters")
    private String name;

    @Size(max = 255, message = "Bank name must not exceed 255 characters")
    @JsonProperty("bank_name")
    private String bankName;

    @Size(min = 15, max = 34, message = "IBAN must be between 15 and 34 characters")
    private String iban;

    @Size(min = 8, max = 11, message = "BIC/SWIFT must be 8 or 11 characters")
    @JsonProperty("bic_swift")
    private String bicSwift;

    public BankDetailsCreateRequest() {
    }

    public BankDetailsCreateRequest(String name, String bankName, String iban, String bicSwift) {
        this.name = name;
        this.bankName = bankName;
        this.iban = iban;
        this.bicSwift = bicSwift;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getBicSwift() {
        return bicSwift;
    }

    public void setBicSwift(String bicSwift) {
        this.bicSwift = bicSwift;
    }

    @Override
    public String toString() {
        return "BankDetailsCreateRequest{" +
                "name='" + name + '\'' +
                ", bankName='" + bankName + '\'' +
                ", iban='" + (iban != null ? "***" + iban.substring(Math.max(0, iban.length() - 4)) : null) + '\'' +
                ", bicSwift='" + bicSwift + '\'' +
                '}';
    }
}
