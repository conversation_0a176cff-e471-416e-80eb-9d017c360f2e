package com.collabhub.be.modules.notifications.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Immutable context object containing all information needed for batch processing operations.
 * 
 * <p>This record encapsulates the complete state and metadata required for processing
 * a batch of notifications, providing type safety and clear contracts for batch
 * processing operations.</p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * BatchProcessingContext context = BatchProcessingContext.builder()
 *     .batchId("user_123_2024-01-15T10:30:00")
 *     .userId(123L)
 *     .batchWindowStart(LocalDateTime.of(2024, 1, 15, 10, 30))
 *     .notifications(notificationList)
 *     .processingAttempt(1)
 *     .maxRetryAttempts(3)
 *     .build();
 * 
 * BatchProcessingResult result = batchProcessor.process(context);
 * </pre>
 * 
 * @param batchId Unique identifier for this batch (format: "user_{userId}_{timestamp}")
 * @param userId The user ID for whom this batch is being processed
 * @param batchWindowStart The start time of the batching window for these notifications
 * @param notifications List of notifications to be processed in this batch
 * @param processingAttempt Current processing attempt number (1-based)
 * @param maxRetryAttempts Maximum number of retry attempts allowed
 * @param lastProcessingError Error message from the previous processing attempt, if any
 * @param estimatedProcessingTime Estimated time required to process this batch
 * @param priority Processing priority for this batch
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public record BatchProcessingContext(
    
    @NotNull(message = "Batch ID cannot be null")
    String batchId,
    
    @NotNull(message = "User ID cannot be null")
    @Positive(message = "User ID must be positive")
    Long userId,
    
    @NotNull(message = "Batch window start cannot be null")
    LocalDateTime batchWindowStart,
    
    @NotNull(message = "Notifications list cannot be null")
    List<NotificationBatchQueueItem> notifications,
    
    @PositiveOrZero(message = "Processing attempt must be non-negative")
    int processingAttempt,
    
    @Positive(message = "Max retry attempts must be positive")
    int maxRetryAttempts,
    
    String lastProcessingError,
    
    Long estimatedProcessingTimeMs,
    
    BatchPriority priority
) {
    
    /**
     * Enumeration for batch processing priorities.
     */
    public enum BatchPriority {
        /**
         * Low priority batch - can be processed during off-peak hours.
         */
        LOW("Low priority, process during off-peak hours"),
        
        /**
         * Normal priority batch - standard processing queue.
         */
        NORMAL("Normal priority, standard processing"),
        
        /**
         * High priority batch - process as soon as possible.
         */
        HIGH("High priority, expedited processing"),
        
        /**
         * Urgent priority batch - process immediately.
         */
        URGENT("Urgent priority, immediate processing required");
        
        private final String description;
        
        BatchPriority(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Compact constructor with validation and defaults.
     */
    public BatchProcessingContext {
        Objects.requireNonNull(batchId, "Batch ID cannot be null");
        Objects.requireNonNull(userId, "User ID cannot be null");
        Objects.requireNonNull(batchWindowStart, "Batch window start cannot be null");
        Objects.requireNonNull(notifications, "Notifications list cannot be null");
        
        if (userId <= 0) {
            throw new IllegalArgumentException("User ID must be positive");
        }
        
        if (processingAttempt < 0) {
            throw new IllegalArgumentException("Processing attempt must be non-negative");
        }
        
        if (maxRetryAttempts <= 0) {
            throw new IllegalArgumentException("Max retry attempts must be positive");
        }
        
        if (notifications.isEmpty()) {
            throw new IllegalArgumentException("Notifications list cannot be empty");
        }
        
        // Set defaults
        if (priority == null) {
            priority = BatchPriority.NORMAL;
        }
        
        if (estimatedProcessingTimeMs == null) {
            estimatedProcessingTimeMs = calculateEstimatedProcessingTime(notifications.size());
        }
    }
    
    /**
     * Creates a builder for constructing BatchProcessingContext instances.
     * 
     * @return a new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * Returns the number of notifications in this batch.
     * 
     * @return the notification count
     */
    public int getNotificationCount() {
        return notifications.size();
    }
    
    /**
     * Checks if this batch has exceeded the maximum retry attempts.
     * 
     * @return true if processing attempt exceeds max retry attempts
     */
    public boolean hasExceededMaxRetries() {
        return processingAttempt > maxRetryAttempts;
    }
    
    /**
     * Checks if this is the first processing attempt.
     * 
     * @return true if this is the first attempt (processingAttempt == 1)
     */
    public boolean isFirstAttempt() {
        return processingAttempt == 1;
    }
    
    /**
     * Gets the last processing error if present.
     * 
     * @return Optional containing the error message, or empty if no previous error
     */
    public Optional<String> getLastProcessingError() {
        return Optional.ofNullable(lastProcessingError);
    }
    
    /**
     * Gets the estimated processing time in milliseconds.
     * 
     * @return the estimated processing time
     */
    public long getEstimatedProcessingTimeMs() {
        return estimatedProcessingTimeMs;
    }
    
    /**
     * Checks if this batch should be processed immediately based on priority.
     * 
     * @return true if priority is HIGH or URGENT
     */
    public boolean requiresImmediateProcessing() {
        return priority == BatchPriority.HIGH || priority == BatchPriority.URGENT;
    }
    
    /**
     * Creates a new context for the next retry attempt.
     * 
     * @param errorMessage the error message from the failed attempt
     * @return a new BatchProcessingContext with incremented attempt count
     */
    public BatchProcessingContext forNextRetry(String errorMessage) {
        return new BatchProcessingContext(
            batchId,
            userId,
            batchWindowStart,
            notifications,
            processingAttempt + 1,
            maxRetryAttempts,
            errorMessage,
            estimatedProcessingTimeMs,
            priority
        );
    }
    
    /**
     * Calculates estimated processing time based on notification count.
     * 
     * @param notificationCount the number of notifications to process
     * @return estimated processing time in milliseconds
     */
    private static long calculateEstimatedProcessingTime(int notificationCount) {
        // Base time: 100ms + 50ms per notification
        return 100L + (notificationCount * 50L);
    }
    
    /**
     * Builder class for constructing BatchProcessingContext instances.
     */
    public static class Builder {
        private String batchId;
        private Long userId;
        private LocalDateTime batchWindowStart;
        private List<NotificationBatchQueueItem> notifications;
        private int processingAttempt = 1;
        private int maxRetryAttempts = 3;
        private String lastProcessingError;
        private Long estimatedProcessingTimeMs;
        private BatchPriority priority = BatchPriority.NORMAL;
        
        private Builder() {}
        
        public Builder batchId(String batchId) {
            this.batchId = batchId;
            return this;
        }
        
        public Builder userId(Long userId) {
            this.userId = userId;
            return this;
        }
        
        public Builder batchWindowStart(LocalDateTime batchWindowStart) {
            this.batchWindowStart = batchWindowStart;
            return this;
        }
        
        public Builder notifications(List<NotificationBatchQueueItem> notifications) {
            this.notifications = notifications;
            return this;
        }
        
        public Builder processingAttempt(int processingAttempt) {
            this.processingAttempt = processingAttempt;
            return this;
        }
        
        public Builder maxRetryAttempts(int maxRetryAttempts) {
            this.maxRetryAttempts = maxRetryAttempts;
            return this;
        }
        
        public Builder lastProcessingError(String lastProcessingError) {
            this.lastProcessingError = lastProcessingError;
            return this;
        }
        
        public Builder estimatedProcessingTimeMs(Long estimatedProcessingTimeMs) {
            this.estimatedProcessingTimeMs = estimatedProcessingTimeMs;
            return this;
        }
        
        public Builder priority(BatchPriority priority) {
            this.priority = priority;
            return this;
        }
        
        /**
         * Builds the BatchProcessingContext instance.
         * 
         * @return a new BatchProcessingContext with the configured values
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public BatchProcessingContext build() {
            return new BatchProcessingContext(
                batchId,
                userId,
                batchWindowStart,
                notifications,
                processingAttempt,
                maxRetryAttempts,
                lastProcessingError,
                estimatedProcessingTimeMs,
                priority
            );
        }
    }
}
