package com.collabhub.be.modules.posts.dto;

import com.collabhub.be.modules.posts.constants.PostConstants;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.jooq.generated.enums.ReviewStatus;

/**
 * Request DTO for submitting or updating a post review.
 */
public class PostReviewRequest {

    @NotNull(message = "Review status is required")
    private ReviewStatus status;

    @JsonProperty("review_notes")
    @Size(max = PostConstants.MAX_REVIEW_NOTES_LENGTH, message = PostConstants.REVIEW_NOTES_TOO_LONG_MESSAGE)
    private String reviewNotes;

    public PostReviewRequest() {}

    public PostReviewRequest(ReviewStatus status, String reviewNotes) {
        this.status = status;
        this.reviewNotes = reviewNotes;
    }

    public ReviewStatus getStatus() {
        return status;
    }

    public void setStatus(ReviewStatus status) {
        this.status = status;
    }

    public String getReviewNotes() {
        return reviewNotes;
    }

    public void setReviewNotes(String reviewNotes) {
        this.reviewNotes = reviewNotes;
    }

    @Override
    public String toString() {
        return "PostReviewRequest{" +
                "status=" + status +
                ", reviewNotes='" + reviewNotes + '\'' +
                '}';
    }
}
