package com.collabhub.be.modules.auth.config;

import com.collabhub.be.modules.auth.constants.JwtClaims;
import com.collabhub.be.modules.auth.model.Role;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * Custom JWT authentication converter that extracts authorities from JWT claims.
 * Converts user roles and their associated permissions into Spring Security authorities
 * for use in @PreAuthorize annotations and security checks.
 */
@Component
public class JwtAuthenticationConverter implements Converter<Jwt, AbstractAuthenticationToken> {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationConverter.class);

    // Constants
    private static final String ROLE_PREFIX = "ROLE_";
    private static final String UNKNOWN_ROLE_LOG = "Unknown role '{}' found in JWT token, skipping permission assignment";

    /**
     * Converts a JWT token into a Spring Security authentication token with authorities.
     *
     * @param jwt the JWT token to convert
     * @return authentication token with extracted authorities
     */
    @Override
    public AbstractAuthenticationToken convert(Jwt jwt) {
        if (jwt == null) {
            logger.warn("Received null JWT token");
            return new JwtAuthenticationToken(jwt, Collections.emptySet());
        }

        Collection<GrantedAuthority> authorities = extractAuthorities(jwt);
        return new JwtAuthenticationToken(jwt, authorities);
    }

    /**
     * Extracts authorities from JWT claims based on user role.
     * Creates both role-based authorities (ROLE_*) and permission-based authorities.
     *
     * @param jwt the JWT token containing role claims
     * @return collection of granted authorities
     */
    private Collection<GrantedAuthority> extractAuthorities(Jwt jwt) {
        Set<GrantedAuthority> authorities = new HashSet<>();

        String roleName = jwt.getClaimAsString(JwtClaims.ROLE);
        if (roleName == null || roleName.trim().isEmpty()) {
            logger.debug("No role found in JWT token");
            return authorities;
        }

        // Add role-based authority
        authorities.add(new SimpleGrantedAuthority(ROLE_PREFIX + roleName));

        // Add permission-based authorities from role
        addPermissionAuthorities(authorities, roleName);

        return authorities;
    }

    /**
     * Adds permission-based authorities for the given role.
     *
     * @param authorities the set to add authorities to
     * @param roleName the role name to get permissions for
     */
    private void addPermissionAuthorities(Set<GrantedAuthority> authorities, String roleName) {
        try {
            Role role = Role.valueOf(roleName);
            role.getPermissions().forEach(permission ->
                authorities.add(new SimpleGrantedAuthority(permission.getPermission()))
            );
        } catch (IllegalArgumentException e) {
            logger.warn(UNKNOWN_ROLE_LOG, roleName);
        }
    }
}
