package com.collabhub.be.modules.brands.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

/**
 * Lightweight response DTO for brand list operations.
 * Contains only essential fields needed for list views to optimize performance.
 */
public class BrandListItemDto {

    @NotNull
    private Long id;

    @NotNull
    private String name;

    @NotNull
    @JsonProperty("company_name")
    private String companyName;

    private String email;

    private String phone;

    private String website;

    public BrandListItemDto() {
    }

    public BrandListItemDto(Long id, String name, String companyName, String email, String phone, String website) {
        this.id = id;
        this.name = name;
        this.companyName = companyName;
        this.email = email;
        this.phone = phone;
        this.website = website;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    @Override
    public String toString() {
        return "BrandListItemDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", companyName='" + companyName + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", website='" + website + '\'' +
                '}';
    }
}
