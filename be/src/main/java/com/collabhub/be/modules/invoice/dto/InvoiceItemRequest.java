package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * Request DTO for invoice line items.
 * Used when creating or updating invoice items.
 */
public class InvoiceItemRequest {

    @NotBlank(message = "Description is required")
    @Size(max = 1000, message = "Description cannot exceed 1000 characters")
    private String description;

    @NotNull(message = "Quantity is required")
    @DecimalMin(value = "0.001", message = "Quantity must be greater than 0")
    @Digits(integer = 10, fraction = 3, message = "Quantity format is invalid")
    private BigDecimal quantity = BigDecimal.ONE;

    @NotNull(message = "Unit price is required")
    @DecimalMin(value = "0", message = "Unit price cannot be negative")
    @Digits(integer = 10, fraction = 2, message = "Unit price format is invalid")
    @JsonProperty("unit_price")
    private BigDecimal unitPrice;

    @JsonProperty("vat_rate")
    @DecimalMin(value = "0", message = "VAT rate cannot be negative")
    @DecimalMax(value = "1", message = "VAT rate cannot exceed 100%")
    @Digits(integer = 1, fraction = 4, message = "VAT rate format is invalid")
    private BigDecimal vatRate = BigDecimal.ZERO;

    @JsonProperty("sort_order")
    @Min(value = 0, message = "Sort order cannot be negative")
    private Integer sortOrder = 0;

    public InvoiceItemRequest() {
    }

    public InvoiceItemRequest(String description, BigDecimal quantity, BigDecimal unitPrice, 
                            BigDecimal vatRate, Integer sortOrder) {
        this.description = description;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.vatRate = vatRate;
        this.sortOrder = sortOrder;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getVatRate() {
        return vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public String toString() {
        return "InvoiceItemRequest{" +
                "description='" + description + '\'' +
                ", quantity=" + quantity +
                ", unitPrice=" + unitPrice +
                ", vatRate=" + vatRate +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
