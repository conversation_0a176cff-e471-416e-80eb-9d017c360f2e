package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Response DTO for next invoice number generation.
 * Contains the suggested next invoice number for creating a new invoice.
 */
public class NextInvoiceNumberResponse {

    @NotBlank
    @JsonProperty("next_invoice_number")
    private String nextInvoiceNumber;

    @NotNull
    @JsonProperty("is_generated")
    private Boolean isGenerated;

    @JsonProperty("pattern_detected")
    private String patternDetected;

    public NextInvoiceNumberResponse() {
    }

    public NextInvoiceNumberResponse(String nextInvoiceNumber, Boolean isGenerated, String patternDetected) {
        this.nextInvoiceNumber = nextInvoiceNumber;
        this.isGenerated = isGenerated;
        this.patternDetected = patternDetected;
    }

    public String getNextInvoiceNumber() {
        return nextInvoiceNumber;
    }

    public void setNextInvoiceNumber(String nextInvoiceNumber) {
        this.nextInvoiceNumber = nextInvoiceNumber;
    }

    public Boolean getIsGenerated() {
        return isGenerated;
    }

    public void setIsGenerated(Boolean isGenerated) {
        this.isGenerated = isGenerated;
    }

    public String getPatternDetected() {
        return patternDetected;
    }

    public void setPatternDetected(String patternDetected) {
        this.patternDetected = patternDetected;
    }

    @Override
    public String toString() {
        return "NextInvoiceNumberResponse{" +
                "nextInvoiceNumber='" + nextInvoiceNumber + '\'' +
                ", isGenerated=" + isGenerated +
                ", patternDetected='" + patternDetected + '\'' +
                '}';
    }
}
