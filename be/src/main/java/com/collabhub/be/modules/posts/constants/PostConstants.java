package com.collabhub.be.modules.posts.constants;

/**
 * Centralized constants for the posts module.
 * Contains validation limits, default values, and other configuration constants.
 */
public final class PostConstants {

    private PostConstants() {
        // Utility class - prevent instantiation
    }

    // Validation Constants
    public static final int MAX_CAPTION_LENGTH = 2200;
    public static final int MAX_REVIEWER_NOTES_LENGTH = 1000;
    public static final int MAX_REVIEW_NOTES_LENGTH = 2000;
    public static final int MAX_REVIEWERS_COUNT = 10;
    public static final int MAX_COMMENT_CONTENT_LENGTH = 2000;
    
    // Validation Messages
    public static final String CAPTION_TOO_LONG_MESSAGE = "Caption cannot exceed " + MAX_CAPTION_LENGTH + " characters";
    public static final String REVIEWER_NOTES_TOO_LONG_MESSAGE = "Reviewer notes cannot exceed " + MAX_REVIEWER_NOTES_LENGTH + " characters";
    public static final String REVIEW_NOTES_TOO_LONG_MESSAGE = "Review notes cannot exceed " + MAX_REVIEW_NOTES_LENGTH + " characters";
    public static final String TOO_MANY_REVIEWERS_MESSAGE = "Cannot assign more than " + MAX_REVIEWERS_COUNT + " reviewers to a post";
    public static final String POST_CONTENT_REQUIRED_MESSAGE = "Post must have either caption or media content";
    public static final String COMMENT_CONTENT_TOO_LONG_MESSAGE = "Comment content cannot exceed " + MAX_COMMENT_CONTENT_LENGTH + " characters";
    public static final String COMMENT_CONTENT_REQUIRED_MESSAGE = "Comment content is required";
    
    // Error Messages
    public static final String POST_NOT_FOUND_MESSAGE = "Post not found";
    public static final String ACCESS_DENIED_MESSAGE = "Access denied to post";
    public static final String CANNOT_VIEW_POST_MESSAGE = "Cannot view this post";
    public static final String CANNOT_EDIT_POST_MESSAGE = "Cannot edit this post";
    public static final String CANNOT_DELETE_POST_MESSAGE = "Cannot delete this post";
    public static final String CANNOT_REVIEW_POST_MESSAGE = "Cannot review this post";
    public static final String REVIEW_NOT_FOUND_MESSAGE = "Review not found";
    public static final String INVALID_MEDIA_FILE_MESSAGE = "Invalid or inaccessible media file";
    public static final String USER_NOT_PARTICIPANT_MESSAGE = "User is not a participant in hub";
    public static final String CANNOT_CREATE_POSTS_MESSAGE = "User role cannot create posts";
    public static final String COMMENT_NOT_FOUND_MESSAGE = "Comment not found";
    public static final String CANNOT_EDIT_COMMENT_MESSAGE = "Cannot edit this comment";
    public static final String CANNOT_DELETE_COMMENT_MESSAGE = "Cannot delete this comment";
    public static final String CANNOT_COMMENT_ON_POST_MESSAGE = "Cannot comment on this post";
    
    // Default Values
    public static final String DEFAULT_FILTER = "all";

    // Permission Constants
    public static final String INSUFFICIENT_PERMISSIONS_MESSAGE = "Insufficient permissions for this operation";
    public static final String NOT_ASSIGNED_REVIEWER_MESSAGE = "User is not assigned as a reviewer for this post";

    // Role-based Access Constants
    public static final String ADMIN_ROLE_NAME = "admin";
    public static final String REVIEWER_ROLE_NAME = "reviewer";
    public static final String REVIEWER_CREATOR_ROLE_NAME = "reviewer_creator";
    public static final String CONTENT_CREATOR_ROLE_NAME = "content_creator";

    // Media Processing Constants
    public static final long UNKNOWN_FILE_SIZE = 0L;
    public static final String DEFAULT_FILENAME = "unknown_file";
    public static final String DEFAULT_MIME_TYPE = "application/octet-stream";
    public static final int DEFAULT_COMMENT_COUNT = 0;

    // MIME Type Constants
    public static final String MIME_TYPE_JPEG = "image/jpeg";
    public static final String MIME_TYPE_PNG = "image/png";
    public static final String MIME_TYPE_GIF = "image/gif";
    public static final String MIME_TYPE_MP4 = "video/mp4";
    public static final String MIME_TYPE_QUICKTIME = "video/quicktime";
    public static final String MIME_TYPE_AVI = "video/x-msvideo";

    // Pagination Constants
    public static final int DEFAULT_PAGE_SIZE = 20;
    public static final int MAX_PAGE_SIZE = 100;
    public static final int DEFAULT_PAGE_NUMBER = 0;

    // Bulk Loading Constants
    public static final int MAX_BULK_LOAD_SIZE = 1000;
    public static final int OPTIMAL_BATCH_SIZE = 50;

    // Performance Constants
    public static final int COMMENT_PAGINATION_SIZE = 50;
    public static final int REVIEWER_BATCH_SIZE = 10;

    // File Processing Constants
    public static final String URL_QUERY_SEPARATOR = "?";
    public static final String URL_PATH_SEPARATOR = "/";
    public static final String FILE_EXTENSION_SEPARATOR = ".";
    public static final String EMAIL_DOMAIN_SEPARATOR = "@";

    // Notification Constants
    public static final String REVIEW_NOTIFICATION_SUBJECT = "New Post Review Assignment";
    public static final String COMMENT_NOTIFICATION_SUBJECT = "New Comment on Post";

    // Method Size Constants
    public static final int MAX_METHOD_LINES = 30;
    public static final int RECOMMENDED_METHOD_LINES = 20;

    // Logging Constants
    public static final String POST_CREATED_LOG = "POST_CREATED";
    public static final String POST_UPDATED_LOG = "POST_UPDATED";
    public static final String POST_DELETED_LOG = "POST_DELETED";
}
