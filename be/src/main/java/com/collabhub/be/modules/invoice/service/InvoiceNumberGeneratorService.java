package com.collabhub.be.modules.invoice.service;

import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.invoice.dto.NextInvoiceNumberResponse;
import com.collabhub.be.modules.invoice.repository.InvoiceRepositoryImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service for generating invoice numbers with intelligent increment logic.
 * Handles both numeric and alphanumeric invoice number patterns.
 */
@Service
public class InvoiceNumberGeneratorService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceNumberGeneratorService.class);

    private final InvoiceRepositoryImpl invoiceRepository;

    public InvoiceNumberGeneratorService(InvoiceRepositoryImpl invoiceRepository) {
        this.invoiceRepository = invoiceRepository;
    }

    /**
     * Generates the next invoice number for the account.
     * Analyzes the latest invoice number and increments it intelligently.
     *
     * @param accountId the account ID for multi-tenancy
     * @return the next invoice number response
     */
    @Transactional(readOnly = true)
    public NextInvoiceNumberResponse generateNextInvoiceNumber(Long accountId) {
        logger.debug("Generating next invoice number for account: {}", accountId);

        Optional<String> latestInvoiceNumber = invoiceRepository.findLatestInvoiceNumber(accountId);

        if (latestInvoiceNumber.isEmpty()) {
            logger.debug("No existing invoices found for account: {}, returning empty string", accountId);
            return new NextInvoiceNumberResponse("", true, InvoiceConstants.EMPTY_PATTERN);
        }

        String latest = latestInvoiceNumber.get();
        String nextNumber = incrementInvoiceNumber(latest);
        String pattern = detectPattern(latest);

        logger.debug("Generated next invoice number: {} from latest: {} for account: {}",
                    nextNumber, latest, accountId);

        return new NextInvoiceNumberResponse(nextNumber, true, pattern);
    }

    /**
     * Increments an invoice number intelligently.
     * Handles both numeric and character increments (e.g., 5 -> 6, A -> B, Z -> AA).
     *
     * @param invoiceNumber the current invoice number
     * @return the incremented invoice number
     */
    public String incrementInvoiceNumber(String invoiceNumber) {
        if (invoiceNumber == null || invoiceNumber.trim().isEmpty()) {
            return InvoiceConstants.FALLBACK_INVOICE_NUMBER;
        }

        char[] chars = invoiceNumber.toCharArray();
        boolean incremented = false;

        // Start from the last character and work backwards
        for (int i = chars.length - 1; i >= 0 && !incremented; i--) {
            char currentChar = chars[i];

            if (Character.isDigit(currentChar)) {
                incremented = incrementNumericCharacter(chars, i);
            } else if (Character.isLetter(currentChar)) {
                incremented = incrementAlphaCharacter(chars, i);
            } else {
                // Non-alphanumeric character, stop here and append increment
                break;
            }
        }

        String result = new String(chars);

        if (!incremented) {
            result = handleCarryOver(invoiceNumber, result);
        }

        return result;
    }

    /**
     * Detects the pattern of an invoice number for categorization.
     *
     * @param invoiceNumber the invoice number to analyze
     * @return a description of the detected pattern
     */
    public String detectPattern(String invoiceNumber) {
        if (invoiceNumber == null || invoiceNumber.trim().isEmpty()) {
            return InvoiceConstants.EMPTY_PATTERN;
        }

        if (invoiceNumber.matches("^[0-9]+$")) {
            return InvoiceConstants.NUMERIC_PATTERN;
        } else if (invoiceNumber.matches("^.*[0-9]+$")) {
            return InvoiceConstants.ALPHANUMERIC_PATTERN;
        } else {
            return InvoiceConstants.TEXT_PATTERN;
        }
    }

    /**
     * Increments a numeric character with carry-over handling.
     *
     * @param chars the character array
     * @param index the index of the character to increment
     * @return true if increment was successful, false if carry-over is needed
     */
    private boolean incrementNumericCharacter(char[] chars, int index) {
        if (chars[index] == '9') {
            chars[index] = '0';
            return false; // Continue to next position (carry over)
        } else {
            chars[index] = (char) (chars[index] + 1);
            return true; // Increment successful
        }
    }

    /**
     * Increments an alphabetic character with carry-over handling.
     *
     * @param chars the character array
     * @param index the index of the character to increment
     * @return true if increment was successful, false if carry-over is needed
     */
    private boolean incrementAlphaCharacter(char[] chars, int index) {
        char currentChar = chars[index];

        if (Character.isUpperCase(currentChar)) {
            if (currentChar == 'Z') {
                chars[index] = 'A';
                return false; // Continue to next position (carry over)
            } else {
                chars[index] = (char) (currentChar + 1);
                return true; // Increment successful
            }
        } else if (Character.isLowerCase(currentChar)) {
            if (currentChar == 'z') {
                chars[index] = 'a';
                return false; // Continue to next position (carry over)
            } else {
                chars[index] = (char) (currentChar + 1);
                return true; // Increment successful
            }
        }

        return false;
    }

    /**
     * Handles carry-over when all characters were at their maximum.
     *
     * @param originalNumber the original invoice number
     * @param currentResult the current result after processing
     * @return the final result with carry-over handled
     */
    private String handleCarryOver(String originalNumber, String currentResult) {
        // All characters were at their maximum (e.g., "999" or "ZZZ")
        // Determine what type of character to prepend based on the last character
        char lastChar = originalNumber.charAt(originalNumber.length() - 1);
        
        if (Character.isDigit(lastChar)) {
            return "1" + currentResult;
        } else if (Character.isUpperCase(lastChar)) {
            return "A" + currentResult;
        } else if (Character.isLowerCase(lastChar)) {
            return "a" + currentResult;
        } else {
            // Fallback: append "1" for non-alphanumeric
            return currentResult + "1";
        }
    }
}
