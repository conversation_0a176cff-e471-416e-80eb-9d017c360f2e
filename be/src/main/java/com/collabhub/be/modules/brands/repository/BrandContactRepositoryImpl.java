package com.collabhub.be.modules.brands.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.BrandContactDao;
import org.jooq.generated.tables.pojos.BrandContact;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.jooq.generated.tables.BrandContact.BRAND_CONTACT;

/**
 * Repository for BrandContact entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy support.
 */
@Repository
public class BrandContactRepositoryImpl extends BrandContactDao {

    private final DSLContext dsl;

    public BrandContactRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all contacts for a specific brand and account.
     *
     * @param brandId the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return list of brand contacts
     */
    public List<BrandContact> findByBrandIdAndAccountId(Long brandId, Long accountId) {
        return dsl.select()
                .from(BRAND_CONTACT)
                .where(BRAND_CONTACT.BRAND_ID.eq(brandId))
                .and(BRAND_CONTACT.ACCOUNT_ID.eq(accountId))
                .orderBy(BRAND_CONTACT.NAME.asc())
                .fetchInto(BrandContact.class);
    }

    /**
     * Finds a specific contact by ID, brand ID, and account ID.
     *
     * @param id the contact ID
     * @param brandId the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return optional containing the contact if found
     */
    public Optional<BrandContact> findByIdAndBrandIdAndAccountId(Long id, Long brandId, Long accountId) {
        return dsl.select()
                .from(BRAND_CONTACT)
                .where(BRAND_CONTACT.ID.eq(id))
                .and(BRAND_CONTACT.BRAND_ID.eq(brandId))
                .and(BRAND_CONTACT.ACCOUNT_ID.eq(accountId))
                .fetchOptionalInto(BrandContact.class);
    }

    /**
     * Deletes all contacts for a specific brand and account.
     *
     * @param brandId the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return number of deleted contacts
     */
    public int deleteByBrandIdAndAccountId(Long brandId, Long accountId) {
        return dsl.deleteFrom(BRAND_CONTACT)
                .where(BRAND_CONTACT.BRAND_ID.eq(brandId))
                .and(BRAND_CONTACT.ACCOUNT_ID.eq(accountId))
                .execute();
    }

    /**
     * Deletes a specific contact by ID, brand ID, and account ID.
     *
     * @param id the contact ID
     * @param brandId the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return true if the contact was deleted, false if not found
     */
    public boolean deleteByIdAndBrandIdAndAccountId(Long id, Long brandId, Long accountId) {
        int deletedRows = dsl.deleteFrom(BRAND_CONTACT)
                .where(BRAND_CONTACT.ID.eq(id))
                .and(BRAND_CONTACT.BRAND_ID.eq(brandId))
                .and(BRAND_CONTACT.ACCOUNT_ID.eq(accountId))
                .execute();

        return deletedRows > 0;
    }

    /**
     * Checks if a contact exists with the given email for the brand (case-insensitive).
     *
     * @param email the contact email
     * @param brandId the brand ID
     * @param accountId the account ID for multi-tenancy
     * @param excludeId optional contact ID to exclude from the check (for updates)
     * @return true if a contact with the email exists
     */
    public boolean existsByEmailAndBrandIdAndAccountId(String email, Long brandId, Long accountId, Long excludeId) {
        var query = dsl.selectCount()
                .from(BRAND_CONTACT)
                .where(BRAND_CONTACT.EMAIL.equalIgnoreCase(email))
                .and(BRAND_CONTACT.BRAND_ID.eq(brandId))
                .and(BRAND_CONTACT.ACCOUNT_ID.eq(accountId));

        if (excludeId != null) {
            query = query.and(BRAND_CONTACT.ID.ne(excludeId));
        }

        return query.fetchOne(0, Integer.class) > 0;
    }

    /**
     * Bulk loads contacts for multiple brands to avoid N+1 queries.
     * Returns a map of brandId -> List<BrandContact> ordered by name.
     *
     * @param brandIds the list of brand IDs
     * @param accountId the account ID for multi-tenancy
     * @return map of brand ID to list of contacts
     */
    public Map<Long, List<BrandContact>> findContactsByBrandIds(List<Long> brandIds, Long accountId) {
        if (brandIds == null || brandIds.isEmpty()) {
            return Map.of();
        }

        List<BrandContact> allContacts = dsl.select()
                .from(BRAND_CONTACT)
                .where(BRAND_CONTACT.BRAND_ID.in(brandIds))
                .and(BRAND_CONTACT.ACCOUNT_ID.eq(accountId))
                .orderBy(BRAND_CONTACT.BRAND_ID.asc(), BRAND_CONTACT.NAME.asc())
                .fetchInto(BrandContact.class);

        Map<Long, List<BrandContact>> contactMap = new HashMap<>();

        // Initialize all brand IDs with empty lists
        for (Long brandId : brandIds) {
            contactMap.put(brandId, new ArrayList<>());
        }

        // Group contacts by brand ID
        for (BrandContact contact : allContacts) {
            contactMap.get(contact.getBrandId()).add(contact);
        }

        return contactMap;
    }

    /**
     * Updates a contact's updated_at timestamp.
     *
     * @param id the contact ID
     * @param brandId the brand ID
     * @param accountId the account ID for multi-tenancy
     */
    public void updateTimestamp(Long id, Long brandId, Long accountId) {
        dsl.update(BRAND_CONTACT)
                .set(BRAND_CONTACT.UPDATED_AT, LocalDateTime.now())
                .where(BRAND_CONTACT.ID.eq(id))
                .and(BRAND_CONTACT.BRAND_ID.eq(brandId))
                .and(BRAND_CONTACT.ACCOUNT_ID.eq(accountId))
                .execute();
    }
}
