package com.collabhub.be.modules.invoice.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Data classes for invoice snapshots.
 * These preserve immutable data at invoice creation time for compliance and audit purposes.
 */
public class InvoiceSnapshot {

    /**
     * Snapshot of issuer company information at invoice creation time.
     */
    public static class IssuerSnapshot {

        @JsonProperty("id")
        private Long id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("address")
        private String address;

        @JsonProperty("city")
        private String city;

        @JsonProperty("postal_code")
        private String postalCode;

        @JsonProperty("country")
        private String country;

        @JsonProperty("vat_number")
        private String vatNumber;

        @JsonProperty("registration_number")
        private String registrationNumber;

        @JsonProperty("phone")
        private String phone;

        @JsonProperty("email")
        private String email;

        @JsonProperty("website")
        private String website;

        public IssuerSnapshot() {
        }

        public IssuerSnapshot(Long id, String name, String address, String city, String postalCode, String country,
                            String vatNumber, String registrationNumber, String phone, String email, String website) {
            this.id = id;
            this.name = name;
            this.address = address;
            this.city = city;
            this.postalCode = postalCode;
            this.country = country;
            this.vatNumber = vatNumber;
            this.registrationNumber = registrationNumber;
            this.phone = phone;
            this.email = email;
            this.website = website;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        
        public String getPostalCode() { return postalCode; }
        public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
        
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
        
        public String getVatNumber() { return vatNumber; }
        public void setVatNumber(String vatNumber) { this.vatNumber = vatNumber; }
        
        public String getRegistrationNumber() { return registrationNumber; }
        public void setRegistrationNumber(String registrationNumber) { this.registrationNumber = registrationNumber; }
        
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getWebsite() { return website; }
        public void setWebsite(String website) { this.website = website; }
    }

    /**
     * Snapshot of recipient company information at invoice creation time.
     */
    public static class RecipientSnapshot {

        @JsonProperty("id")
        private Long id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("address")
        private String address;

        @JsonProperty("city")
        private String city;

        @JsonProperty("postal_code")
        private String postalCode;

        @JsonProperty("country")
        private String country;

        @JsonProperty("vat_number")
        private String vatNumber;

        @JsonProperty("registration_number")
        private String registrationNumber;

        @JsonProperty("email")
        private String email;

        @JsonProperty("phone")
        private String phone;

        public RecipientSnapshot() {
        }

        public RecipientSnapshot(Long id, String name, String address, String city, String postalCode,
                               String country, String vatNumber, String registrationNumber, String email, String phone) {
            this.id = id;
            this.name = name;
            this.address = address;
            this.city = city;
            this.postalCode = postalCode;
            this.country = country;
            this.vatNumber = vatNumber;
            this.registrationNumber = registrationNumber;
            this.email = email;
            this.phone = phone;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        
        public String getPostalCode() { return postalCode; }
        public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
        
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }

        public String getVatNumber() { return vatNumber; }
        public void setVatNumber(String vatNumber) { this.vatNumber = vatNumber; }

        public String getRegistrationNumber() { return registrationNumber; }
        public void setRegistrationNumber(String registrationNumber) { this.registrationNumber = registrationNumber; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
    }

    /**
     * Snapshot of bank details at invoice creation time.
     */
    public static class BankDetailsSnapshot {

        @JsonProperty("id")
        private Long id;

        @JsonProperty("account_name")
        private String accountName;

        @JsonProperty("bank_name")
        private String bankName;

        @JsonProperty("iban")
        private String iban;

        @JsonProperty("bic")
        private String bic;

        public BankDetailsSnapshot() {
        }

        public BankDetailsSnapshot(Long id, String accountName, String bankName, String iban, String bic) {
            this.id = id;
            this.accountName = accountName;
            this.bankName = bankName;
            this.iban = iban;
            this.bic = bic;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getAccountName() { return accountName; }
        public void setAccountName(String accountName) { this.accountName = accountName; }
        
        public String getBankName() { return bankName; }
        public void setBankName(String bankName) { this.bankName = bankName; }
        
        public String getIban() { return iban; }
        public void setIban(String iban) { this.iban = iban; }
        
        public String getBic() { return bic; }
        public void setBic(String bic) { this.bic = bic; }
    }
}
