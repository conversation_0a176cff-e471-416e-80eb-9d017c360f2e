package com.collabhub.be.modules.collaborationhub.dto;

import org.jooq.generated.enums.HubParticipantRole;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Lightweight DTO for collaboration hub list views.
 * Contains essential information for displaying hubs in lists with pagination.
 */
public class CollaborationHubListItemDto {

    @NotNull
    private Long id;

    @NotNull
    private String name;

    @NotNull
    private String brandName;

    @NotNull
    private Long brandId;

    @NotNull
    private HubParticipantRole myRole;

    @NotNull
    private LocalDateTime createdAt;

    public CollaborationHubListItemDto() {}

    public CollaborationHubListItemDto(Long id, String name, String brandName, Long brandId, HubParticipantRole myRole, LocalDateTime createdAt) {
        this.id = id;
        this.name = name;
        this.brandName = brandName;
        this.brandId = brandId;
        this.myRole = myRole;
        this.createdAt = createdAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public HubParticipantRole getMyRole() {
        return myRole;
    }

    public void setMyRole(HubParticipantRole myRole) {
        this.myRole = myRole;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "CollaborationHubListItemDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", brandName='" + brandName + '\'' +
                ", brandId=" + brandId +
                ", myRole=" + myRole +
                ", createdAt=" + createdAt +
                '}';
    }
}
