package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.converter.NotificationConverter;
import com.collabhub.be.modules.notifications.dto.NotificationPageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageResponse;
import com.collabhub.be.modules.notifications.dto.NotificationResponse;
import com.collabhub.be.modules.notifications.exception.NotificationNotFoundException;
import com.collabhub.be.modules.notifications.repository.NotificationRepository;
import org.jooq.generated.tables.pojos.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * Production-grade service for managing notifications for all user types.
 *
 * <p>This service handles notification retrieval, status updates, and pagination for both
 * internal users (with user accounts) and external users (hub participants) using email
 * as the universal identifier. It provides comprehensive validation, error handling,
 * and performance optimizations with a simplified architecture.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Email-based universal identification for all user types</li>
 *   <li>0-based pagination with {@link NotificationPageRequest}</li>
 *   <li>Unified operations for marking notifications as read</li>
 *   <li>Comprehensive validation and error handling</li>
 *   <li>Performance-optimized queries with proper indexing</li>
 *   <li>Automatic user email resolution from JWT claims</li>
 * </ul>
 *
 * <h3>Architecture Benefits:</h3>
 * <ul>
 *   <li>Single service eliminates user type detection complexity</li>
 *   <li>Email serves as universal identifier for all users</li>
 *   <li>Leverages existing email-based database queries</li>
 *   <li>Maintains full feature parity for all user types</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class NotificationManagementService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationManagementService.class);

    // Constants
    private static final String RETRIEVE_NOTIFICATIONS_MESSAGE = "Retrieving notifications for user: {} (page={}, size={}, unreadOnly={})";
    private static final String RETRIEVED_NOTIFICATIONS_MESSAGE = "Retrieved {} notifications for user {} (total: {})";
    private static final String MARK_READ_MESSAGE = "Marking notification as read: id={}, user={}";
    private static final String MARK_READ_SUCCESS_MESSAGE = "Marked notification as read: id={}, user={}";
    private static final String MARK_READ_FAILED_MESSAGE = "Failed to mark notification as read - not found or not owned: id={}, user={}";
    private static final String MARK_ALL_READ_MESSAGE = "Marking all notifications as read for user: {}";
    private static final String MARK_ALL_READ_SUCCESS_MESSAGE = "Marked {} notifications as read for user {}";
    private static final String UNREAD_COUNT_MESSAGE = "Getting unread notification count for user: {}";
    private static final String UNREAD_COUNT_RESULT_MESSAGE = "User {} has {} unread notifications";

    private final NotificationRepository notificationRepository;
    private final NotificationConverter notificationConverter;
    private final JwtClaimsService jwtClaimsService;

    public NotificationManagementService(NotificationRepository notificationRepository,
                                        NotificationConverter notificationConverter,
                                        JwtClaimsService jwtClaimsService) {
        this.notificationRepository = notificationRepository;
        this.notificationConverter = notificationConverter;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Retrieves notifications for the current user with pagination.
     *
     * <p>This method provides paginated access to user notifications with optional filtering
     * for unread notifications only. It uses 0-based pagination and includes total count
     * information for proper pagination controls. Works for both internal and external users
     * using email as the universal identifier.</p>
     *
     * @param pageRequest the page request with pagination and filtering parameters (must not be null)
     * @return page of notifications with pagination metadata
     *
     * @throws IllegalArgumentException if pageRequest is invalid
     */
    @Transactional(readOnly = true)
    public NotificationPageResponse getUserNotifications(@NotNull @Valid NotificationPageRequest pageRequest) {

        String userEmail = getCurrentUserEmail();
        logRetrievalRequest(userEmail, pageRequest);

        List<Notification> notifications = fetchNotifications(userEmail, pageRequest);
        int totalCount = getTotalCount(userEmail, pageRequest.isUnreadOnly());
        List<NotificationResponse> responseList = convertToResponseList(notifications);

        logRetrievalResult(userEmail, responseList, totalCount);
        return createPageResponse(responseList, pageRequest, totalCount);
    }

    /**
     * Marks a specific notification as read.
     *
     * <p>This method marks a notification as read for the current user, ensuring that
     * users can only mark their own notifications as read. If the notification is not
     * found or doesn't belong to the user, an exception is thrown. Works for both
     * internal and external users using email identification.</p>
     *
     * @param notificationId the notification ID (must be positive)
     *
     * @throws IllegalArgumentException if notificationId is null or non-positive
     * @throws NotificationNotFoundException if notification is not found or not owned by user
     */
    @Transactional
    public void markNotificationAsRead(@NotNull @Positive Long notificationId) {

        String userEmail = getCurrentUserEmail();
        logger.debug(MARK_READ_MESSAGE, notificationId, userEmail);

        boolean updated = attemptMarkAsRead(notificationId, userEmail);
        handleMarkAsReadResult(updated, notificationId, userEmail);
    }

    /**
     * Marks all notifications as read for the current user.
     *
     * <p>This method performs a bulk update operation to mark all unread notifications
     * as read for the current user. It returns the number of notifications that were
     * actually updated, which may be zero if all notifications were already read.
     * Works for both internal and external users using email identification.</p>
     *
     * @return number of notifications marked as read (non-negative)
     */
    @Transactional
    public int markAllNotificationsAsRead() {

        String userEmail = getCurrentUserEmail();
        logger.debug(MARK_ALL_READ_MESSAGE, userEmail);

        int updatedCount = performBulkMarkAsRead(userEmail);
        logBulkMarkAsReadResult(userEmail, updatedCount);

        return updatedCount;
    }

    /**
     * Gets the count of unread notifications for the current user.
     *
     * <p>This method provides a fast count of unread notifications for the current user,
     * which is commonly used for displaying notification badges in the UI. Works for
     * both internal and external users using email identification.</p>
     *
     * @return number of unread notifications (non-negative)
     */
    @Transactional(readOnly = true)
    public int getUnreadNotificationCount() {

        String userEmail = getCurrentUserEmail();
        logger.debug(UNREAD_COUNT_MESSAGE, userEmail);

        int count = getUnreadCount(userEmail);
        logUnreadCountResult(userEmail, count);

        return count;
    }

    // ========================================
    // PRIVATE HELPER METHODS
    // ========================================

    /**
     * Gets the current user email from JWT claims.
     * This works for both internal and external users as both have email addresses.
     *
     * @return the current user email (normalized)
     */
    private String getCurrentUserEmail() {
        String email = jwtClaimsService.getCurrentUser().getEmail();
        return normalizeEmail(email);
    }

    /**
     * Normalizes email address for consistent storage and querying.
     */
    private String normalizeEmail(String email) {
        return email.trim().toLowerCase();
    }

    /**
     * Logs the notification retrieval request.
     */
    private void logRetrievalRequest(String userEmail, NotificationPageRequest pageRequest) {
        logger.debug(RETRIEVE_NOTIFICATIONS_MESSAGE, userEmail,
                    pageRequest.getPage(), pageRequest.getSize(), pageRequest.isUnreadOnly());
    }

    /**
     * Fetches notifications from the repository with pagination using email.
     */
    private List<Notification> fetchNotifications(String userEmail, NotificationPageRequest pageRequest) {
        return notificationRepository.findByEmailWithPagination(
                userEmail, pageRequest.getPage(), pageRequest.getSize(), pageRequest.isUnreadOnly());
    }

    /**
     * Gets the total count of notifications for pagination using email.
     */
    private int getTotalCount(String userEmail, boolean unreadOnly) {
        return notificationRepository.countByEmail(userEmail, unreadOnly);
    }

    /**
     * Converts notification entities to response DTOs.
     */
    private List<NotificationResponse> convertToResponseList(List<Notification> notifications) {
        return notificationConverter.toResponseList(notifications);
    }

    /**
     * Logs the notification retrieval result.
     */
    private void logRetrievalResult(String userEmail, List<NotificationResponse> responseList, int totalCount) {
        logger.info(RETRIEVED_NOTIFICATIONS_MESSAGE, responseList.size(), userEmail, totalCount);
    }

    /**
     * Creates the paginated response.
     */
    private NotificationPageResponse createPageResponse(List<NotificationResponse> responseList,
                                                       NotificationPageRequest pageRequest, int totalCount) {
        return NotificationPageResponse.of(responseList, pageRequest, totalCount);
    }

    /**
     * Attempts to mark a notification as read using email.
     */
    private boolean attemptMarkAsRead(Long notificationId, String userEmail) {
        return notificationRepository.markAsReadByEmail(notificationId, userEmail);
    }

    /**
     * Handles the result of marking a notification as read.
     */
    private void handleMarkAsReadResult(boolean updated, Long notificationId, String userEmail) {
        if (updated) {
            logger.info(MARK_READ_SUCCESS_MESSAGE, notificationId, userEmail);
        } else {
            logger.warn(MARK_READ_FAILED_MESSAGE, notificationId, userEmail);
            throw new NotificationNotFoundException("Notification not found or not owned by user: " + notificationId);
        }
    }

    /**
     * Performs the bulk mark as read operation using email.
     */
    private int performBulkMarkAsRead(String userEmail) {
        return notificationRepository.markAllAsReadByEmail(userEmail);
    }

    /**
     * Logs the result of bulk mark as read operation.
     */
    private void logBulkMarkAsReadResult(String userEmail, int updatedCount) {
        logger.info(MARK_ALL_READ_SUCCESS_MESSAGE, updatedCount, userEmail);
    }

    /**
     * Gets the unread count from the repository using email.
     */
    private int getUnreadCount(String userEmail) {
        return notificationRepository.countByEmail(userEmail, true);
    }

    /**
     * Logs the unread count result.
     */
    private void logUnreadCountResult(String userEmail, int count) {
        logger.info(UNREAD_COUNT_RESULT_MESSAGE, userEmail, count);
    }
}
