package com.collabhub.be.modules.posts.service;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.collaborationhub.model.ParticipantDetails;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.converter.PostConverter;
import com.collabhub.be.modules.posts.dto.PostReviewRequest;
import com.collabhub.be.modules.posts.util.PostParticipantUtil;
import com.collabhub.be.modules.posts.dto.PostReviewResponse;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.posts.dto.PostResponse;
import com.collabhub.be.modules.posts.repository.PostRepositoryImpl;
import com.collabhub.be.modules.posts.repository.PostReviewerRepositoryImpl;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.service.NotificationDispatcherService;
import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.service.NotificationTranslationService;
import org.jooq.generated.enums.ReviewStatus;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.PostReviewer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Service for managing post reviews and reviewer assignments.
 * Handles review submission, status updates, and reviewer management.
 */
@Service
public class PostReviewService {

    private static final Logger logger = LoggerFactory.getLogger(PostReviewService.class);

    // Log Messages
    private static final String UPDATING_REVIEWERS_LOG = "Updating reviewers for post {} in hub {}";
    private static final String SUBMITTING_REVIEW_LOG = "Submitting review for post {} by participant {}";
    private static final String REVIEW_SUBMITTED_LOG = "Review submitted for post {} by participant {} with status {}";
    private static final String RECALCULATING_STATUS_LOG = "Recalculating review status for post {}";

    // Business Constants
    private static final int MAX_REVIEWERS_PER_POST = 10;

    private final PostReviewerRepositoryImpl postReviewerRepository;
    private final PostRepositoryImpl postRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final PostConverter postConverter;
    private final PostPermissionService postPermissionService;
    private final PostParticipantUtil participantUtil;
    private final NotificationDispatcherService notificationDispatcherService;
    private final JwtClaimsService jwtClaimsService;
    private final NotificationTranslationService notificationTranslationService;

    public PostReviewService(PostReviewerRepositoryImpl postReviewerRepository,
                           PostRepositoryImpl postRepository,
                           HubParticipantRepositoryImpl participantRepository,
                           PostConverter postConverter,
                           PostPermissionService postPermissionService,
                           PostParticipantUtil participantUtil,
                           NotificationDispatcherService notificationDispatcherService,
                           JwtClaimsService jwtClaimsService,
                           NotificationTranslationService notificationTranslationService) {
        this.postReviewerRepository = postReviewerRepository;
        this.postRepository = postRepository;
        this.participantRepository = participantRepository;
        this.postConverter = postConverter;
        this.postPermissionService = postPermissionService;
        this.participantUtil = participantUtil;
        this.notificationDispatcherService = notificationDispatcherService;
        this.jwtClaimsService = jwtClaimsService;
        this.notificationTranslationService = notificationTranslationService;
    }

    /**
     * Assigns reviewers to a post and sends notifications.
     */
    @Transactional
    public void assignReviewersToPost(Long postId, Long hubId, List<Long> reviewerIds) {
        if (reviewerIds == null || reviewerIds.isEmpty()) {
            logger.debug("No reviewers to assign for post {}", postId);
            return;
        }

        validateReviewerCount(reviewerIds);
        createPostReviewers(postId, hubId, reviewerIds);
        sendReviewerNotifications(postId, reviewerIds);

        logger.info("Assigned {} reviewers to post {}", reviewerIds.size(), postId);
    }

    /**
     * Updates reviewer assignments for a post.
     * Removes old reviewers, adds new ones, and sends notifications to newly assigned reviewers.
     * This operation is atomic - if any part fails, all changes are rolled back.
     */
    @Transactional
    public void updatePostReviewers(Long postId, Long hubId, List<Long> newReviewerIds) {
        logger.debug(UPDATING_REVIEWERS_LOG, postId, newReviewerIds);

        List<Long> currentReviewerIds = getCurrentReviewerIds(postId);
        ReviewerUpdateDiff diff = calculateReviewerDiff(currentReviewerIds, newReviewerIds);

        removeOldReviewers(postId, diff.reviewersToRemove());
        addNewReviewers(postId, hubId, diff.reviewersToAdd());
        sendNotificationsToNewReviewers(postId, diff.reviewersToAdd());

        logger.info("Updated reviewers for post {}: removed={}, added={}",
                   postId, diff.reviewersToRemove().size(), diff.reviewersToAdd().size());
    }

    /**
     * Submits or updates a review for a post.
     */
    @Transactional
    public PostReviewResponse submitReview(Long postId, PostReviewRequest request, UserContext userContext) {
        logger.debug(SUBMITTING_REVIEW_LOG, postId, userContext.getEmail());

        validateReviewSubmission(postId, userContext);
        HubParticipant participant = getParticipantId(postId, userContext);

        updateReviewStatus(postId, participant.getId(), request);
        recalculatePostReviewStatus(postId);

        // Send notification to post creator about the review
        sendPostReviewedNotification(postId, participant, request.getStatus());

        logger.info(REVIEW_SUBMITTED_LOG, postId, participant.getId(), request.getStatus());
        return buildReviewResponse(postId, participant.getId());
    }

    /**
     * Gets all reviewers assigned to a post with their current review status.
     * Uses bulk loading to avoid N+1 queries.
     */
    @Transactional(readOnly = true)
    public List<PostResponse.PostReviewer> getPostReviewers(Long postId) {
        List<PostReviewer> postReviewers = postReviewerRepository.findByPostId(postId);

        if (postReviewers.isEmpty()) {
            return List.of();
        }

        return buildPostReviewerList(postReviewers);
    }

    /**
     * Bulk loads reviewer data for multiple posts to avoid N+1 queries.
     */
    @Transactional(readOnly = true)
    public Map<Long, List<PostResponse.PostReviewer>> bulkLoadPostReviewers(List<Long> postIds) {
        if (postIds.isEmpty()) {
            return Map.of();
        }

        return postReviewerRepository.bulkLoadReviewersByPostIds(postIds)
                .entrySet()
                .stream()
                .collect(java.util.stream.Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> buildPostReviewerList(entry.getValue())
                ));
    }

    /**
     * Creates post reviewers for assigned reviewer IDs.
     */
    private void createPostReviewers(Long postId, Long hubId, List<Long> reviewerIds) {
        for (Long reviewerId : reviewerIds) {
            if (postPermissionService.isValidReviewer(reviewerId, hubId)) {
                postReviewerRepository.createPostReviewer(postId, reviewerId);
                logger.info("Created reviewer assignment: post={}, reviewer={}", postId, reviewerId);
            }
        }
    }



    /**
     * Sends notifications to assigned reviewers.
     */
    private void sendReviewerNotifications(Long postId, List<Long> reviewerIds) {
        if (reviewerIds.isEmpty()) {
            return;
        }

        try {
            // Get post details for notification
            Post post = postRepository.findById(postId);
            if (post == null) {
                logger.warn("Post {} not found when sending reviewer notifications", postId);
                return;
            }

            // Get assigner name from current user context
            UserContext userContext = jwtClaimsService.getCurrentUser();
            String assignerName = userContext.getDisplayName() != null ? userContext.getDisplayName() : "System";

            // Send notification to each reviewer (both internal and external users)
            List<HubParticipant> reviewersToNotify = new ArrayList<>();
            for (Long reviewerId : reviewerIds) {
                HubParticipant reviewer = participantRepository.findById(reviewerId);
                if (reviewer != null) {
                    reviewersToNotify.add(reviewer);
                }
            }

            if (!reviewersToNotify.isEmpty()) {
                String postTitle = post.getCaption() != null && !post.getCaption().trim().isEmpty()
                        ? post.getCaption()
                        : "Post #" + post.getId();
                sendReviewerAssignedNotification(reviewersToNotify, post.getHubId(), postId, postTitle, assignerName);
            }

            logger.info("Sent reviewer assignment notifications for post {} to {} reviewers",
                       postId, reviewerIds.size());
        } catch (Exception e) {
            logger.warn("Failed to send reviewer notifications for post {}: {}", postId, e.getMessage());
        }
    }

    /**
     * Sends notification to post creator when their post is reviewed.
     */
    private void sendPostReviewedNotification(Long postId, HubParticipant reviewer, ReviewStatus reviewStatus) {
        try {
            // Get post details
            Post post = postRepository.findById(postId);
            if (post == null) {
                logger.warn("Post {} not found when sending review notification", postId);
                return;
            }

            // Get post creator participant
            HubParticipant postCreator = participantRepository.findById(post.getCreatorParticipantId());
            if (postCreator == null) {
                logger.debug("Post creator not found for post {}", postId);
                return;
            }

            // Don't notify if reviewer is the same as post creator
            if (postCreator.getId().equals(reviewer.getId())) {
                return;
            }

            String reviewStatusText = reviewStatus == ReviewStatus.approved ? "approved" : "reviewed";
            String postTitle = post.getCaption() != null && !post.getCaption().trim().isEmpty()
                    ? post.getCaption()
                    : "Post #" + post.getId();
            sendPostReviewedNotification(List.of(postCreator), post.getHubId(), postId,
                                        postTitle, reviewer.getName(), reviewStatusText);

            logger.info("Sent post reviewed notification for post {} to creator {}",
                       postId, postCreator.getUserId());
        } catch (Exception e) {
            logger.warn("Failed to send post reviewed notification for post {}: {}", postId, e.getMessage());
        }
    }

    /**
     * Validates that a user can submit a review for a post.
     */
    private void validateReviewSubmission(Long postId, UserContext userContext) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE);
        }

        postPermissionService.validateCanReviewPost(post);
    }

    /**
     * Gets the participant for a user in a post's hub.
     */
    private HubParticipant getParticipantId(Long postId, UserContext userContext) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE);
        }
        return participantRepository.findByHubIdAndEmail(post.getHubId(), userContext.getEmail());
    }

    /**
     * Updates the review status for a post reviewer.
     */
    private void updateReviewStatus(Long postId, Long participantId, PostReviewRequest request) {
        boolean updated = postReviewerRepository.updateReviewStatus(
            postId, participantId, request.getStatus(), request.getReviewNotes());
        
        if (!updated) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.REVIEW_NOT_FOUND_MESSAGE);
        }
    }

    /**
     * Recalculates the overall post review status based on all reviewer responses.
     */
    private void recalculatePostReviewStatus(Long postId) {
        List<PostReviewer> reviewers = postReviewerRepository.findByPostId(postId);
        
        if (reviewers.isEmpty()) {
            return;
        }

        ReviewStatus newStatus = calculateOverallReviewStatus(reviewers);
        postRepository.updateReviewStatus(postId, newStatus);
        
        logger.debug("Updated post {} review status to {}", postId, newStatus);
    }

    /**
     * Calculates the overall review status based on individual reviewer statuses.
     */
    private ReviewStatus calculateOverallReviewStatus(List<PostReviewer> reviewers) {
        boolean hasRework = reviewers.stream()
            .anyMatch(reviewer -> reviewer.getReviewStatus() == ReviewStatus.rework);
        
        if (hasRework) {
            return ReviewStatus.rework;
        }

        boolean allApproved = reviewers.stream()
            .allMatch(reviewer -> reviewer.getReviewStatus() == ReviewStatus.approved);
        
        return allApproved ? ReviewStatus.approved : ReviewStatus.pending;
    }

    /**
     * Builds a PostReviewResponse from post and participant data.
     */
    private PostReviewResponse buildReviewResponse(Long postId, Long participantId) {
        PostReviewer reviewer = postReviewerRepository.findReviewerByPostAndParticipant(postId, participantId);
        if (reviewer == null) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.REVIEW_NOT_FOUND_MESSAGE);
        }

        ParticipantDetails details =
            participantRepository.findParticipantDetailsById(participantId);

        String name = participantUtil.resolveParticipantName(details);

        return new PostReviewResponse(
            postId,
            participantId,
            name,
            details != null ? details.getEmail() : null,
            reviewer.getReviewStatus(),
            reviewer.getReviewNotes(),
            reviewer.getAssignedAt(),
            reviewer.getReviewedAt()
        );
    }

    /**
     * Builds a list of PostResponse.PostReviewer from PostReviewer entities.
     */
    private List<PostResponse.PostReviewer> buildPostReviewerList(List<PostReviewer> postReviewers) {
        List<Long> participantIds = postReviewers.stream()
                .map(PostReviewer::getParticipantId)
                .distinct()
                .toList();

        Map<Long, ParticipantDetails> participantDetailsMap =
                participantRepository.findParticipantDetailsByIds(participantIds);

        return postReviewers.stream()
                .map(reviewer -> convertToPostReviewer(reviewer, participantDetailsMap))
                .filter(reviewer -> reviewer != null)
                .toList();
    }

    /**
     * Converts a PostReviewer entity to PostResponse.PostReviewer DTO using bulk-loaded data.
     */
    private PostResponse.PostReviewer convertToPostReviewer(
            PostReviewer reviewer,
            Map<Long, ParticipantDetails> participantDetailsMap) {

        return participantUtil.convertToEnhancedPostReviewer(
                reviewer,
                participantDetailsMap
        );
    }

    /**
     * Gets current reviewer IDs for a post.
     */
    private List<Long> getCurrentReviewerIds(Long postId) {
        return postReviewerRepository.findByPostId(postId)
                .stream()
                .map(PostReviewer::getParticipantId)
                .toList();
    }

    /**
     * Calculates the difference between current and new reviewer assignments.
     */
    private ReviewerUpdateDiff calculateReviewerDiff(List<Long> currentReviewerIds, List<Long> newReviewerIds) {
        List<Long> safeNewReviewerIds = newReviewerIds != null ? newReviewerIds : List.of();

        List<Long> reviewersToRemove = currentReviewerIds.stream()
                .filter(id -> !safeNewReviewerIds.contains(id))
                .toList();

        List<Long> reviewersToAdd = safeNewReviewerIds.stream()
                .filter(id -> !currentReviewerIds.contains(id))
                .toList();

        return new ReviewerUpdateDiff(reviewersToRemove, reviewersToAdd);
    }

    /**
     * Removes old reviewer assignments and their review data.
     */
    private void removeOldReviewers(Long postId, List<Long> reviewersToRemove) {
        if (!reviewersToRemove.isEmpty()) {
            int removedCount = postReviewerRepository.removePostReviewers(postId, reviewersToRemove);
            logger.debug("Removed {} reviewer assignments from post {}", removedCount, postId);
        }
    }

    /**
     * Adds new reviewer assignments.
     */
    private void addNewReviewers(Long postId, Long hubId, List<Long> reviewersToAdd) {
        if (!reviewersToAdd.isEmpty()) {
            createPostReviewers(postId, hubId, reviewersToAdd);
        }
    }

    /**
     * Sends notifications to newly assigned reviewers.
     */
    private void sendNotificationsToNewReviewers(Long postId, List<Long> reviewersToAdd) {
        if (!reviewersToAdd.isEmpty()) {
            sendReviewerNotifications(postId, reviewersToAdd);
        }
    }

    /**
     * Validates that the number of reviewers doesn't exceed the maximum allowed.
     */
    private void validateReviewerCount(List<Long> reviewerIds) {
        if (reviewerIds.size() > MAX_REVIEWERS_PER_POST) {
            throw new IllegalArgumentException("Cannot assign more than " + MAX_REVIEWERS_PER_POST + " reviewers to a post");
        }
    }

    /**
     * Record to hold the difference between current and new reviewer assignments.
     */
    private record ReviewerUpdateDiff(
        List<Long> reviewersToRemove,
        List<Long> reviewersToAdd
    ) {}

    /**
     * Sends reviewer assigned notification using the unified notification system.
     */
    private void sendReviewerAssignedNotification(List<HubParticipant> reviewers, Long hubId, Long postId,
                                                String postTitle, String assignerName) {
        try {
            if (reviewers.isEmpty()) {
                return;
            }

            // Create entity references for deep linking
            NotificationStorageService.EntityReferences entityReferences =
                NotificationStorageService.EntityReferences.post(hubId, postId);

            // Create metadata with assignment context
            NotificationMetadata metadata = NotificationMetadata.builder()
                .actorName(assignerName)
                .targetTitle(postTitle)
                .build();

            // Send notification using unified system with hub participants
            notificationDispatcherService.dispatchToHubParticipants(
                NotificationType.ASSIGNED_AS_REVIEWER,
                "You've been assigned as a reviewer",
                assignerName + " assigned you to review " + postTitle,
                reviewers,
                entityReferences,
                metadata
            );

        } catch (Exception e) {
            logger.error("Failed to send reviewer assigned notifications: {}", e.getMessage(), e);
        }
    }

    /**
     * Sends post reviewed notification using the unified notification system.
     */
    private void sendPostReviewedNotification(List<HubParticipant> postCreators, Long hubId, Long postId,
                                            String postTitle, String reviewerName, String reviewStatus) {
        try {
            if (postCreators.isEmpty()) {
                return;
            }

            // Create entity references for deep linking
            NotificationStorageService.EntityReferences entityReferences =
                NotificationStorageService.EntityReferences.post(hubId, postId);

            // Create metadata with review context
            NotificationMetadata metadata = NotificationMetadata.builder()
                .actorName(reviewerName)
                .targetTitle(postTitle)
                .reviewStatus(reviewStatus)
                .build();

            // Get localized notification content
            Locale defaultLocale = Locale.forLanguageTag("english");
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("reviewerName", reviewerName);
            parameters.put("postTitle", postTitle);
            parameters.put("reviewStatus", reviewStatus);

            String title = notificationTranslationService.getNotificationTitle(
                NotificationType.POST_REVIEWED, defaultLocale);
            String message = notificationTranslationService.getNotificationMessage(
                NotificationType.POST_REVIEWED, defaultLocale, parameters);

            // Send notification using unified system with hub participants
            notificationDispatcherService.dispatchToHubParticipants(
                NotificationType.POST_REVIEWED,
                title,
                message,
                postCreators,
                entityReferences,
                metadata
            );

        } catch (Exception e) {
            logger.error("Failed to send post reviewed notifications: {}", e.getMessage(), e);
        }
    }
}
