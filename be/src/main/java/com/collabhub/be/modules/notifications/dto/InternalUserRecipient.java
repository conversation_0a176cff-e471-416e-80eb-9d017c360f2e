package com.collabhub.be.modules.notifications.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

import java.util.Objects;

/**
 * Production-grade implementation of NotificationRecipient for internal users.
 *
 * <p>This class represents internal users who are stored in the user table and have
 * a user_id. These users can receive both in-app notifications and email notifications,
 * and have full access to notification preferences and management features.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Immutable value object with comprehensive validation</li>
 *   <li>Factory methods for safe construction</li>
 *   <li>Proper equals/hashCode implementation for collections</li>
 *   <li>Thread-safe and null-safe operations</li>
 * </ul>
 *
 * <h3>Usage Examples:</h3>
 * <pre>
 * // Create from user entity
 * InternalUserRecipient recipient = InternalUserRecipient.of(user.getId(), user.getEmail(), user.getName());
 * 
 * // Create with validation
 * InternalUserRecipient recipient = InternalUserRecipient.builder()
 *     .userId(123L)
 *     .email("<EMAIL>")
 *     .displayName("John Doe")
 *     .build();
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class InternalUserRecipient implements NotificationRecipient {

    @NotNull
    @Positive
    private final Long userId;

    @NotBlank
    @Email
    @Size(max = 255)
    private final String email;

    @NotBlank
    @Size(max = 100)
    private final String displayName;

    /**
     * Private constructor for immutable object creation.
     *
     * @param userId the user ID (must be positive)
     * @param email the email address (must be valid email format)
     * @param displayName the display name (must not be blank)
     */
    private InternalUserRecipient(@NotNull @Positive Long userId, 
                                 @NotBlank @Email String email, 
                                 @NotBlank String displayName) {
        this.userId = Objects.requireNonNull(userId, "User ID cannot be null");
        this.email = Objects.requireNonNull(email, "Email cannot be null").trim();
        this.displayName = Objects.requireNonNull(displayName, "Display name cannot be null").trim();
        
        // Validate during construction
        validate();
    }

    /**
     * Factory method to create an InternalUserRecipient with validation.
     *
     * @param userId the user ID (must be positive)
     * @param email the email address (must be valid)
     * @param displayName the display name (must not be blank)
     * @return new InternalUserRecipient instance
     * 
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static InternalUserRecipient of(@NotNull @Positive Long userId, 
                                          @NotBlank @Email String email, 
                                          @NotBlank String displayName) {
        return new InternalUserRecipient(userId, email, displayName);
    }

    /**
     * Factory method to create from a User entity.
     *
     * @param user the user entity (must not be null)
     * @return new InternalUserRecipient instance
     * 
     * @throws IllegalArgumentException if user is invalid
     */
    public static InternalUserRecipient fromUser(@NotNull org.jooq.generated.tables.pojos.User user) {
        Objects.requireNonNull(user, "User cannot be null");
        
        if (user.getId() == null || user.getId() <= 0) {
            throw new IllegalArgumentException("User must have a positive ID");
        }
        
        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("User must have a valid email address");
        }
        
        String displayName = determineDisplayName(user);
        return new InternalUserRecipient(user.getId(), user.getEmail(), displayName);
    }

    /**
     * Determines the best display name from user data.
     */
    private static String determineDisplayName(@NotNull org.jooq.generated.tables.pojos.User user) {
        // Try full name first
        if (user.getDisplayName() != null && !user.getDisplayName().trim().isEmpty()) {
            return user.getDisplayName().trim();
        }
        
        // Fall back to email prefix
        String email = user.getEmail().trim();
        int atIndex = email.indexOf('@');
        if (atIndex > 0) {
            return email.substring(0, atIndex);
        }
        
        // Last resort
        return "User " + user.getId();
    }

    /**
     * Builder pattern for flexible construction.
     *
     * @return new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    @Override
    public boolean isInternal() {
        return true;
    }

    @Override
    @NotNull
    @Positive
    public Long getUserId() {
        return userId;
    }

    @Override
    @NotBlank
    @Email
    @Size(max = 255)
    public String getEmail() {
        return email;
    }

    @Override
    @NotBlank
    @Size(max = 100)
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        InternalUserRecipient that = (InternalUserRecipient) obj;
        return Objects.equals(userId, that.userId) &&
               Objects.equals(email, that.email) &&
               Objects.equals(displayName, that.displayName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, email, displayName);
    }

    @Override
    public String toString() {
        return String.format("InternalUserRecipient{userId=%d, email='%s', displayName='%s'}", 
                           userId, email, displayName);
    }

    /**
     * Builder class for flexible InternalUserRecipient construction.
     */
    public static final class Builder {
        private Long userId;
        private String email;
        private String displayName;

        private Builder() {}

        public Builder userId(@NotNull @Positive Long userId) {
            this.userId = userId;
            return this;
        }

        public Builder email(@NotBlank @Email String email) {
            this.email = email;
            return this;
        }

        public Builder displayName(@NotBlank String displayName) {
            this.displayName = displayName;
            return this;
        }

        /**
         * Builds the InternalUserRecipient with validation.
         *
         * @return new InternalUserRecipient instance
         * @throws IllegalArgumentException if any required field is missing or invalid
         */
        public InternalUserRecipient build() {
            if (userId == null) {
                throw new IllegalArgumentException("User ID is required");
            }
            if (email == null || email.trim().isEmpty()) {
                throw new IllegalArgumentException("Email is required");
            }
            if (displayName == null || displayName.trim().isEmpty()) {
                throw new IllegalArgumentException("Display name is required");
            }
            
            return new InternalUserRecipient(userId, email, displayName);
        }
    }
}
