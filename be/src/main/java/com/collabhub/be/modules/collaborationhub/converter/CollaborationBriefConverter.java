package com.collabhub.be.modules.collaborationhub.converter;

import com.collabhub.be.modules.collaborationhub.dto.*;
import org.jooq.generated.tables.pojos.CollaborationBrief;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


/**
 * Converter class for mapping between CollaborationBrief DTOs and jOOQ POJOs.
 * Handles conversion between different representations of collaboration brief data.
 * All briefs are visible to all participants within the hub.
 */
@Component
public class CollaborationBriefConverter {

    /**
     * Converts a create request to a jOOQ POJO for database insertion.
     *
     * @param request the create request
     * @param hubId the collaboration hub ID
     * @param createdByParticipantId the participant ID who created the brief
     * @return the jOOQ POJO
     */
    public CollaborationBrief toEntity(CollaborationBriefCreateRequest request, Long hubId, Long createdByParticipantId) {
        CollaborationBrief brief = new CollaborationBrief();
        brief.setHubId(hubId);
        brief.setTitle(request.getTitle());
        brief.setBody(request.getBody());
        brief.setCreatedByParticipantId(createdByParticipantId);
        brief.setCreatedAt(LocalDateTime.now());
        brief.setUpdatedAt(LocalDateTime.now());
        return brief;
    }

    /**
     * Updates an existing jOOQ POJO with data from an update request.
     *
     * @param brief the existing brief entity
     * @param request the update request
     * @return the updated jOOQ POJO
     */
    public CollaborationBrief updateEntity(CollaborationBrief brief, CollaborationBriefUpdateRequest request) {
        brief.setTitle(request.getTitle());
        brief.setBody(request.getBody());
        brief.setUpdatedAt(LocalDateTime.now());
        return brief;
    }

    /**
     * Converts a jOOQ POJO to a response DTO.
     *
     * @param brief the jOOQ POJO
     * @param createdByParticipantName the name of the participant who created the brief
     * @return the response DTO
     */
    public CollaborationBriefResponse toResponse(CollaborationBrief brief, String createdByParticipantName) {
        return new CollaborationBriefResponse(
                brief.getId(),
                brief.getHubId(),
                brief.getTitle(),
                brief.getBody(),
                brief.getCreatedByParticipantId(),
                createdByParticipantName,
                brief.getCreatedAt(),
                brief.getUpdatedAt()
        );
    }

    /**
     * Converts a jOOQ POJO to a lightweight list item DTO.
     *
     * @param brief the jOOQ POJO
     * @param createdByParticipantName the name of the participant who created the brief
     * @return the list item DTO
     */
    public CollaborationBriefListItemDto toListItem(CollaborationBrief brief, String createdByParticipantName) {
        String bodyPreview = brief.getBody() != null && brief.getBody().length() > 200
                ? brief.getBody().substring(0, 200) + "..."
                : brief.getBody();

        return new CollaborationBriefListItemDto(
                brief.getId(),
                brief.getTitle(),
                bodyPreview,
                brief.getCreatedByParticipantId(),
                createdByParticipantName,
                brief.getCreatedAt(),
                brief.getUpdatedAt()
        );
    }


}
