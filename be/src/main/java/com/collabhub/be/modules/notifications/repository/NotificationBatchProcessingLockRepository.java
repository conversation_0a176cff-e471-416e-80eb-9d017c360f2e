package com.collabhub.be.modules.notifications.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.NotificationBatchProcessingLockDao;
import org.jooq.generated.tables.pojos.NotificationBatchProcessingLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.jooq.generated.Tables.NOTIFICATION_BATCH_PROCESSING_LOCK;

/**
 * Repository for NotificationBatchProcessingLock entity using jOOQ for database operations.
 * Handles distributed locking for batch processing coordination across multiple instances.
 */
@Repository
public class NotificationBatchProcessingLockRepository extends NotificationBatchProcessingLockDao {

    private static final Logger log = LoggerFactory.getLogger(NotificationBatchProcessingLockRepository.class);
    private final DSLContext dsl;

    public NotificationBatchProcessingLockRepository(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Attempts to acquire a distributed lock for batch processing.
     * Uses database-level locking to coordinate between multiple application instances.
     *
     * @param lockKey unique identifier for the lock
     * @param lockedBy identifier of the instance acquiring the lock
     * @param lockTimeoutMinutes how long the lock should be held
     * @return true if lock was successfully acquired, false otherwise
     */
    public boolean acquireLock(String lockKey, String lockedBy, int lockTimeoutMinutes) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiresAt = now.plusMinutes(lockTimeoutMinutes);

        try {
            // Try to insert a new lock
            int inserted = dsl.insertInto(NOTIFICATION_BATCH_PROCESSING_LOCK)
                    .set(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCK_KEY, lockKey)
                    .set(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCKED_AT, now)
                    .set(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCKED_BY, lockedBy)
                    .set(NOTIFICATION_BATCH_PROCESSING_LOCK.EXPIRES_AT, expiresAt)
                    .onConflict(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCK_KEY)
                    .doUpdate()
                    .set(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCKED_AT, now)
                    .set(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCKED_BY, lockedBy)
                    .set(NOTIFICATION_BATCH_PROCESSING_LOCK.EXPIRES_AT, expiresAt)
                    .where(NOTIFICATION_BATCH_PROCESSING_LOCK.EXPIRES_AT.lt(now)) // Only update if expired
                    .execute();

            return inserted > 0;

        } catch (Exception e) {
            log.error("Failed to acquire lock for key {}: {}", lockKey, e.getMessage(), e);
            // Lock acquisition failed (likely due to concurrent access)
            return false;
        }
    }

    /**
     * Releases a distributed lock.
     *
     * @param lockKey unique identifier for the lock
     * @param lockedBy identifier of the instance that should own the lock
     * @return true if lock was successfully released, false if not owned by the instance
     */
    public boolean releaseLock(String lockKey, String lockedBy) {
        int deleted = dsl.deleteFrom(NOTIFICATION_BATCH_PROCESSING_LOCK)
                .where(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCK_KEY.eq(lockKey))
                .and(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCKED_BY.eq(lockedBy))
                .execute();

        return deleted > 0;
    }

    /**
     * Checks if a lock is currently held and not expired.
     *
     * @param lockKey unique identifier for the lock
     * @return Optional containing lock info if held, empty if not held or expired
     */
    public Optional<NotificationBatchProcessingLock> getLockInfo(String lockKey) {
        LocalDateTime now = LocalDateTime.now();

        return dsl.select()
                .from(NOTIFICATION_BATCH_PROCESSING_LOCK)
                .where(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCK_KEY.eq(lockKey))
                .and(NOTIFICATION_BATCH_PROCESSING_LOCK.EXPIRES_AT.gt(now))
                .fetchOptionalInto(NotificationBatchProcessingLock.class);
    }

    /**
     * Extends the expiration time of an existing lock.
     *
     * @param lockKey unique identifier for the lock
     * @param lockedBy identifier of the instance that should own the lock
     * @param additionalMinutes additional minutes to extend the lock
     * @return true if lock was successfully extended, false if not owned by the instance
     */
    public boolean extendLock(String lockKey, String lockedBy, int additionalMinutes) {
        LocalDateTime newExpiresAt = LocalDateTime.now().plusMinutes(additionalMinutes);

        int updated = dsl.update(NOTIFICATION_BATCH_PROCESSING_LOCK)
                .set(NOTIFICATION_BATCH_PROCESSING_LOCK.EXPIRES_AT, newExpiresAt)
                .where(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCK_KEY.eq(lockKey))
                .and(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCKED_BY.eq(lockedBy))
                .and(NOTIFICATION_BATCH_PROCESSING_LOCK.EXPIRES_AT.gt(LocalDateTime.now())) // Only if not expired
                .execute();

        return updated > 0;
    }

    /**
     * Cleans up expired locks to prevent database bloat.
     * Should be called periodically to remove stale locks.
     *
     * @return number of expired locks cleaned up
     */
    public int cleanupExpiredLocks() {
        LocalDateTime now = LocalDateTime.now();

        return dsl.deleteFrom(NOTIFICATION_BATCH_PROCESSING_LOCK)
                .where(NOTIFICATION_BATCH_PROCESSING_LOCK.EXPIRES_AT.lt(now))
                .execute();
    }

    /**
     * Forces release of all locks held by a specific instance.
     * Useful for cleanup when an instance shuts down gracefully.
     *
     * @param lockedBy identifier of the instance whose locks should be released
     * @return number of locks released
     */
    public int releaseAllLocksForInstance(String lockedBy) {
        return dsl.deleteFrom(NOTIFICATION_BATCH_PROCESSING_LOCK)
                .where(NOTIFICATION_BATCH_PROCESSING_LOCK.LOCKED_BY.eq(lockedBy))
                .execute();
    }
}
