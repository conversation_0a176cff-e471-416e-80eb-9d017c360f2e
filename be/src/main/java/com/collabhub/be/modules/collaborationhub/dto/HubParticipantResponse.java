package com.collabhub.be.modules.collaborationhub.dto;

import org.jooq.generated.enums.HubParticipantRole;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Response DTO for hub participant information.
 * Used in collaboration hub details to show participant information.
 */
public class HubParticipantResponse {

    @NotNull
    private Long id;

    private Long userId;
    private String email;
    private String name;

    @NotNull
    private HubParticipantRole role;

    @NotNull
    private Boolean isExternal;

    @NotNull
    private LocalDateTime invitedAt;

    private LocalDateTime joinedAt;

    @NotNull
    private String status; // "active", "pending", "removed"

    public HubParticipantResponse() {}

    public HubParticipantResponse(Long id, Long userId, String email, String name,
                                HubParticipantRole role, Boolean isExternal,
                                LocalDateTime invitedAt, LocalDateTime joinedAt) {
        this.id = id;
        this.userId = userId;
        this.email = email;
        this.name = name;
        this.role = role;
        this.isExternal = isExternal;
        this.invitedAt = invitedAt;
        this.joinedAt = joinedAt;
        this.status = determineStatus(isExternal, joinedAt);
    }

    public HubParticipantResponse(Long id, Long userId, String email, String name,
                                HubParticipantRole role, Boolean isExternal,
                                LocalDateTime invitedAt, LocalDateTime joinedAt, String status) {
        this.id = id;
        this.userId = userId;
        this.email = email;
        this.name = name;
        this.role = role;
        this.isExternal = isExternal;
        this.invitedAt = invitedAt;
        this.joinedAt = joinedAt;
        this.status = status;
    }

    private String determineStatus(Boolean isExternal, LocalDateTime joinedAt) {
        if (isExternal != null && isExternal && joinedAt == null) {
            return "pending";
        }
        return "active";
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public HubParticipantRole getRole() {
        return role;
    }

    public void setRole(HubParticipantRole role) {
        this.role = role;
    }

    public Boolean getIsExternal() {
        return isExternal;
    }

    public void setIsExternal(Boolean isExternal) {
        this.isExternal = isExternal;
    }

    public LocalDateTime getInvitedAt() {
        return invitedAt;
    }

    public void setInvitedAt(LocalDateTime invitedAt) {
        this.invitedAt = invitedAt;
    }

    public LocalDateTime getJoinedAt() {
        return joinedAt;
    }

    public void setJoinedAt(LocalDateTime joinedAt) {
        this.joinedAt = joinedAt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "HubParticipantResponse{" +
                "id=" + id +
                ", userId=" + userId +
                ", email='" + email + '\'' +
                ", name='" + name + '\'' +
                ", role=" + role +
                ", isExternal=" + isExternal +
                ", invitedAt=" + invitedAt +
                ", joinedAt=" + joinedAt +
                ", status='" + status + '\'' +
                '}';
    }
}
