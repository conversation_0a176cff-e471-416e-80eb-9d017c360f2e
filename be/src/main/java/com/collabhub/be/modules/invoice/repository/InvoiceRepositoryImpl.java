package com.collabhub.be.modules.invoice.repository;

import org.jooq.generated.enums.InvoiceStatus;
import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.InvoiceDao;
import org.jooq.generated.tables.pojos.Invoice;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.generated.Tables.INVOICE;

/**
 * Repository for Invoice entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy and soft deletion support.
 */
@Repository
public class InvoiceRepositoryImpl extends InvoiceDao {

    private final DSLContext dsl;

    public InvoiceRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all active invoices for a specific account with pagination and filtering.
     *
     * @param accountId the account ID for multi-tenancy
     * @param status optional status filter
     * @param fromDate optional start date filter
     * @param toDate optional end date filter
     * @param offset pagination offset
     * @param limit pagination limit
     * @return list of invoices matching the criteria
     */
    public List<Invoice> findAllByAccountId(Long accountId, InvoiceStatus status, LocalDate fromDate, LocalDate toDate, int offset, int limit) {
        var query = dsl.select()
                .from(INVOICE)
                .where(INVOICE.ACCOUNT_ID.eq(accountId))
                .and(INVOICE.DELETED_AT.isNull());

        if (status != null) {
            query = query.and(INVOICE.STATUS.eq(status));
        }

        if (fromDate != null) {
            query = query.and(INVOICE.ISSUE_DATE.greaterOrEqual(fromDate));
        }

        if (toDate != null) {
            query = query.and(INVOICE.ISSUE_DATE.lessOrEqual(toDate));
        }

        return query.orderBy(INVOICE.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(Invoice.class);
    }

    /**
     * Counts total invoices for the specified account with optional filtering.
     *
     * @param accountId the account ID for multi-tenancy
     * @param status optional status filter
     * @param fromDate optional start date filter
     * @param toDate optional end date filter
     * @return total count of invoices matching the criteria
     */
    public long countByAccountId(Long accountId, InvoiceStatus status, LocalDate fromDate, LocalDate toDate) {
        var query = dsl.selectCount()
                .from(INVOICE)
                .where(INVOICE.ACCOUNT_ID.eq(accountId))
                .and(INVOICE.DELETED_AT.isNull());

        if (status != null) {
            query = query.and(INVOICE.STATUS.eq(status));
        }

        if (fromDate != null) {
            query = query.and(INVOICE.ISSUE_DATE.greaterOrEqual(fromDate));
        }

        if (toDate != null) {
            query = query.and(INVOICE.ISSUE_DATE.lessOrEqual(toDate));
        }

        return query.fetchOne(0, Long.class);
    }

    /**
     * Finds an active invoice by ID and account ID.
     *
     * @param id the invoice ID
     * @param accountId the account ID for multi-tenancy
     * @return optional containing the invoice if found and active
     */
    public Optional<Invoice> findByIdAndAccountId(Long id, Long accountId) {
        return dsl.select()
                .from(INVOICE)
                .where(INVOICE.ID.eq(id))
                .and(INVOICE.ACCOUNT_ID.eq(accountId))
                .and(INVOICE.DELETED_AT.isNull())
                .fetchOptionalInto(Invoice.class);
    }

    /**
     * Finds an invoice by invoice number and account ID.
     *
     * @param invoiceNumber the invoice number
     * @param accountId the account ID for multi-tenancy
     * @return optional containing the invoice if found
     */
    public Optional<Invoice> findByInvoiceNumberAndAccountId(String invoiceNumber, Long accountId) {
        return dsl.select()
                .from(INVOICE)
                .where(INVOICE.INVOICE_NUMBER.eq(invoiceNumber))
                .and(INVOICE.ACCOUNT_ID.eq(accountId))
                .and(INVOICE.DELETED_AT.isNull())
                .fetchOptionalInto(Invoice.class);
    }

    /**
     * Soft deletes an invoice by setting deleted_at timestamp.
     *
     * @param id the invoice ID
     * @param accountId the account ID for multi-tenancy
     * @return true if the invoice was soft deleted, false if not found
     */
    public boolean softDelete(Long id, Long accountId) {
        int rowsAffected = dsl.update(INVOICE)
                .set(INVOICE.DELETED_AT, LocalDateTime.now())
                .where(INVOICE.ID.eq(id))
                .and(INVOICE.ACCOUNT_ID.eq(accountId))
                .and(INVOICE.DELETED_AT.isNull())
                .execute();

        return rowsAffected > 0;
    }

    /**
     * Finds the latest invoice number for the account to generate the next one.
     *
     * @param accountId the account ID for multi-tenancy
     * @param pattern the invoice number pattern to match
     * @return the latest invoice number matching the pattern, or null if none found
     */
    public Optional<String> findLatestInvoiceNumber(Long accountId, String pattern) {
        return dsl.select(INVOICE.INVOICE_NUMBER)
                .from(INVOICE)
                .where(INVOICE.ACCOUNT_ID.eq(accountId))
                .and(INVOICE.INVOICE_NUMBER.like(pattern + "%"))
                .orderBy(INVOICE.CREATED_AT.desc())
                .limit(1)
                .fetchOptionalInto(String.class);
    }

    /**
     * Finds the latest invoice number for the account (without pattern filtering).
     * Orders by invoice number value (varchar) to get the highest invoice number.
     *
     * @param accountId the account ID for multi-tenancy
     * @return the latest invoice number, or empty if none found
     */
    public Optional<String> findLatestInvoiceNumber(Long accountId) {
        return dsl.select(INVOICE.INVOICE_NUMBER)
                .from(INVOICE)
                .where(INVOICE.ACCOUNT_ID.eq(accountId))
                .and(INVOICE.DELETED_AT.isNull())
                .orderBy(INVOICE.INVOICE_NUMBER.desc())
                .limit(1)
                .fetchOptionalInto(String.class);
    }

    /**
     * Finds invoices that are overdue and need status updates.
     *
     * @return list of overdue invoices
     */
    public List<Invoice> findOverdueInvoices() {
        return dsl.select()
                .from(INVOICE)
                .where(INVOICE.DUE_DATE.lessThan(LocalDate.now()))
                .and(INVOICE.STATUS.in(InvoiceStatus.sent))
                .and(INVOICE.DELETED_AT.isNull())
                .fetchInto(Invoice.class);
    }

    /**
     * Updates invoice status.
     *
     * @param id the invoice ID
     * @param accountId the account ID for multi-tenancy
     * @param status the new status
     * @return true if the status was updated, false if not found
     */
    public boolean updateStatus(Long id, Long accountId, InvoiceStatus status) {
        int rowsAffected = dsl.update(INVOICE)
                .set(INVOICE.STATUS, status)
                .set(INVOICE.UPDATED_AT, LocalDateTime.now())
                .where(INVOICE.ID.eq(id))
                .and(INVOICE.ACCOUNT_ID.eq(accountId))
                .and(INVOICE.DELETED_AT.isNull())
                .execute();

        return rowsAffected > 0;
    }
}
