package com.collabhub.be.modules.notifications.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.Objects;

/**
 * Production-grade implementation of NotificationRecipient for external users.
 *
 * <p>This class represents external users who are hub participants without user accounts.
 * They are identified solely by email address and can receive both in-app and email
 * notifications with full feature parity to internal users.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Immutable value object with comprehensive validation</li>
 *   <li>Factory methods for safe construction from hub participants</li>
 *   <li>Proper equals/hashCode implementation for collections</li>
 *   <li>Thread-safe and null-safe operations</li>
 * </ul>
 *
 * <h3>Usage Examples:</h3>
 * <pre>
 * // Create from hub participant
 * ExternalUserRecipient recipient = ExternalUserRecipient.fromHubParticipant(participant);
 * 
 * // Create directly
 * ExternalUserRecipient recipient = ExternalUserRecipient.of("<EMAIL>", "<PERSON>");
 * 
 * // Create with builder
 * ExternalUserRecipient recipient = ExternalUserRecipient.builder()
 *     .email("<EMAIL>")
 *     .displayName("Jane Smith")
 *     .build();
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class ExternalUserRecipient implements NotificationRecipient {

    @NotBlank
    @Email
    @Size(max = 255)
    private final String email;

    @NotBlank
    @Size(max = 100)
    private final String displayName;

    /**
     * Private constructor for immutable object creation.
     *
     * @param email the email address (must be valid email format)
     * @param displayName the display name (must not be blank)
     */
    private ExternalUserRecipient(@NotBlank @Email String email, 
                                 @NotBlank String displayName) {
        this.email = Objects.requireNonNull(email, "Email cannot be null").trim().toLowerCase();
        this.displayName = Objects.requireNonNull(displayName, "Display name cannot be null").trim();
        
        // Validate during construction
        validate();
    }

    /**
     * Factory method to create an ExternalUserRecipient with validation.
     *
     * @param email the email address (must be valid)
     * @param displayName the display name (must not be blank)
     * @return new ExternalUserRecipient instance
     * 
     * @throws IllegalArgumentException if any parameter is invalid
     */
    public static ExternalUserRecipient of(@NotBlank @Email String email, 
                                          @NotBlank String displayName) {
        return new ExternalUserRecipient(email, displayName);
    }

    /**
     * Factory method to create from a HubParticipant entity.
     *
     * @param participant the hub participant (must not be null and must be external)
     * @return new ExternalUserRecipient instance
     * 
     * @throws IllegalArgumentException if participant is invalid or internal
     */
    public static ExternalUserRecipient fromHubParticipant(@NotNull org.jooq.generated.tables.pojos.HubParticipant participant) {
        Objects.requireNonNull(participant, "Hub participant cannot be null");
        
        // Ensure this is an external participant (no user_id)
        if (participant.getUserId() != null) {
            throw new IllegalArgumentException("Hub participant must be external (no user_id)");
        }
        
        if (participant.getEmail() == null || participant.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("External participant must have a valid email address");
        }
        
        String displayName = determineDisplayName(participant);
        return new ExternalUserRecipient(participant.getEmail(), displayName);
    }

    /**
     * Determines the best display name from hub participant data.
     */
    private static String determineDisplayName(@NotNull org.jooq.generated.tables.pojos.HubParticipant participant) {
        // Try name first
        if (participant.getName() != null && !participant.getName().trim().isEmpty()) {
            return participant.getName().trim();
        }
        
        // Fall back to email prefix
        String email = participant.getEmail().trim();
        int atIndex = email.indexOf('@');
        if (atIndex > 0) {
            String prefix = email.substring(0, atIndex);
            // Capitalize first letter and replace dots/underscores with spaces
            return capitalizeAndFormat(prefix);
        }
        
        // Last resort
        return "External User";
    }

    /**
     * Formats email prefix into a readable display name.
     */
    private static String capitalizeAndFormat(String prefix) {
        if (prefix == null || prefix.isEmpty()) {
            return "External User";
        }
        
        // Replace common separators with spaces
        String formatted = prefix.replace(".", " ")
                                .replace("_", " ")
                                .replace("-", " ");
        
        // Capitalize each word
        String[] words = formatted.split("\\s+");
        StringBuilder result = new StringBuilder();
        
        for (String word : words) {
            if (!word.isEmpty()) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    result.append(word.substring(1).toLowerCase());
                }
            }
        }
        
        return result.length() > 0 ? result.toString() : "External User";
    }

    /**
     * Builder pattern for flexible construction.
     *
     * @return new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    @Override
    public boolean isInternal() {
        return false;
    }

    @Override
    @NotNull
    public Long getUserId() {
        throw new UnsupportedOperationException("External users do not have user IDs");
    }

    @Override
    @NotBlank
    @Email
    @Size(max = 255)
    public String getEmail() {
        return email;
    }

    @Override
    @NotBlank
    @Size(max = 100)
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ExternalUserRecipient that = (ExternalUserRecipient) obj;
        return Objects.equals(email, that.email) &&
               Objects.equals(displayName, that.displayName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(email, displayName);
    }

    @Override
    public String toString() {
        return String.format("ExternalUserRecipient{email='%s', displayName='%s'}", 
                           email, displayName);
    }

    /**
     * Builder class for flexible ExternalUserRecipient construction.
     */
    public static final class Builder {
        private String email;
        private String displayName;

        private Builder() {}

        public Builder email(@NotBlank @Email String email) {
            this.email = email;
            return this;
        }

        public Builder displayName(@NotBlank String displayName) {
            this.displayName = displayName;
            return this;
        }

        /**
         * Builds the ExternalUserRecipient with validation.
         *
         * @return new ExternalUserRecipient instance
         * @throws IllegalArgumentException if any required field is missing or invalid
         */
        public ExternalUserRecipient build() {
            if (email == null || email.trim().isEmpty()) {
                throw new IllegalArgumentException("Email is required");
            }
            if (displayName == null || displayName.trim().isEmpty()) {
                throw new IllegalArgumentException("Display name is required");
            }
            
            return new ExternalUserRecipient(email, displayName);
        }
    }
}
