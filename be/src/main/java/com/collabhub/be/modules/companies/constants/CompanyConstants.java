package com.collabhub.be.modules.companies.constants;

/**
 * Constants for the Companies module.
 * Centralizes all magic numbers, string literals, and configuration values.
 */
public final class CompanyConstants {

    private CompanyConstants() {
        // Utility class - prevent instantiation
    }

    // Field size limits
    public static final int COMPANY_NAME_MIN_LENGTH = 1;
    public static final int COMPANY_NAME_MAX_LENGTH = 255;
    public static final int ADDRESS_STREET_MAX_LENGTH = 500;
    public static final int ADDRESS_CITY_MAX_LENGTH = 100;
    public static final int ADDRESS_POSTAL_CODE_MAX_LENGTH = 20;
    public static final int ADDRESS_COUNTRY_MAX_LENGTH = 100;
    public static final int VAT_NUMBER_MAX_LENGTH = 50;
    public static final int REGISTRATION_NUMBER_MAX_LENGTH = 100;
    public static final int PHONE_MAX_LENGTH = 50;
    public static final int EMAIL_MAX_LENGTH = 255;
    public static final int WEBSITE_MAX_LENGTH = 500;

    // Validation messages
    public static final String COMPANY_NAME_REQUIRED = "Company name is required";
    public static final String COMPANY_NAME_SIZE_MESSAGE = "Company name must be between " + COMPANY_NAME_MIN_LENGTH + " and " + COMPANY_NAME_MAX_LENGTH + " characters";
    public static final String ADDRESS_STREET_SIZE_MESSAGE = "Address street must not exceed " + ADDRESS_STREET_MAX_LENGTH + " characters";
    public static final String ADDRESS_CITY_SIZE_MESSAGE = "Address city must not exceed " + ADDRESS_CITY_MAX_LENGTH + " characters";
    public static final String ADDRESS_POSTAL_CODE_SIZE_MESSAGE = "Address postal code must not exceed " + ADDRESS_POSTAL_CODE_MAX_LENGTH + " characters";
    public static final String ADDRESS_COUNTRY_SIZE_MESSAGE = "Address country must not exceed " + ADDRESS_COUNTRY_MAX_LENGTH + " characters";
    public static final String VAT_NUMBER_SIZE_MESSAGE = "VAT number must not exceed " + VAT_NUMBER_MAX_LENGTH + " characters";
    public static final String REGISTRATION_NUMBER_SIZE_MESSAGE = "Registration number must not exceed " + REGISTRATION_NUMBER_MAX_LENGTH + " characters";
    public static final String PHONE_SIZE_MESSAGE = "Phone must not exceed " + PHONE_MAX_LENGTH + " characters";
    public static final String EMAIL_SIZE_MESSAGE = "Email must not exceed " + EMAIL_MAX_LENGTH + " characters";
    public static final String WEBSITE_SIZE_MESSAGE = "Website must not exceed " + WEBSITE_MAX_LENGTH + " characters";
    public static final String EMAIL_VALID_MESSAGE = "Email must be valid";

    // Business validation messages
    public static final String COMPANY_NAME_EMPTY_MESSAGE = "Company name cannot be empty";

    // Error messages
    public static final String COMPANY_NOT_FOUND_MESSAGE = "Account company not found or access denied";
}
