package com.collabhub.be.modules.notifications.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Configuration class to enable scheduling for notification batch processing.
 * This enables the @Scheduled annotations in NotificationBatchProcessor.
 */
@Configuration
@EnableScheduling
public class NotificationSchedulingConfig {
    // Configuration class - no additional beans needed
    // The @EnableScheduling annotation enables the Spring Task Scheduler
}
