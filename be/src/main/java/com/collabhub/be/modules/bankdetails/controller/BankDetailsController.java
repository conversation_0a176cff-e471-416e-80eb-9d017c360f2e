package com.collabhub.be.modules.bankdetails.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.bankdetails.dto.BankDetailsCreateRequest;
import com.collabhub.be.modules.bankdetails.dto.BankDetailsResponse;
import com.collabhub.be.modules.bankdetails.dto.BankDetailsUpdateRequest;
import com.collabhub.be.modules.bankdetails.service.BankDetailsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for Bank Details operations.
 * Handles CRUD operations for bank details with multi-tenancy and permission-based authorization.
 */
@RestController
@RequestMapping("/api/bank-details")
@Tag(name = "Bank Details", description = "Bank details management operations")
public class BankDetailsController {

    private static final Logger logger = LoggerFactory.getLogger(BankDetailsController.class);

    private final BankDetailsService bankDetailsService;
    private final JwtClaimsService jwtClaimsService;

    public BankDetailsController(BankDetailsService bankDetailsService,
                               JwtClaimsService jwtClaimsService) {
        this.bankDetailsService = bankDetailsService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Retrieves all active bank details for the current account.
     *
     * @param jwt the JWT token containing user information
     * @return list of bank details
     */
    @GetMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BANK_READ.permission)")
    @Operation(summary = "Get all bank details", description = "Retrieves all active bank details for the current account")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved bank details"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<List<BankDetailsResponse>> getAllBankDetails(
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving all bank details for account: {}", userContext.getAccountId());

        List<BankDetailsResponse> bankDetails = bankDetailsService.getAllBankDetails(userContext.getAccountId());

        logger.debug("Successfully retrieved {} bank details for account: {}", bankDetails.size(), userContext.getAccountId());

        return ResponseEntity.ok(bankDetails);
    }

    /**
     * Retrieves a specific bank detail by ID.
     *
     * @param id the bank detail ID
     * @param jwt the JWT token containing user information
     * @return the bank detail
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BANK_READ.permission)")
    @Operation(summary = "Get bank detail by ID", description = "Retrieves a specific bank detail by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved bank detail"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Bank detail not found")
    })
    public ResponseEntity<BankDetailsResponse> getBankDetailsById(
            @Parameter(description = "Bank detail ID") @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving bank detail with ID: {} for account: {}", id, userContext.getAccountId());

        BankDetailsResponse bankDetails = bankDetailsService.getBankDetailsById(id, userContext.getAccountId());

        logger.debug("Successfully retrieved bank detail: {} for account: {}", bankDetails.getName(), userContext.getAccountId());

        return ResponseEntity.ok(bankDetails);
    }

    /**
     * Creates a new bank detail.
     *
     * @param request the create request
     * @param jwt the JWT token containing user information
     * @return the created bank detail
     */
    @PostMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BANK_WRITE.permission)")
    @Operation(summary = "Create bank detail", description = "Creates a new bank detail")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Bank detail created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<BankDetailsResponse> createBankDetails(
            @Valid @RequestBody BankDetailsCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Creating new bank detail for account: {}", userContext.getAccountId());

        BankDetailsResponse createdBankDetails = bankDetailsService.createBankDetails(request, userContext.getAccountId());

        logger.info("Successfully created bank detail: {} for account: {}",
                   createdBankDetails.getName(), userContext.getAccountId());

        return ResponseEntity.status(HttpStatus.CREATED).body(createdBankDetails);
    }

    /**
     * Updates an existing bank detail.
     *
     * @param id the bank detail ID
     * @param request the update request
     * @param jwt the JWT token containing user information
     * @return the updated bank detail
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BANK_WRITE.permission)")
    @Operation(summary = "Update bank detail", description = "Updates an existing bank detail")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Bank detail updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Bank detail not found")
    })
    public ResponseEntity<BankDetailsResponse> updateBankDetails(
            @Parameter(description = "Bank detail ID") @PathVariable Long id,
            @Valid @RequestBody BankDetailsUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Updating bank detail with ID: {} for account: {}", id, userContext.getAccountId());

        BankDetailsResponse updatedBankDetails = bankDetailsService.updateBankDetails(id, request, userContext.getAccountId());

        logger.info("Successfully updated bank detail: {} for account: {}",
                   updatedBankDetails.getName(), userContext.getAccountId());

        return ResponseEntity.ok(updatedBankDetails);
    }

    /**
     * Soft deletes a bank detail.
     *
     * @param id the bank detail ID
     * @param jwt the JWT token containing user information
     * @return no content response
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BANK_DELETE.permission)")
    @Operation(summary = "Delete bank detail", description = "Soft deletes a bank detail")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Bank detail deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Bank detail not found")
    })
    public ResponseEntity<Void> deleteBankDetails(
            @Parameter(description = "Bank detail ID") @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Deleting bank detail with ID: {} for account: {}", id, userContext.getAccountId());

        bankDetailsService.deleteBankDetails(id, userContext.getAccountId());

        logger.info("Successfully deleted bank detail with ID: {} for account: {}", id, userContext.getAccountId());

        return ResponseEntity.noContent().build();
    }
}
