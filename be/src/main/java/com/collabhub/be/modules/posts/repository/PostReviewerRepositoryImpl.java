package com.collabhub.be.modules.posts.repository;

import org.jooq.DSLContext;
import org.jooq.generated.enums.ReviewStatus;
import org.jooq.generated.tables.daos.PostReviewerDao;
import org.jooq.generated.tables.pojos.PostReviewer;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.jooq.generated.tables.PostReviewer.POST_REVIEWER;

/**
 * Repository for PostReviewer entity using jOOQ for database operations.
 * Provides type-safe database operations for post reviewer management.
 */
@Repository
@Transactional
public class PostReviewerRepositoryImpl extends PostReviewerDao {

    private final DSLContext dsl;

    public PostReviewerRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all reviewers assigned to a specific post.
     *
     * @param postId the post ID
     * @return list of post reviewers
     */
    public List<PostReviewer> findByPostId(Long postId) {
        return dsl.selectFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .orderBy(POST_REVIEWER.ASSIGNED_AT.asc())
                .fetchInto(PostReviewer.class);
    }

    /**
     * Finds a specific post reviewer assignment.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return optional post reviewer
     */
    public Optional<PostReviewer> findByPostIdAndParticipantId(Long postId, Long participantId) {
        return dsl.selectFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
                .fetchOptionalInto(PostReviewer.class);
    }

    /**
     * Finds a specific post reviewer assignment (non-optional version).
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return post reviewer or null if not found
     */
    public PostReviewer findReviewerByPostAndParticipant(Long postId, Long participantId) {
        return dsl.selectFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
                .fetchOneInto(PostReviewer.class);
    }

    /**
     * Creates a new post reviewer assignment.
     *
     * @param postId        the post ID
     * @param participantId the participant ID
     */
    public void createPostReviewer(Long postId, Long participantId) {
        PostReviewer postReviewer = new PostReviewer();
        postReviewer.setPostId(postId);
        postReviewer.setParticipantId(participantId);
        postReviewer.setAssignedAt(LocalDateTime.now());
        postReviewer.setReviewStatus(ReviewStatus.pending);
        
        insert(postReviewer);
    }

    /**
     * Updates the review status and notes for a post reviewer.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @param status the new review status
     * @param reviewNotes the review notes
     * @return true if updated successfully
     */
    public boolean updateReviewStatus(Long postId, Long participantId, ReviewStatus status, String reviewNotes) {
        int updated = dsl.update(POST_REVIEWER)
                .set(POST_REVIEWER.REVIEW_STATUS, status)
                .set(POST_REVIEWER.REVIEW_NOTES, reviewNotes)
                .set(POST_REVIEWER.REVIEWED_AT, LocalDateTime.now())
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
                .execute();

        return updated > 0;
    }



    /**
     * Checks if a participant is assigned as a reviewer for a post.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return true if participant is assigned as reviewer
     */
    public boolean isParticipantAssignedAsReviewer(Long postId, Long participantId) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(POST_REVIEWER)
                        .where(POST_REVIEWER.POST_ID.eq(postId))
                        .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
        );
    }


    /**
     * Bulk loads reviewers for multiple posts to avoid N+1 queries.
     * Returns a map of postId -> List<PostReviewer>.
     */
    public Map<Long, List<PostReviewer>> bulkLoadReviewersByPostIds(List<Long> postIds) {
        if (postIds.isEmpty()) {
            return Map.of();
        }

        List<PostReviewer> allReviewers = dsl.selectFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.in(postIds))
                .orderBy(POST_REVIEWER.POST_ID.asc(), POST_REVIEWER.ASSIGNED_AT.asc())
                .fetchInto(PostReviewer.class);

        return allReviewers.stream()
                .collect(java.util.stream.Collectors.groupingBy(PostReviewer::getPostId));
    }

    /**
     * Removes a specific reviewer assignment from a post.
     * This will also delete any review data associated with this assignment.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return true if a reviewer was removed
     */
    public boolean removePostReviewer(Long postId, Long participantId) {
        int deleted = dsl.deleteFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
                .execute();

        return deleted > 0;
    }

    /**
     * Removes multiple reviewer assignments from a post in a batch operation.
     * This will also delete any review data associated with these assignments.
     *
     * @param postId the post ID
     * @param participantIds the list of participant IDs to remove
     * @return number of reviewers removed
     */
    public int removePostReviewers(Long postId, List<Long> participantIds) {
        if (participantIds == null || participantIds.isEmpty()) {
            return 0;
        }

        return dsl.deleteFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.in(participantIds))
                .execute();
    }

    /**
     * Removes all reviewer assignments from a post.
     * This will also delete any review data associated with these assignments.
     *
     * @param postId the post ID
     * @return number of reviewers removed
     */
    public int removeAllPostReviewers(Long postId) {
        return dsl.deleteFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .execute();
    }

}
