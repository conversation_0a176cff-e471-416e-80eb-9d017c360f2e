package com.collabhub.be.modules.auth.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * Redirect context for invoice access.
 * Used when external accountants authenticate via magic link to access a specific invoice.
 */
public class InvoiceAccessRedirectContext extends RedirectContext {
    
    @NotNull
    @Positive
    private final Long invoiceId;
    
    private final String accessType;
    
    @JsonCreator
    public InvoiceAccessRedirectContext(
            @JsonProperty("invoiceId") Long invoiceId,
            @JsonProperty("accessType") String accessType) {
        this.invoiceId = invoiceId;
        this.accessType = accessType;
    }
    
    public Long getInvoiceId() {
        return invoiceId;
    }
    
    public String getAccessType() {
        return accessType;
    }
    
    @Override
    public RedirectType getType() {
        return RedirectType.INVOICE_ACCESS;
    }
    
    @Override
    public String getTargetUrl() {
        return "/invoices/" + invoiceId;
    }
    
    @Override
    public boolean isValid() {
        return invoiceId != null && invoiceId > 0;
    }
    
    @Override
    public String toString() {
        return "InvoiceAccessRedirectContext{" +
                "invoiceId=" + invoiceId +
                ", accessType='" + accessType + '\'' +
                '}';
    }
}
