package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Custom pagination response DTO for jOOQ-based pagination.
 * Replaces Spring Data's Page interface to maintain consistency with jOOQ architecture.
 */
public class PageResponse<T> {

    @JsonProperty("content")
    private final List<T> content;

    @JsonProperty("page")
    private final int page;

    @JsonProperty("size")
    private final int size;

    @JsonProperty("total_elements")
    private final long totalElements;

    @JsonProperty("total_pages")
    private final int totalPages;

    @JsonProperty("first")
    private final boolean first;

    @JsonProperty("last")
    private final boolean last;

    @JsonProperty("has_next")
    private final boolean hasNext;

    @JsonProperty("has_previous")
    private final boolean hasPrevious;

    @JsonProperty("number_of_elements")
    private final int numberOfElements;

    public PageResponse(List<T> content, PageRequest pageRequest, long totalElements) {
        this.content = content != null ? content : List.of();
        this.page = pageRequest.getPage();
        this.size = pageRequest.getSize();
        this.totalElements = totalElements;
        this.totalPages = (int) Math.ceil((double) totalElements / pageRequest.getSize());
        this.first = pageRequest.getPage() == 0;
        this.last = pageRequest.getPage() >= (totalPages - 1);
        this.hasNext = !last;
        this.hasPrevious = !first;
        this.numberOfElements = this.content.size();
    }

    public List<T> getContent() {
        return content;
    }

    public int getPage() {
        return page;
    }

    public int getSize() {
        return size;
    }

    public long getTotalElements() {
        return totalElements;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public boolean isFirst() {
        return first;
    }

    public boolean isLast() {
        return last;
    }

    public boolean hasNext() {
        return hasNext;
    }

    public boolean hasPrevious() {
        return hasPrevious;
    }

    public int getNumberOfElements() {
        return numberOfElements;
    }

    /**
     * Creates an empty page response.
     * @param pageRequest the page request
     * @param <T> the content type
     * @return empty page response
     */
    public static <T> PageResponse<T> empty(PageRequest pageRequest) {
        return new PageResponse<>(List.of(), pageRequest, 0);
    }

    /**
     * Creates a page response with content.
     * @param content the page content
     * @param pageRequest the page request
     * @param totalElements the total number of elements
     * @param <T> the content type
     * @return page response with content
     */
    public static <T> PageResponse<T> of(List<T> content, PageRequest pageRequest, long totalElements) {
        return new PageResponse<>(content, pageRequest, totalElements);
    }

    @Override
    public String toString() {
        return "PageResponse{" +
                "page=" + page +
                ", size=" + size +
                ", totalElements=" + totalElements +
                ", totalPages=" + totalPages +
                ", numberOfElements=" + numberOfElements +
                '}';
    }
}
