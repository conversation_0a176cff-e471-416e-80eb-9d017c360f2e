package com.collabhub.be.modules.chat.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.chat.dto.ChatMessageRequest;
import com.collabhub.be.modules.chat.dto.ChatMessageResponse;
import com.collabhub.be.modules.chat.service.ChatMessageService;
import com.collabhub.be.modules.chat.service.ChatNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Controller;

import java.security.Principal;

/**
 * WebSocket controller for real-time chat messaging.
 * Handles WebSocket messages for sending chat messages and broadcasting to participants.
 */
@Controller
public class ChatWebSocketController {

    private static final Logger logger = LoggerFactory.getLogger(ChatWebSocketController.class);

    private final ChatMessageService messageService;
    private final ChatNotificationService notificationService;

    public ChatWebSocketController(ChatMessageService messageService,
                                 ChatNotificationService notificationService) {
        this.messageService = messageService;
        this.notificationService = notificationService;
    }

    /**
     * Handles WebSocket messages sent to a specific chat channel.
     * Messages are sent to: /app/chat/{channelId}/send
     * 
     * @param channelId the chat channel ID
     * @param request the message request
     * @param principal the authenticated user principal
     */
    @MessageMapping("/chat/{channelId}/send")
    public void sendMessage(@DestinationVariable Long channelId,
                           @Payload ChatMessageRequest request,
                           Principal principal) {
        
        logger.info("Received WebSocket message for channel {} from user {}", 
                   channelId, principal.getName());

        try {
            // Extract user context from principal
            UserContext userContext = extractUserContext(principal);

            // Send the message through the service (includes mention notifications)
            ChatMessageResponse response = messageService.sendMessage(channelId, userContext, request);

            // Broadcast the message to all channel participants via WebSocket
            notificationService.broadcastMessageToChannel(channelId, response);

            // Note: Mention notifications are already handled by messageService.sendMessage()

            logger.info("Successfully processed WebSocket message {} for channel {}",
                       response.getId(), channelId);

        } catch (Exception e) {
            logger.error("Error processing WebSocket message for channel {}: {}",
                        channelId, e.getMessage(), e);

            // Send error message back to sender
            notificationService.sendErrorToUser(principal.getName(),
                    "Failed to send message: " + e.getMessage());
        }
    }

    /**
     * Handles typing indicator messages.
     * Messages are sent to: /app/chat/{channelId}/typing
     * 
     * @param channelId the chat channel ID
     * @param principal the authenticated user principal
     */
    @MessageMapping("/chat/{channelId}/typing")
    public void handleTyping(@DestinationVariable Long channelId,
                           Principal principal) {
        
        logger.debug("Received typing indicator for channel {} from user {}", 
                    channelId, principal.getName());

        try {
            // Extract user context from principal
            UserContext userContext = extractUserContext(principal);

            // Broadcast typing indicator to other channel participants
            notificationService.broadcastTypingIndicator(channelId, userContext);

        } catch (Exception e) {
            logger.error("Error processing typing indicator for channel {}: {}",
                        channelId, e.getMessage(), e);
        }
    }

    /**
     * Handles user joining a chat channel.
     * Messages are sent to: /app/chat/{channelId}/join
     * 
     * @param channelId the chat channel ID
     * @param principal the authenticated user principal
     */
    @MessageMapping("/chat/{channelId}/join")
    public void handleJoinChannel(@DestinationVariable Long channelId,
                                Principal principal) {
        
        logger.info("User {} joining channel {}", principal.getName(), channelId);

        try {
            // Extract user context from principal
            UserContext userContext = extractUserContext(principal);

            // Notify other participants that user joined
            notificationService.broadcastUserJoined(channelId, userContext.getEmail());

        } catch (Exception e) {
            logger.error("Error processing channel join for channel {}: {}",
                        channelId, e.getMessage(), e);
        }
    }

    /**
     * Handles user leaving a chat channel.
     * Messages are sent to: /app/chat/{channelId}/leave
     * 
     * @param channelId the chat channel ID
     * @param principal the authenticated user principal
     */
    @MessageMapping("/chat/{channelId}/leave")
    public void handleLeaveChannel(@DestinationVariable Long channelId,
                                 Principal principal) {
        
        logger.info("User {} leaving channel {}", principal.getName(), channelId);

        try {
            // Extract user context from principal
            UserContext userContext = extractUserContext(principal);

            // Notify other participants that user left
            notificationService.broadcastUserLeft(channelId, userContext.getEmail());

        } catch (Exception e) {
            logger.error("Error processing channel leave for channel {}: {}",
                        channelId, e.getMessage(), e);
        }
    }

    /**
     * Extracts UserContext from the WebSocket principal.
     */
    private UserContext extractUserContext(Principal principal) {
        if (principal instanceof UsernamePasswordAuthenticationToken authToken) {
            Object credentials = authToken.getPrincipal();
            if (credentials instanceof UserContext userContext) {
                return userContext;
            }
        }
        throw new IllegalArgumentException("Invalid principal type: " + principal.getClass());
    }
}
