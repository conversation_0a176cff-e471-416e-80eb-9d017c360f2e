package com.collabhub.be.modules.auth.service;

import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.repository.RefreshTokenRepository;
import org.jooq.generated.enums.UserTypeEnum;
import org.jooq.generated.tables.pojos.RefreshToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.jooq.JSONB;

/**
 * Service for managing refresh tokens with rotation support.
 * Handles token generation, validation, and rotation according to OWASP best practices.
 */
@Service
@Transactional
public class RefreshTokenService {

    private static final Logger logger = LoggerFactory.getLogger(RefreshTokenService.class);
    private static final SecureRandom secureRandom = new SecureRandom();

    // Constants
    private static final String EXTERNAL_USER_ID_PREFIX = "ext_";
    private static final int TOKEN_MASK_LENGTH = 8;

    private final RefreshTokenRepository refreshTokenRepository;
    private final AuthProperties authProperties;

    public RefreshTokenService(RefreshTokenRepository refreshTokenRepository, 
                             AuthProperties authProperties) {
        this.refreshTokenRepository = refreshTokenRepository;
        this.authProperties = authProperties;
    }

    /**
     * Creates a new refresh token for an internal user.
     *
     * @param userId the internal user ID
     * @param userAgent the user agent string
     * @return the created refresh token
     */
    @Transactional
    public RefreshToken createInternalUserRefreshToken(@NotNull Long userId, String userAgent) {
        return createRefreshToken(userId, null, UserTypeEnum.INTERNAL, userAgent);
    }

    /**
     * Creates a new refresh token for an external user.
     *
     * @param email the external user's email
     * @param userAgent the user agent string
     * @return the created refresh token
     */
    @Transactional
    public RefreshToken createExternalUserRefreshToken(@NotBlank String email, String userAgent) {
        Long externalUserId = generateExternalUserId(email);
        return createRefreshToken(externalUserId, email, UserTypeEnum.EXTERNAL_PARTICIPANT, userAgent);
    }

    /**
     * Creates a new refresh token for a user.
     *
     * @param userId the user ID (internal user ID or generated external user ID)
     * @param userAgent the user agent string
     * @return the created refresh token
     * @deprecated Use createInternalUserRefreshToken or createExternalUserRefreshToken instead
     */
    @Deprecated
    public RefreshToken createRefreshToken(Long userId, String userAgent) {
        return createRefreshToken(userId, null, UserTypeEnum.INTERNAL, userAgent);
    }

    /**
     * Creates a new refresh token with specified user type.
     *
     * @param userId the user ID (internal user ID or generated external user ID)
     * @param email the email (required for external users, null for internal users)
     * @param userType the user type
     * @param userAgent the user agent string
     * @return the created refresh token
     */
    private RefreshToken createRefreshToken(@NotNull Long userId, String email,
                                          @NotNull UserTypeEnum userType, String userAgent) {
        String token = generateSecureToken();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiresAt = now.plus(authProperties.getRefreshToken().getTtl());

        RefreshToken refreshToken = new RefreshToken();
        refreshToken.setToken(token);
        refreshToken.setUserId(userId);
        refreshToken.setEmail(email);
        refreshToken.setUserType(userType);
        refreshToken.setIssuedAt(now);
        refreshToken.setExpiresAt(expiresAt);
        refreshToken.setUserAgent(userAgent);
        refreshToken.setCreatedAt(now);

        refreshTokenRepository.insert(refreshToken);

        logger.debug("Created {} refresh token for user {} with expiry {}",
                    userType, userId, expiresAt);

        return refreshToken;
    }

    /**
     * Generates a consistent external user ID from email.
     * Uses hash of email to ensure same ID for same email.
     */
    private Long generateExternalUserId(@NotBlank String email) {
        // Generate a consistent negative ID based on email hash
        // Negative IDs distinguish external users from internal users
        return -(Math.abs(email.toLowerCase().hashCode()) % Long.MAX_VALUE);
    }

    /**
     * Creates a new refresh token for a user with metadata.
     *
     * @param userId the user ID
     * @param userAgent the user agent string
     * @param metadata optional metadata to store with the token
     * @return the created refresh token
     * @deprecated Use createInternalUserRefreshToken or createExternalUserRefreshToken instead
     */
    @Deprecated
    public RefreshToken createRefreshTokenWithMetadata(Long userId, String userAgent, Map<String, Object> metadata) {
        RefreshToken refreshToken = createRefreshToken(userId, null, UserTypeEnum.INTERNAL, userAgent);

        // Set metadata if provided (for backward compatibility)
        if (metadata != null && !metadata.isEmpty()) {
            refreshToken.setMetadata(JSONB.valueOf(convertMapToJsonString(metadata)));
            refreshTokenRepository.update(refreshToken);
        }

        return refreshToken;
    }

    /**
     * Validates and retrieves a refresh token.
     * 
     * @param token the refresh token string
     * @return Optional containing the refresh token if valid
     */
    @Transactional(readOnly = true)
    public Optional<RefreshToken> validateRefreshToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            logger.warn("Attempted to validate null or empty refresh token");
            return Optional.empty();
        }

        RefreshToken refreshToken = refreshTokenRepository.fetchOneByToken(token);
        
        if (refreshToken == null) {
            logger.warn("Refresh token not found or invalid: {}", maskToken(token));
            return Optional.empty();
        }

        if (!refreshToken.getExpiresAt().isAfter(LocalDateTime.now()) || refreshToken.getRevoked()) {
            logger.warn("Refresh token is expired or revoked for user {}: {}", 
                       refreshToken.getUserId(), maskToken(token));
            return Optional.empty();
        }

        logger.debug("Validated refresh token for user {}", refreshToken.getUserId());
        return Optional.of(refreshToken);
    }

    /**
     * Rotates a refresh token by creating a new one and revoking the old one.
     * Preserves metadata from the old token.
     *
     * @param oldToken the current refresh token
     * @param userAgent the user agent string
     * @return the new refresh token
     */
    public RefreshToken rotateRefreshToken(RefreshToken oldToken, String userAgent) {
        if (!authProperties.getRefreshToken().isRotationEnabled()) {
            logger.debug("Refresh token rotation is disabled, returning existing token");
            return oldToken;
        }

        // Check if the token chain has exceeded absolute lifetime
        if (hasExceededAbsoluteLifetime(oldToken)) {
            logger.warn("Refresh token chain has exceeded absolute lifetime for user {}",
                       oldToken.getUserId());
            revokeAllTokensForUser(oldToken.getUserId());
            throw new IllegalStateException("Refresh token chain has exceeded absolute lifetime");
        }

        logger.debug("Rotating {} refresh token for user {}",
                    oldToken.getUserType(), oldToken.getUserId());

        // Create new token preserving user type and email
        RefreshToken newToken = createRefreshToken(
                oldToken.getUserId(),
                oldToken.getEmail(),
                oldToken.getUserType(),
                userAgent
        );

        // Preserve metadata if present (for backward compatibility)
        if (oldToken.getMetadata() != null) {
            newToken.setMetadata(oldToken.getMetadata());
            refreshTokenRepository.update(newToken);
        }

        // Revoke old token and link to new one
        refreshTokenRepository.revokeToken(oldToken.getId(), newToken.getId());

        logger.debug("Rotated {} refresh token for user {} from {} to {}",
                    oldToken.getUserType(), oldToken.getUserId(),
                    maskToken(oldToken.getToken()), maskToken(newToken.getToken()));

        return newToken;
    }

    /**
     * Revokes a specific refresh token.
     * 
     * @param token the refresh token to revoke
     */
    public void revokeRefreshToken(RefreshToken token) {
        refreshTokenRepository.revokeToken(token.getId(), null);
        logger.debug("Revoked refresh token for user {}: {}", 
                    token.getUserId(), maskToken(token.getToken()));
    }

    /**
     * Revokes all refresh tokens for a user.
     * 
     * @param userId the user ID
     */
    public void revokeAllTokensForUser(Long userId) {
        refreshTokenRepository.revokeAllTokensForUser(userId);
        logger.debug("Revoked all refresh tokens for user {}", userId);
    }



    /**
     * Generates a cryptographically secure random token.
     * 
     * @return the generated token string
     */
    private String generateSecureToken() {
        byte[] tokenBytes = new byte[authProperties.getRefreshToken().getTokenLength()];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }

    /**
     * Checks if a refresh token chain has exceeded the absolute lifetime.
     *
     * @param token the refresh token to check
     * @return true if exceeded absolute lifetime
     */
    private boolean hasExceededAbsoluteLifetime(RefreshToken token) {
        LocalDateTime absoluteExpiry = token.getIssuedAt().plus(authProperties.getRefreshToken().getAbsoluteLifetime());
        return LocalDateTime.now().isAfter(absoluteExpiry);
    }

    /**
     * Masks a token for logging purposes to prevent token leakage.
     *
     * @param token the token to mask
     * @return the masked token
     */
    private String maskToken(String token) {
        if (token == null || token.length() < TOKEN_MASK_LENGTH) {
            return "***";
        }
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }

    /**
     * Extracts metadata from a refresh token.
     *
     * @param token the refresh token
     * @return the metadata map, or null if no metadata
     * @deprecated Metadata-based approach is deprecated, use user_type column instead
     */
    @Deprecated
    @SuppressWarnings("unchecked")
    public Map<String, Object> extractMetadataFromToken(RefreshToken token) {
        if (token.getMetadata() == null) {
            return null;
        }

        try {
            String jsonString = token.getMetadata().data();
            return parseJsonStringToMap(jsonString);
        } catch (Exception e) {
            logger.warn("Failed to parse metadata from refresh token for user {}: {}",
                       token.getUserId(), e.getMessage());
            return null;
        }
    }

    /**
     * Checks if a refresh token belongs to an external user.
     *
     * @param refreshToken the refresh token to check
     * @return true if the token belongs to an external user
     */
    public boolean isExternalUserToken(@NotNull RefreshToken refreshToken) {
        UserTypeEnum userType = refreshToken.getUserType();
        return userType == UserTypeEnum.EXTERNAL_PARTICIPANT || userType == UserTypeEnum.EXTERNAL_ACCOUNTANT;
    }

    /**
     * Checks if a refresh token belongs to an internal user.
     *
     * @param refreshToken the refresh token to check
     * @return true if the token belongs to an internal user
     */
    public boolean isInternalUserToken(@NotNull RefreshToken refreshToken) {
        return refreshToken.getUserType() == UserTypeEnum.INTERNAL;
    }

    /**
     * Extracts email from an external user refresh token.
     *
     * @param refreshToken the external user refresh token
     * @return the email address
     * @throws IllegalArgumentException if token is not for external user or email is missing
     */
    @NotBlank
    public String getExternalUserEmail(@NotNull RefreshToken refreshToken) {
        if (!isExternalUserToken(refreshToken)) {
            throw new IllegalArgumentException("Token is not for external user");
        }

        String email = refreshToken.getEmail();
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("External user token missing email");
        }

        return email;
    }

    /**
     * Converts a map to JSON string for storage.
     */
    private String convertMapToJsonString(Map<String, Object> map) {
        // Simple JSON conversion - in production, use Jackson or similar
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) json.append(",");
            json.append("\"").append(entry.getKey()).append("\":\"")
                .append(entry.getValue()).append("\"");
            first = false;
        }
        json.append("}");
        return json.toString();
    }

    /**
     * Parses JSON string to map.
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseJsonStringToMap(String jsonString) {
        // Simple JSON parsing - in production, use Jackson or similar
        Map<String, Object> map = new java.util.HashMap<>();
        if (jsonString == null || jsonString.trim().isEmpty() || "{}".equals(jsonString.trim())) {
            return map;
        }

        // Remove braces and split by comma
        String content = jsonString.trim().substring(1, jsonString.length() - 1);
        if (content.trim().isEmpty()) {
            return map;
        }

        String[] pairs = content.split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":");
            if (keyValue.length == 2) {
                String key = keyValue[0].trim().replaceAll("\"", "");
                String value = keyValue[1].trim().replaceAll("\"", "");
                map.put(key, value);
            }
        }
        return map;
    }
}
