package com.collabhub.be.modules.invoice.validation;

import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Custom validation annotation for unique invoice number within account.
 * Note: This is primarily for documentation - actual uniqueness validation
 * is handled in the service layer due to multi-tenancy requirements.
 */
@Documented
@Constraint(validatedBy = {})
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface UniqueInvoiceNumber {

    String message() default InvoiceConstants.UNIQUE_INVOICE_NUMBER_MESSAGE;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
