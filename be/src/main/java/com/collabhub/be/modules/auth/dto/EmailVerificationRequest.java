package com.collabhub.be.modules.auth.dto;

import jakarta.validation.constraints.NotBlank;

/**
 * Request DTO for email verification.
 * Contains the verification token sent via email.
 */
public class EmailVerificationRequest {

    @NotBlank(message = "Verification token is required")
    private String token;

    public EmailVerificationRequest() {
    }

    public EmailVerificationRequest(String token) {
        this.token = token;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
