package com.collabhub.be.modules.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request DTO for sending a chat message.
 */
public class ChatMessageRequest {

    @Size(max = 4000, message = "Message content cannot exceed 4000 characters")
    private String content;

    @JsonProperty("attachment_uris")
    @Size(max = 10, message = "Cannot attach more than 10 files to a message")
    private List<String> attachmentUris;

    @JsonProperty("attachments")
    @Size(max = 10, message = "Cannot attach more than 10 files to a message")
    private List<AttachmentRequestDto> attachments;

    public ChatMessageRequest() {}

    public ChatMessageRequest(String content, List<String> attachmentUris) {
        this.content = content;
        this.attachmentUris = attachmentUris;
    }

    public ChatMessageRequest(String content, List<String> attachmentUris, List<AttachmentRequestDto> attachments) {
        this.content = content;
        this.attachmentUris = attachmentUris;
        this.attachments = attachments;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getAttachmentUris() {
        return attachmentUris;
    }

    public void setAttachmentUris(List<String> attachmentUris) {
        this.attachmentUris = attachmentUris;
    }

    public List<AttachmentRequestDto> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<AttachmentRequestDto> attachments) {
        this.attachments = attachments;
    }
}
