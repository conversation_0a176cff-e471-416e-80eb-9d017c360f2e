package com.collabhub.be.modules.companies.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.companies.constants.CompanyConstants;
import com.collabhub.be.modules.companies.dto.AccountCompanyCreateRequest;
import com.collabhub.be.modules.companies.dto.AccountCompanyUpdateRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service for validating account company operations.
 * Centralizes business validation logic for account companies.
 */
@Service
public class AccountCompanyValidationService {

    private static final Logger logger = LoggerFactory.getLogger(AccountCompanyValidationService.class);

    /**
     * Validates account company creation request.
     *
     * @param request the create request
     * @param accountId the account ID for multi-tenancy
     * @throws BadRequestException if validation fails
     */
    @Transactional(readOnly = true)
    public void validateCreateRequest(AccountCompanyCreateRequest request, Long accountId) {
        logger.debug("Validating account company creation request for account: {}", accountId);

        // Additional business validation can be added here
        // For now, Bean Validation annotations handle most validation
        
        logger.debug("Account company creation validation passed for account: {}", accountId);
    }

    /**
     * Validates account company update request.
     *
     * @param request the update request
     * @param accountId the account ID for multi-tenancy
     * @throws BadRequestException if validation fails
     */
    @Transactional(readOnly = true)
    public void validateUpdateRequest(AccountCompanyUpdateRequest request, Long accountId) {
        logger.debug("Validating account company update request for account: {}", accountId);

        // Validate company name if provided
        if (request.getCompanyName() != null && request.getCompanyName().trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.FIELD_REQUIRED, CompanyConstants.COMPANY_NAME_EMPTY_MESSAGE);
        }

        // Additional business validation can be added here
        
        logger.debug("Account company update validation passed for account: {}", accountId);
    }

    /**
     * Validates that a company name is not empty or whitespace only.
     *
     * @param companyName the company name to validate
     * @throws BadRequestException if the company name is invalid
     */
    public void validateCompanyName(String companyName) {
        if (companyName != null && companyName.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.FIELD_REQUIRED, CompanyConstants.COMPANY_NAME_EMPTY_MESSAGE);
        }
    }
}
