package com.collabhub.be.modules.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * DTO representing a file attachment in a chat message.
 */
public class AttachmentDto {

    @NotBlank
    private String url;

    @NotBlank
    private String filename;

    @JsonProperty("content_type")
    private String contentType;

    @NotNull
    private Long size;

    private String type; // "image", "video", "document", etc.

    public AttachmentDto() {}

    public AttachmentDto(String url, String filename, String contentType, Long size, String type) {
        this.url = url;
        this.filename = filename;
        this.contentType = contentType;
        this.size = size;
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
