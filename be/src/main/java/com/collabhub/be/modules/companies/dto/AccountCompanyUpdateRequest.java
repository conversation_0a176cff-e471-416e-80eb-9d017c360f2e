package com.collabhub.be.modules.companies.dto;

import com.collabhub.be.modules.companies.constants.CompanyConstants;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for updating an existing account company.
 * All fields are optional for partial updates.
 * Company name validation is applied only if provided.
 */
public class AccountCompanyUpdateRequest {

    @Size(min = CompanyConstants.COMPANY_NAME_MIN_LENGTH, max = CompanyConstants.COMPANY_NAME_MAX_LENGTH, message = CompanyConstants.COMPANY_NAME_SIZE_MESSAGE)
    @JsonProperty("company_name")
    private String companyName;

    @Size(max = CompanyConstants.ADDRESS_STREET_MAX_LENGTH, message = CompanyConstants.ADDRESS_STREET_SIZE_MESSAGE)
    @JsonProperty("address_street")
    private String addressStreet;

    @Size(max = CompanyConstants.ADDRESS_CITY_MAX_LENGTH, message = CompanyConstants.ADDRESS_CITY_SIZE_MESSAGE)
    @JsonProperty("address_city")
    private String addressCity;

    @Size(max = CompanyConstants.ADDRESS_POSTAL_CODE_MAX_LENGTH, message = CompanyConstants.ADDRESS_POSTAL_CODE_SIZE_MESSAGE)
    @JsonProperty("address_postal_code")
    private String addressPostalCode;

    @Size(max = CompanyConstants.ADDRESS_COUNTRY_MAX_LENGTH, message = CompanyConstants.ADDRESS_COUNTRY_SIZE_MESSAGE)
    @JsonProperty("address_country")
    private String addressCountry;

    @Size(max = CompanyConstants.VAT_NUMBER_MAX_LENGTH, message = CompanyConstants.VAT_NUMBER_SIZE_MESSAGE)
    @JsonProperty("vat_number")
    private String vatNumber;

    @Size(max = CompanyConstants.REGISTRATION_NUMBER_MAX_LENGTH, message = CompanyConstants.REGISTRATION_NUMBER_SIZE_MESSAGE)
    @JsonProperty("registration_number")
    private String registrationNumber;

    @Size(max = CompanyConstants.PHONE_MAX_LENGTH, message = CompanyConstants.PHONE_SIZE_MESSAGE)
    private String phone;

    @Email(message = CompanyConstants.EMAIL_VALID_MESSAGE)
    @Size(max = CompanyConstants.EMAIL_MAX_LENGTH, message = CompanyConstants.EMAIL_SIZE_MESSAGE)
    private String email;

    @Size(max = CompanyConstants.WEBSITE_MAX_LENGTH, message = CompanyConstants.WEBSITE_SIZE_MESSAGE)
    private String website;

    public AccountCompanyUpdateRequest() {
    }

    public AccountCompanyUpdateRequest(String companyName, String addressStreet, String addressCity,
                                     String addressPostalCode, String addressCountry, String vatNumber,
                                     String registrationNumber, String phone, String email, String website) {
        this.companyName = companyName;
        this.addressStreet = addressStreet;
        this.addressCity = addressCity;
        this.addressPostalCode = addressPostalCode;
        this.addressCountry = addressCountry;
        this.vatNumber = vatNumber;
        this.registrationNumber = registrationNumber;
        this.phone = phone;
        this.email = email;
        this.website = website;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getAddressStreet() {
        return addressStreet;
    }

    public void setAddressStreet(String addressStreet) {
        this.addressStreet = addressStreet;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressPostalCode() {
        return addressPostalCode;
    }

    public void setAddressPostalCode(String addressPostalCode) {
        this.addressPostalCode = addressPostalCode;
    }

    public String getAddressCountry() {
        return addressCountry;
    }

    public void setAddressCountry(String addressCountry) {
        this.addressCountry = addressCountry;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    @Override
    public String toString() {
        return "AccountCompanyUpdateRequest{" +
                "companyName='" + companyName + '\'' +
                ", addressStreet='" + addressStreet + '\'' +
                ", addressCity='" + addressCity + '\'' +
                ", addressPostalCode='" + addressPostalCode + '\'' +
                ", addressCountry='" + addressCountry + '\'' +
                ", vatNumber='" + vatNumber + '\'' +
                ", registrationNumber='" + registrationNumber + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", website='" + website + '\'' +
                '}';
    }
}
