package com.collabhub.be.modules.auth.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

/**
 * Base class for magic link redirect context.
 * Supports different types of redirects based on user type and authentication context.
 */
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes({
    @JsonSubTypes.Type(value = HubAccessRedirectContext.class, name = "HUB_ACCESS"),
    @JsonSubTypes.Type(value = InvoiceAccessRedirectContext.class, name = "INVOICE_ACCESS"),
    @JsonSubTypes.Type(value = GeneralAccessRedirectContext.class, name = "GENERAL_ACCESS")
})
public abstract class RedirectContext {
    
    /**
     * Gets the redirect type for this context.
     */
    public abstract RedirectType getType();
    
    /**
     * Gets the target URL for this redirect context.
     * Implementations should generate appropriate URLs based on their context.
     */
    public abstract String getTargetUrl();
    
    /**
     * Validates that the redirect context is valid and secure.
     */
    public abstract boolean isValid();
}
