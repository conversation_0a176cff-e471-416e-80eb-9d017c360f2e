package com.collabhub.be.modules.notifications.constants;

import java.time.Duration;

/**
 * Constants for notification batching operations.
 * 
 * <p>This class centralizes all magic numbers, strings, and configuration values
 * used throughout the notification batching system, providing a single source of
 * truth for system-wide constants and improving maintainability.</p>
 * 
 * <h3>Constant Categories:</h3>
 * <ul>
 *   <li><strong>Timing:</strong> Batch windows, processing intervals, timeouts</li>
 *   <li><strong>Limits:</strong> Batch sizes, retry attempts, queue sizes</li>
 *   <li><strong>Database:</strong> Table names, column constraints, indexes</li>
 *   <li><strong>Processing:</strong> Lock keys, instance identifiers, error codes</li>
 *   <li><strong>Email:</strong> Template names, subject prefixes, content limits</li>
 * </ul>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class BatchingConstants {
    
    // Prevent instantiation
    private BatchingConstants() {
        throw new UnsupportedOperationException("Constants class cannot be instantiated");
    }
    
    /**
     * Timing-related constants for batch processing operations.
     */
    public static final class Timing {
        
        /**
         * Default batching window duration in minutes.
         * Notifications within this window are grouped together.
         */
        public static final int DEFAULT_BATCH_WINDOW_MINUTES = 3;
        
        /**
         * High priority batching window duration in minutes.
         * Used for urgent notifications that need faster processing.
         */
        public static final int HIGH_PRIORITY_BATCH_WINDOW_MINUTES = 1;
        
        /**
         * Default batch processing interval in seconds.
         * How often the batch processor runs to check for ready batches.
         */
        public static final int DEFAULT_PROCESSING_INTERVAL_SECONDS = 180; // 3 minutes
        
        /**
         * Default retry delay in minutes.
         * Time to wait before retrying failed batch processing.
         */
        public static final int DEFAULT_RETRY_DELAY_MINUTES = 5;
        
        /**
         * Default lock timeout in minutes.
         * Maximum time a distributed lock can be held before automatic release.
         */
        public static final int DEFAULT_LOCK_TIMEOUT_MINUTES = 10;
        
        /**
         * Default cleanup retention period in hours.
         * How long to keep processed notifications before cleanup.
         */
        public static final int DEFAULT_CLEANUP_RETENTION_HOURS = 24;
        
        /**
         * Processing timeout for individual batches in minutes.
         * Maximum time allowed for processing a single batch.
         */
        public static final int BATCH_PROCESSING_TIMEOUT_MINUTES = 5;
        
        /**
         * Grace period for batch window calculations in seconds.
         * Buffer time to account for processing delays.
         */
        public static final int BATCH_WINDOW_GRACE_PERIOD_SECONDS = 30;
        
        // Duration constants for programmatic use
        public static final Duration DEFAULT_BATCH_WINDOW = Duration.ofMinutes(DEFAULT_BATCH_WINDOW_MINUTES);
        public static final Duration HIGH_PRIORITY_BATCH_WINDOW = Duration.ofMinutes(HIGH_PRIORITY_BATCH_WINDOW_MINUTES);
        public static final Duration PROCESSING_INTERVAL = Duration.ofSeconds(DEFAULT_PROCESSING_INTERVAL_SECONDS);
        public static final Duration RETRY_DELAY = Duration.ofMinutes(DEFAULT_RETRY_DELAY_MINUTES);
        public static final Duration LOCK_TIMEOUT = Duration.ofMinutes(DEFAULT_LOCK_TIMEOUT_MINUTES);
        public static final Duration CLEANUP_RETENTION = Duration.ofHours(DEFAULT_CLEANUP_RETENTION_HOURS);
        
        private Timing() {}
    }
    
    /**
     * Limit constants for batch processing operations.
     */
    public static final class Limits {
        
        /**
         * Maximum number of notifications to include in a single batch email.
         * Prevents emails from becoming too large or overwhelming.
         */
        public static final int MAX_NOTIFICATIONS_PER_BATCH = 50;
        
        /**
         * Maximum number of retry attempts for failed batch processing.
         * After this limit, notifications are moved to dead letter status.
         */
        public static final int MAX_RETRY_ATTEMPTS = 3;
        
        /**
         * Maximum number of batch groups to process in a single run.
         * Prevents processor from being overwhelmed by large queues.
         */
        public static final int MAX_BATCH_GROUPS_PER_RUN = 100;
        
        /**
         * Batch size for cleanup operations.
         * Number of records to delete in a single cleanup operation.
         */
        public static final int CLEANUP_BATCH_SIZE = 1000;
        
        /**
         * Maximum length for error messages stored in the database.
         */
        public static final int MAX_ERROR_MESSAGE_LENGTH = 500;
        
        /**
         * Maximum length for batch IDs.
         */
        public static final int MAX_BATCH_ID_LENGTH = 100;
        
        /**
         * Maximum number of notifications to fetch in a single database query.
         */
        public static final int MAX_NOTIFICATIONS_PER_QUERY = 1000;
        
        /**
         * Minimum batch size to warrant batch processing.
         * Batches smaller than this may be processed immediately.
         */
        public static final int MIN_BATCH_SIZE_FOR_PROCESSING = 2;
        
        private Limits() {}
    }
    
    /**
     * Database-related constants.
     */
    public static final class Database {
        
        /**
         * Name of the notification batch queue table.
         */
        public static final String BATCH_QUEUE_TABLE = "notification_batch_queue";
        
        /**
         * Name of the batch processing lock table.
         */
        public static final String PROCESSING_LOCK_TABLE = "notification_batch_processing_lock";
        
        /**
         * Name of the main notification table.
         */
        public static final String NOTIFICATION_TABLE = "notification";
        
        /**
         * Column name for batch queue status.
         */
        public static final String STATUS_COLUMN = "status";
        
        /**
         * Column name for batch window start time.
         */
        public static final String BATCH_WINDOW_START_COLUMN = "batch_window_start";
        
        /**
         * Column name for user ID.
         */
        public static final String USER_ID_COLUMN = "user_id";
        
        /**
         * Column name for retry count.
         */
        public static final String RETRY_COUNT_COLUMN = "retry_count";
        
        /**
         * Column name for created timestamp.
         */
        public static final String CREATED_AT_COLUMN = "created_at";
        
        /**
         * Column name for processed timestamp.
         */
        public static final String PROCESSED_AT_COLUMN = "processed_at";
        
        private Database() {}
    }
    
    /**
     * Processing-related constants.
     */
    public static final class Processing {
        
        /**
         * Lock key for the main batch processor.
         */
        public static final String BATCH_PROCESSOR_LOCK_KEY = "notification_batch_processor";
        
        /**
         * Lock key for cleanup operations.
         */
        public static final String CLEANUP_PROCESSOR_LOCK_KEY = "notification_cleanup_processor";
        
        /**
         * Prefix for batch IDs.
         */
        public static final String BATCH_ID_PREFIX = "batch_";
        
        /**
         * Separator for batch ID components.
         */
        public static final String BATCH_ID_SEPARATOR = "_";
        
        /**
         * Format string for batch IDs: "batch_user_{userId}_{timestamp}"
         */
        public static final String BATCH_ID_FORMAT = BATCH_ID_PREFIX + "user_%d_%s";
        
        /**
         * Prefix for instance identifiers.
         */
        public static final String INSTANCE_ID_PREFIX = "instance_";
        
        /**
         * Default instance identifier when hostname cannot be determined.
         */
        public static final String DEFAULT_INSTANCE_ID = "unknown_instance";
        
        /**
         * Thread name prefix for batch processing threads.
         */
        public static final String BATCH_THREAD_PREFIX = "BatchProcessor-";
        
        /**
         * Thread name prefix for cleanup threads.
         */
        public static final String CLEANUP_THREAD_PREFIX = "BatchCleanup-";
        
        private Processing() {}
    }
    
    /**
     * Email-related constants.
     */
    public static final class Email {
        
        /**
         * Template name for batched notification emails.
         */
        public static final String BATCHED_NOTIFICATIONS_TEMPLATE = "email/batched-notifications";
        
        /**
         * Subject prefix for single notification emails.
         */
        public static final String SINGLE_NOTIFICATION_SUBJECT_PREFIX = "Collaboration Hub - ";
        
        /**
         * Subject format for multiple notifications.
         */
        public static final String MULTIPLE_NOTIFICATIONS_SUBJECT_FORMAT = "%d new notifications from Collaboration Hub";
        
        /**
         * Subject format for single type notifications.
         */
        public static final String SINGLE_TYPE_NOTIFICATIONS_SUBJECT_FORMAT = "%d new %s notifications";
        
        /**
         * Maximum length for email subjects.
         */
        public static final int MAX_EMAIL_SUBJECT_LENGTH = 200;
        
        /**
         * Maximum number of notification types to show in subject.
         */
        public static final int MAX_TYPES_IN_SUBJECT = 3;
        
        /**
         * Default sender email address.
         */
        public static final String DEFAULT_SENDER_EMAIL = "<EMAIL>";
        
        /**
         * Default sender name.
         */
        public static final String DEFAULT_SENDER_NAME = "Collaboration Hub";
        
        private Email() {}
    }
    
    /**
     * Error codes for batch processing operations.
     */
    public static final class ErrorCodes {
        
        /**
         * Generic batch processing error.
         */
        public static final String BATCH_PROCESSING_ERROR = "BATCH_PROCESSING_ERROR";
        
        /**
         * Lock acquisition failure.
         */
        public static final String LOCK_ACQUISITION_FAILED = "LOCK_ACQUISITION_FAILED";
        
        /**
         * Email delivery failure.
         */
        public static final String EMAIL_DELIVERY_FAILED = "EMAIL_DELIVERY_FAILED";
        
        /**
         * Database operation failure.
         */
        public static final String DATABASE_OPERATION_FAILED = "DATABASE_OPERATION_FAILED";
        
        /**
         * Template processing failure.
         */
        public static final String TEMPLATE_PROCESSING_FAILED = "TEMPLATE_PROCESSING_FAILED";
        
        /**
         * Validation failure.
         */
        public static final String VALIDATION_FAILED = "VALIDATION_FAILED";
        
        /**
         * Configuration error.
         */
        public static final String CONFIGURATION_ERROR = "CONFIGURATION_ERROR";
        
        /**
         * Timeout error.
         */
        public static final String TIMEOUT_ERROR = "TIMEOUT_ERROR";
        
        /**
         * Unknown error.
         */
        public static final String UNKNOWN_ERROR = "UNKNOWN_ERROR";
        
        private ErrorCodes() {}
    }
    
    /**
     * Logging-related constants.
     */
    public static final class Logging {
        
        /**
         * Log marker for batch processing operations.
         */
        public static final String BATCH_PROCESSING_MARKER = "BATCH_PROCESSING";
        
        /**
         * Log marker for cleanup operations.
         */
        public static final String CLEANUP_MARKER = "BATCH_CLEANUP";
        
        /**
         * Log marker for lock operations.
         */
        public static final String LOCK_MARKER = "BATCH_LOCK";
        
        /**
         * Log marker for email operations.
         */
        public static final String EMAIL_MARKER = "BATCH_EMAIL";
        
        /**
         * Log marker for validation operations.
         */
        public static final String VALIDATION_MARKER = "BATCH_VALIDATION";
        
        private Logging() {}
    }
    
    /**
     * Metrics and monitoring constants.
     */
    public static final class Metrics {
        
        /**
         * Metric name for batch processing duration.
         */
        public static final String BATCH_PROCESSING_DURATION = "notification.batch.processing.duration";
        
        /**
         * Metric name for batch size distribution.
         */
        public static final String BATCH_SIZE_DISTRIBUTION = "notification.batch.size.distribution";
        
        /**
         * Metric name for processing success rate.
         */
        public static final String PROCESSING_SUCCESS_RATE = "notification.batch.success.rate";
        
        /**
         * Metric name for retry attempts.
         */
        public static final String RETRY_ATTEMPTS = "notification.batch.retry.attempts";
        
        /**
         * Metric name for queue depth.
         */
        public static final String QUEUE_DEPTH = "notification.batch.queue.depth";
        
        /**
         * Metric name for lock contention.
         */
        public static final String LOCK_CONTENTION = "notification.batch.lock.contention";
        
        private Metrics() {}
    }
}
