package com.collabhub.be.modules.invoice.converter;

import com.collabhub.be.modules.invoice.dto.InvoiceListItemDto;
import org.jooq.generated.enums.InvoiceStatus;
import org.jooq.generated.tables.pojos.Invoice;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Converter class for mapping Invoice entities to lightweight list item DTOs.
 * Optimized for list view performance by excluding heavy fields and using bulk brand name fetching.
 */
@Component
public class InvoiceListItemConverter {

    /**
     * Converts a list of invoices to lightweight list item DTOs with bulk brand name fetching.
     * This method optimizes performance by fetching all brand names in a single query.
     *
     * @param invoices the list of invoice entities
     * @param brandNamesById map of brand ID to brand name (fetched in bulk)
     * @return the list of lightweight list item DTOs
     */
    public List<InvoiceListItemDto> toListItems(List<Invoice> invoices, Map<Long, String> brandNamesById) {
        if (invoices == null) {
            return List.of();
        }

        return invoices.stream()
                .map(invoice -> toListItem(invoice, brandNamesById))
                .toList();
    }

    /**
     * Converts a single invoice to a lightweight list item DTO.
     *
     * @param invoice the invoice entity
     * @param brandNamesById map of brand ID to brand name
     * @return the lightweight list item DTO
     */
    private InvoiceListItemDto toListItem(Invoice invoice, Map<Long, String> brandNamesById) {
        if (invoice == null) {
            return null;
        }

        // Get recipient brand name
        String recipientName = brandNamesById.get(invoice.getRecipientId());

        // Calculate derived fields
        Integer daysUntilDue = calculateDaysUntilDue(invoice.getDueDate());
        Boolean isOverdue = invoice.getDueDate().isBefore(LocalDate.now()) &&
                           !InvoiceStatus.paid.equals(invoice.getStatus());

        InvoiceListItemDto dto = new InvoiceListItemDto(
                invoice.getId(),
                invoice.getInvoiceNumber(),
                invoice.getStatus(),
                invoice.getIssueDate(),
                invoice.getDueDate(),
                invoice.getSubtotal(),
                invoice.getTotalVat(),
                invoice.getTotalAmount(),
                invoice.getCurrency(),
                invoice.getRecipientId(),
                recipientName
        );

        dto.setDaysUntilDue(daysUntilDue);
        dto.setIsOverdue(isOverdue);

        return dto;
    }

    /**
     * Calculates days until due date.
     *
     * @param dueDate the due date
     * @return days until due (negative if overdue)
     */
    private Integer calculateDaysUntilDue(LocalDate dueDate) {
        if (dueDate == null) {
            return null;
        }
        return (int) ChronoUnit.DAYS.between(LocalDate.now(), dueDate);
    }
}
