package com.collabhub.be.modules.posts.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.posts.dto.PostCommentCreateRequest;
import com.collabhub.be.modules.posts.dto.PostCommentUpdateRequest;
import com.collabhub.be.modules.posts.dto.PostCommentResponse;
import com.collabhub.be.modules.posts.dto.PostCommentListResponse;
import com.collabhub.be.modules.posts.service.PostCommentService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing post comments.
 * Provides endpoints for CRUD operations on comments.
 */
@RestController
@RequestMapping("/api")
@Tag(name = "Post Comments", description = "Comment management for posts")
public class PostCommentController {

    private static final Logger logger = LoggerFactory.getLogger(PostCommentController.class);

    private final PostCommentService commentService;
    private final JwtClaimsService jwtClaimsService;

    public PostCommentController(PostCommentService commentService, JwtClaimsService jwtClaimsService) {
        this.commentService = commentService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Creates a new comment on a post.
     */
    @PostMapping("/posts/{postId}/comments")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_COMMENT.permission)")
    @Operation(summary = "Create a comment on a post", description = "Creates a new comment on the specified post")
    public ResponseEntity<PostCommentResponse> createComment(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @Valid @RequestBody PostCommentCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Creating comment on post {}", postId);

        PostCommentResponse response = commentService.createComment(postId, request);

        logger.debug("Successfully created comment {} on post {}", response.getId(), postId);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Updates an existing comment.
     */
    @PutMapping("/comments/{commentId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_COMMENT.permission)")
    @Operation(summary = "Update a comment", description = "Updates the content of an existing comment")
    public ResponseEntity<PostCommentResponse> updateComment(
            @Parameter(description = "Comment ID") @PathVariable Long commentId,
            @Valid @RequestBody PostCommentUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Updating comment {}", commentId);

        PostCommentResponse response = commentService.updateComment(commentId, request);

        logger.debug("Successfully updated comment {}", commentId);
        return ResponseEntity.ok(response);
    }

    /**
     * Deletes a comment.
     */
    @DeleteMapping("/comments/{commentId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_COMMENT.permission)")
    @Operation(summary = "Delete a comment", description = "Deletes an existing comment")
    public ResponseEntity<Void> deleteComment(
            @Parameter(description = "Comment ID") @PathVariable Long commentId,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Deleting comment {}", commentId);

        commentService.deleteComment(commentId);

        logger.debug("Successfully deleted comment {}", commentId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Retrieves a specific comment by ID.
     */
    @GetMapping("/comments/{commentId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_READ.permission)")
    @Operation(summary = "Get a comment", description = "Retrieves a specific comment by ID")
    public ResponseEntity<PostCommentResponse> getComment(
            @Parameter(description = "Comment ID") @PathVariable Long commentId,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving comment {}", commentId);

        PostCommentResponse response = commentService.getComment(commentId);

        return ResponseEntity.ok(response);
    }

    /**
     * Retrieves comments for a post with pagination.
     */
    @GetMapping("/posts/{postId}/comments")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_READ.permission)")
    @Operation(summary = "Get comments for a post", description = "Retrieves paginated comments for the specified post")
    public ResponseEntity<PostCommentListResponse> getCommentsForPost(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving comments for post {} (page {}, size {})", postId, page, size);

        PageRequest pageRequest = PageRequest.of(page, size);
        PostCommentListResponse response = commentService.getCommentsForPost(postId, pageRequest);

        logger.debug("Successfully retrieved {} comments for post {}",
                response.getComments().size(), postId);

        return ResponseEntity.ok(response);
    }
}
