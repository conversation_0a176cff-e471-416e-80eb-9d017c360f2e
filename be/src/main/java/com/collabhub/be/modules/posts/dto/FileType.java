package com.collabhub.be.modules.posts.dto;

/**
 * Enum representing supported file types for media uploads.
 */
public enum FileType {
    IMAGE("image"),
    VIDEO("video");

    private final String value;

    FileType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Creates a FileType from a string value.
     */
    public static FileType fromString(String value) {
        if (value == null) {
            return IMAGE; // Default fallback
        }
        
        for (FileType type : FileType.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }
        
        return IMAGE; // Default fallback for unknown types
    }

    /**
     * Determines file type from MIME type.
     */
    public static FileType fromMimeType(String mimeType) {
        if (mimeType != null && mimeType.startsWith("video/")) {
            return VIDEO;
        }
        return IMAGE;
    }

    @Override
    public String toString() {
        return value;
    }
}
