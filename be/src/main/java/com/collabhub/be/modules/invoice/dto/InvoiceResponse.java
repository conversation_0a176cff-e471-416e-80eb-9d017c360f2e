package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import org.jooq.generated.enums.InvoiceStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Response DTO for invoices.
 * Contains complete invoice information including items, recipients, and calculated fields.
 */
public class InvoiceResponse {

    @NotNull
    private Long id;

    @NotNull
    @JsonProperty("account_id")
    private Long accountId;

    @NotNull
    @JsonProperty("invoice_number")
    private String invoiceNumber;

    @NotNull
    private InvoiceStatus status;

    @NotNull
    private String template;

    @NotNull
    private String currency;

    @NotNull
    @JsonProperty("issue_date")
    private LocalDate issueDate;

    @NotNull
    @JsonProperty("due_date")
    private LocalDate dueDate;

    private String notes;

    @NotNull
    private BigDecimal subtotal;

    @NotNull
    @JsonProperty("total_vat")
    private BigDecimal totalVat;

    @NotNull
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;

    @NotNull
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @NotNull
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    // Entity IDs (snapshots are stored separately and fetched by hash when needed)
    @JsonProperty("issuer_id")
    private Long issuerId;

    @JsonProperty("recipient_id")
    private Long recipientId;

    @JsonProperty("bank_details_id")
    private Long bankDetailsId;

    // Snapshot hash fields for fetching snapshot data
    @JsonProperty("issuer_snapshot_hash")
    private String issuerSnapshotHash;

    @JsonProperty("recipient_snapshot_hash")
    private String recipientSnapshotHash;

    @JsonProperty("bank_details_snapshot_hash")
    private String bankDetailsSnapshotHash;

    // Related entities
    @NotNull
    private List<InvoiceItemResponse> items;

    @NotNull
    private List<InvoiceRecipientResponse> recipients;

    // Calculated fields
    @JsonProperty("days_until_due")
    private Integer daysUntilDue;

    @JsonProperty("is_overdue")
    private Boolean isOverdue;

    @JsonProperty("next_invoice_number")
    private String nextInvoiceNumber;

    public InvoiceResponse() {
    }

    public InvoiceResponse(Long id, Long accountId, String invoiceNumber, InvoiceStatus status,
                         String template, String currency, LocalDate issueDate, LocalDate dueDate,
                         String notes, BigDecimal subtotal, BigDecimal totalVat, BigDecimal totalAmount,
                         LocalDateTime createdAt, LocalDateTime updatedAt,
                         Long issuerId, Long recipientId, Long bankDetailsId,
                         String issuerSnapshotHash, String recipientSnapshotHash, String bankDetailsSnapshotHash,
                         List<InvoiceItemResponse> items, List<InvoiceRecipientResponse> recipients) {
        this.id = id;
        this.accountId = accountId;
        this.invoiceNumber = invoiceNumber;
        this.status = status;
        this.template = template;
        this.currency = currency;
        this.issueDate = issueDate;
        this.dueDate = dueDate;
        this.notes = notes;
        this.subtotal = subtotal;
        this.totalVat = totalVat;
        this.totalAmount = totalAmount;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.issuerId = issuerId;
        this.recipientId = recipientId;
        this.bankDetailsId = bankDetailsId;
        this.issuerSnapshotHash = issuerSnapshotHash;
        this.recipientSnapshotHash = recipientSnapshotHash;
        this.bankDetailsSnapshotHash = bankDetailsSnapshotHash;
        this.items = items;
        this.recipients = recipients;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public InvoiceStatus getStatus() {
        return status;
    }

    public void setStatus(InvoiceStatus status) {
        this.status = status;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public LocalDate getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDate issueDate) {
        this.issueDate = issueDate;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public BigDecimal getTotalVat() {
        return totalVat;
    }

    public void setTotalVat(BigDecimal totalVat) {
        this.totalVat = totalVat;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getIssuerId() {
        return issuerId;
    }

    public void setIssuerId(Long issuerId) {
        this.issuerId = issuerId;
    }

    public Long getRecipientId() {
        return recipientId;
    }

    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }

    public Long getBankDetailsId() {
        return bankDetailsId;
    }

    public void setBankDetailsId(Long bankDetailsId) {
        this.bankDetailsId = bankDetailsId;
    }

    public List<InvoiceItemResponse> getItems() {
        return items;
    }

    public void setItems(List<InvoiceItemResponse> items) {
        this.items = items;
    }

    public List<InvoiceRecipientResponse> getRecipients() {
        return recipients;
    }

    public void setRecipients(List<InvoiceRecipientResponse> recipients) {
        this.recipients = recipients;
    }

    public Integer getDaysUntilDue() {
        return daysUntilDue;
    }

    public void setDaysUntilDue(Integer daysUntilDue) {
        this.daysUntilDue = daysUntilDue;
    }

    public Boolean getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(Boolean isOverdue) {
        this.isOverdue = isOverdue;
    }

    public String getNextInvoiceNumber() {
        return nextInvoiceNumber;
    }

    public void setNextInvoiceNumber(String nextInvoiceNumber) {
        this.nextInvoiceNumber = nextInvoiceNumber;
    }

    public String getIssuerSnapshotHash() {
        return issuerSnapshotHash;
    }

    public void setIssuerSnapshotHash(String issuerSnapshotHash) {
        this.issuerSnapshotHash = issuerSnapshotHash;
    }

    public String getRecipientSnapshotHash() {
        return recipientSnapshotHash;
    }

    public void setRecipientSnapshotHash(String recipientSnapshotHash) {
        this.recipientSnapshotHash = recipientSnapshotHash;
    }

    public String getBankDetailsSnapshotHash() {
        return bankDetailsSnapshotHash;
    }

    public void setBankDetailsSnapshotHash(String bankDetailsSnapshotHash) {
        this.bankDetailsSnapshotHash = bankDetailsSnapshotHash;
    }

    @Override
    public String toString() {
        return "InvoiceResponse{" +
                "id=" + id +
                ", accountId=" + accountId +
                ", invoiceNumber='" + invoiceNumber + '\'' +
                ", status=" + status +
                ", template='" + template + '\'' +
                ", currency='" + currency + '\'' +
                ", issueDate=" + issueDate +
                ", dueDate=" + dueDate +
                ", notes='" + notes + '\'' +
                ", subtotal=" + subtotal +
                ", totalVat=" + totalVat +
                ", totalAmount=" + totalAmount +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", issuerId=" + issuerId +
                ", recipientId=" + recipientId +
                ", bankDetailsId=" + bankDetailsId +
                ", items=" + items +
                ", recipients=" + recipients +
                ", daysUntilDue=" + daysUntilDue +
                ", isOverdue=" + isOverdue +
                '}';
    }
}
