package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Refactored service for managing collaboration hub participants.
 * Delegates to focused services for specific responsibilities following SOLID principles.
 * This service acts as a facade to maintain backward compatibility while improving maintainability.
 */
@Service
public class HubParticipantService {

    private static final Logger logger = LoggerFactory.getLogger(HubParticipantService.class);

    // Log Messages
    private static final String INVITING_PARTICIPANTS_LOG = "Inviting {} participants to hub {} by user {}";
    private static final String PARTICIPANTS_INVITED_LOG = "PARTICIPANTS_INVITED: hubId={}, accountId={}, inviterUserId={}, participantCount={}, types={}";

    private final HubParticipantInvitationService invitationService;
    private final HubParticipantRoleService roleService;
    private final HubParticipantManagementService managementService;
    private final CollabHubPermissionService hubPermissionService;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final UserRepository userRepository;
    private final JwtClaimsService jwtClaimsService;

    public HubParticipantService(HubParticipantInvitationService invitationService,
                                         HubParticipantRoleService roleService,
                                         HubParticipantManagementService managementService,
                                         CollabHubPermissionService hubPermissionService,
                                         CollaborationHubRepositoryImpl hubRepository,
                                         UserRepository userRepository,
                                         JwtClaimsService jwtClaimsService) {
        this.invitationService = invitationService;
        this.roleService = roleService;
        this.managementService = managementService;
        this.hubPermissionService = hubPermissionService;
        this.hubRepository = hubRepository;
        this.userRepository = userRepository;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Invites participants to a collaboration hub.
     * Delegates to the invitation service for processing.
     *
     * @param hubId the hub ID
     * @param request the invitation request
     * @param accountId the account ID for multi-tenancy
     * @param inviterUserId the inviter user ID
     * @return invitation response with results
     */
    @Transactional
    public HubParticipantInviteResponse inviteParticipants(Long hubId, @Valid HubParticipantInviteRequest request,
                                                         Long accountId, Long inviterUserId) {
        logger.info(INVITING_PARTICIPANTS_LOG, request.getParticipants().size(), hubId, inviterUserId);

        // Validate admin access using our new permission service
        hubPermissionService.validateHubAdminAccess(hubId);
        CollaborationHub hub = hubRepository.findById(hubId);
        if (hub == null) {
            throw new NotFoundException(ErrorCode.HUB_NOT_FOUND, "Hub not found");
        }

        // Ensure account ADMIN users have a participant entry for management actions
        UserContext userContext = jwtClaimsService.getCurrentUser();
        managementService.ensureAdminParticipantExists(hubId, userContext);

        // Get inviter context for enhanced email templates
        HubParticipantInvitationService.InvitationContext invitationContext = buildInvitationContext(inviterUserId, accountId);

        List<HubParticipantInviteResponse.InvitedParticipant> invitedParticipants =
                invitationService.processAllInvitations(hub, request, accountId, invitationContext);

        logInvitationResults(hubId, accountId, inviterUserId, invitedParticipants, request);
        return new HubParticipantInviteResponse(invitedParticipants);
    }

    /**
     * Gets a paginated list of hub participants with filtering and role-based visibility.
     * Delegates to the management service.
     *
     * @param hubId the hub ID
     * @param pageRequest pagination parameters
     * @param role optional role filter
     * @return paginated list of participants
     */
    @Transactional(readOnly = true)
    public HubParticipantListResponse getHubParticipants(Long hubId, PageRequest pageRequest,
                                                       HubParticipantRole role) {
        return managementService.getHubParticipants(hubId, pageRequest, role);
    }

    /**
     * Gets details of a specific participant.
     * Delegates to the management service.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return participant details
     */
    @Transactional(readOnly = true)
    public HubParticipantResponse getParticipantDetails(Long hubId, Long participantId,
                                                      Long accountId, Long userId) {
        return managementService.getParticipantDetails(hubId, participantId);
    }

    /**
     * Updates a participant's role with proper validation.
     * Delegates to the role service.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param newRole the new role
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return updated participant response
     */
    @Transactional
    public HubParticipantResponse updateParticipantRole(Long hubId, Long participantId, HubParticipantRole newRole,
                                                       Long accountId, Long userId) {
        // Ensure account ADMIN users have a participant entry for management actions
        UserContext userContext = jwtClaimsService.getCurrentUser();
        managementService.ensureAdminParticipantExists(hubId, userContext);

        return roleService.updateParticipantRole(hubId, participantId, newRole, accountId, userId);
    }

    /**
     * Removes a participant from a hub with proper validation.
     * Delegates to the management service.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     */
    @Transactional
    public void removeParticipant(Long hubId, Long participantId, Long accountId, Long userId) {
        // Ensure account ADMIN users have a participant entry for management actions
        UserContext userContext = jwtClaimsService.getCurrentUser();
        managementService.ensureAdminParticipantExists(hubId, userContext);

        managementService.removeParticipant(hubId, participantId, accountId, userId);
    }

    /**
     * Resends invitation to an external participant with email notification.
     * Delegates to the management service.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     */
    @Transactional
    public void resendInvitation(Long hubId, Long participantId, Long accountId, Long userId) {
        // Ensure account ADMIN users have a participant entry for management actions
        UserContext userContext = jwtClaimsService.getCurrentUser();
        managementService.ensureAdminParticipantExists(hubId, userContext);

        managementService.resendInvitation(hubId, participantId, accountId, userId);
    }

    /**
     * Generates a magic link URL for an external participant without sending an email.
     * Delegates to the management service.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return response containing the magic link URL
     */
    @Transactional(readOnly = true)
    public HubParticipantInviteUrlResponse generateParticipantInviteUrl(Long hubId, Long participantId,
                                                                       Long accountId, Long userId) {
        // Ensure account ADMIN users have a participant entry for management actions
        UserContext userContext = jwtClaimsService.getCurrentUser();
        managementService.ensureAdminParticipantExists(hubId, userContext);

        return managementService.generateParticipantInviteUrl(hubId, participantId, accountId, userId);
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Builds invitation context for enhanced email templates.
     */
    private HubParticipantInvitationService.InvitationContext buildInvitationContext(Long inviterUserId, Long accountId) {
        User inviter = userRepository.findById(inviterUserId);
        String inviterName = inviter != null ? inviter.getDisplayName() : "Unknown User";
        
        // For now, use a simple account name - in production, this would come from account service
        String accountName = "Collaboration Hub Account";
        
        return new HubParticipantInvitationService.InvitationContext(inviterName, accountName);
    }

    /**
     * Logs invitation results for audit purposes.
     */
    private void logInvitationResults(Long hubId, Long accountId, Long inviterUserId,
                                    List<HubParticipantInviteResponse.InvitedParticipant> invitedParticipants,
                                    HubParticipantInviteRequest request) {
        
        String participantTypes = request.getParticipants().stream()
                .map(p -> p.getType().toString())
                .collect(Collectors.joining(","));

        logger.info(PARTICIPANTS_INVITED_LOG, hubId, accountId, inviterUserId, 
                   invitedParticipants.size(), participantTypes);
    }
}
