package com.collabhub.be.modules.notifications.controller;

import com.collabhub.be.modules.notifications.dto.NotificationPreferenceResponse;
import com.collabhub.be.modules.notifications.dto.NotificationPreferenceUpdateRequest;
import com.collabhub.be.modules.notifications.service.NotificationPreferenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for notification preference operations.
 * Handles user notification preference management with proper authorization.
 */
@RestController
@RequestMapping("/api/notification-preferences")
@Tag(name = "Notification Preferences", description = "User notification preference management")
public class NotificationPreferenceController {

    private static final Logger logger = LoggerFactory.getLogger(NotificationPreferenceController.class);

    private final NotificationPreferenceService notificationPreferenceService;

    public NotificationPreferenceController(NotificationPreferenceService notificationPreferenceService) {
        this.notificationPreferenceService = notificationPreferenceService;
    }

    /**
     * Retrieves notification preferences for the current user.
     *
     * @return list of notification preferences
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get notification preferences", description = "Retrieves all notification preferences for the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification preferences retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<List<NotificationPreferenceResponse>> getNotificationPreferences() {
        logger.debug("Retrieving notification preferences for current user");

        List<NotificationPreferenceResponse> preferences = notificationPreferenceService.getUserNotificationPreferences();

        logger.info("Successfully retrieved {} notification preferences", preferences.size());
        return ResponseEntity.ok(preferences);
    }

    /**
     * Updates notification preferences for the current user.
     *
     * @param request the update request containing preference changes
     * @return updated notification preferences
     */
    @PutMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Update notification preferences", description = "Updates notification preferences for the current user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification preferences updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<List<NotificationPreferenceResponse>> updateNotificationPreferences(
            @Valid @RequestBody NotificationPreferenceUpdateRequest request) {
        
        logger.debug("Updating notification preferences for current user");

        List<NotificationPreferenceResponse> updatedPreferences = 
                notificationPreferenceService.updateNotificationPreferences(request);

        logger.info("Successfully updated {} notification preferences", updatedPreferences.size());
        return ResponseEntity.ok(updatedPreferences);
    }
}
