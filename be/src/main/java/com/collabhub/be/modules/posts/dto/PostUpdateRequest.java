package com.collabhub.be.modules.posts.dto;

import com.collabhub.be.modules.posts.constants.PostConstants;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request DTO for updating an existing post.
 * Must have either caption or media content (or both).
 */
public class PostUpdateRequest {

    @Size(max = PostConstants.MAX_CAPTION_LENGTH, message = PostConstants.CAPTION_TOO_LONG_MESSAGE)
    private String caption;

    @JsonProperty("media_uris")
    private List<String> mediaUris;

    @JsonProperty("reviewer_notes")
    @Size(max = PostConstants.MAX_REVIEWER_NOTES_LENGTH, message = PostConstants.REVIEWER_NOTES_TOO_LONG_MESSAGE)
    private String reviewerNotes;

    @JsonProperty("reviewer_ids")
    @Size(max = PostConstants.MAX_REVIEWERS_COUNT, message = PostConstants.TOO_MANY_REVIEWERS_MESSAGE)
    private List<Long> reviewerIds;

    public PostUpdateRequest() {}

    public PostUpdateRequest(String caption, List<String> mediaUris, String reviewerNotes, List<Long> reviewerIds) {
        this.caption = caption;
        this.mediaUris = mediaUris;
        this.reviewerNotes = reviewerNotes;
        this.reviewerIds = reviewerIds;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public List<String> getMediaUris() {
        return mediaUris;
    }

    public void setMediaUris(List<String> mediaUris) {
        this.mediaUris = mediaUris;
    }

    public String getReviewerNotes() {
        return reviewerNotes;
    }

    public void setReviewerNotes(String reviewerNotes) {
        this.reviewerNotes = reviewerNotes;
    }

    public List<Long> getReviewerIds() {
        return reviewerIds;
    }

    public void setReviewerIds(List<Long> reviewerIds) {
        this.reviewerIds = reviewerIds;
    }

    /**
     * Validates that the post has either caption or media content.
     * This method is called by Bean Validation framework.
     */
    @AssertTrue(message = PostConstants.POST_CONTENT_REQUIRED_MESSAGE)
    public boolean isValidPostContent() {
        // Check if caption has content
        boolean hasCaption = caption != null && !caption.trim().isEmpty();

        // Check if media URIs has content
        boolean hasMedia = mediaUris != null && !mediaUris.isEmpty() &&
                          mediaUris.stream().anyMatch(uri -> uri != null && !uri.trim().isEmpty());

        // At least one must be present
        return hasCaption || hasMedia;
    }

    @Override
    public String toString() {
        return "PostUpdateRequest{" +
                "caption='" + caption + '\'' +
                ", mediaUris=" + mediaUris +
                ", reviewerNotes='" + reviewerNotes + '\'' +
                ", reviewerIds=" + reviewerIds +
                '}';
    }
}
