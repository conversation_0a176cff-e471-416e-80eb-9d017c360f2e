package com.collabhub.be.modules.auth.service;

import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.dto.EmailVerificationRequest;
import com.collabhub.be.modules.auth.dto.EmailVerificationResponse;
import com.collabhub.be.modules.auth.dto.RegistrationRequest;
import com.collabhub.be.modules.auth.dto.RegistrationResponse;
import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.model.TokenType;
import com.collabhub.be.modules.auth.repository.AccountRepository;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.auth.repository.VerificationTokenRepository;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.InternalServerErrorException;
import org.jooq.generated.tables.pojos.Account;
import org.jooq.generated.tables.pojos.User;
import org.jooq.generated.tables.pojos.VerificationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;

/**
 * Service for handling user registration and account creation.
 * Creates new accounts with the registering user as the admin.
 */
@Service
public class RegistrationService {

    private static final Logger logger = LoggerFactory.getLogger(RegistrationService.class);
    private static final SecureRandom secureRandom = new SecureRandom();

    private final UserRepository userRepository;
    private final AccountRepository accountRepository;
    private final VerificationTokenRepository verificationTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;
    private final AuthProperties authProperties;

    public RegistrationService(UserRepository userRepository,
                             AccountRepository accountRepository,
                             VerificationTokenRepository verificationTokenRepository,
                             PasswordEncoder passwordEncoder,
                             EmailService emailService,
                             AuthProperties authProperties) {
        this.userRepository = userRepository;
        this.accountRepository = accountRepository;
        this.verificationTokenRepository = verificationTokenRepository;
        this.passwordEncoder = passwordEncoder;
        this.emailService = emailService;
        this.authProperties = authProperties;
    }

    /**
     * Registers a new user and creates a new account.
     * The registering user becomes the admin of the new account.
     *
     * @param request the registration request
     * @return registration response with verification details
     * @throws ConflictException if email already exists
     * @throws InternalServerErrorException if registration fails
     */
    @Transactional
    public RegistrationResponse registerUser(RegistrationRequest request) {
        logger.info("Starting registration for email: {}", request.getEmail());

        // Check if email already exists
        if (userRepository.findByEmail(request.getEmail()).isPresent()) {
            logger.warn("Registration failed - email already exists: {}", request.getEmail());
            throw new ConflictException(ErrorCode.EMAIL_ALREADY_EXISTS, "Email address is already registered");
        }

        try {
            // Create new account
            Account account = new Account();
            account.setName(request.getAccountName());
            account.setCreatedAt(LocalDateTime.now());
            accountRepository.insert(account);
            Long accountId = account.getId();

            // Create user with disabled status
            User user = new User();
            user.setEmail(request.getEmail());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setDisplayName(request.getDisplayName());
            user.setRole(Role.ADMIN.getRoleName());
            user.setAccountId(accountId);
            user.setInternal(true);
            user.setEnabled(false); // Disabled until email verification
            user.setCreatedAt(LocalDateTime.now());
            userRepository.insert(user);
            Long userId = user.getId();

            // Generate verification token
            String verificationToken = generateSecureToken();
            LocalDateTime expiresAt = LocalDateTime.now()
                    .plus(authProperties.getVerificationToken().getEmailVerificationTtl());

            // Store verification token
            VerificationToken createdToken = verificationTokenRepository.createToken(
                    userId,
                    verificationToken,
                    TokenType.EMAIL_VERIFICATION,
                    expiresAt
            );

            // Send verification email
            boolean emailSent = emailService.sendEmailVerification(
                    request.getEmail(),
                    request.getDisplayName(),
                    createdToken.getToken()
            );

            if (!emailSent) {
                logger.warn("Failed to send verification email to: {}", request.getEmail());
                // Don't fail registration if email fails, user can request resend
            }

            logger.info("Registration completed successfully for email: {}", request.getEmail());

            return new RegistrationResponse(
                    "Registration successful. Please check your email to verify your account.",
                    request.getEmail(),
                    true
            );

        } catch (Exception e) {
            logger.error("Registration failed for email: {}", request.getEmail(), e);
            throw new InternalServerErrorException(ErrorCode.REGISTRATION_FAILED, "Registration failed due to internal error");
        }
    }

    /**
     * Verifies a user's email address using the verification token.
     * Enables the user account upon successful verification.
     *
     * @param request the email verification request containing the token
     * @return verification response with confirmation details
     * @throws BadRequestException if token is invalid or expired
     * @throws InternalServerErrorException if verification fails
     */
    @Transactional
    public EmailVerificationResponse verifyEmail(EmailVerificationRequest request) {
        logger.info("Starting email verification for token: {}", maskToken(request.getToken()));

        try {
            // Find and validate verification token
            Optional<VerificationToken> tokenOpt = verificationTokenRepository
                    .findByTokenAndType(request.getToken(), TokenType.EMAIL_VERIFICATION);

            if (tokenOpt.isEmpty()) {
                logger.warn("Invalid or expired verification token: {}", maskToken(request.getToken()));
                throw new BadRequestException(
                        ErrorCode.VERIFICATION_TOKEN_INVALID,
                        "Invalid or expired verification token"
                );
            }

            VerificationToken verificationToken = tokenOpt.get();
            Long userId = verificationToken.getUserId();

            // Find user (including disabled users)
            User user = userRepository.findByIdIncludingDisabled(userId);
            if (user == null) {
                logger.error("User not found for verification token: {}", userId);
                throw new InternalServerErrorException(
                        ErrorCode.EMAIL_VERIFICATION_FAILED,
                        "User not found"
                );
            }

            // Check if user is already enabled
            if (user.getEnabled()) {
                logger.info("User {} is already verified", user.getEmail());
                // Revoke token anyway to prevent reuse
                verificationTokenRepository.revokeToken(request.getToken());

                return new EmailVerificationResponse(
                        "Email address is already verified",
                        user.getEmail(),
                        true
                );
            }

            // Enable user account
            boolean userEnabled = userRepository.enableUser(userId);
            if (!userEnabled) {
                logger.error("Failed to enable user account for user: {}", userId);
                throw new InternalServerErrorException(
                        ErrorCode.EMAIL_VERIFICATION_FAILED,
                        "Failed to enable user account"
                );
            }

            // Revoke verification token to prevent reuse
            boolean tokenRevoked = verificationTokenRepository.revokeToken(request.getToken());
            if (!tokenRevoked) {
                logger.warn("Failed to revoke verification token: {}", maskToken(request.getToken()));
                // Don't fail the verification if token revocation fails
            }

            logger.info("Email verification completed successfully for user: {}", user.getEmail());

            return new EmailVerificationResponse(
                    "Email address verified successfully. You can now log in to your account.",
                    user.getEmail(),
                    true
            );

        } catch (BadRequestException e) {
            // Re-throw validation exceptions
            throw e;
        } catch (Exception e) {
            logger.error("Email verification failed for token: {}", maskToken(request.getToken()), e);
            throw new InternalServerErrorException(
                    ErrorCode.EMAIL_VERIFICATION_FAILED,
                    "Email verification failed due to internal error"
            );
        }
    }

    /**
     * Generates a cryptographically secure random token.
     *
     * @return base64 encoded secure token
     */
    private String generateSecureToken() {
        byte[] tokenBytes = new byte[32]; // 256 bits
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }

    /**
     * Masks a token for logging purposes to prevent token exposure.
     *
     * @param token the token to mask
     * @return masked token showing only first and last 4 characters
     */
    private String maskToken(String token) {
        if (token == null || token.length() < 8) {
            return "****";
        }
        return token.substring(0, 4) + "****" + token.substring(token.length() - 4);
    }
}
