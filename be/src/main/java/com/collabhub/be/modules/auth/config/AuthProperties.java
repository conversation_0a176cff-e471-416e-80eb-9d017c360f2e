package com.collabhub.be.modules.auth.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;

/**
 * Configuration properties for authentication and JWT settings.
 * All values are externally configurable for different environments.
 */
@ConfigurationProperties(prefix = "app.auth")
@Validated
public class AuthProperties {
    
    /**
     * JWT configuration properties.
     */
    private Jwt jwt = new Jwt();
    
    /**
     * Refresh token configuration properties.
     */
    private RefreshToken refreshToken = new RefreshToken();

    /**
     * Cookie configuration properties.
     */
    private Cookie cookie = new Cookie();

    /**
     * Verification token configuration properties.
     */
    private VerificationToken verificationToken = new VerificationToken();
    

    public Jwt getJwt() {
        return jwt;
    }
    
    public void setJwt(Jwt jwt) {
        this.jwt = jwt;
    }
    
    public RefreshToken getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(RefreshToken refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Cookie getCookie() {
        return cookie;
    }

    public void setCookie(Cookie cookie) {
        this.cookie = cookie;
    }

    public VerificationToken getVerificationToken() {
        return verificationToken;
    }

    public void setVerificationToken(VerificationToken verificationToken) {
        this.verificationToken = verificationToken;
    }
    
    public static class Jwt {
        
        /**
         * JWT issuer claim.
         */
        @NotBlank
        private String issuer = "https://api.collabhub.com";
        
        /**
         * JWT audience claim.
         */
        @NotBlank
        private String audience = "collabhub-api";
        
        /**
         * Access token time-to-live.
         */
        @NotNull
        private Duration accessTokenTtl = Duration.ofMinutes(15);
        
        /**
         * JWT signature algorithm.
         */
        @NotBlank
        private String signatureAlgorithm = "RS256";
        
        /**
         * Clock skew tolerance for JWT validation.
         */
        @NotNull
        private Duration clockSkew = Duration.ofMinutes(2);
        
        public String getIssuer() {
            return issuer;
        }
        
        public void setIssuer(String issuer) {
            this.issuer = issuer;
        }
        
        public String getAudience() {
            return audience;
        }
        
        public void setAudience(String audience) {
            this.audience = audience;
        }
        
        public Duration getAccessTokenTtl() {
            return accessTokenTtl;
        }
        
        public void setAccessTokenTtl(Duration accessTokenTtl) {
            this.accessTokenTtl = accessTokenTtl;
        }
        
        public String getSignatureAlgorithm() {
            return signatureAlgorithm;
        }
        
        public void setSignatureAlgorithm(String signatureAlgorithm) {
            this.signatureAlgorithm = signatureAlgorithm;
        }
        
        public Duration getClockSkew() {
            return clockSkew;
        }
        
        public void setClockSkew(Duration clockSkew) {
            this.clockSkew = clockSkew;
        }
    }
    
    public static class RefreshToken {
        
        /**
         * Refresh token time-to-live.
         */
        @NotNull
        private Duration ttl = Duration.ofDays(30);
        
        /**
         * Absolute maximum lifetime for refresh token chains.
         */
        @NotNull
        private Duration absoluteLifetime = Duration.ofDays(90);
        
        /**
         * Whether refresh token rotation is enabled.
         */
        private boolean rotationEnabled = true;
        
        /**
         * Length of generated refresh tokens.
         */
        @Min(32)
        private int tokenLength = 64;
        
        public Duration getTtl() {
            return ttl;
        }
        
        public void setTtl(Duration ttl) {
            this.ttl = ttl;
        }
        
        public Duration getAbsoluteLifetime() {
            return absoluteLifetime;
        }
        
        public void setAbsoluteLifetime(Duration absoluteLifetime) {
            this.absoluteLifetime = absoluteLifetime;
        }
        
        public boolean isRotationEnabled() {
            return rotationEnabled;
        }
        
        public void setRotationEnabled(boolean rotationEnabled) {
            this.rotationEnabled = rotationEnabled;
        }
        
        public int getTokenLength() {
            return tokenLength;
        }
        
        public void setTokenLength(int tokenLength) {
            this.tokenLength = tokenLength;
        }
    }

    public static class Cookie {

        /**
         * Name of the refresh token cookie.
         */
        @NotBlank
        private String refreshTokenName = "refresh_token";

        /**
         * Cookie domain. If null, uses the request domain.
         */
        private String domain;

        /**
         * Cookie path.
         */
        @NotBlank
        private String path = "/";

        /**
         * Whether cookies should be secure (HTTPS only).
         * Should be true in production.
         */
        private boolean secure = true;

        /**
         * SameSite cookie attribute for CSRF protection.
         */
        @NotBlank
        private String sameSite = "Strict";

        /**
         * Whether to set HttpOnly attribute.
         */
        private boolean httpOnly = true;

        public String getRefreshTokenName() {
            return refreshTokenName;
        }

        public void setRefreshTokenName(String refreshTokenName) {
            this.refreshTokenName = refreshTokenName;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public boolean isSecure() {
            return secure;
        }

        public void setSecure(boolean secure) {
            this.secure = secure;
        }

        public String getSameSite() {
            return sameSite;
        }

        public void setSameSite(String sameSite) {
            this.sameSite = sameSite;
        }

        public boolean isHttpOnly() {
            return httpOnly;
        }

        public void setHttpOnly(boolean httpOnly) {
            this.httpOnly = httpOnly;
        }
    }

    public static class VerificationToken {

        /**
         * Email verification token time-to-live.
         */
        @NotNull
        private Duration emailVerificationTtl = Duration.ofHours(24);

        /**
         * Magic link token time-to-live.
         */
        @NotNull
        private Duration magicLinkTtl = Duration.ofHours(24);

        /**
         * Unsubscribe token time-to-live.
         */
        @NotNull
        private Duration unsubscribeTokenTtl = Duration.ofDays(30);

        public Duration getEmailVerificationTtl() {
            return emailVerificationTtl;
        }

        public void setEmailVerificationTtl(Duration emailVerificationTtl) {
            this.emailVerificationTtl = emailVerificationTtl;
        }

        public Duration getMagicLinkTtl() {
            return magicLinkTtl;
        }

        public void setMagicLinkTtl(Duration magicLinkTtl) {
            this.magicLinkTtl = magicLinkTtl;
        }

        public Duration getUnsubscribeTokenTtl() {
            return unsubscribeTokenTtl;
        }

        public void setUnsubscribeTokenTtl(Duration unsubscribeTokenTtl) {
            this.unsubscribeTokenTtl = unsubscribeTokenTtl;
        }
    }
}
