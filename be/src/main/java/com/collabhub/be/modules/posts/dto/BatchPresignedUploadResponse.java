package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Response DTO for batch presigned upload URL generation.
 * Contains multiple presigned URLs for concurrent file uploads.
 */
public class BatchPresignedUploadResponse {

    @JsonProperty("upload_urls")
    private List<PresignedUploadResponse> uploadUrls;

    @JsonProperty("expires_in_minutes")
    private int expiresInMinutes = 15;

    public BatchPresignedUploadResponse() {}

    public BatchPresignedUploadResponse(List<PresignedUploadResponse> uploadUrls) {
        this.uploadUrls = uploadUrls;
    }

    public BatchPresignedUploadResponse(List<PresignedUploadResponse> uploadUrls, int expiresInMinutes) {
        this.uploadUrls = uploadUrls;
        this.expiresInMinutes = expiresInMinutes;
    }

    public List<PresignedUploadResponse> getUploadUrls() {
        return uploadUrls;
    }

    public void setUploadUrls(List<PresignedUploadResponse> uploadUrls) {
        this.uploadUrls = uploadUrls;
    }

    public int getExpiresInMinutes() {
        return expiresInMinutes;
    }

    public void setExpiresInMinutes(int expiresInMinutes) {
        this.expiresInMinutes = expiresInMinutes;
    }

    @Override
    public String toString() {
        return "BatchPresignedUploadResponse{" +
                "uploadUrls=" + uploadUrls +
                ", expiresInMinutes=" + expiresInMinutes +
                '}';
    }
}
