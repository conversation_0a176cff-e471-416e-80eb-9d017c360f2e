package com.collabhub.be.modules.collaborationhub.converter;

import com.collabhub.be.modules.collaborationhub.dto.*;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Converter class for mapping between CollaborationHub DTOs and jOOQ POJOs.
 * Handles conversion between different representations of collaboration hub data.
 */
@Component
public class CollaborationHubConverter {

    /**
     * Converts a create request DTO to a jOOQ POJO for database insertion.
     *
     * @param request the create request DTO
     * @param accountId the account ID for multi-tenancy
     * @param createdBy the user ID who is creating the hub
     * @return the jOOQ POJO ready for database insertion
     */
    public CollaborationHub toCollaborationHub(CollaborationHubCreateRequest request, Long accountId, Long createdBy) {
        if (request == null) {
            return null;
        }

        CollaborationHub hub = new CollaborationHub();
        hub.setAccountId(accountId);
        hub.setBrandId(request.getBrandId());
        hub.setName(request.getName());
        hub.setDescription(request.getDescription());
        hub.setCreatedBy(createdBy);
        hub.setCreatedAt(LocalDateTime.now());
        hub.setUpdatedAt(LocalDateTime.now());

        return hub;
    }

    /**
     * Updates an existing jOOQ POJO with data from an update request DTO.
     *
     * @param hub the existing jOOQ POJO to update
     * @param request the update request DTO
     * @return the updated jOOQ POJO
     */
    public CollaborationHub updateCollaborationHub(CollaborationHub hub, CollaborationHubUpdateRequest request) {
        if (hub == null || request == null) {
            return hub;
        }

        hub.setName(request.getName());
        hub.setDescription(request.getDescription());
        hub.setUpdatedAt(LocalDateTime.now());

        return hub;
    }

    /**
     * Converts a jOOQ POJO to a detailed response DTO for API responses.
     *
     * @param hub the jOOQ POJO
     * @param brandName the brand name
     * @param myRole the current user's role in the hub
     * @return the response DTO
     */
    public CollaborationHubResponse toResponse(CollaborationHub hub, String brandName, 
                                             HubParticipantRole myRole) {
        if (hub == null) {
            return null;
        }

        return new CollaborationHubResponse(
                hub.getId(),
                hub.getName(),
                hub.getBrandId(),
                brandName,
                hub.getDescription(),
                myRole,
                hub.getCreatedAt()
        );
    }

    /**
     * Converts a jOOQ POJO to a lightweight list item DTO.
     *
     * @param hub the jOOQ POJO
     * @param brandName the brand name
     * @param myRole the current user's role in the hub
     * @return the list item DTO
     */
    public CollaborationHubListItemDto toListItem(CollaborationHub hub, String brandName, HubParticipantRole myRole) {
        if (hub == null) {
            return null;
        }

        return new CollaborationHubListItemDto(
                hub.getId(),
                hub.getName(),
                brandName,
                hub.getBrandId(),
                myRole,
                hub.getCreatedAt()
        );
    }
}
