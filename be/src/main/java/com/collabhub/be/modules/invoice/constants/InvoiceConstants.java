package com.collabhub.be.modules.invoice.constants;

/**
 * Constants for the Invoice module.
 * Centralizes all magic numbers, string literals, and configuration values.
 */
public final class InvoiceConstants {

    private InvoiceConstants() {
        // Utility class - prevent instantiation
    }

    // Field size limits
    public static final int INVOICE_NUMBER_MAX_LENGTH = 50;
    public static final int TEMPLATE_NAME_MAX_LENGTH = 50;
    public static final int CURRENCY_CODE_LENGTH = 3;
    public static final int NOTES_MAX_LENGTH = 2000;
    public static final int EMAIL_MAX_LENGTH = 255;
    public static final int STATUS_NOTE_MAX_LENGTH = 500;

    // Validation messages
    public static final String ISSUER_ID_REQUIRED = "Issuer ID is required";
    public static final String RECIPIENT_ID_REQUIRED = "Recipient ID is required";
    public static final String INVOICE_NUMBER_REQUIRED = "Invoice number is required";
    public static final String ISSUE_DATE_REQUIRED = "Issue date is required";
    public static final String DUE_DATE_REQUIRED = "Due date is required";
    public static final String ITEMS_REQUIRED = "At least one invoice item is required";
    public static final String RECIPIENTS_REQUIRED = "At least one recipient is required";
    public static final String EMAIL_REQUIRED = "Email is required";
    public static final String RECIPIENT_TYPE_REQUIRED = "Recipient type is required";
    public static final String RECIPIENT_SOURCE_REQUIRED = "Recipient source is required";
    public static final String STATUS_REQUIRED = "Status is required";

    // Size validation messages
    public static final String INVOICE_NUMBER_SIZE_MESSAGE = "Invoice number cannot exceed " + INVOICE_NUMBER_MAX_LENGTH + " characters";
    public static final String TEMPLATE_SIZE_MESSAGE = "Template name cannot exceed " + TEMPLATE_NAME_MAX_LENGTH + " characters";
    public static final String CURRENCY_SIZE_MESSAGE = "Currency must be " + CURRENCY_CODE_LENGTH + " characters";
    public static final String NOTES_SIZE_MESSAGE = "Notes cannot exceed " + NOTES_MAX_LENGTH + " characters";
    public static final String EMAIL_SIZE_MESSAGE = "Email cannot exceed " + EMAIL_MAX_LENGTH + " characters";
    public static final String STATUS_NOTE_SIZE_MESSAGE = "Note cannot exceed " + STATUS_NOTE_MAX_LENGTH + " characters";

    // Business validation messages
    public static final String ISSUE_DATE_FUTURE_MESSAGE = "Issue date cannot be in the future";
    public static final String DUE_DATE_BEFORE_ISSUE_MESSAGE = "Due date must be on or after issue date";
    public static final String INVALID_EMAIL_MESSAGE = "Invalid email format";
    public static final String UNIQUE_INVOICE_NUMBER_MESSAGE = "Invoice number must be unique within account";

    // Error messages
    public static final String INVOICE_NOT_FOUND_MESSAGE = "Invoice not found or access denied";
    public static final String INVOICE_ALREADY_SENT_MESSAGE = "Invoice has already been sent. Use force send to send again.";
    public static final String INVOICE_MISSING_ISSUER_MESSAGE = "Invoice must have an issuer before it can be sent or downloaded";
    public static final String INVOICE_MISSING_RECIPIENT_MESSAGE = "Invoice must have a recipient before it can be sent or downloaded";

    // PDF generation constants
    public static final String PDF_CONTENT_TYPE = "application/pdf";
    public static final String PDF_FILENAME_PREFIX = "invoice-";
    public static final String PDF_FILENAME_EXTENSION = ".pdf";

    // Email service constants
    public static final int EMAIL_THREAD_POOL_SIZE = 5;
    public static final String EMAIL_FROM_ADDRESS = "<EMAIL>";
    public static final String EMAIL_TEMPLATE_PATH = "email/invoice-delivery";
    public static final String EMAIL_CHARSET = "UTF-8";

    // PDF generation constants
    public static final String PDF_TEMPLATE_PATH = "invoice/pdf/default";
    public static final String PDF_CHARSET = "UTF-8";
    public static final String PDF_FILENAME_FORMAT = "Invoice_%s.pdf";

    // Snapshot constants
    public static final String HASH_ALGORITHM = "SHA-256";

    // Invoice number generation constants
    public static final String FALLBACK_INVOICE_NUMBER = "1";
    public static final String EMPTY_PATTERN = "empty";
    public static final String NUMERIC_PATTERN = "numeric";
    public static final String ALPHANUMERIC_PATTERN = "alphanumeric";
    public static final String TEXT_PATTERN = "text";

    // Date formatting constants
    public static final String DEFAULT_DATE_FORMAT = "dd/MM/yyyy";
    public static final String US_DATE_FORMAT = "MM/dd/yyyy";

    // Currency symbols
    public static final String USD_SYMBOL = "$";
    public static final String GBP_SYMBOL = "£";
    public static final String EUR_SYMBOL = "€";

    // Default values
    public static final String DEFAULT_TEMPLATE = "standard";
    public static final String DEFAULT_CURRENCY = "USD";
}
