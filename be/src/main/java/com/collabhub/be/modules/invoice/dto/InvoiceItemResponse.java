package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

/**
 * Response DTO for invoice line items.
 * Contains calculated fields like line total and VAT amount.
 */
public class InvoiceItemResponse {

    @NotNull
    private Long id;

    @NotNull
    private String description;

    @NotNull
    private BigDecimal quantity;

    @NotNull
    @JsonProperty("unit_price")
    private BigDecimal unitPrice;

    @NotNull
    @JsonProperty("vat_rate")
    private BigDecimal vatRate;

    @NotNull
    @JsonProperty("line_total")
    private BigDecimal lineTotal;

    @NotNull
    @JsonProperty("vat_amount")
    private BigDecimal vatAmount;

    @NotNull
    @JsonProperty("sort_order")
    private Integer sortOrder;

    public InvoiceItemResponse() {
    }

    public InvoiceItemResponse(Long id, String description, BigDecimal quantity,
                             BigDecimal unitPrice, BigDecimal vatRate, BigDecimal lineTotal,
                             BigDecimal vatAmount, Integer sortOrder) {
        this.id = id;
        this.description = description;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.vatRate = vatRate;
        this.lineTotal = lineTotal;
        this.vatAmount = vatAmount;
        this.sortOrder = sortOrder;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getVatRate() {
        return vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public BigDecimal getLineTotal() {
        return lineTotal;
    }

    public void setLineTotal(BigDecimal lineTotal) {
        this.lineTotal = lineTotal;
    }

    public BigDecimal getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = vatAmount;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public String toString() {
        return "InvoiceItemResponse{" +
                "id=" + id +
                ", description='" + description + '\'' +
                ", quantity=" + quantity +
                ", unitPrice=" + unitPrice +
                ", vatRate=" + vatRate +
                ", lineTotal=" + lineTotal +
                ", vatAmount=" + vatAmount +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
