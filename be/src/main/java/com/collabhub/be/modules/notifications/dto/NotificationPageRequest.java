package com.collabhub.be.modules.notifications.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * Custom pagination request DTO for notification queries.
 * Follows the same pattern as other modules in the codebase.
 */
@Schema(description = "Pagination request for notifications")
public class NotificationPageRequest {

    @Min(value = 0, message = "Page number must be non-negative")
    @Schema(description = "Page number (0-based)", example = "0")
    private int page = 0;

    @Min(value = 1, message = "Page size must be at least 1")
    @Max(value = 100, message = "Page size cannot exceed 100")
    @Schema(description = "Page size", example = "20")
    private int size = 20;

    @Schema(description = "Return only unread notifications", example = "false")
    private boolean unreadOnly = false;

    public NotificationPageRequest() {}

    public NotificationPageRequest(int page, int size, boolean unreadOnly) {
        this.page = page;
        this.size = size;
        this.unreadOnly = unreadOnly;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public boolean isUnreadOnly() {
        return unreadOnly;
    }

    public void setUnreadOnly(boolean unreadOnly) {
        this.unreadOnly = unreadOnly;
    }

    /**
     * Calculates the offset for database queries.
     * @return the offset (page * size)
     */
    public int getOffset() {
        return page * size;
    }

    /**
     * Creates a NotificationPageRequest with default size.
     * @param page the page number
     * @return new NotificationPageRequest instance
     */
    public static NotificationPageRequest of(int page) {
        return new NotificationPageRequest(page, 20, false);
    }

    /**
     * Creates a NotificationPageRequest with specified page and size.
     * @param page the page number
     * @param size the page size
     * @return new NotificationPageRequest instance
     */
    public static NotificationPageRequest of(int page, int size) {
        return new NotificationPageRequest(page, size, false);
    }

    /**
     * Creates a NotificationPageRequest with all parameters.
     * @param page the page number
     * @param size the page size
     * @param unreadOnly whether to return only unread notifications
     * @return new NotificationPageRequest instance
     */
    public static NotificationPageRequest of(int page, int size, boolean unreadOnly) {
        return new NotificationPageRequest(page, size, unreadOnly);
    }

    @Override
    public String toString() {
        return "NotificationPageRequest{" +
                "page=" + page +
                ", size=" + size +
                ", unreadOnly=" + unreadOnly +
                '}';
    }
}
