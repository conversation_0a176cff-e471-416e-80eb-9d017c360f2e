package com.collabhub.be.modules.auth.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * System permissions for fine-grained access control.
 * Permissions can be assigned to roles or users directly.
 */
@Schema(enumAsRef = true)
public enum Permission {
    
    // Account management permissions
    ACCOUNT_READ("account:read"),
    ACCOUNT_WRITE("account:write"),
    ACCOUNT_DELETE("account:delete"),
    
    // User management permissions
    USER_READ("user:read"),
    USER_WRITE("user:write"),
    USER_DELETE("user:delete"),
    USER_INVITE("user:invite"),
    
    // Collaboration hub permissions
    HUB_READ("hub:read"),
    HUB_WRITE("hub:write"),
    HUB_DELETE("hub:delete"),
    HUB_INVITE("hub:invite"),

    // Hub participant permissions
    HUB_PARTICIPANT_READ("hub-participant:read"),
    HUB_PARTICIPANT_WRITE("hub-participant:write"),
    HUB_PARTICIPANT_INVITE("hub-participant:invite"),
    HUB_PARTICIPANT_MANAGE("hub-participant:manage"),
    
    // Content permissions
    CONTENT_READ("content:read"),
    CONTENT_WRITE("content:write"),
    CONTENT_DELETE("content:delete"),
    CONTENT_REVIEW("content:review"),

    // Post permissions
    POST_READ("post:read"),
    POST_WRITE("post:write"),
    POST_DELETE("post:delete"),
    POST_UPDATE("post:update"),
    POST_COMMENT("post:comment"),
    
    // Invoice permissions
    INVOICE_READ("invoice:read"),
    INVOICE_WRITE("invoice:write"),
    INVOICE_DELETE("invoice:delete"),
    INVOICE_SEND("invoice:send"),
    
    // Brand CRM permissions
    BRAND_READ("brand:read"),
    BRAND_WRITE("brand:write"),
    BRAND_DELETE("brand:delete"),

    // Company management permissions
    COMPANY_READ("company:read"),
    COMPANY_WRITE("company:write"),
    COMPANY_DELETE("company:delete"),

    // Bank details permissions
    BANK_READ("bank:read"),
    BANK_WRITE("bank:write"),
    BANK_DELETE("bank:delete"),

    // Chat permissions
    CHAT_READ("chat:read"),
    CHAT_WRITE("chat:write"),
    CHAT_CHANNEL_READ("chat-channel:read"),
    CHAT_CHANNEL_MANAGE("chat-channel:manage"),

    // Brief permissions
    BRIEF_READ("brief:read"),
    BRIEF_WRITE("brief:write"),
    BRIEF_DELETE("brief:delete"),
    BRIEF_UPDATE("brief:update");
    
    private final String permission;
    
    Permission(String permission) {
        this.permission = permission;
    }
    
    /**
     * Returns the permission string for authorization checks.
     */
    public String getPermission() {
        return permission;
    }
    
    /**
     * Creates a Permission from a string value.
     * 
     * @param permissionString the permission string
     * @return the corresponding Permission enum
     * @throws IllegalArgumentException if the permission string is invalid
     */
    public static Permission fromString(String permissionString) {
        if (permissionString == null || permissionString.trim().isEmpty()) {
            throw new IllegalArgumentException("Permission string cannot be null or empty");
        }
        
        for (Permission permission : Permission.values()) {
            if (permission.getPermission().equals(permissionString)) {
                return permission;
            }
        }
        
        throw new IllegalArgumentException("Invalid permission: " + permissionString);
    }
}
