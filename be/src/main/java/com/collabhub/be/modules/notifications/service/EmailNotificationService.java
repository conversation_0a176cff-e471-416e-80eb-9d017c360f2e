package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.auth.service.EmailService;
import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.repository.NotificationPreferenceRepository;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Service for handling email notification delivery with flood control.
 * Implements batching and rate limiting to prevent email spam.
 */
@Service
public class EmailNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(EmailNotificationService.class);

    // Flood control constants
    private static final int MAX_EMAILS_PER_USER_PER_HOUR = 10;
    private static final int BATCH_SIZE = 50;
    private static final int BATCH_DELAY_MINUTES = 5;

    // In-memory flood control tracking (in production, consider Redis)
    private final Map<Long, List<LocalDateTime>> userEmailHistory = new ConcurrentHashMap<>();

    private final EmailService emailService;
    private final UserRepository userRepository;
    private final NotificationPreferenceRepository preferenceRepository;

    public EmailNotificationService(EmailService emailService,
                                  UserRepository userRepository,
                                  NotificationPreferenceRepository preferenceRepository) {
        this.emailService = emailService;
        this.userRepository = userRepository;
        this.preferenceRepository = preferenceRepository;
    }

    /**
     * Sends email notifications to multiple users with flood control.
     *
     * @param type the notification type
     * @param title the email subject
     * @param message the email content
     * @param recipientUserIds the user IDs to send emails to
     * @param entityReferences optional entity references for email context
     */
    @Async
    @Transactional
    public void sendEmailNotifications(NotificationType type, String title, String message,
                                     Set<Long> recipientUserIds, 
                                     NotificationStorageService.EntityReferences entityReferences) {
        
        logger.debug("Processing email notifications: type={}, recipients={}", type, recipientUserIds.size());

        // Apply flood control filtering
        Set<Long> filteredRecipients = applyFloodControl(recipientUserIds);
        
        if (filteredRecipients.isEmpty()) {
            logger.info("No recipients after flood control filtering for notification type: {}", type);
            return;
        }

        // Batch the recipients to avoid overwhelming the email service
        List<Set<Long>> batches = createBatches(filteredRecipients, BATCH_SIZE);
        
        logger.info("Sending {} email notifications in {} batches for type: {}", 
                   filteredRecipients.size(), batches.size(), type);

        for (int i = 0; i < batches.size(); i++) {
            Set<Long> batch = batches.get(i);
            
            // Add delay between batches (except for the first one)
            if (i > 0) {
                try {
                    Thread.sleep(BATCH_DELAY_MINUTES * 60 * 1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.warn("Email batch processing interrupted", e);
                    return;
                }
            }
            
            sendEmailBatch(type, title, message, batch, entityReferences);
        }

        logger.info("Completed email notification delivery for type: {}", type);
    }

    /**
     * Applies flood control to filter out users who have received too many emails recently.
     */
    private Set<Long> applyFloodControl(Set<Long> recipientUserIds) {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        
        return recipientUserIds.stream()
                .filter(userId -> {
                    List<LocalDateTime> userHistory = userEmailHistory.getOrDefault(userId, new ArrayList<>());
                    
                    // Remove old entries
                    userHistory.removeIf(timestamp -> timestamp.isBefore(oneHourAgo));
                    
                    // Check if user is under the limit
                    boolean underLimit = userHistory.size() < MAX_EMAILS_PER_USER_PER_HOUR;
                    
                    if (underLimit) {
                        // Add current timestamp
                        userHistory.add(LocalDateTime.now());
                        userEmailHistory.put(userId, userHistory);
                    } else {
                        logger.debug("User {} excluded from email notification due to flood control", userId);
                    }
                    
                    return underLimit;
                })
                .collect(Collectors.toSet());
    }

    /**
     * Creates batches of user IDs for processing.
     */
    private List<Set<Long>> createBatches(Set<Long> userIds, int batchSize) {
        List<Set<Long>> batches = new ArrayList<>();
        Set<Long> currentBatch = new HashSet<>();
        
        for (Long userId : userIds) {
            currentBatch.add(userId);
            
            if (currentBatch.size() >= batchSize) {
                batches.add(new HashSet<>(currentBatch));
                currentBatch.clear();
            }
        }
        
        // Add remaining items as the last batch
        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }
        
        return batches;
    }

    /**
     * Sends a batch of emails.
     * This is a placeholder - actual implementation would integrate with email service.
     */
    private void sendEmailBatch(NotificationType type, String title, String message,
                              Set<Long> recipientUserIds,
                              NotificationStorageService.EntityReferences entityReferences) {

        logger.debug("Sending email batch: type={}, recipients={}", type, recipientUserIds.size());

        // Fetch user email addresses from user IDs
        List<User> users = userRepository.findByIds(recipientUserIds.stream().toList());

        for (User user : users) {
            try {
                if (user.getEmail() != null && !user.getEmail().trim().isEmpty()) {
                    // Check notification preferences before sending
                    if (shouldSendEmailNotification(user.getEmail(), type)) {
                        boolean sent = emailService.sendSimpleEmail(user.getEmail(), title, message);
                        if (sent) {
                            recordEmailSent(user.getId());
                            logger.debug("Sent email notification to user {}: {}", user.getId(), type);
                        } else {
                            logger.warn("Failed to send email notification to user {}: {}", user.getId(), type);
                        }
                    } else {
                        logger.debug("Skipped email notification for user {} due to preferences: {}", user.getId(), type);
                    }
                } else {
                    logger.warn("User {} has no valid email address", user.getId());
                }
            } catch (Exception e) {
                logger.error("Error sending email notification to user {}: {}", user.getId(), e.getMessage());
            }
        }

        logger.debug("Completed email batch: type={}, recipients={}", type, recipientUserIds.size());
    }

    /**
     * Checks if an email notification should be sent based on user preferences.
     * Always sends critical notifications like invitations regardless of preferences.
     */
    private boolean shouldSendEmailNotification(String email, NotificationType type) {
        // Always send critical notifications
        if (isCriticalNotification(type)) {
            return true;
        }

        // Check user preferences for non-critical notifications
        Boolean enabled = preferenceRepository.isNotificationEnabledForEmail(email, type, NotificationChannel.EMAIL);
        return enabled == null || enabled; // Default to enabled if no preference exists
    }

    /**
     * Determines if a notification type is critical and should always be sent.
     */
    private boolean isCriticalNotification(NotificationType type) {
        return type == NotificationType.INVITE_TO_HUB;
    }

    /**
     * Records that an email was sent to a user for flood control tracking.
     */
    private void recordEmailSent(Long userId) {
        List<LocalDateTime> userHistory = userEmailHistory.getOrDefault(userId, new ArrayList<>());
        userHistory.add(LocalDateTime.now());
        userEmailHistory.put(userId, userHistory);
    }

    /**
     * Cleans up old email history entries to prevent memory leaks.
     * Should be called periodically.
     */
    public void cleanupEmailHistory() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        
        userEmailHistory.entrySet().removeIf(entry -> {
            List<LocalDateTime> history = entry.getValue();
            history.removeIf(timestamp -> timestamp.isBefore(oneHourAgo));
            return history.isEmpty();
        });
        
        logger.debug("Cleaned up email history, {} users remaining", userEmailHistory.size());
    }
}
