package com.collabhub.be.modules.auth.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * Redirect context for collaboration hub access.
 * Used when external participants authenticate via magic link to access a specific hub.
 */
public class HubAccessRedirectContext extends RedirectContext {
    
    @NotNull
    @Positive
    private final Long hubId;
    
    private final String role;
    
    @JsonCreator
    public HubAccessRedirectContext(
            @JsonProperty("hubId") Long hubId,
            @JsonProperty("role") String role) {
        this.hubId = hubId;
        this.role = role;
    }
    
    public Long getHubId() {
        return hubId;
    }
    
    public String getRole() {
        return role;
    }
    
    @Override
    public RedirectType getType() {
        return RedirectType.HUB_ACCESS;
    }
    
    @Override
    public String getTargetUrl() {
        return "/hubs/" + hubId;
    }
    
    @Override
    public boolean isValid() {
        return hubId != null && hubId > 0;
    }
    
    @Override
    public String toString() {
        return "HubAccessRedirectContext{" +
                "hubId=" + hubId +
                ", role='" + role + '\'' +
                '}';
    }
}
