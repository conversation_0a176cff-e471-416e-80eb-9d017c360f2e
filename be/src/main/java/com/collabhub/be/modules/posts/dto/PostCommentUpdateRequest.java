package com.collabhub.be.modules.posts.dto;

import com.collabhub.be.modules.posts.constants.PostConstants;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for updating an existing post comment.
 */
public class PostCommentUpdateRequest {

    @NotBlank(message = PostConstants.COMMENT_CONTENT_REQUIRED_MESSAGE)
    @Size(max = PostConstants.MAX_COMMENT_CONTENT_LENGTH, message = PostConstants.COMMENT_CONTENT_TOO_LONG_MESSAGE)
    private String content;

    public PostCommentUpdateRequest() {}

    public PostCommentUpdateRequest(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "PostCommentUpdateRequest{" +
                "content='" + content + '\'' +
                '}';
    }
}
