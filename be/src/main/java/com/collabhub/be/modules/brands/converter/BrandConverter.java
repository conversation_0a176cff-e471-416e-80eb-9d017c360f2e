package com.collabhub.be.modules.brands.converter;

import com.collabhub.be.modules.brands.dto.BrandCreateRequest;
import com.collabhub.be.modules.brands.dto.BrandResponse;
import com.collabhub.be.modules.brands.dto.BrandUpdateRequest;
import com.collabhub.be.modules.brands.dto.BrandContactResponse;
import org.jooq.generated.tables.pojos.Brand;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Converter class for mapping between Brand DTOs and jOOQ POJOs.
 * Handles conversion between different representations of brand data.
 */
@Component
public class BrandConverter {

    /**
     * Converts a create request DTO to a jOOQ POJO for database insertion.
     *
     * @param request the create request DTO
     * @param accountId the account ID for multi-tenancy
     * @return the jOOQ POJO ready for database insertion
     */
    public Brand toBrand(BrandCreateRequest request, Long accountId) {
        if (request == null) {
            return null;
        }

        Brand brand = new Brand();
        brand.setAccountId(accountId);
        brand.setName(request.getName());
        brand.setCompanyName(request.getCompanyName());
        brand.setAddressStreet(request.getAddressStreet());
        brand.setAddressCity(request.getAddressCity());
        brand.setAddressPostalCode(request.getAddressPostalCode());
        brand.setAddressCountry(request.getAddressCountry());
        brand.setVatNumber(request.getVatNumber());
        brand.setRegistrationNumber(request.getRegistrationNumber());
        brand.setPhone(request.getPhone());
        brand.setEmail(request.getEmail());
        brand.setWebsite(request.getWebsite());
        brand.setCreatedAt(LocalDateTime.now());
        brand.setUpdatedAt(LocalDateTime.now());

        return brand;
    }

    /**
     * Updates an existing jOOQ POJO with data from an update request DTO.
     *
     * @param brand the existing jOOQ POJO to update
     * @param request the update request DTO
     * @return the updated jOOQ POJO
     */
    public Brand updateBrand(Brand brand, BrandUpdateRequest request) {
        if (brand == null || request == null) {
            return brand;
        }

        brand.setName(request.getName());
        brand.setCompanyName(request.getCompanyName());
        brand.setAddressStreet(request.getAddressStreet());
        brand.setAddressCity(request.getAddressCity());
        brand.setAddressPostalCode(request.getAddressPostalCode());
        brand.setAddressCountry(request.getAddressCountry());
        brand.setVatNumber(request.getVatNumber());
        brand.setRegistrationNumber(request.getRegistrationNumber());
        brand.setPhone(request.getPhone());
        brand.setEmail(request.getEmail());
        brand.setWebsite(request.getWebsite());
        brand.setUpdatedAt(LocalDateTime.now());

        return brand;
    }

    /**
     * Converts a jOOQ POJO to a response DTO for API responses.
     *
     * @param brand the jOOQ POJO
     * @param contacts the list of brand contacts
     * @return the response DTO
     */
    public BrandResponse toResponse(Brand brand, List<BrandContactResponse> contacts) {
        if (brand == null) {
            return null;
        }

        return new BrandResponse(
                brand.getId(),
                brand.getAccountId(),
                brand.getName(),
                brand.getCompanyName(),
                brand.getAddressStreet(),
                brand.getAddressCity(),
                brand.getAddressPostalCode(),
                brand.getAddressCountry(),
                brand.getVatNumber(),
                brand.getRegistrationNumber(),
                brand.getPhone(),
                brand.getEmail(),
                brand.getWebsite(),
                brand.getCreatedAt(),
                brand.getUpdatedAt(),
                contacts != null ? contacts : List.of()
        );
    }
}
