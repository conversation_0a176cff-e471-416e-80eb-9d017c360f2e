package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Service for handling notification translations and internationalization.
 * Provides localized notification titles, messages, and email templates.
 * 
 * This service is designed to be production-ready with proper error handling,
 * fallbacks, and extensibility for future languages.
 */
@Service
public class NotificationTranslationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationTranslationService.class);

    // Default locale for fallback
    private static final Locale DEFAULT_LOCALE = Locale.ENGLISH;

    // Translation file path pattern
    private static final String TRANSLATION_PATH_PATTERN = "classpath:notifications/translations/%s.json";

    // Translation storage - loaded from JSON files
    private final Map<String, JsonNode> translations = new HashMap<>();

    private final ResourceLoader resourceLoader;
    private final ObjectMapper objectMapper;

    public NotificationTranslationService(ResourceLoader resourceLoader, ObjectMapper objectMapper) {
        this.resourceLoader = resourceLoader;
        this.objectMapper = objectMapper;
    }

    @PostConstruct
    public void initializeTranslations() {
        loadTranslationsFromFiles();
    }
    
    /**
     * Gets localized notification title.
     */
    public String getNotificationTitle(NotificationType type, Locale locale) {
        String path = "notification.title." + type.name().toLowerCase();
        return getTranslation(path, locale, getDefaultTitle(type));
    }

    /**
     * Gets localized notification message with parameter substitution.
     */
    public String getNotificationMessage(NotificationType type, Locale locale, Map<String, Object> parameters) {
        String path = "notification.message." + type.name().toLowerCase();
        String template = getTranslation(path, locale, getDefaultMessage(type));
        return substituteParameters(template, parameters);
    }

    /**
     * Gets localized email subject.
     */
    public String getEmailSubject(NotificationType type, Locale locale, Map<String, Object> parameters) {
        String path = "notification.email.subject." + type.name().toLowerCase();
        String template = getTranslation(path, locale, getDefaultEmailSubject(type));
        return substituteParameters(template, parameters);
    }

    /**
     * Gets localized email body.
     */
    public String getEmailBody(NotificationType type, Locale locale, Map<String, Object> parameters) {
        String path = "notification.email.body." + type.name().toLowerCase();
        String template = getTranslation(path, locale, getDefaultEmailBody(type));
        return substituteParameters(template, parameters);
    }
    
    /**
     * Gets translation for a specific path and locale with fallback.
     * Path format: "notification.title.invite_to_hub"
     */
    private String getTranslation(String path, Locale locale, String defaultValue) {
        try {
            String localeKey = locale != null ? locale.getLanguage() : DEFAULT_LOCALE.getLanguage();

            // Try to get translation for requested locale
            JsonNode localeTranslations = translations.get(localeKey);
            String translation = getValueFromPath(localeTranslations, path);
            if (translation != null) {
                return translation;
            }

            // Fallback to default locale
            if (!DEFAULT_LOCALE.getLanguage().equals(localeKey)) {
                JsonNode defaultTranslations = translations.get(DEFAULT_LOCALE.getLanguage());
                translation = getValueFromPath(defaultTranslations, path);
                if (translation != null) {
                    return translation;
                }
            }

            // Final fallback to provided default
            return defaultValue;

        } catch (Exception e) {
            logger.warn("Failed to get translation for path '{}' and locale '{}': {}", path, locale, e.getMessage());
            return defaultValue;
        }
    }

    /**
     * Extracts value from JsonNode using dot-separated path.
     */
    private String getValueFromPath(JsonNode node, String path) {
        if (node == null || path == null) {
            return null;
        }

        String[] parts = path.split("\\.");
        JsonNode current = node;

        for (String part : parts) {
            current = current.get(part);
            if (current == null) {
                return null;
            }
        }

        return current.isTextual() ? current.asText() : null;
    }
    
    /**
     * Substitutes parameters in template strings.
     * Supports {paramName} syntax.
     */
    private String substituteParameters(String template, Map<String, Object> parameters) {
        if (template == null || parameters == null || parameters.isEmpty()) {
            return template;
        }
        
        String result = template;
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    /**
     * Loads translations from JSON files in the resources directory.
     * Supports multiple languages by loading files like en.json, es.json, etc.
     */
    private void loadTranslationsFromFiles() {
        // List of supported languages - can be made configurable
        String[] supportedLanguages = {"en", "es", "fr"};

        for (String language : supportedLanguages) {
            try {
                String resourcePath = String.format(TRANSLATION_PATH_PATTERN, language);
                Resource resource = resourceLoader.getResource(resourcePath);

                if (resource.exists()) {
                    try (InputStream inputStream = resource.getInputStream()) {
                        JsonNode translationNode = objectMapper.readTree(inputStream);
                        translations.put(language, translationNode);
                        logger.info("Loaded translations for language: {}", language);
                    }
                } else {
                    logger.warn("Translation file not found for language: {} at path: {}", language, resourcePath);
                }

            } catch (IOException e) {
                logger.error("Failed to load translations for language: {}: {}", language, e.getMessage(), e);
            }
        }

        // Ensure we have at least English translations
        if (!translations.containsKey(DEFAULT_LOCALE.getLanguage())) {
            logger.error("Critical: Default locale translations ({}) not loaded. Notification service may not work properly.",
                        DEFAULT_LOCALE.getLanguage());
        }

        logger.info("Initialized notification translations for {} locales: {}",
                   translations.size(), translations.keySet());
    }
    
    // Default fallback methods
    private String getDefaultTitle(NotificationType type) {
        return "Notification: " + type.name();
    }
    
    private String getDefaultMessage(NotificationType type) {
        return "You have a new " + type.name().toLowerCase().replace("_", " ") + " notification.";
    }
    
    private String getDefaultEmailSubject(NotificationType type) {
        return "Collaboration Hub: " + type.name().replace("_", " ");
    }
    
    private String getDefaultEmailBody(NotificationType type) {
        return "You have received a notification from Collaboration Hub.\n\nBest regards,\nCollaboration Hub Team";
    }
}
