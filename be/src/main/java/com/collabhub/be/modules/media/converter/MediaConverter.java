package com.collabhub.be.modules.media.converter;

import com.collabhub.be.modules.media.dto.MediaDto;
import com.collabhub.be.modules.media.dto.MediaListDto;
import com.collabhub.be.modules.posts.dto.MediaItem;
import com.collabhub.be.service.s3.S3StorageService;
import org.jooq.generated.tables.pojos.Media;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Converter for Media entity and DTOs.
 * Handles conversion between jOOQ POJOs and DTOs with URL generation.
 */
@Component
public class MediaConverter {

    private static final Logger logger = LoggerFactory.getLogger(MediaConverter.class);

    private final S3StorageService s3StorageService;

    public MediaConverter(S3StorageService s3StorageService) {
        this.s3StorageService = s3StorageService;
    }

    /**
     * Converts a Media entity to a detailed DTO.
     */
    public MediaDto toDto(Media media) {
        if (media == null) {
            return null;
        }

        String url = generateS3Url(media.getS3Bucket(), media.getS3Key(), media.getAccountId());
        String type = determineMediaType(media.getMimeType());

        return new MediaDto(
                media.getId(),
                media.getAccountId(),
                media.getOriginalFilename(),
                media.getFileSizeBytes(),
                media.getFileExtension(),
                media.getMimeType(),
                media.getS3Key(),
                media.getS3Bucket(),
                url,
                type,
                media.getCreatedAt(),
                media.getUpdatedAt(),
                media.getDeletedAt()
        );
    }

    /**
     * Converts a Media entity to a lightweight list DTO.
     */
    public MediaListDto toListDto(Media media) {
        if (media == null) {
            return null;
        }

        String url = generateS3Url(media.getS3Bucket(), media.getS3Key(), media.getAccountId());
        String type = determineMediaType(media.getMimeType());

        return new MediaListDto(
                media.getId(),
                media.getOriginalFilename(),
                media.getFileSizeBytes(),
                media.getMimeType(),
                url,
                type,
                media.getCreatedAt()
        );
    }

    /**
     * Converts a Media entity to a MediaItem for API compatibility.
     * This maintains compatibility with existing frontend code.
     */
    public MediaItem toMediaItem(Media media) {
        if (media == null) {
            return null;
        }

        String url = generateS3Url(media.getS3Bucket(), media.getS3Key(), media.getAccountId());
        String type = determineMediaType(media.getMimeType());

        return new MediaItem(
                url,
                type,
                media.getFileSizeBytes(),
                media.getMimeType()
        );
    }

    /**
     * Creates a new Media entity from file upload information.
     */
    public Media createMediaFromUpload(String originalFilename, Long fileSizeBytes, 
                                     String mimeType, String s3Key, String s3Bucket, 
                                     Long accountId) {
        Media media = new Media();
        media.setAccountId(accountId);
        media.setOriginalFilename(originalFilename);
        media.setFileSizeBytes(fileSizeBytes);
        media.setFileExtension(extractFileExtension(originalFilename));
        media.setMimeType(mimeType);
        media.setS3Key(s3Key);
        media.setS3Bucket(s3Bucket);
        media.setCreatedAt(LocalDateTime.now());
        media.setUpdatedAt(LocalDateTime.now());
        
        return media;
    }

    /**
     * Converts a list of Media entities to MediaItems for API compatibility.
     */
    public List<MediaItem> toMediaItems(List<Media> mediaList) {
        if (mediaList == null) {
            return List.of();
        }

        return mediaList.stream()
                .map(this::toMediaItem)
                .toList();
    }

    /**
     * Converts a list of Media entities to list DTOs.
     */
    public List<MediaListDto> toListDtos(List<Media> mediaList) {
        if (mediaList == null) {
            return List.of();
        }

        return mediaList.stream()
                .map(this::toListDto)
                .toList();
    }

    /**
     * Generates S3 URL for a media file.
     * For security, this generates presigned download URLs with account validation.
     */
    private String generateS3Url(String bucket, String key, Long accountId) {
        try {
            // Generate the base file URL first
            String fileUrl = s3StorageService.generateFileUrl(bucket, key);

            // Generate presigned download URL with account validation
            return s3StorageService.generatePresignedDownloadUrl(fileUrl, accountId);
        } catch (Exception e) {
            logger.warn("Failed to generate presigned S3 URL for bucket: {}, key: {}, account: {}", bucket, key, accountId, e);
            // Fallback to direct URL for backward compatibility, but this should be avoided in production
            return s3StorageService.generateFileUrl(bucket, key);
        }
    }

    /**
     * Generates S3 URL for a media file without account context (fallback).
     * @deprecated Use generateS3Url(bucket, key, accountId) for secure access
     */
    @Deprecated
    private String generateS3Url(String bucket, String key) {
        try {
            return s3StorageService.generateFileUrl(bucket, key);
        } catch (Exception e) {
            logger.warn("Failed to generate S3 URL for bucket: {}, key: {}", bucket, key, e);
            return "";
        }
    }

    /**
     * Determines media type (image/video) from MIME type.
     */
    private String determineMediaType(String mimeType) {
        if (mimeType == null) {
            return "image"; // Default fallback
        }

        String lowerMimeType = mimeType.toLowerCase();
        if (lowerMimeType.startsWith("video/")) {
            return "video";
        } else if (lowerMimeType.startsWith("image/")) {
            return "image";
        }

        // Fallback based on common MIME types
        return "image";
    }

    /**
     * Extracts file extension from filename.
     */
    private String extractFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1).toLowerCase();
        }

        return "";
    }
}
