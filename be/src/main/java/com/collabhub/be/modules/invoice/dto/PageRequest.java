package com.collabhub.be.modules.invoice.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * Custom pagination request DTO for jOOQ-based pagination.
 * Replaces Spring Data's Pageable interface to maintain consistency with jOOQ architecture.
 */
public class PageRequest {

    @Min(value = 0, message = "Page number must be non-negative")
    private int page = 0;

    @Min(value = 1, message = "Page size must be at least 1")
    @Max(value = 100, message = "Page size cannot exceed 100")
    private int size = 20;

    public PageRequest() {
    }

    public PageRequest(int page, int size) {
        this.page = page;
        this.size = size;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    /**
     * Calculates the offset for database queries.
     * @return the offset (page * size)
     */
    public int getOffset() {
        return page * size;
    }

    /**
     * Creates a PageRequest with default size.
     * @param page the page number
     * @return new PageRequest instance
     */
    public static PageRequest of(int page) {
        return new PageRequest(page, 20);
    }

    /**
     * Creates a PageRequest with specified page and size.
     * @param page the page number
     * @param size the page size
     * @return new PageRequest instance
     */
    public static PageRequest of(int page, int size) {
        return new PageRequest(page, size);
    }

    @Override
    public String toString() {
        return "PageRequest{" +
                "page=" + page +
                ", size=" + size +
                '}';
    }
}
