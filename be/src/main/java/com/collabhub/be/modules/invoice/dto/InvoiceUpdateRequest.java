package com.collabhub.be.modules.invoice.dto;

import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;
import java.util.List;

/**
 * Request DTO for updating an existing invoice.
 * All fields are optional - only provided fields will be updated.
 * Note: Some fields like invoice_number, issue_date should only be updated with proper validation.
 */
public class InvoiceUpdateRequest {

    @JsonProperty("issuer_id")
    private Long issuerId;

    @JsonProperty("recipient_id")
    private Long recipientId;

    @JsonProperty("bank_details_id")
    private Long bankDetailsId;

    @Size(max = InvoiceConstants.INVOICE_NUMBER_MAX_LENGTH, message = InvoiceConstants.INVOICE_NUMBER_SIZE_MESSAGE)
    @JsonProperty("invoice_number")
    private String invoiceNumber;

    @Size(min = InvoiceConstants.CURRENCY_CODE_LENGTH, max = InvoiceConstants.CURRENCY_CODE_LENGTH, message = InvoiceConstants.CURRENCY_SIZE_MESSAGE)
    private String currency;

    @JsonProperty("issue_date")
    private LocalDate issueDate;

    @JsonProperty("due_date")
    private LocalDate dueDate;

    @Size(max = InvoiceConstants.NOTES_MAX_LENGTH, message = InvoiceConstants.NOTES_SIZE_MESSAGE)
    private String notes;

    @Size(max = InvoiceConstants.TEMPLATE_NAME_MAX_LENGTH, message = InvoiceConstants.TEMPLATE_SIZE_MESSAGE)
    private String template;

    @Valid
    private List<InvoiceItemRequest> items;

    @Valid
    private List<InvoiceRecipientRequest> recipients;

    public InvoiceUpdateRequest() {
    }

    public InvoiceUpdateRequest(Long issuerId, Long recipientId, Long bankDetailsId, String invoiceNumber,
                              String currency, LocalDate issueDate, LocalDate dueDate,
                              String notes, String template, List<InvoiceItemRequest> items,
                              List<InvoiceRecipientRequest> recipients) {
        this.issuerId = issuerId;
        this.recipientId = recipientId;
        this.bankDetailsId = bankDetailsId;
        this.invoiceNumber = invoiceNumber;
        this.currency = currency;
        this.issueDate = issueDate;
        this.dueDate = dueDate;
        this.notes = notes;
        this.template = template;
        this.items = items;
        this.recipients = recipients;
    }

    public Long getIssuerId() {
        return issuerId;
    }

    public void setIssuerId(Long issuerId) {
        this.issuerId = issuerId;
    }

    public Long getRecipientId() {
        return recipientId;
    }

    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }

    public Long getBankDetailsId() {
        return bankDetailsId;
    }

    public void setBankDetailsId(Long bankDetailsId) {
        this.bankDetailsId = bankDetailsId;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public LocalDate getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDate issueDate) {
        this.issueDate = issueDate;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public List<InvoiceItemRequest> getItems() {
        return items;
    }

    public void setItems(List<InvoiceItemRequest> items) {
        this.items = items;
    }

    public List<InvoiceRecipientRequest> getRecipients() {
        return recipients;
    }

    public void setRecipients(List<InvoiceRecipientRequest> recipients) {
        this.recipients = recipients;
    }

    @Override
    public String toString() {
        return "InvoiceUpdateRequest{" +
                "issuerId=" + issuerId +
                ", recipientId=" + recipientId +
                ", bankDetailsId=" + bankDetailsId +
                ", invoiceNumber='" + invoiceNumber + '\'' +
                ", currency='" + currency + '\'' +
                ", issueDate=" + issueDate +
                ", dueDate=" + dueDate +
                ", notes='" + notes + '\'' +
                ", template='" + template + '\'' +
                ", items=" + items +
                ", recipients=" + recipients +
                '}';
    }
}
