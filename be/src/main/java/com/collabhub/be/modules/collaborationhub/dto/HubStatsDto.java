package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotNull;

/**
 * DTO for collaboration hub statistics.
 * Contains aggregated data about hub activity and content.
 */
public class HubStatsDto {

    @NotNull
    private Long totalPosts;

    @NotNull
    private Long pendingPosts;

    @NotNull
    private Long approvedPosts;

    @NotNull
    private Long activeChats;

    public HubStatsDto() {}

    public HubStatsDto(Long totalPosts, Long pendingPosts, Long approvedPosts, Long activeChats) {
        this.totalPosts = totalPosts;
        this.pendingPosts = pendingPosts;
        this.approvedPosts = approvedPosts;
        this.activeChats = activeChats;
    }

    public Long getTotalPosts() {
        return totalPosts;
    }

    public void setTotalPosts(Long totalPosts) {
        this.totalPosts = totalPosts;
    }

    public Long getPendingPosts() {
        return pendingPosts;
    }

    public void setPendingPosts(Long pendingPosts) {
        this.pendingPosts = pendingPosts;
    }

    public Long getApprovedPosts() {
        return approvedPosts;
    }

    public void setApprovedPosts(Long approvedPosts) {
        this.approvedPosts = approvedPosts;
    }

    public Long getActiveChats() {
        return activeChats;
    }

    public void setActiveChats(Long activeChats) {
        this.activeChats = activeChats;
    }

    @Override
    public String toString() {
        return "HubStatsDto{" +
                "totalPosts=" + totalPosts +
                ", pendingPosts=" + pendingPosts +
                ", approvedPosts=" + approvedPosts +
                ", activeChats=" + activeChats +
                '}';
    }
}
