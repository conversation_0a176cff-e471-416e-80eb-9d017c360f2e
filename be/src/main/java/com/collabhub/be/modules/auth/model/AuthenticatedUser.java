package com.collabhub.be.modules.auth.model;

import java.util.Objects;

/**
 * Represents an authenticated user extracted from JWT access token claims.
 * This POJO is used throughout the application to access current user context.
 */
public class AuthenticatedUser {
    
    private final Long id;
    private final String email;
    private final String displayName;
    private final Role role;
    private final Long accountId;
    private final boolean internal;
    
    public AuthenticatedUser(Long id, String email, String displayName, 
                           Role role, Long accountId, boolean internal) {
        this.id = Objects.requireNonNull(id, "User ID cannot be null");
        this.email = Objects.requireNonNull(email, "Email cannot be null");
        this.displayName = Objects.requireNonNull(displayName, "Display name cannot be null");
        this.role = Objects.requireNonNull(role, "Role cannot be null");
        this.accountId = Objects.requireNonNull(accountId, "Account ID cannot be null");
        this.internal = internal;
    }
    
    /**
     * Returns the user's unique identifier.
     */
    public Long getId() {
        return id;
    }
    
    /**
     * Returns the user's email address.
     */
    public String getEmail() {
        return email;
    }
    
    /**
     * Returns the user's display name.
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Returns the user's role within their account.
     */
    public Role getRole() {
        return role;
    }
    
    /**
     * Returns the account ID for multi-tenancy.
     */
    public Long getAccountId() {
        return accountId;
    }
    
    /**
     * Returns true if this is an internal user, false for external users.
     */
    public boolean isInternal() {
        return internal;
    }
    
    /**
     * Checks if the user has admin role.
     */
    public boolean isAdmin() {
        return Role.ADMIN.equals(role);
    }
    
    /**
     * Returns a string representation suitable for logging (excludes sensitive data).
     */
    public String toLogString() {
        return String.format("AuthenticatedUser{id=%d, email='%s', role=%s, accountId=%d, internal=%s}", 
                           id, email, role, accountId, internal);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AuthenticatedUser that = (AuthenticatedUser) o;
        return internal == that.internal &&
               Objects.equals(id, that.id) &&
               Objects.equals(email, that.email) &&
               Objects.equals(displayName, that.displayName) &&
               role == that.role &&
               Objects.equals(accountId, that.accountId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, email, displayName, role, accountId, internal);
    }
    
    @Override
    public String toString() {
        return "AuthenticatedUser{" +
               "id=" + id +
               ", email='" + email + '\'' +
               ", displayName='" + displayName + '\'' +
               ", role=" + role +
               ", accountId=" + accountId +
               ", internal=" + internal +
               '}';
    }
}
