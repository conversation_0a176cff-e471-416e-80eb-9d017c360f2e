package com.collabhub.be.modules.collaborationhub.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Response DTO for participant invite URL generation.
 * Contains the magic link URL for manual sharing without sending an email.
 */
public class HubParticipantInviteUrlResponse {

    @JsonProperty("invite_url")
    @NotNull
    private String inviteUrl;

    @JsonProperty("participant_id")
    @NotNull
    private Long participantId;

    @JsonProperty("participant_email")
    @NotNull
    private String participantEmail;

    @JsonProperty("participant_name")
    private String participantName;

    @JsonProperty("expires_at")
    @NotNull
    private LocalDateTime expiresAt;

    public HubParticipantInviteUrlResponse() {}

    public HubParticipantInviteUrlResponse(String inviteUrl, Long participantId, String participantEmail,
                                         String participantName, LocalDateTime expiresAt) {
        this.inviteUrl = inviteUrl;
        this.participantId = participantId;
        this.participantEmail = participantEmail;
        this.participantName = participantName;
        this.expiresAt = expiresAt;
    }

    public String getInviteUrl() {
        return inviteUrl;
    }

    public void setInviteUrl(String inviteUrl) {
        this.inviteUrl = inviteUrl;
    }

    public Long getParticipantId() {
        return participantId;
    }

    public void setParticipantId(Long participantId) {
        this.participantId = participantId;
    }

    public String getParticipantEmail() {
        return participantEmail;
    }

    public void setParticipantEmail(String participantEmail) {
        this.participantEmail = participantEmail;
    }

    public String getParticipantName() {
        return participantName;
    }

    public void setParticipantName(String participantName) {
        this.participantName = participantName;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    @Override
    public String toString() {
        return "HubParticipantInviteUrlResponse{" +
                "inviteUrl='[PROTECTED]'" +
                ", participantId=" + participantId +
                ", participantEmail='" + participantEmail + '\'' +
                ", participantName='" + participantName + '\'' +
                ", expiresAt=" + expiresAt +
                '}';
    }
}
