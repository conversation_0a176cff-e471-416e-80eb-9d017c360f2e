package com.collabhub.be.modules.posts.dto;

import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.modules.invoice.dto.PageRequest;

import java.util.List;

/**
 * Response DTO for listing posts with pagination and filtering.
 * Extends the generic PageResponse to include post-specific filters.
 */
public class PostListResponse extends PageResponse<PostListItemResponse> {

    public PostListResponse(List<PostListItemResponse> content, PageRequest pageRequest,
                           long totalElements) {
        super(content, pageRequest, totalElements);
    }

    @Override
    public String toString() {
        return "PostListResponse{" +
                ", content=" + getContent() +
                ", page=" + getPage() +
                ", size=" + getSize() +
                ", totalElements=" + getTotalElements() +
                '}';
    }
}
