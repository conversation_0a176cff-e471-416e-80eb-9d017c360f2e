package com.collabhub.be.modules.brands.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * Response DTO for brand contact operations.
 * Contains all brand contact information including metadata.
 */
public class BrandContactResponse {

    @NotNull
    private Long id;

    @NotNull
    @JsonProperty("brand_id")
    private Long brandId;

    @NotNull
    @JsonProperty("account_id")
    private Long accountId;

    @NotNull
    private String name;

    @NotNull
    private String email;

    private String notes;

    @NotNull
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @NotNull
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    public BrandContactResponse() {
    }

    public BrandContactResponse(Long id, Long brandId, Long accountId, String name, String email,
                              String notes, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.brandId = brandId;
        this.accountId = accountId;
        this.name = name;
        this.email = email;
        this.notes = notes;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "BrandContactResponse{" +
                "id=" + id +
                ", brandId=" + brandId +
                ", accountId=" + accountId +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", notes='" + notes + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
