package com.collabhub.be.modules.collaborationhub.dto;

import org.jooq.generated.enums.HubParticipantRole;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for collaboration hub details.
 * Contains complete hub information including participants and statistics.
 */
public class CollaborationHubResponse {

    @NotNull
    private Long id;

    @NotNull
    private String name;

    @NotNull
    private Long brandId;

    @NotNull
    private String brandName;

    private String description;

    @NotNull
    private HubParticipantRole myRole;

    @NotNull
    private LocalDateTime createdAt;

    public CollaborationHubResponse() {}

    public CollaborationHubResponse(Long id, String name, Long brandId, String brandName, 
                                  String description, HubParticipantRole myRole, LocalDateTime createdAt) {
        this.id = id;
        this.name = name;
        this.brandId = brandId;
        this.brandName = brandName;
        this.description = description;
        this.myRole = myRole;
        this.createdAt = createdAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public HubParticipantRole getMyRole() {
        return myRole;
    }

    public void setMyRole(HubParticipantRole myRole) {
        this.myRole = myRole;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "CollaborationHubResponse{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", brandId=" + brandId +
                ", brandName='" + brandName + '\'' +
                ", description='" + description + '\'' +
                ", myRole=" + myRole +
                ", createdAt=" + createdAt +
                '}';
    }
}
