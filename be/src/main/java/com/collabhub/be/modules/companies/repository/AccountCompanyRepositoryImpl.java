package com.collabhub.be.modules.companies.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.AccountCompanyDao;
import org.jooq.generated.tables.pojos.AccountCompany;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.generated.tables.AccountCompany.ACCOUNT_COMPANY;

/**
 * Repository for Account Company entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy and soft deletion support.
 */
@Repository
public class AccountCompanyRepositoryImpl extends AccountCompanyDao {

    private final DSLContext dsl;

    public AccountCompanyRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all active (not soft deleted) account companies for a specific account.
     *
     * @param accountId the account ID for multi-tenancy
     * @return list of active account companies
     */
    public List<AccountCompany> findAllByAccountId(Long accountId) {
        return dsl.select()
                .from(ACCOUNT_COMPANY)
                .where(ACCOUNT_COMPANY.ACCOUNT_ID.eq(accountId))
                .and(ACCOUNT_COMPANY.DELETED_AT.isNull())
                .orderBy(ACCOUNT_COMPANY.COMPANY_NAME.asc())
                .fetchInto(AccountCompany.class);
    }

    /**
     * Finds an active account company by ID and account ID.
     *
     * @param id the company ID
     * @param accountId the account ID for multi-tenancy
     * @return optional containing the account company if found and active
     */
    public Optional<AccountCompany> findByIdAndAccountId(Long id, Long accountId) {
        return dsl.select()
                .from(ACCOUNT_COMPANY)
                .where(ACCOUNT_COMPANY.ID.eq(id))
                .and(ACCOUNT_COMPANY.ACCOUNT_ID.eq(accountId))
                .and(ACCOUNT_COMPANY.DELETED_AT.isNull())
                .fetchOptionalInto(AccountCompany.class);
    }


    /**
     * Soft deletes an account company by setting deleted_at timestamp.
     *
     * @param id the company ID
     * @param accountId the account ID for multi-tenancy
     * @return true if the company was soft deleted, false if not found
     */
    public boolean softDelete(Long id, Long accountId) {
        int rowsAffected = dsl.update(ACCOUNT_COMPANY)
                .set(ACCOUNT_COMPANY.DELETED_AT, LocalDateTime.now())
                .where(ACCOUNT_COMPANY.ID.eq(id))
                .and(ACCOUNT_COMPANY.ACCOUNT_ID.eq(accountId))
                .and(ACCOUNT_COMPANY.DELETED_AT.isNull())
                .execute();

        return rowsAffected > 0;
    }

    /**
     * Checks if an account company exists by ID and account ID.
     *
     * @param id the company ID
     * @param accountId the account ID for multi-tenancy
     * @return true if the company exists and is active, false otherwise
     */
    public boolean existsByIdAndAccountId(Long id, Long accountId) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(ACCOUNT_COMPANY)
                        .where(ACCOUNT_COMPANY.ID.eq(id))
                        .and(ACCOUNT_COMPANY.ACCOUNT_ID.eq(accountId))
                        .and(ACCOUNT_COMPANY.DELETED_AT.isNull())
        );
    }
}
