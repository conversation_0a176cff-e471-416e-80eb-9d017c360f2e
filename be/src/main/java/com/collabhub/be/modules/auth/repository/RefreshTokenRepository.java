package com.collabhub.be.modules.auth.repository;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.generated.tables.daos.RefreshTokenDao;
import org.jooq.generated.tables.pojos.RefreshToken;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.generated.tables.RefreshToken.REFRESH_TOKEN;

/**
 * Repository for RefreshToken entity using jOOQ for database operations.
 * Handles opaque refresh token storage and rotation.
 */
@Repository
public class RefreshTokenRepository extends RefreshTokenDao {

    private final DSLContext dsl;

    public RefreshTokenRepository(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds a refresh token by token string.
     *
     * @param token the refresh token string
     * @return Optional containing the refresh token if found and valid
     */
    public Optional<RefreshToken> findByToken(String token) {
        return dsl.select()
                .from(REFRESH_TOKEN)
                .where(REFRESH_TOKEN.TOKEN.eq(token))
                .and(REFRESH_TOKEN.REVOKED.eq(false))
                .and(REFRESH_TOKEN.EXPIRES_AT.gt(LocalDateTime.now()))
                .fetchOptionalInto(RefreshToken.class);
    }

    /**
     * Revokes a refresh token by ID.
     *
     * @param tokenId the token ID to revoke
     * @param replacedBy the ID of the replacement token (optional)
     */
    public void revokeToken(Long tokenId, Long replacedBy) {
        dsl.update(REFRESH_TOKEN)
                .set(REFRESH_TOKEN.REVOKED, true)
                .set(REFRESH_TOKEN.REPLACED_BY, replacedBy)
                .where(REFRESH_TOKEN.ID.eq(tokenId))
                .execute();
    }

    /**
     * Revokes all refresh tokens for a user.
     *
     * @param userId the user ID
     */
    public void revokeAllTokensForUser(Long userId) {
        dsl.update(REFRESH_TOKEN)
                .set(REFRESH_TOKEN.REVOKED, true)
                .where(REFRESH_TOKEN.USER_ID.eq(userId))
                .and(REFRESH_TOKEN.REVOKED.eq(false))
                .execute();
    }
}
