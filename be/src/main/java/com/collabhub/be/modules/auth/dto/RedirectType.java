package com.collabhub.be.modules.auth.dto;

/**
 * Enum representing different types of magic link redirects.
 * Each type corresponds to a specific authentication context and target destination.
 */
public enum RedirectType {
    /**
     * Redirect to a specific collaboration hub.
     * Used when external participants are invited to join a hub.
     */
    HUB_ACCESS("HUB_ACCESS"),
    
    /**
     * Redirect to a specific invoice for accountant access.
     * Used when external accountants are invited to access invoice data.
     */
    INVOICE_ACCESS("INVOICE_ACCESS"),
    
    /**
     * General access redirect (default dashboard or home page).
     * Used for general authentication without specific context.
     */
    GENERAL_ACCESS("GENERAL_ACCESS");
    
    private final String value;
    
    RedirectType(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    /**
     * Converts string value to RedirectType enum.
     */
    public static RedirectType fromValue(String value) {
        for (RedirectType type : RedirectType.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown redirect type: " + value);
    }
}
