package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.notifications.dto.ExternalUserRecipient;
import com.collabhub.be.modules.notifications.dto.InternalUserRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.exception.NotificationBatchingException;
import com.collabhub.be.modules.notifications.util.NotificationRecipientUtils;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Production-grade notification dispatcher service that coordinates notification delivery.
 *
 * <p>This service provides the main entry point for dispatching notifications throughout
 * the application, handling routing between in-app and email channels based on user types,
 * preferences, and notification urgency levels. It uses strongly-typed data structures
 * and comprehensive error handling for production reliability.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Strongly-typed metadata using {@link NotificationMetadata} for type safety</li>
 *   <li>Urgency-based routing with {@link NotificationUrgency} for batching control</li>
 *   <li>Automatic user type detection and channel routing</li>
 *   <li>Comprehensive logging and error handling</li>
 *   <li>Production-grade validation and null safety</li>
 * </ul>
 *
 * <h3>Usage Examples:</h3>
 * <pre>
 * // Dispatch with strongly-typed metadata
 * NotificationMetadata metadata = NotificationMetadata.builder()
 *     .actorName("John Doe")
 *     .targetTitle("Summer Campaign")
 *     .actionContext("mentioned you in a comment")
 *     .deepLinkPath("/app/posts/123#comment-456")
 *     .build();
 *
 * dispatcher.dispatchNotification(
 *     NotificationType.COMMENT_MENTION,
 *     "You were mentioned",
 *     "John Doe mentioned you in a comment",
 *     List.of(userId),
 *     entityReferences,
 *     metadata,
 *     NotificationUrgency.HIGH
 * );
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class NotificationDispatcherService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationDispatcherService.class);

    // Constants
    private static final int MAX_TITLE_LENGTH = 255;
    private static final int MAX_MESSAGE_LENGTH = 1000;
    private static final String MIXED_DISPATCH_START_MESSAGE = "Dispatching mixed notification: type={}, internal={}, external={}";
    private static final String MIXED_DISPATCH_COMPLETE_MESSAGE = "Mixed notification dispatch completed: type={}, urgency={}, internal={}, external={}";

    private final NotificationService notificationService;
    private final NotificationStorageService notificationStorageService;
    private final EmailNotificationService emailNotificationService;
    private final NotificationBatchingService notificationBatchingService;
    private final UserRepository userRepository;
    private final NotificationPreferenceService notificationPreferenceService;
    private final ExternalEmailNotificationService externalEmailNotificationService;

    public NotificationDispatcherService(NotificationService notificationService,
                                       NotificationStorageService notificationStorageService,
                                       EmailNotificationService emailNotificationService,
                                       NotificationBatchingService notificationBatchingService,
                                       UserRepository userRepository,
                                       NotificationPreferenceService notificationPreferenceService,
                                       ExternalEmailNotificationService externalEmailNotificationService) {
        this.notificationService = notificationService;
        this.notificationStorageService = notificationStorageService;
        this.emailNotificationService = emailNotificationService;
        this.notificationBatchingService = notificationBatchingService;
        this.userRepository = userRepository;
        this.notificationPreferenceService = notificationPreferenceService;
        this.externalEmailNotificationService = externalEmailNotificationService;
    }



    /**
     * Dispatches a notification to multiple users through appropriate channels.
     *
     * <p><strong>DEPRECATED:</strong> Use {@link #dispatchToHubParticipants} or {@link #dispatchMixedNotification} instead.
     * This method only works with internal users and will be removed in a future version.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipientUserIds the user IDs to notify (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     * @deprecated Use {@link #dispatchToHubParticipants} or {@link #dispatchMixedNotification} instead
     */
    @Deprecated(since = "1.0.0", forRemoval = true)
    @Transactional
    public void dispatchNotification(@NotNull @Valid NotificationType type,
                                   @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                   @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                   @NotEmpty List<Long> recipientUserIds,
                                   NotificationStorageService.EntityReferences entityReferences,
                                   @Valid NotificationMetadata metadata) {

        NotificationUrgency urgency = determineDefaultUrgency(type);
        dispatchNotification(type, title, message, recipientUserIds, entityReferences, metadata, urgency);
    }

    /**
     * Dispatches a notification to multiple users with full control over urgency and metadata.
     *
     * <p><strong>DEPRECATED:</strong> Use {@link #dispatchToHubParticipants} or {@link #dispatchMixedNotification} instead.
     * This method only works with internal users and will be removed in a future version.</p>
     *
     * @param type the notification type
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipientUserIds the user IDs to notify (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level affecting batching and delivery timing
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     * @deprecated Use {@link #dispatchToHubParticipants} or {@link #dispatchMixedNotification} instead
     */
    @Deprecated(since = "1.0.0", forRemoval = true)
    @Transactional
    public void dispatchNotification(@NotNull @Valid NotificationType type,
                                   @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                   @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                   @NotEmpty List<Long> recipientUserIds,
                                   NotificationStorageService.EntityReferences entityReferences,
                                   @Valid NotificationMetadata metadata,
                                   @NotNull @Valid NotificationUrgency urgency) {

        // Convert user IDs to internal recipients for backward compatibility
        List<NotificationRecipient> recipients = convertUserIdsToInternalRecipients(recipientUserIds);
        dispatchMixedNotification(type, title, message, recipients, entityReferences, metadata, urgency);
    }

    /**
     * Convenience method for simple notifications without entity references or metadata.
     *
     * <p><strong>DEPRECATED:</strong> Use {@link #dispatchToHubParticipants} or {@link #dispatchMixedNotification} instead.
     * This method only works with internal users and will be removed in a future version.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipientUserIds the user IDs to notify (must not be empty)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     * @deprecated Use {@link #dispatchToHubParticipants} or {@link #dispatchMixedNotification} instead
     */
    @Deprecated(since = "1.0.0", forRemoval = true)
    @Transactional
    public void dispatchSimpleNotification(@NotNull @Valid NotificationType type,
                                         @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                         @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                         @NotEmpty List<Long> recipientUserIds) {
        dispatchNotification(type, title, message, recipientUserIds, null, NotificationMetadata.empty());
    }

    // ========================================
    // HUB PARTICIPANT-BASED METHODS (PREFERRED)
    // ========================================

    /**
     * Dispatches notifications to hub participants (both internal and external users).
     *
     * <p>This is the preferred method for hub-based notifications, supporting both
     * internal users (with user_id) and external users (email-only). It automatically
     * converts hub participants to the appropriate recipient types.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param hubParticipants the hub participants to notify (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchToHubParticipants(@NotNull @Valid NotificationType type,
                                        @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                        @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                        @NotEmpty List<org.jooq.generated.tables.pojos.HubParticipant> hubParticipants,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        @Valid NotificationMetadata metadata) {

        NotificationUrgency urgency = determineDefaultUrgency(type);
        dispatchToHubParticipants(type, title, message, hubParticipants, entityReferences, metadata, urgency);
    }

    /**
     * Dispatches notifications to hub participants with full urgency control.
     *
     * <p>This method provides complete control over notification dispatch for hub
     * participants, including urgency levels and comprehensive validation.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param hubParticipants the hub participants to notify (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level (must not be null)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchToHubParticipants(@NotNull @Valid NotificationType type,
                                        @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                        @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                        @NotEmpty List<org.jooq.generated.tables.pojos.HubParticipant> hubParticipants,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        @Valid NotificationMetadata metadata,
                                        @NotNull @Valid NotificationUrgency urgency) {

        List<NotificationRecipient> recipients = convertHubParticipantsToRecipients(hubParticipants);
        dispatchMixedNotification(type, title, message, recipients, entityReferences, metadata, urgency);
    }

    /**
     * Dispatches notifications to user IDs (backward compatibility method).
     *
     * <p>This method converts user IDs to internal recipients and dispatches using the
     * unified system. Use {@link #dispatchToHubParticipants} for new code.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param userIds the user IDs to notify (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level (must not be null)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchToUserIds(@NotNull @Valid NotificationType type,
                                @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                @NotEmpty List<Long> userIds,
                                NotificationStorageService.EntityReferences entityReferences,
                                @Valid NotificationMetadata metadata,
                                @NotNull @Valid NotificationUrgency urgency) {

        List<NotificationRecipient> recipients = convertUserIdsToInternalRecipients(userIds);
        dispatchMixedNotification(type, title, message, recipients, entityReferences, metadata, urgency);
    }

    // ========================================
    // UNIFIED MIXED RECIPIENT METHODS
    // ========================================

    /**
     * Dispatches notifications to mixed recipients (internal and external users).
     *
     * <p>This is the primary method for the unified notification system, supporting both
     * internal users (with user_id) and external users (email-only). It provides full
     * feature parity including in-app notifications, email batching, and preferences.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipients the mixed recipient list (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchMixedNotification(@NotNull @Valid NotificationType type,
                                        @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                        @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                        @NotEmpty List<NotificationRecipient> recipients,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        @Valid NotificationMetadata metadata) {

        NotificationUrgency urgency = determineDefaultUrgency(type);
        dispatchMixedNotification(type, title, message, recipients, entityReferences, metadata, urgency);
    }

    /**
     * Dispatches notifications to mixed recipients with full urgency control.
     *
     * <p>This method provides complete control over notification dispatch for mixed
     * recipient types, including urgency levels and comprehensive validation.</p>
     *
     * @param type the notification type (must not be null)
     * @param title the notification title (max 255 characters)
     * @param message the notification message (max 1000 characters)
     * @param recipients the mixed recipient list (must not be empty)
     * @param entityReferences optional entity references for deep linking
     * @param metadata optional strongly-typed metadata providing additional context
     * @param urgency notification urgency level (must not be null)
     *
     * @throws IllegalArgumentException if required parameters are invalid
     * @throws NotificationBatchingException if dispatch processing fails
     */
    @Transactional
    public void dispatchMixedNotification(@NotNull @Valid NotificationType type,
                                        @NotBlank @Size(max = MAX_TITLE_LENGTH) String title,
                                        @NotBlank @Size(max = MAX_MESSAGE_LENGTH) String message,
                                        @NotEmpty List<NotificationRecipient> recipients,
                                        NotificationStorageService.EntityReferences entityReferences,
                                        @Valid NotificationMetadata metadata,
                                        @NotNull @Valid NotificationUrgency urgency) {

        validateMixedDispatchParameters(type, title, message, recipients, urgency);

        List<InternalUserRecipient> internalRecipients = NotificationRecipientUtils.filterInternalUsers(recipients);
        List<ExternalUserRecipient> externalRecipients = NotificationRecipientUtils.filterExternalUsers(recipients);

        logger.debug(MIXED_DISPATCH_START_MESSAGE, type, internalRecipients.size(), externalRecipients.size());

        processInternalUserNotifications(internalRecipients, type, title, message, entityReferences, metadata, urgency);
        processExternalUserNotifications(externalRecipients, type, title, message, entityReferences, metadata, urgency);

        logger.info(MIXED_DISPATCH_COMPLETE_MESSAGE, type, urgency, internalRecipients.size(), externalRecipients.size());
    }

    /**
     * Determines the default urgency level for a notification type.
     *
     * @param type the notification type
     * @return the default urgency level
     */
    private NotificationUrgency determineDefaultUrgency(@NotNull NotificationType type) {
        return NotificationUrgency.getDefaultForType(type);
    }



    // ========================================
    // MIXED RECIPIENT PROCESSING METHODS
    // ========================================

    /**
     * Validates mixed dispatch parameters for consistency and business rules.
     */
    private void validateMixedDispatchParameters(@NotNull NotificationType type,
                                               @NotBlank String title,
                                               @NotBlank String message,
                                               @NotEmpty List<NotificationRecipient> recipients,
                                               @NotNull NotificationUrgency urgency) {

        // Basic validation
        if (title.length() > MAX_TITLE_LENGTH) {
            throw new IllegalArgumentException("Title exceeds maximum length of " + MAX_TITLE_LENGTH + " characters");
        }

        if (message.length() > MAX_MESSAGE_LENGTH) {
            throw new IllegalArgumentException("Message exceeds maximum length of " + MAX_MESSAGE_LENGTH + " characters");
        }

        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipients list cannot be empty");
        }

        // Validate all recipients
        NotificationRecipientUtils.validateRecipients(recipients);
    }

    /**
     * Processes notifications for internal users directly.
     */
    private void processInternalUserNotifications(List<InternalUserRecipient> internalRecipients,
                                                NotificationType type, String title, String message,
                                                NotificationStorageService.EntityReferences entityReferences,
                                                NotificationMetadata metadata, NotificationUrgency urgency) {

        if (internalRecipients.isEmpty()) {
            return;
        }

        logger.debug("Processing notifications for {} internal users", internalRecipients.size());

        // Create delivery plan for internal users based on preferences
        List<Long> userIds = internalRecipients.stream()
                                              .map(InternalUserRecipient::getUserId)
                                              .collect(Collectors.toList());

        NotificationService.NotificationDeliveryPlan deliveryPlan = notificationService.processNotificationEvent(type, userIds);

        // Process in-app notifications
        if (deliveryPlan.hasInAppRecipients()) {
            notificationStorageService.createNotifications(type, title, message, deliveryPlan.getInAppRecipients(),
                                                          entityReferences, metadata);
            logger.info("Created {} in-app notifications for internal users", deliveryPlan.getInAppRecipients().size());
        }

        // Process email notifications
        if (deliveryPlan.hasEmailRecipients()) {
            logger.debug("Processing email notifications: type={}, urgency={}, recipients={}, shouldBypassBatching={}",
                        type, urgency, deliveryPlan.getEmailRecipients().size(), urgency.shouldBypassBatching());

            if (urgency.shouldBypassBatching()) {
                logger.info("Bypassing batching for {} notifications due to urgency: {}",
                           deliveryPlan.getEmailRecipients().size(), urgency);
                emailNotificationService.sendEmailNotifications(type, title, message, deliveryPlan.getEmailRecipients(), entityReferences);
                logger.info("Sent immediate email notifications to {} internal users", deliveryPlan.getEmailRecipients().size());
            } else {
                logger.debug("Queueing {} notifications for batching with urgency: {}",
                            deliveryPlan.getEmailRecipients().size(), urgency);
                Set<Long> immediateRecipients = notificationBatchingService.queueNotificationsForBatching(
                    type, title, message, deliveryPlan.getEmailRecipients(), entityReferences, metadata, urgency);

                if (!immediateRecipients.isEmpty()) {
                    logger.warn("POTENTIAL DUPLICATE: Batching service returned {} immediate recipients when batching was expected",
                               immediateRecipients.size());
                    emailNotificationService.sendEmailNotifications(type, title, message, immediateRecipients, entityReferences);
                    logger.info("Sent immediate email notifications to {} internal users", immediateRecipients.size());
                }

                int batchedCount = deliveryPlan.getEmailRecipients().size() - immediateRecipients.size();
                if (batchedCount > 0) {
                    logger.info("Queued {} email notifications for batching", batchedCount);
                }
            }
        }
    }

    /**
     * Processes notifications for external users with full feature parity.
     * External users now receive both in-app and email notifications just like internal users.
     */
    private void processExternalUserNotifications(List<ExternalUserRecipient> externalRecipients,
                                                NotificationType type, String title, String message,
                                                NotificationStorageService.EntityReferences entityReferences,
                                                NotificationMetadata metadata, NotificationUrgency urgency) {

        if (externalRecipients.isEmpty()) {
            return;
        }

        logger.debug("Processing notifications for {} external users", externalRecipients.size());

        // Create delivery plan for external users based on preferences
        ExternalNotificationDeliveryPlan deliveryPlan = createExternalDeliveryPlan(type, externalRecipients);

        // Process in-app notifications (mandatory for all external users)
        processExternalInAppNotifications(externalRecipients, type, title, message, entityReferences, metadata);

        // Process email notifications (optional based on preferences)
        processExternalEmailNotifications(deliveryPlan, type, title, message, entityReferences, metadata, urgency);

        logger.info("Processed notifications for {} external users (in-app: {}, email: {})",
                   externalRecipients.size(), externalRecipients.size(), deliveryPlan.getEmailRecipients().size());
    }

    /**
     * Processes in-app notifications for external users (mandatory).
     * All external users receive in-app notifications regardless of preferences.
     */
    private void processExternalInAppNotifications(List<ExternalUserRecipient> externalRecipients,
                                                 NotificationType type, String title, String message,
                                                 NotificationStorageService.EntityReferences entityReferences,
                                                 NotificationMetadata metadata) {

        if (externalRecipients.isEmpty()) {
            return;
        }

        // Convert external recipients to the unified recipient list for storage
        List<NotificationRecipient> recipients = new ArrayList<>(externalRecipients);

        // Create in-app notifications using the unified storage service
        notificationStorageService.createMixedNotifications(type, title, message, recipients, entityReferences, metadata);

        logger.info("Created {} in-app notifications for external users", externalRecipients.size());
    }

    /**
     * Creates a delivery plan for external users based on email preferences.
     * In-app notifications are mandatory, only email notifications respect preferences.
     */
    private ExternalNotificationDeliveryPlan createExternalDeliveryPlan(NotificationType type,
                                                                       List<ExternalUserRecipient> recipients) {
        ExternalNotificationDeliveryPlan deliveryPlan = new ExternalNotificationDeliveryPlan();

        for (ExternalUserRecipient recipient : recipients) {
            // Check email preferences for external users (in-app is always enabled)
            Boolean emailEnabled = notificationPreferenceService.isExternalNotificationEnabled(
                recipient.getEmail(), type, NotificationChannel.EMAIL);

            if (emailEnabled != null && emailEnabled) {
                deliveryPlan.addEmailRecipient(recipient);
            }
        }

        return deliveryPlan;
    }

    /**
     * Processes email notifications for external users (optional based on preferences).
     * This is separate from in-app notifications which are mandatory for external users.
     */
    private void processExternalEmailNotifications(ExternalNotificationDeliveryPlan deliveryPlan,
                                                 NotificationType type, String title, String message,
                                                 NotificationStorageService.EntityReferences entityReferences,
                                                 NotificationMetadata metadata, NotificationUrgency urgency) {

        if (!deliveryPlan.hasEmailRecipients()) {
            return;
        }

        List<ExternalUserRecipient> emailRecipients = deliveryPlan.getEmailRecipients();

        if (urgency.shouldBypassBatching()) {
            // Send immediately
            externalEmailNotificationService.sendImmediateEmails(type, title, message, emailRecipients, entityReferences);
        } else {
            // Queue for batching
            notificationBatchingService.queueExternalNotificationsForBatching(
                type, title, message, emailRecipients, entityReferences, metadata, urgency);
        }
    }

    /**
     * Data class for external user delivery planning.
     */
    private static class ExternalNotificationDeliveryPlan {
        private final List<ExternalUserRecipient> emailRecipients = new java.util.ArrayList<>();

        public void addEmailRecipient(ExternalUserRecipient recipient) {
            emailRecipients.add(recipient);
        }

        public List<ExternalUserRecipient> getEmailRecipients() {
            return emailRecipients;
        }

        public boolean hasEmailRecipients() {
            return !emailRecipients.isEmpty();
        }
    }

    // ========================================
    // RECIPIENT CONVERSION HELPER METHODS
    // ========================================

    /**
     * Converts a list of user IDs to internal notification recipients.
     *
     * <p>This method performs bulk loading to avoid N+1 queries and creates
     * InternalUserRecipient instances for each valid user.</p>
     *
     * @param userIds the user IDs to convert (must not be null or empty)
     * @return list of internal user recipients
     * @throws IllegalArgumentException if any user ID is invalid or user not found
     */
    private List<NotificationRecipient> convertUserIdsToInternalRecipients(@NotEmpty List<Long> userIds) {
        if (userIds.isEmpty()) {
            return List.of();
        }

        // Bulk load users to avoid N+1 queries
        List<User> users = userRepository.findByIds(userIds);

        if (users.size() != userIds.size()) {
            logger.warn("Some user IDs not found: requested={}, found={}", userIds.size(), users.size());
        }

        return users.stream()
                   .map(InternalUserRecipient::fromUser)
                   .collect(Collectors.toList());
    }

    /**
     * Converts a list of hub participants to notification recipients.
     *
     * <p>This method handles mixed participants (both internal and external) and
     * performs bulk loading for internal users to avoid N+1 queries.</p>
     *
     * @param hubParticipants the hub participants to convert (must not be null or empty)
     * @return list of notification recipients (mixed types)
     * @throws IllegalArgumentException if any participant is invalid
     */
    private List<NotificationRecipient> convertHubParticipantsToRecipients(
            @NotEmpty List<org.jooq.generated.tables.pojos.HubParticipant> hubParticipants) {

        if (hubParticipants.isEmpty()) {
            return List.of();
        }

        List<NotificationRecipient> recipients = new ArrayList<>();
        List<Long> internalUserIds = new ArrayList<>();

        // Separate internal and external participants
        for (org.jooq.generated.tables.pojos.HubParticipant participant : hubParticipants) {
            if (participant.getUserId() != null) {
                // Internal participant - collect user ID for bulk loading
                internalUserIds.add(participant.getUserId());
            } else {
                // External participant - create recipient directly
                recipients.add(ExternalUserRecipient.fromHubParticipant(participant));
            }
        }

        // Bulk load internal users if any
        if (!internalUserIds.isEmpty()) {
            List<User> users = userRepository.findByIds(internalUserIds);
            recipients.addAll(users.stream()
                                  .map(InternalUserRecipient::fromUser)
                                  .collect(Collectors.toList()));
        }

        return recipients;
    }

}
