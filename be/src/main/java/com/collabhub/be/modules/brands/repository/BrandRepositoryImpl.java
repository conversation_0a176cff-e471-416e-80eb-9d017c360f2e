package com.collabhub.be.modules.brands.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.BrandDao;
import org.jooq.generated.tables.pojos.Brand;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.jooq.generated.tables.Brand.BRAND;

/**
 * Repository for Brand entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy and soft deletion support.
 */
@Repository
public class BrandRepositoryImpl extends BrandDao {

    private final DSLContext dsl;

    public BrandRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds brands with filtering and pagination for a specific account.
     *
     * @param accountId the account ID for multi-tenancy
     * @param nameFilter optional name filter (case-insensitive partial match)
     * @param offset the number of records to skip
     * @param limit the maximum number of records to return
     * @return list of active brands matching the criteria
     */
    public List<Brand> findBrandsWithPagination(Long accountId, String nameFilter, int offset, int limit) {
        var query = dsl.select()
                .from(BRAND)
                .where(BRAND.ACCOUNT_ID.eq(accountId))
                .and(BRAND.DELETED_AT.isNull());

        // Add name filter if provided
        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            query = query.and(BRAND.NAME.containsIgnoreCase(nameFilter.trim()));
        }

        return query.orderBy(BRAND.NAME.asc())
                .offset(offset)
                .limit(limit)
                .fetchInto(Brand.class);
    }

    /**
     * Counts brands with filtering for a specific account.
     *
     * @param accountId the account ID for multi-tenancy
     * @param nameFilter optional name filter (case-insensitive partial match)
     * @return total count of active brands matching the criteria
     */
    public long countBrandsWithFilter(Long accountId, String nameFilter) {
        var query = dsl.selectCount()
                .from(BRAND)
                .where(BRAND.ACCOUNT_ID.eq(accountId))
                .and(BRAND.DELETED_AT.isNull());

        // Add name filter if provided
        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            query = query.and(BRAND.NAME.containsIgnoreCase(nameFilter.trim()));
        }

        return query.fetchOne(0, Long.class);
    }

    /**
     * Finds an active brand by ID and account ID.
     *
     * @param id the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return optional containing the brand if found and active
     */
    public Optional<Brand> findByIdAndAccountId(Long id, Long accountId) {
        return dsl.select()
                .from(BRAND)
                .where(BRAND.ID.eq(id))
                .and(BRAND.ACCOUNT_ID.eq(accountId))
                .and(BRAND.DELETED_AT.isNull())
                .fetchOptionalInto(Brand.class);
    }

    /**
     * Soft deletes a brand by setting the deleted_at timestamp.
     *
     * @param id the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return true if the brand was soft deleted, false if not found
     */
    public boolean softDeleteByIdAndAccountId(Long id, Long accountId) {
        int updatedRows = dsl.update(BRAND)
                .set(BRAND.DELETED_AT, LocalDateTime.now())
                .set(BRAND.UPDATED_AT, LocalDateTime.now())
                .where(BRAND.ID.eq(id))
                .and(BRAND.ACCOUNT_ID.eq(accountId))
                .and(BRAND.DELETED_AT.isNull())
                .execute();

        return updatedRows > 0;
    }

    /**
     * Checks if a brand exists with the given name for the account (excluding soft deleted).
     *
     * @param name the brand name
     * @param accountId the account ID for multi-tenancy
     * @param excludeId optional brand ID to exclude from the check (for updates)
     * @return true if a brand with the name exists
     */
    public boolean existsByNameAndAccountId(String name, Long accountId, Long excludeId) {
        var query = dsl.selectCount()
                .from(BRAND)
                .where(BRAND.NAME.eq(name))
                .and(BRAND.ACCOUNT_ID.eq(accountId))
                .and(BRAND.DELETED_AT.isNull());

        if (excludeId != null) {
            query = query.and(BRAND.ID.ne(excludeId));
        }

        return query.fetchOne(0, Integer.class) > 0;
    }

    /**
     * Finds brand names by IDs for bulk operations.
     * Used for optimizing invoice list views by fetching recipient brand names in bulk.
     *
     * @param brandIds the list of brand IDs
     * @param accountId the account ID for multi-tenancy
     * @return map of brand ID to brand name
     */
    public Map<Long, String> findBrandNamesByIds(List<Long> brandIds, Long accountId) {
        if (brandIds == null || brandIds.isEmpty()) {
            return Map.of();
        }

        return dsl.select(BRAND.ID, BRAND.NAME)
                .from(BRAND)
                .where(BRAND.ID.in(brandIds))
                .and(BRAND.ACCOUNT_ID.eq(accountId))
                .and(BRAND.DELETED_AT.isNull())
                .fetchMap(BRAND.ID, BRAND.NAME);
    }

    /**
     * Updates a brand's updated_at timestamp.
     *
     * @param id the brand ID
     * @param accountId the account ID for multi-tenancy
     */
    public void updateTimestamp(Long id, Long accountId) {
        dsl.update(BRAND)
                .set(BRAND.UPDATED_AT, LocalDateTime.now())
                .where(BRAND.ID.eq(id))
                .and(BRAND.ACCOUNT_ID.eq(accountId))
                .and(BRAND.DELETED_AT.isNull())
                .execute();
    }

    /**
     * Checks if a brand exists by ID and account ID.
     *
     * @param id the brand ID
     * @param accountId the account ID for multi-tenancy
     * @return true if the brand exists and is active, false otherwise
     */
    public boolean existsByIdAndAccountId(Long id, Long accountId) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(BRAND)
                        .where(BRAND.ID.eq(id))
                        .and(BRAND.ACCOUNT_ID.eq(accountId))
                        .and(BRAND.DELETED_AT.isNull())
        );
    }
}
