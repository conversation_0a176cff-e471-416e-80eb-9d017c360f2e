package com.collabhub.be.modules.invoice.dto;

import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.jooq.generated.enums.InvoiceStatus;

/**
 * Request DTO for updating invoice status.
 * Used for manual status transitions.
 */
public class InvoiceStatusUpdateRequest {

    @NotNull(message = InvoiceConstants.STATUS_REQUIRED)
    private InvoiceStatus status;

    @Size(max = InvoiceConstants.STATUS_NOTE_MAX_LENGTH, message = InvoiceConstants.STATUS_NOTE_SIZE_MESSAGE)
    private String note;

    public InvoiceStatusUpdateRequest() {
    }

    public InvoiceStatusUpdateRequest(InvoiceStatus status, String note) {
        this.status = status;
        this.note = note;
    }

    public InvoiceStatus getStatus() {
        return status;
    }

    public void setStatus(InvoiceStatus status) {
        this.status = status;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    @Override
    public String toString() {
        return "InvoiceStatusUpdateRequest{" +
                "status=" + status +
                ", note='" + note + '\'' +
                '}';
    }
}
