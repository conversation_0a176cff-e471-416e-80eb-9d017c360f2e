package com.collabhub.be.modules.chat.repository;

import org.jooq.DSLContext;
import org.jooq.generated.enums.ChatChannelScope;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.daos.ChatChannelDao;
import org.jooq.generated.tables.pojos.ChatChannel;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.generated.Tables.CHAT_CHANNEL_PARTICIPANTS;
import static org.jooq.generated.Tables.CHAT_MESSAGE;
import static org.jooq.generated.tables.ChatChannel.CHAT_CHANNEL;
import static org.jooq.generated.tables.HubParticipant.HUB_PARTICIPANT;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;

/**
 * Repository for ChatChannel entity using jOOQ for database operations.
 * Provides type-safe database operations for chat channel management.
 */
@Repository
public class ChatChannelRepositoryImpl extends ChatChannelDao {

    private final DSLContext dsl;

    public ChatChannelRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds a chat channel by ID and verifies the participant has access to it.
     * 
     * @param channelId the channel ID
     * @param participantId the participant ID
     * @return optional chat channel if accessible
     */
    public Optional<ChatChannel> findAccessibleChannelById(Long channelId, Long participantId) {
        var query = dsl.select()
                .from(CHAT_CHANNEL)
                .where(CHAT_CHANNEL.ID.eq(channelId));

        // Simplified access control: general channels for all, custom channels for participants only
        // All participants can access general channels
        // Custom channels require participant membership (checked via junction table)
        query = query.and(
            // General channels - accessible to all hub participants
            CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.valueOf("general"))
            // Custom channels - only accessible to participants in the channel
            .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.valueOf("custom"))
                .and(DSL.exists(
                    dsl.selectOne()
                        .from(CHAT_CHANNEL_PARTICIPANTS)
                        .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(CHAT_CHANNEL.ID))
                        .and(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID.eq(participantId))
                )))
        );

        return query.fetchOptionalInto(ChatChannel.class);
    }

    /**
     * Creates a general chat channel for a new collaboration hub.
     * This replaces the old createDefaultChannels method.
     *
     * @param hubId the hub ID
     * @param createdByParticipantId the participant who created the hub (admin)
     * @return the created general channel
     */
    public ChatChannel createGeneralChannel(Long hubId, Long createdByParticipantId) {
        LocalDateTime now = LocalDateTime.now();

        ChatChannel generalChannel = new ChatChannel();
        generalChannel.setHubId(hubId);
        generalChannel.setName("General");
        generalChannel.setScope(ChatChannelScope.valueOf("general"));
        generalChannel.setCreatedByParticipantId(createdByParticipantId);
        generalChannel.setCreatedAt(now);
        generalChannel.setLastActivityAt(now);
        insert(generalChannel);

        return generalChannel;
    }

    /**
     * Creates a custom chat channel with specific participants.
     *
     * @param hubId the hub ID
     * @param name the channel name
     * @param description the channel description (optional)
     * @param createdByParticipantId the participant creating the channel
     * @return the created custom channel
     */
    public ChatChannel createCustomChannel(Long hubId, String name, String description, Long createdByParticipantId) {
        LocalDateTime now = LocalDateTime.now();

        ChatChannel customChannel = new ChatChannel();
        customChannel.setHubId(hubId);
        customChannel.setName(name);
        customChannel.setDescription(description);
        customChannel.setScope(ChatChannelScope.valueOf("custom"));
        customChannel.setCreatedByParticipantId(createdByParticipantId);
        customChannel.setCreatedAt(now);
        customChannel.setLastActivityAt(now);
        insert(customChannel);

        return customChannel;
    }

    /**
     * Updates the last activity timestamp for a channel.
     * This will be called from the application layer when messages are sent.
     * 
     * @param channelId the channel ID
     */
    public void updateLastActivity(Long channelId) {
        dsl.update(CHAT_CHANNEL)
                .set(CHAT_CHANNEL.LAST_ACTIVITY_AT, LocalDateTime.now())
                .where(CHAT_CHANNEL.ID.eq(channelId))
                .execute();
    }

    /**
     * Counts unread messages for a participant in a channel.
     * This is a simplified implementation that counts messages created after
     * the participant's last activity in the hub.
     *
     * @param channelId the channel ID
     * @param participantId the participant ID
     * @return unread message count
     */
    public Long countUnreadMessages(Long channelId, Long participantId) {
        // Simplified implementation: count all messages in the channel
        // excluding the participant's own messages
        // In a full implementation, you'd track read receipts in a separate table

        return dsl.selectCount()
                .from(CHAT_MESSAGE)
                .where(CHAT_MESSAGE.CHANNEL_ID.eq(channelId))
                .and(CHAT_MESSAGE.PARTICIPANT_ID.ne(participantId)) // Don't count own messages
                .fetchOne(0, Long.class);
    }

    /**
     * Counts participants who have access to a channel.
     * 
     * @param channelId the channel ID
     * @return participant count
     */
    public Long countChannelParticipants(Long channelId) {
        ChatChannel channel = fetchOneById(channelId);
        if (channel == null) {
            return 0L;
        }

        var query = dsl.selectCount()
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(channel.getHubId()))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull());

        // Simplified scope-based filtering: general or custom only
        if (channel.getScope() == ChatChannelScope.custom) {
            // Only participants in chat_channel_participants can access custom channels
            query = query.and(
                DSL.exists(
                    dsl.selectOne()
                        .from(CHAT_CHANNEL_PARTICIPANTS)
                        .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(channel.getId()))
                        .and(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID.eq(HUB_PARTICIPANT.ID))
                )
            );
        }
        // General channels are accessible to all hub participants (no additional filtering needed)

        return query.fetchOne(0, Long.class);
    }

    /**
     * Finds accessible chat channels with pagination support.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param participantRole the participant's role
     * @param pageRequest the pagination request
     * @return list of accessible chat channels
     */
    public List<ChatChannel> findAccessibleChannelsByHubAndParticipantWithPagination(Long hubId, Long participantId,
                                                                                    HubParticipantRole participantRole,
                                                                                    PageRequest pageRequest) {
        var query = dsl.select()
                .from(CHAT_CHANNEL)
                .where(CHAT_CHANNEL.HUB_ID.eq(hubId));

        // Simplified access control: general channels for all, custom channels for participants only
        query = query.and(
            // General channels - accessible to all hub participants
            CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.general)
            // Custom channels - only accessible to participants in the channel
            .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.custom)
                .and(DSL.exists(
                    dsl.selectOne()
                        .from(CHAT_CHANNEL_PARTICIPANTS)
                        .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(CHAT_CHANNEL.ID))
                        .and(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID.eq(participantId))
                )))
        );

        return query.orderBy(CHAT_CHANNEL.LAST_ACTIVITY_AT.desc().nullsLast())
                .limit(pageRequest.getSize())
                .offset(pageRequest.getOffset())
                .fetchInto(ChatChannel.class);
    }

    /**
     * Counts accessible chat channels for a participant.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param participantRole the participant's role
     * @return count of accessible channels
     */
    public long countAccessibleChannelsByHubAndParticipant(Long hubId, Long participantId,
                                                          HubParticipantRole participantRole) {
        var query = dsl.selectCount()
                .from(CHAT_CHANNEL)
                .where(CHAT_CHANNEL.HUB_ID.eq(hubId));

        // Simplified access control: general channels for all, custom channels for participants only
        query = query.and(
            // General channels - accessible to all hub participants
            CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.valueOf("general"))
            // Custom channels - only accessible to participants in the channel
            .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.valueOf("custom"))
                .and(DSL.exists(
                    dsl.selectOne()
                        .from(CHAT_CHANNEL_PARTICIPANTS)
                        .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(CHAT_CHANNEL.ID))
                        .and(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID.eq(participantId))
                )))
        );

        return query.fetchOne(0, Long.class);
    }
}
