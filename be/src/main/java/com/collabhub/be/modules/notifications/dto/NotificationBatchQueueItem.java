package com.collabhub.be.modules.notifications.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * Immutable data transfer object representing a notification in the batch processing queue.
 * 
 * <p>This record provides a strongly-typed representation of queued notifications,
 * replacing the previous POJO approach with compile-time safety and clear data contracts.
 * It encapsulates all information needed to process and deliver a batched notification.</p>
 * 
 * <h3>Lifecycle States:</h3>
 * <ul>
 *   <li><strong>PENDING:</strong> Queued and waiting for batch processing</li>
 *   <li><strong>PROCESSING:</strong> Currently being processed by batch processor</li>
 *   <li><strong>SENT:</strong> Successfully delivered to recipient</li>
 *   <li><strong>FAILED:</strong> Processing failed, may be eligible for retry</li>
 * </ul>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * NotificationBatchQueueItem item = NotificationBatchQueueItem.builder()
 *     .id(123L)
 *     .userId(456L)
 *     .notificationType(NotificationType.COMMENT_MENTION)
 *     .urgency(NotificationUrgency.HIGH)
 *     .title("You were mentioned in a comment")
 *     .message("John Doe mentioned you in a comment on 'Summer Campaign'")
 *     .status(BatchQueueStatus.PENDING)
 *     .batchWindowStart(LocalDateTime.now().minusMinutes(2))
 *     .metadata(NotificationMetadata.builder()
 *         .actorName("John Doe")
 *         .targetTitle("Summer Campaign")
 *         .build())
 *     .build();
 * </pre>
 * 
 * @param id Unique identifier for this queue item
 * @param userId The user ID who should receive this notification
 * @param notificationType The type of notification being sent
 * @param urgency The urgency level affecting batching behavior
 * @param title Short notification title for display
 * @param message Full notification message content
 * @param status Current processing status of this notification
 * @param batchWindowStart Start time of the batching window for this notification
 * @param metadata Structured metadata providing additional context
 * @param entityReferences References to related entities (hub, post, comment, etc.)
 * @param retryCount Number of processing retry attempts made
 * @param lastRetryAt Timestamp of the last retry attempt
 * @param errorMessage Error message from the last failed processing attempt
 * @param createdAt Timestamp when this notification was first queued
 * @param processedAt Timestamp when this notification was successfully processed
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Schema(description = "Notification item in the batch processing queue with complete processing context")
public record NotificationBatchQueueItem(
    
    @Schema(description = "Unique identifier for this queue item", example = "123")
    @Positive(message = "ID must be positive")
    Long id,
    
    @Schema(description = "User ID who should receive this notification", example = "456")
    @NotNull(message = "User ID cannot be null")
    @Positive(message = "User ID must be positive")
    Long userId,
    
    @Schema(description = "Type of notification being sent", enumAsRef = true)
    @NotNull(message = "Notification type cannot be null")
    NotificationType notificationType,
    
    @Schema(description = "Urgency level affecting batching behavior", enumAsRef = true)
    @NotNull(message = "Urgency cannot be null")
    NotificationUrgency urgency,
    
    @Schema(description = "Short notification title for display", example = "You were mentioned in a comment")
    @NotBlank(message = "Title cannot be blank")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    String title,
    
    @Schema(description = "Full notification message content", example = "John Doe mentioned you in a comment on 'Summer Campaign'")
    @NotBlank(message = "Message cannot be blank")
    @Size(max = 1000, message = "Message must not exceed 1000 characters")
    String message,
    
    @Schema(description = "Current processing status", enumAsRef = true)
    @NotNull(message = "Status cannot be null")
    BatchQueueStatus status,
    
    @Schema(description = "Start time of the batching window", example = "2024-01-15T10:30:00")
    @NotNull(message = "Batch window start cannot be null")
    LocalDateTime batchWindowStart,
    
    @Schema(description = "Structured metadata providing additional context")
    @Valid
    NotificationMetadata metadata,
    
    @Schema(description = "References to related entities")
    @Valid
    EntityReferences entityReferences,
    
    @Schema(description = "Number of processing retry attempts made", example = "0")
    @PositiveOrZero(message = "Retry count must be non-negative")
    int retryCount,
    
    @Schema(description = "Timestamp of the last retry attempt")
    LocalDateTime lastRetryAt,
    
    @Schema(description = "Error message from the last failed processing attempt")
    @Size(max = 500, message = "Error message must not exceed 500 characters")
    String errorMessage,
    
    @Schema(description = "Timestamp when this notification was first queued", example = "2024-01-15T10:28:00")
    @NotNull(message = "Created at cannot be null")
    LocalDateTime createdAt,
    
    @Schema(description = "Timestamp when this notification was successfully processed")
    LocalDateTime processedAt
) {
    
    /**
     * Compact constructor with validation and null safety.
     */
    public NotificationBatchQueueItem {
        Objects.requireNonNull(userId, "User ID cannot be null");
        Objects.requireNonNull(notificationType, "Notification type cannot be null");
        Objects.requireNonNull(urgency, "Urgency cannot be null");
        Objects.requireNonNull(status, "Status cannot be null");
        Objects.requireNonNull(batchWindowStart, "Batch window start cannot be null");
        Objects.requireNonNull(createdAt, "Created at cannot be null");
        
        if (title == null || title.trim().isEmpty()) {
            throw new IllegalArgumentException("Title cannot be blank");
        }
        
        if (message == null || message.trim().isEmpty()) {
            throw new IllegalArgumentException("Message cannot be blank");
        }
        
        if (userId <= 0) {
            throw new IllegalArgumentException("User ID must be positive");
        }
        
        if (retryCount < 0) {
            throw new IllegalArgumentException("Retry count must be non-negative");
        }
        
        // Trim strings
        title = title.trim();
        message = message.trim();
        if (errorMessage != null) {
            errorMessage = errorMessage.trim();
            if (errorMessage.isEmpty()) {
                errorMessage = null;
            }
        }
    }
    
    /**
     * Creates a builder for constructing NotificationBatchQueueItem instances.
     * 
     * @return a new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * Checks if this notification has metadata.
     * 
     * @return true if metadata is present and has content, false otherwise
     */
    public boolean hasMetadata() {
        return metadata != null && metadata.hasContent();
    }
    
    /**
     * Checks if this notification has entity references.
     * 
     * @return true if entity references are present, false otherwise
     */
    public boolean hasEntityReferences() {
        return entityReferences != null;
    }
    
    /**
     * Gets the error message from the last failed processing attempt.
     * 
     * @return Optional containing the error message, or empty if no error
     */
    public Optional<String> getErrorMessage() {
        return Optional.ofNullable(errorMessage);
    }
    
    /**
     * Gets the timestamp of the last retry attempt.
     * 
     * @return Optional containing the retry timestamp, or empty if no retries
     */
    public Optional<LocalDateTime> getLastRetryAt() {
        return Optional.ofNullable(lastRetryAt);
    }
    
    /**
     * Gets the timestamp when this notification was processed.
     * 
     * @return Optional containing the processed timestamp, or empty if not processed
     */
    public Optional<LocalDateTime> getProcessedAt() {
        return Optional.ofNullable(processedAt);
    }
    
    /**
     * Checks if this notification is eligible for retry.
     * 
     * @param maxRetryAttempts the maximum number of retry attempts allowed
     * @return true if retry count is less than max attempts and status is FAILED
     */
    public boolean isEligibleForRetry(int maxRetryAttempts) {
        return status.isFailedState() && retryCount < maxRetryAttempts;
    }
    
    /**
     * Checks if this notification is ready for processing based on batch window timing.
     * 
     * @param currentTime the current time to compare against
     * @return true if the batch window has elapsed and status is PENDING
     */
    public boolean isReadyForProcessing(LocalDateTime currentTime) {
        return status.isPending() && batchWindowStart.isBefore(currentTime);
    }
    
    /**
     * Creates a copy of this item with updated status.
     * 
     * @param newStatus the new status to set
     * @return a new NotificationBatchQueueItem with the updated status
     */
    public NotificationBatchQueueItem withStatus(BatchQueueStatus newStatus) {
        return new NotificationBatchQueueItem(
            id, userId, notificationType, urgency, title, message,
            newStatus, batchWindowStart, metadata, entityReferences,
            retryCount, lastRetryAt, errorMessage, createdAt,
            newStatus == BatchQueueStatus.SENT ? LocalDateTime.now() : processedAt
        );
    }
    
    /**
     * Creates a copy of this item with incremented retry count and error message.
     * 
     * @param newErrorMessage the error message from the failed attempt
     * @return a new NotificationBatchQueueItem with updated retry information
     */
    public NotificationBatchQueueItem withRetryIncrement(String newErrorMessage) {
        return new NotificationBatchQueueItem(
            id, userId, notificationType, urgency, title, message,
            BatchQueueStatus.FAILED, batchWindowStart, metadata, entityReferences,
            retryCount + 1, LocalDateTime.now(), newErrorMessage, createdAt, processedAt
        );
    }
    
    /**
     * Builder class for constructing NotificationBatchQueueItem instances.
     */
    public static class Builder {
        private Long id;
        private Long userId;
        private NotificationType notificationType;
        private NotificationUrgency urgency;
        private String title;
        private String message;
        private BatchQueueStatus status = BatchQueueStatus.PENDING;
        private LocalDateTime batchWindowStart;
        private NotificationMetadata metadata;
        private EntityReferences entityReferences;
        private int retryCount = 0;
        private LocalDateTime lastRetryAt;
        private String errorMessage;
        private LocalDateTime createdAt = LocalDateTime.now();
        private LocalDateTime processedAt;
        
        private Builder() {}
        
        public Builder id(Long id) {
            this.id = id;
            return this;
        }
        
        public Builder userId(Long userId) {
            this.userId = userId;
            return this;
        }
        
        public Builder notificationType(NotificationType notificationType) {
            this.notificationType = notificationType;
            return this;
        }
        
        public Builder urgency(NotificationUrgency urgency) {
            this.urgency = urgency;
            return this;
        }
        
        public Builder title(String title) {
            this.title = title;
            return this;
        }
        
        public Builder message(String message) {
            this.message = message;
            return this;
        }
        
        public Builder status(BatchQueueStatus status) {
            this.status = status;
            return this;
        }
        
        public Builder batchWindowStart(LocalDateTime batchWindowStart) {
            this.batchWindowStart = batchWindowStart;
            return this;
        }
        
        public Builder metadata(NotificationMetadata metadata) {
            this.metadata = metadata;
            return this;
        }
        
        public Builder entityReferences(EntityReferences entityReferences) {
            this.entityReferences = entityReferences;
            return this;
        }
        
        public Builder retryCount(int retryCount) {
            this.retryCount = retryCount;
            return this;
        }
        
        public Builder lastRetryAt(LocalDateTime lastRetryAt) {
            this.lastRetryAt = lastRetryAt;
            return this;
        }
        
        public Builder errorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }
        
        public Builder createdAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }
        
        public Builder processedAt(LocalDateTime processedAt) {
            this.processedAt = processedAt;
            return this;
        }
        
        /**
         * Builds the NotificationBatchQueueItem instance.
         * 
         * @return a new NotificationBatchQueueItem with the configured values
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public NotificationBatchQueueItem build() {
            return new NotificationBatchQueueItem(
                id, userId, notificationType, urgency, title, message,
                status, batchWindowStart, metadata, entityReferences,
                retryCount, lastRetryAt, errorMessage, createdAt, processedAt
            );
        }
    }
    
    /**
     * Strongly-typed entity references for notifications.
     */
    public record EntityReferences(
        Long hubId,
        Long postId,
        Long commentId,
        Long chatChannelId,
        Long briefId
    ) {
        
        /**
         * Creates an EntityReferences for a hub-related notification.
         */
        public static EntityReferences hub(Long hubId) {
            return new EntityReferences(hubId, null, null, null, null);
        }
        
        /**
         * Creates an EntityReferences for a post-related notification.
         */
        public static EntityReferences post(Long hubId, Long postId) {
            return new EntityReferences(hubId, postId, null, null, null);
        }
        
        /**
         * Creates an EntityReferences for a comment-related notification.
         */
        public static EntityReferences comment(Long hubId, Long postId, Long commentId) {
            return new EntityReferences(hubId, postId, commentId, null, null);
        }
        
        /**
         * Creates an EntityReferences for a chat-related notification.
         */
        public static EntityReferences chat(Long hubId, Long chatChannelId) {
            return new EntityReferences(hubId, null, null, chatChannelId, null);
        }
        
        /**
         * Creates an EntityReferences for a brief-related notification.
         */
        public static EntityReferences brief(Long hubId, Long briefId) {
            return new EntityReferences(hubId, null, null, null, briefId);
        }
    }
}
