package com.collabhub.be.modules.auth.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.AccountDao;
import org.springframework.stereotype.Repository;

/**
 * Repository for Account entity using jOOQ for database operations.
 * Handles multi-tenant account management.
 */
@Repository
public class AccountRepository extends AccountDao {

    private final DSLContext dsl;

    public AccountRepository(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }


}
