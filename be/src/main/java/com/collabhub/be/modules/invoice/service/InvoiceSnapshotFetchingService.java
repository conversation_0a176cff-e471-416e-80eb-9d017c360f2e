package com.collabhub.be.modules.invoice.service;

import com.collabhub.be.modules.bankdetails.repository.BankDetailsRepositoryImpl;
import com.collabhub.be.modules.brands.repository.BrandRepositoryImpl;
import com.collabhub.be.modules.companies.repository.AccountCompanyRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.InvoiceResponse;
import com.collabhub.be.modules.invoice.dto.InvoiceSnapshotResponse;
import org.jooq.generated.enums.InvoiceStatus;
import org.jooq.generated.tables.pojos.AccountCompany;
import org.jooq.generated.tables.pojos.BankDetails;
import org.jooq.generated.tables.pojos.Brand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service for fetching invoice snapshot data.
 * Centralizes the logic for retrieving issuer, recipient, and bank details snapshots
 * for both email and PDF generation, eliminating code duplication.
 */
@Service
public class InvoiceSnapshotFetchingService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceSnapshotFetchingService.class);

    private final InvoiceSnapshotService snapshotService;
    private final AccountCompanyRepositoryImpl accountCompanyRepository;
    private final BrandRepositoryImpl brandRepository;
    private final BankDetailsRepositoryImpl bankDetailsRepository;

    public InvoiceSnapshotFetchingService(InvoiceSnapshotService snapshotService,
                                         AccountCompanyRepositoryImpl accountCompanyRepository,
                                         BrandRepositoryImpl brandRepository,
                                         BankDetailsRepositoryImpl bankDetailsRepository) {
        this.snapshotService = snapshotService;
        this.accountCompanyRepository = accountCompanyRepository;
        this.brandRepository = brandRepository;
        this.bankDetailsRepository = bankDetailsRepository;
    }

    /**
     * Fetches issuer snapshot for email or PDF generation.
     * For DRAFT invoices, fetches live data from AccountCompany.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     *
     * @param invoice the invoice response
     * @return issuer snapshot response
     */
    @Transactional(readOnly = true)
    public InvoiceSnapshotResponse.IssuerSnapshotResponse fetchIssuerSnapshot(InvoiceResponse invoice) {
        logger.debug("Fetching issuer snapshot for invoice {} (status: {})", 
                    invoice.getId(), invoice.getStatus());

        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            return fetchLiveIssuerData(invoice);
        } else {
            return fetchStoredIssuerSnapshot(invoice);
        }
    }

    /**
     * Fetches recipient snapshot for email or PDF generation.
     * For DRAFT invoices, fetches live data from Brand.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     *
     * @param invoice the invoice response
     * @return recipient snapshot response
     */
    @Transactional(readOnly = true)
    public InvoiceSnapshotResponse.RecipientSnapshotResponse fetchRecipientSnapshot(InvoiceResponse invoice) {
        logger.debug("Fetching recipient snapshot for invoice {} (status: {})", 
                    invoice.getId(), invoice.getStatus());

        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            return fetchLiveRecipientData(invoice);
        } else {
            return fetchStoredRecipientSnapshot(invoice);
        }
    }

    /**
     * Fetches bank details snapshot for email or PDF generation.
     * For DRAFT invoices, fetches live data from BankDetails.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     *
     * @param invoice the invoice response
     * @return bank details snapshot response
     */
    @Transactional(readOnly = true)
    public InvoiceSnapshotResponse.BankDetailsSnapshotResponse fetchBankDetailsSnapshot(InvoiceResponse invoice) {
        logger.debug("Fetching bank details snapshot for invoice {} (status: {})", 
                    invoice.getId(), invoice.getStatus());

        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            return fetchLiveBankDetailsData(invoice);
        } else {
            return fetchStoredBankDetailsSnapshot(invoice);
        }
    }

    /**
     * Fetches live issuer data for DRAFT invoices.
     */
    private InvoiceSnapshotResponse.IssuerSnapshotResponse fetchLiveIssuerData(InvoiceResponse invoice) {
        if (invoice.getIssuerId() == null) {
            logger.debug("No issuer ID found for invoice {}", invoice.getId());
            return new InvoiceSnapshotResponse.IssuerSnapshotResponse();
        }

        return accountCompanyRepository.findByIdAndAccountId(invoice.getIssuerId(), invoice.getAccountId())
                .map(this::convertAccountCompanyToIssuerResponse)
                .orElseGet(() -> {
                    logger.warn("Issuer not found: ID={}, accountId={}", invoice.getIssuerId(), invoice.getAccountId());
                    return new InvoiceSnapshotResponse.IssuerSnapshotResponse();
                });
    }

    /**
     * Fetches stored issuer snapshot for non-DRAFT invoices.
     */
    private InvoiceSnapshotResponse.IssuerSnapshotResponse fetchStoredIssuerSnapshot(InvoiceResponse invoice) {
        return snapshotService.getIssuerSnapshotByHash(invoice.getIssuerSnapshotHash());
    }

    /**
     * Fetches live recipient data for DRAFT invoices.
     */
    private InvoiceSnapshotResponse.RecipientSnapshotResponse fetchLiveRecipientData(InvoiceResponse invoice) {
        if (invoice.getRecipientId() == null) {
            logger.debug("No recipient ID found for invoice {}", invoice.getId());
            return new InvoiceSnapshotResponse.RecipientSnapshotResponse();
        }

        return brandRepository.findByIdAndAccountId(invoice.getRecipientId(), invoice.getAccountId())
                .map(this::convertBrandToRecipientResponse)
                .orElseGet(() -> {
                    logger.warn("Recipient not found: ID={}, accountId={}", invoice.getRecipientId(), invoice.getAccountId());
                    return new InvoiceSnapshotResponse.RecipientSnapshotResponse();
                });
    }

    /**
     * Fetches stored recipient snapshot for non-DRAFT invoices.
     */
    private InvoiceSnapshotResponse.RecipientSnapshotResponse fetchStoredRecipientSnapshot(InvoiceResponse invoice) {
        return snapshotService.getRecipientSnapshotByHash(invoice.getRecipientSnapshotHash());
    }

    /**
     * Fetches live bank details data for DRAFT invoices.
     */
    private InvoiceSnapshotResponse.BankDetailsSnapshotResponse fetchLiveBankDetailsData(InvoiceResponse invoice) {
        if (invoice.getBankDetailsId() == null) {
            logger.debug("No bank details ID found for invoice {}", invoice.getId());
            return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse();
        }

        return bankDetailsRepository.findByIdAndAccountId(invoice.getBankDetailsId(), invoice.getAccountId())
                .map(this::convertBankDetailsToBankDetailsResponse)
                .orElseGet(() -> {
                    logger.warn("Bank details not found: ID={}, accountId={}", invoice.getBankDetailsId(), invoice.getAccountId());
                    return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse();
                });
    }

    /**
     * Fetches stored bank details snapshot for non-DRAFT invoices.
     */
    private InvoiceSnapshotResponse.BankDetailsSnapshotResponse fetchStoredBankDetailsSnapshot(InvoiceResponse invoice) {
        return snapshotService.getBankDetailsSnapshotByHash(invoice.getBankDetailsSnapshotHash());
    }

    /**
     * Converts AccountCompany to IssuerSnapshotResponse.
     */
    private InvoiceSnapshotResponse.IssuerSnapshotResponse convertAccountCompanyToIssuerResponse(AccountCompany company) {
        return new InvoiceSnapshotResponse.IssuerSnapshotResponse(
                company.getId(),
                company.getCompanyName(),
                company.getAddressStreet(),
                company.getAddressCity(),
                company.getAddressPostalCode(),
                company.getAddressCountry(),
                company.getVatNumber(),
                company.getRegistrationNumber(),
                company.getPhone(),
                company.getEmail(),
                company.getWebsite()
        );
    }

    /**
     * Converts Brand to RecipientSnapshotResponse.
     */
    private InvoiceSnapshotResponse.RecipientSnapshotResponse convertBrandToRecipientResponse(Brand brand) {
        return new InvoiceSnapshotResponse.RecipientSnapshotResponse(
                brand.getId(),
                brand.getName(),
                brand.getAddressStreet(),
                brand.getAddressCity(),
                brand.getAddressPostalCode(),
                brand.getAddressCountry(),
                brand.getVatNumber(),
                brand.getRegistrationNumber(),
                brand.getEmail(),
                brand.getPhone()
        );
    }

    /**
     * Converts BankDetails to BankDetailsSnapshotResponse.
     */
    private InvoiceSnapshotResponse.BankDetailsSnapshotResponse convertBankDetailsToBankDetailsResponse(BankDetails bankDetails) {
        return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse(
                bankDetails.getId(),
                bankDetails.getName(),
                bankDetails.getBankName(),
                bankDetails.getIban(),
                bankDetails.getBicswift()
        );
    }
}
