package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * Response DTO for invoice delivery logs.
 * Contains information about email delivery attempts and their status.
 */
public class InvoiceDeliveryLogResponse {

    @NotNull
    private Long id;

    @NotNull
    @JsonProperty("invoice_id")
    private Long invoiceId;

    @JsonProperty("recipient_email")
    private String recipientEmail;

    @NotNull
    @JsonProperty("delivery_status")
    private String deliveryStatus;

    @JsonProperty("error_message")
    private String errorMessage;

    @NotNull
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    public InvoiceDeliveryLogResponse() {
    }

    public InvoiceDeliveryLogResponse(Long id, Long invoiceId, String recipientEmail, 
                                    String deliveryStatus, String errorMessage, LocalDateTime createdAt) {
        this.id = id;
        this.invoiceId = invoiceId;
        this.recipientEmail = recipientEmail;
        this.deliveryStatus = deliveryStatus;
        this.errorMessage = errorMessage;
        this.createdAt = createdAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getRecipientEmail() {
        return recipientEmail;
    }

    public void setRecipientEmail(String recipientEmail) {
        this.recipientEmail = recipientEmail;
    }

    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "InvoiceDeliveryLogResponse{" +
                "id=" + id +
                ", invoiceId=" + invoiceId +
                ", recipientEmail='" + recipientEmail + '\'' +
                ", deliveryStatus='" + deliveryStatus + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
