package com.collabhub.be.modules.media.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.MediaDao;
import org.jooq.generated.tables.pojos.Media;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.jooq.generated.Tables.*;
import org.jooq.impl.DSL;

/**
 * Repository for Media entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy support and soft deletion.
 */
@Repository
public class MediaRepositoryImpl extends MediaDao {

    private final DSLContext dsl;

    public MediaRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds media by ID with account validation and soft deletion check.
     */
    public Optional<Media> findByIdAndAccountId(Long mediaId, Long accountId) {
        return dsl.selectFrom(MEDIA)
                .where(MEDIA.ID.eq(mediaId))
                .and(MEDIA.ACCOUNT_ID.eq(accountId))
                .and(MEDIA.DELETED_AT.isNull())
                .fetchOptionalInto(Media.class);
    }

    /**
     * Finds media by S3 key with account validation.
     */
    public Optional<Media> findByS3KeyAndAccountId(String s3Key, Long accountId) {
        return dsl.selectFrom(MEDIA)
                .where(MEDIA.S3_KEY.eq(s3Key))
                .and(MEDIA.ACCOUNT_ID.eq(accountId))
                .and(MEDIA.DELETED_AT.isNull())
                .fetchOptionalInto(Media.class);
    }

    /**
     * Finds all media for an account with pagination.
     */
    public List<Media> findByAccountId(Long accountId, int offset, int limit) {
        return dsl.selectFrom(MEDIA)
                .where(MEDIA.ACCOUNT_ID.eq(accountId))
                .and(MEDIA.DELETED_AT.isNull())
                .orderBy(MEDIA.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(Media.class);
    }

    /**
     * Counts total media files for an account.
     */
    public long countByAccountId(Long accountId) {
        return dsl.selectCount()
                .from(MEDIA)
                .where(MEDIA.ACCOUNT_ID.eq(accountId))
                .and(MEDIA.DELETED_AT.isNull())
                .fetchOne(0, Long.class);
    }

    /**
     * Calculates total storage usage for an account in bytes.
     */
    public long calculateAccountStorageUsage(Long accountId) {
        Long totalSize = dsl.select(DSL.sum(MEDIA.FILE_SIZE_BYTES))
                .from(MEDIA)
                .where(MEDIA.ACCOUNT_ID.eq(accountId))
                .and(MEDIA.DELETED_AT.isNull())
                .fetchOne(0, Long.class);
        
        return totalSize != null ? totalSize : 0L;
    }

    /**
     * Finds media files by MIME type for an account.
     */
    public List<Media> findByAccountIdAndMimeType(Long accountId, String mimeType, int offset, int limit) {
        return dsl.selectFrom(MEDIA)
                .where(MEDIA.ACCOUNT_ID.eq(accountId))
                .and(MEDIA.MIME_TYPE.eq(mimeType))
                .and(MEDIA.DELETED_AT.isNull())
                .orderBy(MEDIA.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(Media.class);
    }

    /**
     * Finds media files by type (image/video) for an account.
     */
    public List<Media> findByAccountIdAndType(Long accountId, String type, int offset, int limit) {
        var query = dsl.selectFrom(MEDIA)
                .where(MEDIA.ACCOUNT_ID.eq(accountId))
                .and(MEDIA.DELETED_AT.isNull());

        if ("image".equals(type)) {
            query = query.and(MEDIA.MIME_TYPE.like("image/%"));
        } else if ("video".equals(type)) {
            query = query.and(MEDIA.MIME_TYPE.like("video/%"));
        }

        return query.orderBy(MEDIA.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(Media.class);
    }

    /**
     * Soft deletes a media file by setting deleted_at timestamp.
     */
    public boolean softDeleteMedia(Long mediaId, Long accountId) {
        int updated = dsl.update(MEDIA)
                .set(MEDIA.DELETED_AT, LocalDateTime.now())
                .set(MEDIA.UPDATED_AT, LocalDateTime.now())
                .where(MEDIA.ID.eq(mediaId))
                .and(MEDIA.ACCOUNT_ID.eq(accountId))
                .and(MEDIA.DELETED_AT.isNull())
                .execute();

        return updated > 0;
    }

    /**
     * Finds media files associated with a specific post.
     */
    public List<Media> findByPostId(Long postId) {
        return dsl.select(MEDIA.fields())
                .from(MEDIA)
                .join(POST_MEDIA).on(POST_MEDIA.MEDIA_ID.eq(MEDIA.ID))
                .where(POST_MEDIA.POST_ID.eq(postId))
                .and(MEDIA.DELETED_AT.isNull())
                .orderBy(POST_MEDIA.DISPLAY_ORDER.asc())
                .fetchInto(Media.class);
    }

    /**
     * Bulk loads media files for multiple posts to avoid N+1 queries.
     * Returns a map of postId -> List<Media> ordered by display_order.
     */
    public Map<Long, List<Media>> bulkFindByPostIds(List<Long> postIds) {
        if (postIds.isEmpty()) {
            return Map.of();
        }

        // Use jOOQ's fetchGroups to group by POST_ID and fetchInto for type-safe conversion
        return dsl.select(MEDIA.fields())
                .select(POST_MEDIA.POST_ID)
                .from(MEDIA)
                .join(POST_MEDIA).on(POST_MEDIA.MEDIA_ID.eq(MEDIA.ID))
                .where(POST_MEDIA.POST_ID.in(postIds))
                .and(MEDIA.DELETED_AT.isNull())
                .orderBy(POST_MEDIA.POST_ID.asc(), POST_MEDIA.DISPLAY_ORDER.asc())
                .fetchGroups(
                    POST_MEDIA.POST_ID,
                    record -> record.into(MEDIA).into(Media.class)
                );
    }

    /**
     * Finds media files associated with a specific chat message.
     */
    public List<Media> findByChatMessageId(Long chatMessageId) {
        return dsl.select(MEDIA.fields())
                .from(MEDIA)
                .join(CHAT_MESSAGE_MEDIA).on(CHAT_MESSAGE_MEDIA.MEDIA_ID.eq(MEDIA.ID))
                .where(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID.eq(chatMessageId))
                .and(MEDIA.DELETED_AT.isNull())
                .orderBy(CHAT_MESSAGE_MEDIA.DISPLAY_ORDER.asc())
                .fetchInto(Media.class);
    }

    /**
     * Bulk loads media files for multiple chat messages to avoid N+1 queries.
     * Returns a map of chatMessageId -> List<Media> ordered by display_order.
     */
    public Map<Long, List<Media>> bulkFindByChatMessageIds(List<Long> chatMessageIds) {
        if (chatMessageIds.isEmpty()) {
            return Map.of();
        }

        // Use jOOQ's fetchGroups to group by CHAT_MESSAGE_ID and fetchInto for type-safe conversion
        return dsl.select(MEDIA.fields())
                .select(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID)
                .from(MEDIA)
                .join(CHAT_MESSAGE_MEDIA).on(CHAT_MESSAGE_MEDIA.MEDIA_ID.eq(MEDIA.ID))
                .where(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID.in(chatMessageIds))
                .and(MEDIA.DELETED_AT.isNull())
                .orderBy(CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID.asc(), CHAT_MESSAGE_MEDIA.DISPLAY_ORDER.asc())
                .fetchGroups(
                    CHAT_MESSAGE_MEDIA.CHAT_MESSAGE_ID,
                    record -> record.into(MEDIA).into(Media.class)
                );
    }

    /**
     * Overrides the base findById to include soft deletion check.
     */
    @Override
    public Media findById(Long id) {
        return dsl.selectFrom(MEDIA)
                .where(MEDIA.ID.eq(id))
                .and(MEDIA.DELETED_AT.isNull())
                .fetchOneInto(Media.class);
    }

    /**
     * Creates a new media record and returns it with generated ID.
     */
    public Media createMedia(Media media) {
        media.setCreatedAt(LocalDateTime.now());
        media.setUpdatedAt(LocalDateTime.now());
        
        insert(media);
        return media;
    }
}
