package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.exception.UnauthorizedException;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;

/**
 * Service for collaboration hub access control and permission validation.
 * Handles permissions related to accessing collaboration hub content and participating in hubs.
 * 
 * Responsibilities:
 * - Fetching posts, briefs, participants, chats (read access)
 * - Posting in chats (write access)
 * - Viewing and participating in hubs
 * 
 * Access Rules:
 * - App-level ADMIN: Full access to all hubs
 * - Hub ADMIN: Full access within that specific hub
 * - Internal user (not app admin, not hub participant): Read-only access
 * - External & Brand Contacts: Must be explicitly added as participants
 */
@Service
@Validated
public class CollabHubPermissionService {

    private static final Logger logger = LoggerFactory.getLogger(CollabHubPermissionService.class);

    // Constants
    private static final String VALIDATING_HUB_ACCESS_LOG = "Validating hub access for user {} to hub {}";
    private static final String HUB_ACCESS_GRANTED_LOG = "Hub access granted for user {} to hub {}";
    private static final String HUB_ACCESS_DENIED_MESSAGE = "Access denied to collaboration hub";

    // Additional constants from HubParticipantPermissionService
    private static final String VALIDATING_ADMIN_ACCESS_LOG = "Validating admin access for user {} to hub {}";
    private static final String ADMIN_ACCESS_GRANTED_LOG = "Admin access granted for user {} to hub {}";
    private static final String VALIDATING_PARTICIPANT_MANAGEMENT_LOG = "Validating participant management access for user {} to hub {}";
    private static final String LAST_ADMIN_CHECK_LOG = "Checking if participant {} is last admin in hub {}";

    // Access levels
    private static final String ADMIN_ACCESS = "ADMIN_ACCESS";
    private static final String PARTICIPANT_ACCESS = "PARTICIPANT_ACCESS";
    private static final String FORBIDDEN = "FORBIDDEN";

    // Error messages
    private static final String HUB_ADMIN_ACCESS_REQUIRED_MESSAGE = "Admin access required for this operation";

    private final JwtClaimsService jwtClaimsService;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final HubParticipantRepositoryImpl participantRepository;

    public CollabHubPermissionService(JwtClaimsService jwtClaimsService,
                                    CollaborationHubRepositoryImpl hubRepository,
                                    HubParticipantRepositoryImpl participantRepository) {
        this.jwtClaimsService = jwtClaimsService;
        this.hubRepository = hubRepository;
        this.participantRepository = participantRepository;
    }

    // ========================================
    // Hub Content Access Validation
    // ========================================

    /**
     * Validates that the current user can view hub content (posts, briefs, participants, chats).
     * Uses the 5-tier access control system with proper account validation.
     *
     * @param hubId the hub ID
     * @throws ForbiddenException if access is denied
     * @throws NotFoundException if hub not found
     * @throws UnauthorizedException if account role is invalid
     */
    @Transactional(readOnly = true)
    public void validateCanParticipantAccessHubContent(@NotNull Long hubId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug(VALIDATING_HUB_ACCESS_LOG, userContext.getEmail(), hubId);

        String accessLevel = determineAccessLevel(hubId, userContext);
        if (FORBIDDEN.equals(accessLevel)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                    HUB_ACCESS_DENIED_MESSAGE + ": " + hubId);
        }

        logger.debug(HUB_ACCESS_GRANTED_LOG, userContext.getEmail(), hubId);
    }

    // ========================================
    // Hub Admin Access Control Methods
    // ========================================

    /**
     * Validates that the current user has admin access to a specific hub.
     * Throws exception if admin access is denied.
     *
     * @param hubId the hub ID
     * @throws ForbiddenException if admin access is denied
     */
    @Transactional(readOnly = true)
    public void validateHubAdminAccess(@NotNull Long hubId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug(VALIDATING_ADMIN_ACCESS_LOG, userContext.getEmail(), hubId);

        String accessLevel = determineAccessLevel(hubId, userContext);
        if (!ADMIN_ACCESS.equals(accessLevel)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, HUB_ADMIN_ACCESS_REQUIRED_MESSAGE);
        }

        logger.debug(ADMIN_ACCESS_GRANTED_LOG, userContext.getEmail(), hubId);
    }


    // ========================================
    // Access Level Determination
    // ========================================

    /**
     * Determines the access level for a user in a specific hub.
     * Implements the 5-tier access control system with proper account validation.
     *
     * @param hubId the hub ID
     * @param userContext the user context
     * @return access level (FULL_ACCESS, READ_ONLY, or FORBIDDEN)
     */
    private String determineAccessLevel(@NotNull Long hubId, @NotNull UserContext userContext) {
        // 1. Global Admin Override (INTERNAL + ADMIN role) → ALL permissions
        if (userContext.getRole() == Role.ADMIN && userContext.isInternalUser()) {
            CollaborationHub hub = hubRepository.findById(hubId);
            if (hub == null || !hub.getAccountId().equals(userContext.getAccountId())) {
                return FORBIDDEN;
            }

            return ADMIN_ACCESS;
        }

        // 2-3. Get hub participant (if exists) with optimized query
        HubParticipant participant = participantRepository.validateHubAccessWithParticipant(
                hubId, userContext.getAccountId(), userContext.getEmail());

        if (participant != null) {
            if (participant.getRole() == HubParticipantRole.admin) {
                return ADMIN_ACCESS; // Hub Admin
            }

            return PARTICIPANT_ACCESS; // Hub Participant with Role
        }

        // 4. External Non-Participant → FORBIDDEN
        if (userContext.isExternalUser()) {
            return FORBIDDEN;
        }

        // 5. Internal Non-Participant → READ-ONLY (but must validate account)
        if (userContext.isInternalUser()) {
            CollaborationHub hub = hubRepository.findById(hubId);
            if (hub == null || !hub.getAccountId().equals(userContext.getAccountId())) {
                return FORBIDDEN;
            }
        }

        return PARTICIPANT_ACCESS;
    }

    /**
     * Checks if a participant is the last admin in the hub.
     */
    private boolean isLastAdmin(HubParticipant participant, Long hubId) {
        logger.debug(LAST_ADMIN_CHECK_LOG, participant.getId(), hubId);
        long adminCount = participantRepository.countActiveAdminsByHubId(hubId);
        return adminCount <= 1;
    }
}
