package com.collabhub.be.modules.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Response DTO for user registration.
 * Returns confirmation that registration was successful and email verification is required.
 */
public class RegistrationResponse {

    private String message;
    
    private String email;
    
    @JsonProperty("verification_required")
    private boolean verificationRequired;

    public RegistrationResponse() {
    }

    public RegistrationResponse(String message, String email, boolean verificationRequired) {
        this.message = message;
        this.email = email;
        this.verificationRequired = verificationRequired;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isVerificationRequired() {
        return verificationRequired;
    }

    public void setVerificationRequired(boolean verificationRequired) {
        this.verificationRequired = verificationRequired;
    }

    @Override
    public String toString() {
        return "RegistrationResponse{" +
                "message='" + message + '\'' +
                ", email='" + email + '\'' +
                ", verificationRequired=" + verificationRequired +
                '}';
    }
}
