package com.collabhub.be.modules.invoice.dto;

import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.invoice.validation.ValidInvoiceDates;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

import java.time.LocalDate;
import java.util.List;

/**
 * Request DTO for creating a new invoice.
 * Contains all necessary information to create an invoice with items and recipients.
 */
@ValidInvoiceDates
public class InvoiceCreateRequest {

    @NotNull(message = InvoiceConstants.ISSUER_ID_REQUIRED)
    @JsonProperty("issuer_id")
    private Long issuerId;

    @NotNull(message = InvoiceConstants.RECIPIENT_ID_REQUIRED)
    @JsonProperty("recipient_id")
    private Long recipientId;

    @JsonProperty("bank_details_id")
    private Long bankDetailsId;

    @NotBlank(message = InvoiceConstants.INVOICE_NUMBER_REQUIRED)
    @Size(max = InvoiceConstants.INVOICE_NUMBER_MAX_LENGTH, message = InvoiceConstants.INVOICE_NUMBER_SIZE_MESSAGE)
    @JsonProperty("invoice_number")
    private String invoiceNumber;

    @Size(max = InvoiceConstants.TEMPLATE_NAME_MAX_LENGTH, message = InvoiceConstants.TEMPLATE_SIZE_MESSAGE)
    private String template = InvoiceConstants.DEFAULT_TEMPLATE;

    @Size(min = InvoiceConstants.CURRENCY_CODE_LENGTH, max = InvoiceConstants.CURRENCY_CODE_LENGTH, message = InvoiceConstants.CURRENCY_SIZE_MESSAGE)
    private String currency = InvoiceConstants.DEFAULT_CURRENCY;

    @NotNull(message = InvoiceConstants.ISSUE_DATE_REQUIRED)
    @JsonProperty("issue_date")
    private LocalDate issueDate;

    @NotNull(message = InvoiceConstants.DUE_DATE_REQUIRED)
    @JsonProperty("due_date")
    private LocalDate dueDate;

    @Size(max = InvoiceConstants.NOTES_MAX_LENGTH, message = InvoiceConstants.NOTES_SIZE_MESSAGE)
    private String notes;

    @NotEmpty(message = InvoiceConstants.ITEMS_REQUIRED)
    @Valid
    private List<InvoiceItemRequest> items;

    @NotEmpty(message = InvoiceConstants.RECIPIENTS_REQUIRED)
    @Valid
    private List<InvoiceRecipientRequest> recipients;

    public InvoiceCreateRequest() {
    }

    public InvoiceCreateRequest(Long issuerId, Long recipientId, Long bankDetailsId, String invoiceNumber,
                              String template, String currency, LocalDate issueDate, LocalDate dueDate,
                              String notes, List<InvoiceItemRequest> items, List<InvoiceRecipientRequest> recipients) {
        this.issuerId = issuerId;
        this.recipientId = recipientId;
        this.bankDetailsId = bankDetailsId;
        this.invoiceNumber = invoiceNumber;
        this.template = template;
        this.currency = currency;
        this.issueDate = issueDate;
        this.dueDate = dueDate;
        this.notes = notes;
        this.items = items;
        this.recipients = recipients;
    }

    public Long getIssuerId() {
        return issuerId;
    }

    public void setIssuerId(Long issuerId) {
        this.issuerId = issuerId;
    }

    public Long getRecipientId() {
        return recipientId;
    }

    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }

    public Long getBankDetailsId() {
        return bankDetailsId;
    }

    public void setBankDetailsId(Long bankDetailsId) {
        this.bankDetailsId = bankDetailsId;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public LocalDate getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDate issueDate) {
        this.issueDate = issueDate;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<InvoiceItemRequest> getItems() {
        return items;
    }

    public void setItems(List<InvoiceItemRequest> items) {
        this.items = items;
    }

    public List<InvoiceRecipientRequest> getRecipients() {
        return recipients;
    }

    public void setRecipients(List<InvoiceRecipientRequest> recipients) {
        this.recipients = recipients;
    }

    @Override
    public String toString() {
        return "InvoiceCreateRequest{" +
                "issuerId=" + issuerId +
                ", recipientId=" + recipientId +
                ", bankDetailsId=" + bankDetailsId +
                ", invoiceNumber='" + invoiceNumber + '\'' +
                ", template='" + template + '\'' +
                ", currency='" + currency + '\'' +
                ", issueDate=" + issueDate +
                ", dueDate=" + dueDate +
                ", notes='" + notes + '\'' +
                ", items=" + items +
                ", recipients=" + recipients +
                '}';
    }
}
