package com.collabhub.be.modules.notifications.converter;

import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationPreferenceResponse;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import org.jooq.generated.tables.pojos.NotificationPreference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Converter for NotificationPreference entity to DTOs.
 * Handles conversion between database entities and API response objects.
 */
@Component
public class NotificationPreferenceConverter {

    /**
     * Converts a NotificationPreference entity to a response DTO.
     *
     * @param preference the notification preference entity
     * @return the response DTO
     */
    public NotificationPreferenceResponse toResponse(NotificationPreference preference) {
        if (preference == null) {
            return null;
        }

        return new NotificationPreferenceResponse(
                preference.getId(),
                preference.getUserId(),
                preference.getEmail(),
                NotificationType.fromJooqEnum(preference.getType()),
                NotificationChannel.fromJooqEnum(preference.getChannel()),
                preference.getEnabled()
        );
    }

    /**
     * Converts a list of NotificationPreference entities to response DTOs.
     *
     * @param preferences the list of notification preference entities
     * @return the list of response DTOs
     */
    public List<NotificationPreferenceResponse> toResponseList(List<NotificationPreference> preferences) {
        if (preferences == null) {
            return List.of();
        }

        return preferences.stream()
                .map(this::toResponse)
                .toList();
    }
}
