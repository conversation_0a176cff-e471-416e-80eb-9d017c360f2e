package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Lightweight DTO for collaboration brief list items.
 * Used in paginated lists to avoid N+1 query problems.
 * All briefs are visible to all participants within the hub.
 */
public class CollaborationBriefListItemDto {

    @NotNull
    private Long id;

    @NotNull
    private String title;

    private String bodyPreview; // First 200 characters of body

    @NotNull
    private Long createdByParticipantId;

    private String createdByParticipantName;

    @NotNull
    private LocalDateTime createdAt;

    @NotNull
    private LocalDateTime updatedAt;

    public CollaborationBriefListItemDto() {}

    public CollaborationBriefListItemDto(Long id, String title, String bodyPreview,
                                         Long createdByParticipantId, String createdByParticipantName,
                                         LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.title = title;
        this.bodyPreview = bodyPreview;
        this.createdByParticipantId = createdByParticipantId;
        this.createdByParticipantName = createdByParticipantName;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBodyPreview() {
        return bodyPreview;
    }

    public void setBodyPreview(String bodyPreview) {
        this.bodyPreview = bodyPreview;
    }

    public Long getCreatedByParticipantId() {
        return createdByParticipantId;
    }

    public void setCreatedByParticipantId(Long createdByParticipantId) {
        this.createdByParticipantId = createdByParticipantId;
    }

    public String getCreatedByParticipantName() {
        return createdByParticipantName;
    }

    public void setCreatedByParticipantName(String createdByParticipantName) {
        this.createdByParticipantName = createdByParticipantName;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "CollaborationBriefListItemDto{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", bodyPreview='" + bodyPreview + '\'' +
                ", createdByParticipantId=" + createdByParticipantId +
                ", createdByParticipantName='" + createdByParticipantName + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
