package com.collabhub.be.modules.notifications.dto;

import org.jooq.generated.enums.NotificationChannelEnum;

/**
 * Enum representing the channels through which notifications can be delivered.
 * Maps to the database notification_channel_enum for consistency.
 */
public enum NotificationChannel {
    /**
     * In-app notifications displayed within the application interface.
     * Available for both internal and external users.
     */
    IN_APP(NotificationChannelEnum.IN_APP),

    /**
     * Email notifications sent to the user's email address.
     * Available for both internal and external users.
     */
    EMAIL(NotificationChannelEnum.EMAIL);

    private final NotificationChannelEnum jooqEnum;

    NotificationChannel(NotificationChannelEnum jooqEnum) {
        this.jooqEnum = jooqEnum;
    }

    /**
     * Returns the corresponding jOOQ enum for database operations.
     */
    public NotificationChannelEnum toJooqEnum() {
        return jooqEnum;
    }

    /**
     * Converts from jOOQ enum to DTO enum.
     */
    public static NotificationChannel fromJooqEnum(NotificationChannelEnum jooqEnum) {
        if (jooqEnum == null) {
            return null;
        }

        for (NotificationChannel channel : values()) {
            if (channel.jooqEnum == jooqEnum) {
                return channel;
            }
        }

        throw new IllegalArgumentException("Unknown NotificationChannelEnum: " + jooqEnum);
    }
}
