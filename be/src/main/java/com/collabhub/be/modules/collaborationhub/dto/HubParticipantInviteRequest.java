package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * Request DTO for inviting participants to a collaboration hub.
 * Contains a list of participants to invite with their respective types and roles.
 */
public class HubParticipantInviteRequest {

    @NotEmpty(message = "At least one participant must be specified")
    @Size(max = 50, message = "Cannot invite more than 50 participants at once")
    @Valid
    private List<ParticipantInviteItem> participants;

    public HubParticipantInviteRequest() {}

    public HubParticipantInviteRequest(List<ParticipantInviteItem> participants) {
        this.participants = participants;
    }

    public List<ParticipantInviteItem> getParticipants() {
        return participants;
    }

    public void setParticipants(List<ParticipantInviteItem> participants) {
        this.participants = participants;
    }

    @Override
    public String toString() {
        return "HubParticipantInviteRequest{" +
                "participants=" + participants +
                '}';
    }
}
