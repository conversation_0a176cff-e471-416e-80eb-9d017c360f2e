package com.collabhub.be.modules.chat.repository;

import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static org.jooq.generated.Tables.CHAT_CHANNEL_PARTICIPANTS;

// Note: This will use jOOQ generated classes once they're available
// For now, using manual queries until jOOQ regeneration

/**
 * Repository for ChatChannelParticipants entity using jOOQ for database operations.
 * Provides type-safe database operations for chat channel participant management.
 */
@Repository
public class ChatChannelParticipantRepositoryImpl {

    private final DSLContext dsl;

    public ChatChannelParticipantRepositoryImpl(DSLContext dsl) {
        this.dsl = dsl;
    }

    /**
     * Adds participants to a chat channel.
     *
     * @param channelId the channel ID
     * @param participantIds the participant IDs to add
     * @param addedByParticipantId the participant ID who is adding them
     */
    public void addParticipantsToChannel(Long channelId, List<Long> participantIds, Long addedByParticipantId) {
        LocalDateTime now = LocalDateTime.now();
        
        for (Long participantId : participantIds) {
            dsl.insertInto(CHAT_CHANNEL_PARTICIPANTS)
                .set(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID, channelId)
                .set(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID, participantId)
                .set(CHAT_CHANNEL_PARTICIPANTS.ADDED_BY_PARTICIPANT_ID, addedByParticipantId)
                .set(CHAT_CHANNEL_PARTICIPANTS.ADDED_AT, now)
                .set(CHAT_CHANNEL_PARTICIPANTS.CREATED_AT, now)
                .onConflict(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID, CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID)
                .doNothing()
                .execute();
        }
    }

    /**
     * Removes participants from a chat channel.
     *
     * @param channelId the channel ID
     * @param participantIds the participant IDs to remove
     */
    public void removeParticipantsFromChannel(Long channelId, List<Long> participantIds) {
        dsl.deleteFrom(CHAT_CHANNEL_PARTICIPANTS)
                .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(channelId))
                .and(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID.in(participantIds))
                .execute();
    }

    /**
     * Gets all participant IDs for a channel.
     *
     * @param channelId the channel ID
     * @return list of participant IDs
     */
    public List<Long> getChannelParticipantIds(Long channelId) {
        return dsl.select(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID)
                .from(CHAT_CHANNEL_PARTICIPANTS)
                .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(channelId))
                .fetch(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID);
    }

    /**
     * Checks if a participant has access to a channel.
     *
     * @param channelId the channel ID
     * @param participantId the participant ID
     * @return true if participant has access
     */
    public boolean hasChannelAccess(Long channelId, Long participantId) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(CHAT_CHANNEL_PARTICIPANTS)
                        .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(channelId))
                        .and(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID.eq(participantId))
        );
    }

    /**
     * Gets the count of participants in a channel.
     *
     * @param channelId the channel ID
     * @return participant count
     */
    public long getChannelParticipantCount(Long channelId) {
        return dsl.selectCount()
                .from(CHAT_CHANNEL_PARTICIPANTS)
                .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(channelId))
                .fetchOne(0, Long.class);
    }

    /**
     * Removes all participants from a channel (used when deleting channel).
     *
     * @param channelId the channel ID
     */
    public void removeAllParticipantsFromChannel(Long channelId) {
        dsl.deleteFrom(CHAT_CHANNEL_PARTICIPANTS)
                .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(channelId))
                .execute();
    }
}
