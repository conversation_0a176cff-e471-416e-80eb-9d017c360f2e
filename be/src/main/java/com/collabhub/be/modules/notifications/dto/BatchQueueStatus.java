package com.collabhub.be.modules.notifications.dto;

/**
 * Enumeration representing the processing status of notifications in the batch queue.
 * 
 * <p>This enum defines the lifecycle states of batched notifications from initial queuing
 * through final processing or failure. Each status represents a specific stage in the
 * batch processing pipeline with clear transitions between states.</p>
 * 
 * <h3>Status Transitions:</h3>
 * <pre>
 * PENDING → PROCESSING → SENT (success path)
 *         ↘ PROCESSING → FAILED → PENDING (retry path)
 *                      ↘ FAILED (final failure after max retries)
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public enum BatchQueueStatus {
    
    /**
     * Initial status when a notification is first queued for batching.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>Notification is waiting to be processed</li>
     *   <li>Can transition to PROCESSING when batch processor picks it up</li>
     *   <li>Eligible for batch processing based on batch window timing</li>
     * </ul>
     * 
     * <p><strong>Next States:</strong> PROCESSING</p>
     * 
     * @see #PROCESSING
     */
    PENDING("PENDING", "Notification is queued and waiting for batch processing"),
    
    /**
     * Status indicating a notification is currently being processed.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>Notification has been picked up by the batch processor</li>
     *   <li>Prevents duplicate processing by other instances</li>
     *   <li>Should transition to SENT or FAILED within processing timeout</li>
     * </ul>
     * 
     * <p><strong>Next States:</strong> SENT, FAILED</p>
     * 
     * @see #SENT
     * @see #FAILED
     */
    PROCESSING("PROCESSING", "Notification is currently being processed by batch processor"),
    
    /**
     * Final success status indicating the notification was successfully delivered.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>Email was successfully sent to the recipient</li>
     *   <li>Terminal state - no further processing needed</li>
     *   <li>Eligible for cleanup after retention period</li>
     * </ul>
     * 
     * <p><strong>Next States:</strong> None (terminal state)</p>
     */
    SENT("SENT", "Notification was successfully delivered via email"),
    
    /**
     * Status indicating processing failed but may be eligible for retry.
     * 
     * <p><strong>Characteristics:</strong></p>
     * <ul>
     *   <li>Processing encountered an error (network, email service, etc.)</li>
     *   <li>May transition back to PENDING for retry if under retry limit</li>
     *   <li>Becomes terminal state after maximum retry attempts</li>
     * </ul>
     * 
     * <p><strong>Next States:</strong> PENDING (for retry), or terminal after max retries</p>
     * 
     * @see #PENDING
     */
    FAILED("FAILED", "Notification processing failed, may be retried");
    
    private final String databaseValue;
    private final String description;
    
    /**
     * Constructs a BatchQueueStatus with the specified database value and description.
     * 
     * @param databaseValue the string value stored in the database
     * @param description human-readable description of this status
     */
    BatchQueueStatus(String databaseValue, String description) {
        this.databaseValue = databaseValue;
        this.description = description;
    }
    
    /**
     * Returns the database representation of this status.
     * 
     * <p>This value is used when storing the status in the database and should
     * match the CHECK constraint values in the notification_batch_queue table.</p>
     * 
     * @return the database string value for this status
     */
    public String getDatabaseValue() {
        return databaseValue;
    }
    
    /**
     * Returns a human-readable description of this status.
     * 
     * @return descriptive text explaining what this status means
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Converts a database string value to the corresponding BatchQueueStatus enum.
     * 
     * <p>This method provides safe conversion from database values to enum constants,
     * with proper error handling for invalid values.</p>
     * 
     * @param databaseValue the string value from the database
     * @return the corresponding BatchQueueStatus enum constant
     * @throws IllegalArgumentException if the database value doesn't match any enum constant
     * 
     * @example
     * <pre>
     * BatchQueueStatus status = BatchQueueStatus.fromDatabaseValue("PENDING");
     * // Returns BatchQueueStatus.PENDING
     * </pre>
     */
    public static BatchQueueStatus fromDatabaseValue(String databaseValue) {
        if (databaseValue == null) {
            throw new IllegalArgumentException("Database value cannot be null");
        }
        
        for (BatchQueueStatus status : values()) {
            if (status.databaseValue.equals(databaseValue)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("Unknown batch queue status: " + databaseValue);
    }
    
    /**
     * Checks if this status represents a terminal state (no further processing possible).
     * 
     * @return true if this is a terminal status (SENT), false otherwise
     */
    public boolean isTerminal() {
        return this == SENT;
    }
    
    /**
     * Checks if this status represents a failed state that may be eligible for retry.
     * 
     * @return true if this status is FAILED, false otherwise
     */
    public boolean isFailedState() {
        return this == FAILED;
    }
    
    /**
     * Checks if this status represents an active processing state.
     * 
     * @return true if this status is PROCESSING, false otherwise
     */
    public boolean isProcessing() {
        return this == PROCESSING;
    }
    
    /**
     * Checks if this status represents a pending state ready for processing.
     * 
     * @return true if this status is PENDING, false otherwise
     */
    public boolean isPending() {
        return this == PENDING;
    }
}
