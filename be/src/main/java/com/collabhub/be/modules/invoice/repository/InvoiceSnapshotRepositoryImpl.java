package com.collabhub.be.modules.invoice.repository;

import com.collabhub.be.modules.invoice.model.InvoiceSnapshotEntity;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.jooq.impl.DSL.*;

/**
 * Repository implementation for InvoiceSnapshot using jOOQ.
 * Provides database operations for deduplicated snapshot storage.
 * 
 * Note: This implementation uses manual table/field references until jOOQ classes are regenerated.
 */
@Repository
public class InvoiceSnapshotRepositoryImpl implements InvoiceSnapshotRepository {

    private final DSLContext dsl;

    // Manual table and field references (will be replaced with generated jOOQ classes after migration)
    private static final String TABLE_NAME = "invoice_snapshots";
    private static final String ID_FIELD = "id";
    private static final String HASH_FIELD = "hash";
    private static final String ENTITY_TYPE_FIELD = "entity_type";
    private static final String SNAPSHOT_DATA_FIELD = "snapshot_data";
    private static final String CREATED_AT_FIELD = "created_at";

    public InvoiceSnapshotRepositoryImpl(DSLContext dsl) {
        this.dsl = dsl;
    }

    @Override
    public Optional<InvoiceSnapshotEntity> findByHash(String hash) {
        Record record = dsl.select()
                .from(table(TABLE_NAME))
                .where(field(HASH_FIELD).eq(hash))
                .fetchOne();

        return record != null ? Optional.of(mapRecordToEntity(record)) : Optional.empty();
    }

    @Override
    public InvoiceSnapshotEntity saveOrGetExisting(InvoiceSnapshotEntity snapshot) {
        // First try to find existing snapshot by hash
        Optional<InvoiceSnapshotEntity> existing = findByHash(snapshot.getHash());
        if (existing.isPresent()) {
            return existing.get();
        }

        // If not found, create new one
        return create(snapshot.getHash(), snapshot.getEntityType(), snapshot.getSnapshotData());
    }

    @Override
    public InvoiceSnapshotEntity create(String hash, InvoiceSnapshotEntity.EntityType entityType, JSONB snapshotData) {
        LocalDateTime now = LocalDateTime.now();
        
        Record record = dsl.insertInto(table(TABLE_NAME))
                .set(field(HASH_FIELD), hash)
                .set(field(ENTITY_TYPE_FIELD), entityType.name())
                .set(field(SNAPSHOT_DATA_FIELD), snapshotData)
                .set(field(CREATED_AT_FIELD), now)
                .returning()
                .fetchOne();

        if (record == null) {
            throw new RuntimeException("Failed to create invoice snapshot");
        }

        return mapRecordToEntity(record);
    }

    @Override
    public boolean existsByHash(String hash) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(table(TABLE_NAME))
                        .where(field(HASH_FIELD).eq(hash))
        );
    }

    /**
     * Maps a jOOQ Record to InvoiceSnapshotEntity.
     */
    private InvoiceSnapshotEntity mapRecordToEntity(Record record) {
        return new InvoiceSnapshotEntity(
                record.get(ID_FIELD, Long.class),
                record.get(HASH_FIELD, String.class),
                InvoiceSnapshotEntity.EntityType.valueOf(record.get(ENTITY_TYPE_FIELD, String.class)),
                record.get(SNAPSHOT_DATA_FIELD, JSONB.class),
                record.get(CREATED_AT_FIELD, LocalDateTime.class)
        );
    }
}
