package com.collabhub.be.modules.notifications.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * Production-grade response DTO for notification data.
 *
 * <p>This DTO contains all notification information for display in the UI with
 * comprehensive validation and proper schema documentation for TypeScript generation.
 * It provides strongly-typed access to notification data with clear field descriptions.</p>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Schema(description = "Notification response containing complete notification details with validation")
public class NotificationResponse {

    @NotNull(message = "Notification ID cannot be null")
    @Positive(message = "Notification ID must be positive")
    @Schema(description = "Unique notification identifier", example = "1", required = true)
    private Long id;

    @NotNull(message = "User ID cannot be null")
    @Positive(message = "User ID must be positive")
    @Schema(description = "User ID who should receive this notification", example = "123", required = true)
    private Long userId;

    @NotNull(message = "Notification type cannot be null")
    @Schema(description = "Type of notification", enumAsRef = true, required = true)
    private NotificationType type;

    @NotBlank(message = "Title cannot be blank")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    @Schema(description = "Short notification title", example = "New comment on your post", required = true)
    private String title;

    @NotBlank(message = "Message cannot be blank")
    @Size(max = 1000, message = "Message must not exceed 1000 characters")
    @Schema(description = "Full notification message", example = "John Doe commented on your post 'Summer Campaign'", required = true)
    private String message;

    @NotNull(message = "Status cannot be null")
    @Schema(description = "Read status of the notification", enumAsRef = true, required = true)
    private NotificationStatus status;

    @Positive(message = "Collaboration hub ID must be positive if provided")
    @Schema(description = "Related collaboration hub ID for deep linking", example = "456")
    private Long collaborationHubId;

    @Positive(message = "Post ID must be positive if provided")
    @Schema(description = "Related post ID for deep linking", example = "789")
    private Long postId;

    @Positive(message = "Comment ID must be positive if provided")
    @Schema(description = "Related comment ID for deep linking", example = "101")
    private Long commentId;

    @Positive(message = "Chat channel ID must be positive if provided")
    @Schema(description = "Related chat channel ID for deep linking", example = "202")
    private Long chatChannelId;

    @Positive(message = "Brief ID must be positive if provided")
    @Schema(description = "Related brief ID for deep linking", example = "303")
    private Long briefId;

    @NotNull(message = "Created at cannot be null")
    @Schema(description = "When the notification was created", example = "2024-01-15T10:30:00", required = true)
    private LocalDateTime createdAt;

    @Schema(description = "When the notification was read (null if unread)", example = "2024-01-15T10:35:00")
    private LocalDateTime readAt;

    // Constructors
    public NotificationResponse() {}

    public NotificationResponse(Long id, Long userId, NotificationType type, String title, String message,
                              NotificationStatus status, Long collaborationHubId, Long postId, Long commentId,
                              Long chatChannelId, Long briefId, LocalDateTime createdAt, LocalDateTime readAt) {
        this.id = id;
        this.userId = userId;
        this.type = type;
        this.title = title;
        this.message = message;
        this.status = status;
        this.collaborationHubId = collaborationHubId;
        this.postId = postId;
        this.commentId = commentId;
        this.chatChannelId = chatChannelId;
        this.briefId = briefId;
        this.createdAt = createdAt;
        this.readAt = readAt;
    }

    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public NotificationType getType() { return type; }
    public void setType(NotificationType type) { this.type = type; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public NotificationStatus getStatus() { return status; }
    public void setStatus(NotificationStatus status) { this.status = status; }

    public Long getCollaborationHubId() { return collaborationHubId; }
    public void setCollaborationHubId(Long collaborationHubId) { this.collaborationHubId = collaborationHubId; }

    public Long getPostId() { return postId; }
    public void setPostId(Long postId) { this.postId = postId; }

    public Long getCommentId() { return commentId; }
    public void setCommentId(Long commentId) { this.commentId = commentId; }

    public Long getChatChannelId() { return chatChannelId; }
    public void setChatChannelId(Long chatChannelId) { this.chatChannelId = chatChannelId; }

    public Long getBriefId() { return briefId; }
    public void setBriefId(Long briefId) { this.briefId = briefId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getReadAt() { return readAt; }
    public void setReadAt(LocalDateTime readAt) { this.readAt = readAt; }
}
