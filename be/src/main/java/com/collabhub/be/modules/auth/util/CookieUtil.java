package com.collabhub.be.modules.auth.util;

import com.collabhub.be.modules.auth.config.AuthProperties;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;
import java.util.Optional;

/**
 * Utility class for handling HTTP cookies in authentication flows.
 * Provides consistent cookie creation, retrieval, and deletion methods.
 */
@Component
public class CookieUtil {

    private final AuthProperties authProperties;

    public CookieUtil(AuthProperties authProperties) {
        this.authProperties = authProperties;
    }

    /**
     * Creates a refresh token cookie with security attributes.
     *
     * @param refreshToken the refresh token value
     * @return the configured cookie
     */
    public Cookie createRefreshTokenCookie(String refreshToken) {
        AuthProperties.Cookie cookieConfig = authProperties.getCookie();
        Duration refreshTokenTtl = authProperties.getRefreshToken().getTtl();
        
        Cookie cookie = new Cookie(cookieConfig.getRefreshTokenName(), refreshToken);
        cookie.setHttpOnly(cookieConfig.isHttpOnly());
        cookie.setSecure(cookieConfig.isSecure());
        cookie.setPath(cookieConfig.getPath());
        cookie.setMaxAge((int) refreshTokenTtl.toSeconds());
        
        // Set domain if configured
        if (cookieConfig.getDomain() != null && !cookieConfig.getDomain().isEmpty()) {
            cookie.setDomain(cookieConfig.getDomain());
        }
        
        return cookie;
    }

    /**
     * Adds a refresh token cookie to the HTTP response.
     *
     * @param response the HTTP response
     * @param refreshToken the refresh token value
     */
    public void addRefreshTokenCookie(HttpServletResponse response, String refreshToken) {
        Cookie cookie = createRefreshTokenCookie(refreshToken);
        
        // Add SameSite attribute manually since it's not supported in Cookie class
        String cookieHeader = buildCookieHeader(cookie);
        response.addHeader("Set-Cookie", cookieHeader);
    }

    /**
     * Retrieves the refresh token from the request cookies.
     *
     * @param request the HTTP request
     * @return the refresh token if found
     */
    public Optional<String> getRefreshTokenFromCookies(HttpServletRequest request) {
        if (request.getCookies() == null) {
            return Optional.empty();
        }
        
        String cookieName = authProperties.getCookie().getRefreshTokenName();
        
        return Arrays.stream(request.getCookies())
                .filter(cookie -> cookieName.equals(cookie.getName()))
                .map(Cookie::getValue)
                .filter(value -> value != null && !value.isEmpty())
                .findFirst();
    }

    /**
     * Clears the refresh token cookie by setting it to expire immediately.
     *
     * @param response the HTTP response
     */
    public void clearRefreshTokenCookie(HttpServletResponse response) {
        AuthProperties.Cookie cookieConfig = authProperties.getCookie();
        
        Cookie cookie = new Cookie(cookieConfig.getRefreshTokenName(), "");
        cookie.setHttpOnly(cookieConfig.isHttpOnly());
        cookie.setSecure(cookieConfig.isSecure());
        cookie.setPath(cookieConfig.getPath());
        cookie.setMaxAge(0); // Expire immediately
        
        // Set domain if configured
        if (cookieConfig.getDomain() != null && !cookieConfig.getDomain().isEmpty()) {
            cookie.setDomain(cookieConfig.getDomain());
        }
        
        // Add SameSite attribute manually
        String cookieHeader = buildCookieHeader(cookie);
        response.addHeader("Set-Cookie", cookieHeader);
    }

    /**
     * Builds a complete cookie header string including SameSite attribute.
     *
     * @param cookie the cookie to build header for
     * @return the complete cookie header string
     */
    private String buildCookieHeader(Cookie cookie) {
        StringBuilder header = new StringBuilder();
        
        header.append(cookie.getName()).append("=").append(cookie.getValue());
        
        if (cookie.getMaxAge() >= 0) {
            header.append("; Max-Age=").append(cookie.getMaxAge());
        }
        
        if (cookie.getPath() != null) {
            header.append("; Path=").append(cookie.getPath());
        }
        
        if (cookie.getDomain() != null) {
            header.append("; Domain=").append(cookie.getDomain());
        }
        
        if (cookie.getSecure()) {
            header.append("; Secure");
        }
        
        if (cookie.isHttpOnly()) {
            header.append("; HttpOnly");
        }
        
        // Add SameSite attribute
        String sameSite = authProperties.getCookie().getSameSite();
        if (sameSite != null && !sameSite.isEmpty()) {
            header.append("; SameSite=").append(sameSite);
        }
        
        return header.toString();
    }
}
