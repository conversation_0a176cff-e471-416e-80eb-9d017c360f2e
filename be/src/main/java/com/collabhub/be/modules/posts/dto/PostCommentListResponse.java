package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.collabhub.be.modules.chat.dto.MentionDto;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;
import java.util.List;

/**
 * Response DTO for paginated list of post comments.
 */
public class PostCommentListResponse {

    @NotNull
    private List<PostCommentItem> comments;

    @JsonProperty("total_count")
    private long totalCount;

    @JsonProperty("page_size")
    private int pageSize;

    @JsonProperty("current_page")
    private int currentPage;

    @JsonProperty("total_pages")
    private int totalPages;

    @JsonProperty("has_next")
    private boolean hasNext;

    @JsonProperty("has_previous")
    private boolean hasPrevious;

    public PostCommentListResponse() {}

    public PostCommentListResponse(List<PostCommentItem> comments, long totalCount, int pageSize, 
                                  int currentPage, int totalPages, boolean hasNext, boolean hasPrevious) {
        this.comments = comments;
        this.totalCount = totalCount;
        this.pageSize = pageSize;
        this.currentPage = currentPage;
        this.totalPages = totalPages;
        this.hasNext = hasNext;
        this.hasPrevious = hasPrevious;
    }

    public List<PostCommentItem> getComments() {
        return comments;
    }

    public void setComments(List<PostCommentItem> comments) {
        this.comments = comments;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public boolean isHasNext() {
        return hasNext;
    }

    public void setHasNext(boolean hasNext) {
        this.hasNext = hasNext;
    }

    public boolean isHasPrevious() {
        return hasPrevious;
    }

    public void setHasPrevious(boolean hasPrevious) {
        this.hasPrevious = hasPrevious;
    }

    /**
     * Nested class representing a comment item in the list.
     */
    public static class PostCommentItem {
        private Long id;
        private String content;
        private CommentAuthor author;

        @JsonProperty("created_at")
        private LocalDateTime createdAt;

        @JsonProperty("updated_at")
        private LocalDateTime updatedAt;

        @JsonProperty("can_edit")
        private boolean canEdit;

        @JsonProperty("can_delete")
        private boolean canDelete;

        private List<MentionDto> mentions;

        public PostCommentItem() {}

        public PostCommentItem(Long id, String content, CommentAuthor author, LocalDateTime createdAt,
                              LocalDateTime updatedAt, boolean canEdit, boolean canDelete) {
            this.id = id;
            this.content = content;
            this.author = author;
            this.createdAt = createdAt;
            this.updatedAt = updatedAt;
            this.canEdit = canEdit;
            this.canDelete = canDelete;
        }

        public PostCommentItem(Long id, String content, CommentAuthor author, LocalDateTime createdAt,
                              LocalDateTime updatedAt, boolean canEdit, boolean canDelete, List<MentionDto> mentions) {
            this.id = id;
            this.content = content;
            this.author = author;
            this.createdAt = createdAt;
            this.updatedAt = updatedAt;
            this.canEdit = canEdit;
            this.canDelete = canDelete;
            this.mentions = mentions;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public CommentAuthor getAuthor() {
            return author;
        }

        public void setAuthor(CommentAuthor author) {
            this.author = author;
        }

        public LocalDateTime getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
        }

        public LocalDateTime getUpdatedAt() {
            return updatedAt;
        }

        public void setUpdatedAt(LocalDateTime updatedAt) {
            this.updatedAt = updatedAt;
        }

        public boolean isCanEdit() {
            return canEdit;
        }

        public void setCanEdit(boolean canEdit) {
            this.canEdit = canEdit;
        }

        public boolean isCanDelete() {
            return canDelete;
        }

        public void setCanDelete(boolean canDelete) {
            this.canDelete = canDelete;
        }

        public List<MentionDto> getMentions() {
            return mentions;
        }

        public void setMentions(List<MentionDto> mentions) {
            this.mentions = mentions;
        }
    }

    /**
     * Nested class representing the comment author.
     */
    public static class CommentAuthor {
        private Long id;
        private String name;
        private String email;

        public CommentAuthor() {}

        public CommentAuthor(Long id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }

    @Override
    public String toString() {
        return "PostCommentListResponse{" +
                "comments=" + comments +
                ", totalCount=" + totalCount +
                ", pageSize=" + pageSize +
                ", currentPage=" + currentPage +
                ", totalPages=" + totalPages +
                ", hasNext=" + hasNext +
                ", hasPrevious=" + hasPrevious +
                '}';
    }
}
