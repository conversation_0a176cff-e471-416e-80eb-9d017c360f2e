package com.collabhub.be.modules.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request DTO for creating a custom chat channel.
 */
public class ChatChannelCreateRequest {

    @NotBlank(message = "Channel name is required")
    @Size(max = 255, message = "Channel name must not exceed 255 characters")
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    @NotNull(message = "Participant IDs are required")
    @Size(min = 1, message = "At least one participant must be selected")
    @JsonProperty("participant_ids")
    private List<Long> participantIds;

    public ChatChannelCreateRequest() {}

    public ChatChannelCreateRequest(String name, String description, List<Long> participantIds) {
        this.name = name;
        this.description = description;
        this.participantIds = participantIds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Long> getParticipantIds() {
        return participantIds;
    }

    public void setParticipantIds(List<Long> participantIds) {
        this.participantIds = participantIds;
    }

    @Override
    public String toString() {
        return "ChatChannelCreateRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", participantIds=" + participantIds +
                '}';
    }
}
