package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.collaborationhub.converter.CollaborationHubConverter;
import com.collabhub.be.modules.collaborationhub.converter.HubParticipantConverter;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.chat.service.ChatChannelService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.collabhub.be.modules.posts.constants.PostConstants.USER_NOT_PARTICIPANT_MESSAGE;

/**
 * Service for managing collaboration hubs and their participants.
 * Handles business logic, validation, and multi-tenancy.
 */
@Service
public class CollaborationHubService {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationHubService.class);

    // Error Messages
    private static final String HUB_NOT_FOUND_MESSAGE = "Hub not found with ID: ";
    private static final String HUB_NAME_EXISTS_MESSAGE = "Hub name already exists: ";

    // Log Messages
    private static final String CREATING_HUB_LOG = "Creating collaboration hub '{}' for account {} by user {}";
    private static final String CREATED_HUB_LOG = "Successfully created collaboration hub with ID {} for account {}";
    private static final String UPDATING_HUB_LOG = "Updating collaboration hub {} for account {} by user {}";
    private static final String UPDATED_HUB_LOG = "Successfully updated collaboration hub {} for account {}";
    private static final String DELETING_HUB_LOG = "Deleting collaboration hub {} for account {} by user {}";
    private static final String DELETED_HUB_LOG = "Successfully deleted collaboration hub {} for account {}";
    private static final String RETRIEVING_HUBS_LOG = "Retrieving collaboration hubs for account {} by user {} with filters: name='{}', brandId={}";
    private static final String RETRIEVING_HUB_DETAILS_LOG = "Retrieving collaboration hub details for hub {} by user {}";

    // Pagination Constants
    private static final int DEFAULT_PAGE_SIZE = 20;

    private final CollaborationHubRepositoryImpl hubRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationHubConverter hubConverter;
    private final HubParticipantConverter participantConverter;
    private final ChatChannelService chatChannelService;
    private final CollabHubPermissionService hubPermissionService;
    private final JwtClaimsService jwtClaimsService;

    public CollaborationHubService(CollaborationHubRepositoryImpl hubRepository,
                                 HubParticipantRepositoryImpl participantRepository,
                                 CollaborationHubConverter hubConverter,
                                 HubParticipantConverter participantConverter,
                                 ChatChannelService chatChannelService,
                                 CollabHubPermissionService hubPermissionService,
                                 JwtClaimsService jwtClaimsService) {
        this.hubRepository = hubRepository;
        this.participantRepository = participantRepository;
        this.hubConverter = hubConverter;
        this.participantConverter = participantConverter;
        this.chatChannelService = chatChannelService;
        this.hubPermissionService = hubPermissionService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Creates a new collaboration hub and automatically adds the creator as an admin participant.
     *
     * @param request the hub creation request
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID of the creator
     * @param userEmail the email of the creator
     * @param userDisplayName the display name of the creator
     * @return the created hub response
     */
    @Transactional
    public CollaborationHubResponse createHub(@Valid CollaborationHubCreateRequest request, Long accountId,
                                            Long userId, String userEmail, String userDisplayName) {
        logger.info(CREATING_HUB_LOG, request.getName(), accountId, userId);

        CollaborationHub hub = createHubEntity(request, accountId, userId);
        HubParticipant adminParticipant = createAdminParticipant(hub.getId(), userId, userEmail, userDisplayName);
        createGeneralChatChannel(hub.getId(), adminParticipant.getId());

        logger.info(CREATED_HUB_LOG, hub.getId(), accountId);

        return buildHubResponse(hub, HubParticipantRole.admin);
    }

    /**
     * Updates an existing collaboration hub.
     *
     * @param hubId the hub ID
     * @param request the update request
     * @return the updated hub response
     */
    @Transactional
    public CollaborationHubResponse updateHub(Long hubId, @Valid CollaborationHubUpdateRequest request) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.info(UPDATING_HUB_LOG, hubId, userContext.getAccountId(), userContext.getUserId());

        hubPermissionService.validateHubAdminAccess(hubId);
        CollaborationHub hub = findHubById(hubId);

        updateHubEntity(hub, request);

        logger.info(UPDATED_HUB_LOG, hubId, userContext.getAccountId());

        return getHubDetails(hubId);
    }

    /**
     * Gets detailed information about a collaboration hub.
     *
     * @param hubId the hub ID
     * @return the hub details
     */
    @Transactional(readOnly = true)
    public CollaborationHubResponse getHubDetails(Long hubId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug(RETRIEVING_HUB_DETAILS_LOG, hubId, userContext.getUserId());

        hubPermissionService.validateCanParticipantAccessHubContent(hubId);
        CollaborationHub hub = findHubById(hubId);
        HubParticipantRole userRole = getUserRoleInHub(hubId, userContext);
        String brandName = getBrandNameForHub(hubId);

        return hubConverter.toResponse(hub, brandName, userRole);
    }

    /**
     * Gets a paginated list of collaboration hubs for a user.
     *
     * @param pageRequest the pagination request
     * @param nameFilter optional name filter
     * @param brandId optional brand filter
     * @return paginated list of hubs
     */
    @Transactional(readOnly = true)
    public PageResponse<CollaborationHubListItemDto> getHubs(PageRequest pageRequest,
                                                            String nameFilter, Long brandId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug(RETRIEVING_HUBS_LOG, userContext.getAccountId(), userContext.getUserId(), nameFilter, brandId);

        int offset = pageRequest.getOffset();

        if (userContext.isExternalUser()) {
            return getHubsForExternalUser(pageRequest, userContext, nameFilter, brandId, offset);
        } else {
            return getHubsForInternalUser(pageRequest, userContext, nameFilter, brandId, offset);
        }
    }

    /**
     * Deletes a collaboration hub (admin only).
     *
     * @param hubId the hub ID
     */
    @Transactional
    public void deleteHub(Long hubId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.info(DELETING_HUB_LOG, hubId, userContext.getAccountId(), userContext.getUserId());

        hubPermissionService.validateHubAdminAccess(hubId);
        hubRepository.deleteById(hubId);

        logger.info(DELETED_HUB_LOG, hubId, userContext.getAccountId());
    }

    // ========================================
    // Data Access Helper Methods
    // ========================================

    /**
     * Finds a hub by ID with validation.
     */
    private CollaborationHub findHubById(Long hubId) {
        CollaborationHub hub = hubRepository.findById(hubId);
        if (hub == null) {
            throw new NotFoundException(ErrorCode.HUB_NOT_FOUND, HUB_NOT_FOUND_MESSAGE + hubId);
        }
        return hub;
    }

    /**
     * Gets the user's role in a hub.
     */
    private HubParticipantRole getUserRoleInHub(Long hubId, UserContext userContext) {
        HubParticipant participant = participantRepository.findByHubIdAndEmail(hubId, userContext.getEmail());
        if (participant == null || participant.getRemovedAt() != null) {
            throw new ForbiddenException(ErrorCode.HUB_ACCESS_DENIED, USER_NOT_PARTICIPANT_MESSAGE);
        }
        return participant.getRole();
    }

    /**
     * Gets the brand name for a hub.
     */
    private String getBrandNameForHub(Long hubId) {
        return hubRepository.getBrandNameForHub(hubId);
    }


    /**
     * Gets hubs for internal users with account-based filtering.
     */
    private PageResponse<CollaborationHubListItemDto> getHubsForInternalUser(PageRequest pageRequest,
                                                                           UserContext userContext,
                                                                           String nameFilter, Long brandId,
                                                                           int offset) {
        List<CollaborationHub> hubs = hubRepository.findHubsWithPagination(
                userContext.getAccountId(), userContext.getUserId(), nameFilter, brandId, offset, pageRequest.getSize());
        long totalCount = hubRepository.countHubsWithFilter(
                userContext.getAccountId(), userContext.getUserId(), nameFilter, brandId);
        List<CollaborationHubListItemDto> hubListItems = convertHubsToListItemsForInternalUser(hubs, userContext.getUserId());

        return new PageResponse<>(hubListItems, pageRequest, totalCount);
    }

    /**
     * Gets hubs for external users with email-based filtering.
     */
    private PageResponse<CollaborationHubListItemDto> getHubsForExternalUser(PageRequest pageRequest,
                                                                           UserContext userContext,
                                                                           String nameFilter, Long brandId,
                                                                           int offset) {
        List<CollaborationHub> hubs = hubRepository.findHubsByParticipantEmail(
                userContext.getEmail(), nameFilter, brandId, offset, pageRequest.getSize());
        long totalCount = hubRepository.countHubsByParticipantEmail(
                userContext.getEmail(), nameFilter, brandId);
        List<CollaborationHubListItemDto> hubListItems = convertHubsToListItemsForExternalUser(hubs, userContext.getEmail());

        return new PageResponse<>(hubListItems, pageRequest, totalCount);
    }



    /**
     * Converts hubs to list item DTOs for internal users with bulk loading to avoid N+1 queries.
     */
    private List<CollaborationHubListItemDto> convertHubsToListItemsForInternalUser(List<CollaborationHub> hubs, Long userId) {
        if (hubs == null || hubs.isEmpty()) {
            return List.of();
        }

        List<Long> hubIds = hubs.stream().map(CollaborationHub::getId).toList();
        Map<Long, String> brandNames = getBrandNamesForHubs(hubIds);
        Map<Long, HubParticipantRole> userRoles = getUserRolesInHubs(hubIds, userId);

        return hubs.stream()
                .map(hub -> hubConverter.toListItem(hub, brandNames.get(hub.getId()), userRoles.get(hub.getId())))
                .toList();
    }

    /**
     * Converts hubs to list item DTOs for external users with bulk loading to avoid N+1 queries.
     */
    private List<CollaborationHubListItemDto> convertHubsToListItemsForExternalUser(List<CollaborationHub> hubs, String email) {
        if (hubs == null || hubs.isEmpty()) {
            return List.of();
        }

        List<Long> hubIds = hubs.stream().map(CollaborationHub::getId).toList();
        Map<Long, String> brandNames = getBrandNamesForHubs(hubIds);
        Map<Long, HubParticipantRole> userRoles = getUserRolesByEmailInHubs(hubIds, email);

        return hubs.stream()
                .map(hub -> hubConverter.toListItem(hub, brandNames.get(hub.getId()), userRoles.get(hub.getId())))
                .toList();
    }



    /**
     * Bulk loads brand names for multiple hubs.
     */
    private Map<Long, String> getBrandNamesForHubs(List<Long> hubIds) {
        return hubRepository.getBrandNamesForHubs(hubIds);
    }

    /**
     * Bulk loads user roles for multiple hubs.
     */
    private Map<Long, HubParticipantRole> getUserRolesInHubs(List<Long> hubIds, Long userId) {
        return hubRepository.getUserRolesInHubs(hubIds, userId);
    }

    /**
     * Bulk loads user roles by email for multiple hubs (for external participants).
     */
    private Map<Long, HubParticipantRole> getUserRolesByEmailInHubs(List<Long> hubIds, String email) {
        return hubRepository.getUserRolesByEmailInHubs(hubIds, email);
    }

    // ========================================
    // Business Logic Helper Methods
    // ========================================

    /**
     * Creates the hub entity and persists it.
     */
    private CollaborationHub createHubEntity(CollaborationHubCreateRequest request, Long accountId, Long userId) {
        CollaborationHub hub = hubConverter.toCollaborationHub(request, accountId, userId);
        hubRepository.insert(hub);
        return hub;
    }

    /**
     * Creates admin participant for the hub creator.
     */
    private HubParticipant createAdminParticipant(Long hubId, Long userId, String userEmail, String userDisplayName) {
        HubParticipant adminParticipant = participantConverter.createInternalParticipant(
                hubId, userId, userEmail, userDisplayName, HubParticipantRole.admin);
        participantRepository.insert(adminParticipant);
        return adminParticipant;
    }

    /**
     * Creates general chat channel for the hub.
     */
    private void createGeneralChatChannel(Long hubId, Long createdByParticipantId) {
        chatChannelService.createGeneralChannelForHub(hubId, createdByParticipantId);
    }

    /**
     * Builds hub response with brand name lookup.
     */
    private CollaborationHubResponse buildHubResponse(CollaborationHub hub, HubParticipantRole userRole) {
        String brandName = getBrandNameForHub(hub.getId());
        return hubConverter.toResponse(hub, brandName, userRole);
    }

    /**
     * Updates hub entity with new data.
     */
    private void updateHubEntity(CollaborationHub hub, CollaborationHubUpdateRequest request) {
        hubConverter.updateCollaborationHub(hub, request);
        hubRepository.update(hub);
    }
}
