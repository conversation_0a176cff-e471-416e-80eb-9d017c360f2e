package com.collabhub.be.modules.auth.service;

import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.repository.UserRepository;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * Custom UserDetailsService implementation for Spring Security.
 * Loads user details from the database for authentication.
 */
@Service
@Transactional(readOnly = true)
public class CustomUserDetailsService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(CustomUserDetailsService.class);

    private final UserRepository userRepository;

    public CustomUserDetailsService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * Loads user details by email address for authentication.
     * 
     * @param email the user's email address
     * @return UserDetails for the authenticated user
     * @throws UsernameNotFoundException if user is not found or disabled
     */
    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        logger.debug("Loading user details for email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> {
                    logger.warn("User not found with email: {}", email);
                    return new UsernameNotFoundException("User not found with email: " + email);
                });

        if (!user.getEnabled()) {
            logger.warn("User account is disabled for email: {}", email);
            throw new UsernameNotFoundException("User account is disabled: " + email);
        }

        logger.debug("Successfully loaded user details for email: {}", email);
        
        return new CustomUserPrincipal(user);
    }

    /**
         * Custom UserDetails implementation that wraps our User entity.
         * This allows Spring Security to work with our domain model.
         */
        public record CustomUserPrincipal(User user) implements UserDetails {

        /**
             * Returns the User entity for access to custom properties.
             */
            @Override
            public User user() {
                return user;
            }

            @Override
            public Collection<? extends GrantedAuthority> getAuthorities() {
                Set<GrantedAuthority> authorities = new HashSet<>();

                // Add role as authority
                authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getRole()));

                // Add permissions as authorities
                Role.fromString(user.getRole()).getPermissions().forEach(permission ->
                        authorities.add(new SimpleGrantedAuthority(permission.getPermission()))
                );

                return authorities;
            }

            @Override
            public String getPassword() {
                return user.getPassword();
            }

            @Override
            public String getUsername() {
                return user.getEmail();
            }

            @Override
            public boolean isAccountNonExpired() {
                return true; // We don't implement account expiration
            }

            @Override
            public boolean isAccountNonLocked() {
                return true; // We don't implement account locking
            }

            @Override
            public boolean isCredentialsNonExpired() {
                return true; // We don't implement credential expiration
            }

            @Override
            public boolean isEnabled() {
                return user.getEnabled();
            }

            @Override
            public String toString() {
                return "CustomUserPrincipal{" +
                        "userId=" + user.getId() +
                        ", email='" + user.getEmail() + '\'' +
                        ", role=" + user.getRole() +
                        ", accountId=" + user.getAccountId() +
                        ", enabled=" + user.getEnabled() +
                        '}';
            }
        }
}
