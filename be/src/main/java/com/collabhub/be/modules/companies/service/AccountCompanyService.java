package com.collabhub.be.modules.companies.service;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.companies.constants.CompanyConstants;
import com.collabhub.be.modules.companies.converter.AccountCompanyConverter;
import com.collabhub.be.modules.companies.dto.AccountCompanyCreateRequest;
import com.collabhub.be.modules.companies.dto.AccountCompanyResponse;
import com.collabhub.be.modules.companies.dto.AccountCompanyUpdateRequest;
import com.collabhub.be.modules.companies.repository.AccountCompanyRepositoryImpl;
import com.collabhub.be.modules.companies.service.AccountCompanyValidationService;
import org.jooq.generated.tables.pojos.AccountCompany;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service layer for Account Company operations.
 * Handles business logic, validation, and multi-tenancy for account companies.
 */
@Service
@Transactional
public class AccountCompanyService {

    private static final Logger logger = LoggerFactory.getLogger(AccountCompanyService.class);

    private final AccountCompanyRepositoryImpl accountCompanyRepository;
    private final AccountCompanyConverter accountCompanyConverter;
    private final AccountCompanyValidationService validationService;

    public AccountCompanyService(AccountCompanyRepositoryImpl accountCompanyRepository,
                               AccountCompanyConverter accountCompanyConverter,
                               AccountCompanyValidationService validationService) {
        this.accountCompanyRepository = accountCompanyRepository;
        this.accountCompanyConverter = accountCompanyConverter;
        this.validationService = validationService;
    }

    /**
     * Retrieves all active account companies for the specified account.
     *
     * @param accountId the account ID for multi-tenancy
     * @return list of account company responses
     */
    @Transactional(readOnly = true)
    public List<AccountCompanyResponse> getAllAccountCompanies(Long accountId) {
        logger.debug("Retrieving all account companies for account: {}", accountId);

        List<AccountCompany> companies = accountCompanyRepository.findAllByAccountId(accountId);

        logger.debug("Found {} account companies for account: {}", companies.size(), accountId);

        return companies.stream()
                .map(accountCompanyConverter::toResponse)
                .collect(Collectors.toList());
    }

    /**
     * Retrieves a specific account company by ID.
     *
     * @param id the company ID
     * @param accountId the account ID for multi-tenancy
     * @return the account company response
     * @throws NotFoundException if the company is not found
     * @throws ForbiddenException if the company doesn't belong to the account
     */
    @Transactional(readOnly = true)
    public AccountCompanyResponse getAccountCompanyById(Long id, Long accountId) {
        logger.debug("Retrieving account company with ID: {} for account: {}", id, accountId);

        AccountCompany company = findCompanyByIdAndAccountId(id, accountId);

        logger.debug("Successfully retrieved account company: {}", company.getCompanyName());

        return accountCompanyConverter.toResponse(company);
    }

    /**
     * Creates a new account company.
     *
     * @param request the create request
     * @param accountId the account ID for multi-tenancy
     * @return the created account company response
     * @throws BadRequestException if validation fails
     */
    public AccountCompanyResponse createAccountCompany(AccountCompanyCreateRequest request, Long accountId) {
        logger.debug("Creating new account company for account: {}", accountId);

        // Validate the create request
        validationService.validateCreateRequest(request, accountId);

        // Create and persist the company
        AccountCompany accountCompany = createAndPersistCompany(request, accountId);

        logger.info("Successfully created account company: {} for account: {}",
                accountCompany.getCompanyName(), accountId);

        return accountCompanyConverter.toResponse(accountCompany);
    }

    /**
     * Creates and persists a new account company entity.
     */
    private AccountCompany createAndPersistCompany(AccountCompanyCreateRequest request, Long accountId) {
        // Convert request to entity
        AccountCompany accountCompany = accountCompanyConverter.toAccountCompany(request, accountId);

        // Create the company
        accountCompanyRepository.insert(accountCompany);

        return accountCompany;
    }

    /**
     * Updates an existing account company.
     *
     * @param id the company ID
     * @param request the update request
     * @param accountId the account ID for multi-tenancy
     * @return the updated account company response
     * @throws NotFoundException if the company is not found
     * @throws ForbiddenException if the company doesn't belong to the account
     * @throws BadRequestException if validation fails
     */
    public AccountCompanyResponse updateAccountCompany(Long id, AccountCompanyUpdateRequest request, Long accountId) {
        logger.debug("Updating account company with ID: {} for account: {}", id, accountId);

        // Validate the update request
        validationService.validateUpdateRequest(request, accountId);

        // Find and update the company
        AccountCompany updatedCompany = findAndUpdateCompany(id, request, accountId);

        logger.info("Successfully updated account company: {} for account: {}",
                   updatedCompany.getCompanyName(), accountId);

        return accountCompanyConverter.toResponse(updatedCompany);
    }

    /**
     * Finds and updates an existing account company.
     */
    private AccountCompany findAndUpdateCompany(Long id, AccountCompanyUpdateRequest request, Long accountId) {
        // Find existing company
        AccountCompany existingCompany = findCompanyByIdAndAccountId(id, accountId);

        // Update the company
        AccountCompany updatedCompany = accountCompanyConverter.updateAccountCompany(existingCompany, request);
        accountCompanyRepository.update(updatedCompany);

        return updatedCompany;
    }

    /**
     * Finds a company by ID and account ID, throwing NotFoundException if not found.
     */
    private AccountCompany findCompanyByIdAndAccountId(Long id, Long accountId) {
        return accountCompanyRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> {
                    logger.warn("Account company not found: ID={}, accountId={}", id, accountId);
                    return new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                            CompanyConstants.COMPANY_NOT_FOUND_MESSAGE);
                });
    }

    /**
     * Soft deletes an account company.
     *
     * @param id the company ID
     * @param accountId the account ID for multi-tenancy
     * @throws NotFoundException if the company is not found
     * @throws ForbiddenException if the company doesn't belong to the account
     */
    public void deleteAccountCompany(Long id, Long accountId) {
        logger.debug("Soft deleting account company with ID: {} for account: {}", id, accountId);

        boolean deleted = accountCompanyRepository.softDelete(id, accountId);

        if (!deleted) {
            logger.warn("Account company not found for deletion: ID={}, accountId={}", id, accountId);
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    CompanyConstants.COMPANY_NOT_FOUND_MESSAGE);
        }

        logger.info("Successfully soft deleted account company with ID: {} for account: {}", id, accountId);
    }
}
