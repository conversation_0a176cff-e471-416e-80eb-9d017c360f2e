package com.collabhub.be.modules.auth.constants;

/**
 * Constants for JWT claim names to ensure consistency between token generation and consumption.
 * Centralizes all JWT claim name definitions to prevent typos and maintain consistency.
 */
public final class JwtClaims {

    // Standard JWT claims
    public static final String SUBJECT = "sub";
    public static final String ISSUER = "iss";
    public static final String AUDIENCE = "aud";

    // Custom application claims
    public static final String EMAIL = "email";
    public static final String DISPLAY_NAME = "display_name";
    public static final String ROLE = "role";
    public static final String ACCOUNT_ID = "account_id";
    public static final String INTERNAL = "internal";
    public static final String EXTERNAL = "external";
    public static final String BRAND_CONTACT = "brand_contact";

    // External participant claims
    public static final String IS_EXTERNAL = "is_external";

    // Subject prefixes for different user types
    public static final String EXTERNAL_USER_SUBJECT_PREFIX = "ext_user_";

    // Private constructor to prevent instantiation
    private JwtClaims() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
