package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for creating a new collaboration hub.
 * Contains the required information to create a hub and automatically
 * add the creator as an admin participant.
 */
public class CollaborationHubCreateRequest {

    @NotBlank(message = "Hub name is required")
    @Size(max = 255, message = "Hub name must not exceed 255 characters")
    private String name;

    @NotNull(message = "Brand ID is required")
    private Long brandId;

    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;

    public CollaborationHubCreateRequest() {}

    public CollaborationHubCreateRequest(String name, Long brandId, String description) {
        this.name = name;
        this.brandId = brandId;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "CollaborationHubCreateRequest{" +
                "name='" + name + '\'' +
                ", brandId=" + brandId +
                ", description='" + description + '\'' +
                '}';
    }
}
