package com.collabhub.be.modules.bankdetails.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * Response DTO for bank details operations.
 * Contains all bank details information including metadata.
 */
public class BankDetailsResponse {

    @NotNull
    private Long id;

    @NotNull
    @JsonProperty("account_id")
    private Long accountId;

    @NotNull
    private String name;

    @JsonProperty("bank_name")
    private String bankName;

    private String iban;

    @JsonProperty("bic_swift")
    private String bicSwift;

    @NotNull
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @NotNull
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    public BankDetailsResponse() {
    }

    public BankDetailsResponse(Long id, Long accountId, String name, String bankName, 
                             String iban, String bicSwift, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.accountId = accountId;
        this.name = name;
        this.bankName = bankName;
        this.iban = iban;
        this.bicSwift = bicSwift;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getBicSwift() {
        return bicSwift;
    }

    public void setBicSwift(String bicSwift) {
        this.bicSwift = bicSwift;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "BankDetailsResponse{" +
                "id=" + id +
                ", accountId=" + accountId +
                ", name='" + name + '\'' +
                ", bankName='" + bankName + '\'' +
                ", iban='" + (iban != null ? "***" + iban.substring(Math.max(0, iban.length() - 4)) : null) + '\'' +
                ", bicSwift='" + bicSwift + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
