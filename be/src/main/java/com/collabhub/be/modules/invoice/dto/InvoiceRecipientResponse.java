package com.collabhub.be.modules.invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import org.jooq.generated.enums.RecipientSource;
import org.jooq.generated.enums.RecipientType;

import java.time.LocalDateTime;

/**
 * Response DTO for invoice recipients.
 * Contains delivery tracking information.
 */
public class InvoiceRecipientResponse {

    @NotNull
    private Long id;

    @NotNull
    private String email;

    @NotNull
    private RecipientType type;

    @NotNull
    private RecipientSource source;

    @JsonProperty("brand_contact_id")
    private Long brandContactId;

    @JsonProperty("first_sent_at")
    private LocalDateTime firstSentAt;

    @JsonProperty("last_sent_at")
    private LocalDateTime lastSentAt;

    @NotNull
    @JsonProperty("send_count")
    private Integer sendCount;

    public InvoiceRecipientResponse() {
    }

    public InvoiceRecipientResponse(Long id, String email, RecipientType type,
                                  RecipientSource source, Long brandContactId, LocalDateTime firstSentAt,
                                  LocalDateTime lastSentAt, Integer sendCount) {
        this.id = id;
        this.email = email;
        this.type = type;
        this.source = source;
        this.brandContactId = brandContactId;
        this.firstSentAt = firstSentAt;
        this.lastSentAt = lastSentAt;
        this.sendCount = sendCount;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public RecipientType getType() {
        return type;
    }

    public void setType(RecipientType type) {
        this.type = type;
    }

    public RecipientSource getSource() {
        return source;
    }

    public void setSource(RecipientSource source) {
        this.source = source;
    }

    public Long getBrandContactId() {
        return brandContactId;
    }

    public void setBrandContactId(Long brandContactId) {
        this.brandContactId = brandContactId;
    }

    public LocalDateTime getFirstSentAt() {
        return firstSentAt;
    }

    public void setFirstSentAt(LocalDateTime firstSentAt) {
        this.firstSentAt = firstSentAt;
    }

    public LocalDateTime getLastSentAt() {
        return lastSentAt;
    }

    public void setLastSentAt(LocalDateTime lastSentAt) {
        this.lastSentAt = lastSentAt;
    }

    public Integer getSendCount() {
        return sendCount;
    }

    public void setSendCount(Integer sendCount) {
        this.sendCount = sendCount;
    }

    @Override
    public String toString() {
        return "InvoiceRecipientResponse{" +
                "id=" + id +
                ", email='" + email + '\'' +
                ", type=" + type +
                ", source=" + source +
                ", brandContactId=" + brandContactId +
                ", firstSentAt=" + firstSentAt +
                ", lastSentAt=" + lastSentAt +
                ", sendCount=" + sendCount +
                '}';
    }
}
