package com.collabhub.be.modules.auth.model;

import java.util.Arrays;
import java.util.Set;
import java.util.EnumSet;
import java.util.stream.Collectors;

/**
 * User roles within the system.
 * Roles are scoped per account for multi-tenancy.
 * Each role has a set of permissions that define what actions the user can perform.
 */
public enum Role {
    /**
     * System administrator with full access to account resources.
     */
    ADMIN(Arrays.stream(Permission.values()).collect(Collectors.toSet())),

    /**
     * External participant with hub-scoped access permissions.
     * Used for external users (brand contacts, freelance creators) who access
     * collaboration hubs via magic links. Access is restricted to assigned hubs only.
     */
    EXTERNAL_PARTICIPANT(EnumSet.of(
            // Hub access (scoped to assigned hubs via JWT claims)
            Permission.HUB_READ,

            // Post access (own posts only for editing, enforced at service layer)
            Permission.POST_READ,
            Permission.POST_WRITE,
            Permission.POST_UPDATE,
            Permission.POST_COMMENT,

            // Chat access
            Permission.CHAT_READ,
            Permission.CHAT_WRITE,
            Permission.CHAT_CHANNEL_READ,

            // Brief access (read-only)
            Permission.BRIEF_READ,
            Permission.HUB_PARTICIPANT_READ,
            Permission.CONTENT_REVIEW
    ));

    private final Set<Permission> permissions;

    Role(Set<Permission> permissions) {
        this.permissions = EnumSet.copyOf(permissions);
    }

    /**
     * Returns the permissions associated with this role.
     */
    public Set<Permission> getPermissions() {
        return EnumSet.copyOf(permissions);
    }

    /**
     * Checks if this role has the specified permission.
     */
    public boolean hasPermission(Permission permission) {
        return permissions.contains(permission);
    }

    /**
     * Returns the role name as a string for database storage.
     */
    public String getRoleName() {
        return this.name();
    }
    
    /**
     * Creates a Role from a string value.
     * 
     * @param roleName the role name string
     * @return the corresponding Role enum
     * @throws IllegalArgumentException if the role name is invalid
     */
    public static Role fromString(String roleName) {
        if (roleName == null || roleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Role name cannot be null or empty");
        }
        
        try {
            return Role.valueOf(roleName.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid role: " + roleName, e);
        }
    }
}
