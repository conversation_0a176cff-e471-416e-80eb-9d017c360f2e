package com.collabhub.be.modules.notifications.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * Request DTO for updating notification preferences.
 * Contains a list of preference updates to be applied in a single transaction.
 */
public class NotificationPreferenceUpdateRequest {

    @Valid
    @NotEmpty
    private List<PreferenceUpdate> preferences;

    public NotificationPreferenceUpdateRequest() {}

    public NotificationPreferenceUpdateRequest(List<PreferenceUpdate> preferences) {
        this.preferences = preferences;
    }

    public List<PreferenceUpdate> getPreferences() {
        return preferences;
    }

    public void setPreferences(List<PreferenceUpdate> preferences) {
        this.preferences = preferences;
    }

    /**
     * Individual preference update within the batch request.
     */
    public static class PreferenceUpdate {

        @Schema(enumAsRef = true)
        @NotNull
        private NotificationType type;

        @Schema(enumAsRef = true)
        @NotNull
        private NotificationChannel channel;

        @NotNull
        private Boolean enabled;

        public PreferenceUpdate() {}

        public PreferenceUpdate(NotificationType type, NotificationChannel channel, Boolean enabled) {
            this.type = type;
            this.channel = channel;
            this.enabled = enabled;
        }

        public NotificationType getType() {
            return type;
        }

        public void setType(NotificationType type) {
            this.type = type;
        }

        public NotificationChannel getChannel() {
            return channel;
        }

        public void setChannel(NotificationChannel channel) {
            this.channel = channel;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }
    }
}
