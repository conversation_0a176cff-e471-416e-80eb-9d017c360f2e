package com.collabhub.be.modules.invoice.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.InvoiceRecipientDao;
import org.jooq.generated.tables.pojos.InvoiceRecipient;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.jooq.generated.Tables.INVOICE_RECIPIENT;

/**
 * Repository for InvoiceRecipient entity using jOOQ for database operations.
 * Provides type-safe database operations for invoice recipients.
 */
@Repository
public class InvoiceRecipientRepositoryImpl extends InvoiceRecipientDao {

    private final DSLContext dsl;

    public InvoiceRecipientRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all recipients for a specific invoice.
     *
     * @param invoiceId the invoice ID
     * @return list of invoice recipients
     */
    public List<InvoiceRecipient> findByInvoiceId(Long invoiceId) {
        return dsl.select()
                .from(INVOICE_RECIPIENT)
                .where(INVOICE_RECIPIENT.INVOICE_ID.eq(invoiceId))
                .orderBy(INVOICE_RECIPIENT.CREATED_AT.asc())
                .fetchInto(InvoiceRecipient.class);
    }

    /**
     * Finds all recipients for multiple invoices in a single query.
     * This method optimizes performance by avoiding N+1 queries when fetching recipients for multiple invoices.
     *
     * @param invoiceIds the list of invoice IDs
     * @return list of invoice recipients for all specified invoices
     */
    public List<InvoiceRecipient> findByInvoiceIds(List<Long> invoiceIds) {
        if (invoiceIds == null || invoiceIds.isEmpty()) {
            return List.of();
        }

        return dsl.select()
                .from(INVOICE_RECIPIENT)
                .where(INVOICE_RECIPIENT.INVOICE_ID.in(invoiceIds))
                .orderBy(INVOICE_RECIPIENT.INVOICE_ID.asc(), INVOICE_RECIPIENT.CREATED_AT.asc())
                .fetchInto(InvoiceRecipient.class);
    }

    /**
     * Deletes all recipients for a specific invoice.
     * Used when updating invoice recipients - delete all and re-insert.
     *
     * @param invoiceId the invoice ID
     * @return number of deleted recipients
     */
    public int deleteByInvoiceId(Long invoiceId) {
        return dsl.deleteFrom(INVOICE_RECIPIENT)
                .where(INVOICE_RECIPIENT.INVOICE_ID.eq(invoiceId))
                .execute();
    }

    /**
     * Batch inserts invoice recipients.
     *
     * @param recipients the list of invoice recipients to insert
     */
    @Transactional
    public void batchInsert(List<InvoiceRecipient> recipients) {
        if (recipients == null || recipients.isEmpty()) {
            return;
        }
        insert(recipients);
    }

    /**
     * Updates send tracking information for a recipient.
     *
     * @param invoiceId the invoice ID
     * @param email the recipient email
     * @param sentAt the timestamp when sent
     */
    public void updateSendTracking(Long invoiceId, String email, LocalDateTime sentAt) {
        dsl.update(INVOICE_RECIPIENT)
                .set(INVOICE_RECIPIENT.LAST_SENT_AT, sentAt)
                .set(INVOICE_RECIPIENT.SEND_COUNT, INVOICE_RECIPIENT.SEND_COUNT.plus(1))
                .where(INVOICE_RECIPIENT.INVOICE_ID.eq(invoiceId))
                .and(INVOICE_RECIPIENT.EMAIL.eq(email))
                .execute();

        // Set first_sent_at if this is the first send
        dsl.update(INVOICE_RECIPIENT)
                .set(INVOICE_RECIPIENT.FIRST_SENT_AT, sentAt)
                .where(INVOICE_RECIPIENT.INVOICE_ID.eq(invoiceId))
                .and(INVOICE_RECIPIENT.EMAIL.eq(email))
                .and(INVOICE_RECIPIENT.FIRST_SENT_AT.isNull())
                .execute();
    }
}
