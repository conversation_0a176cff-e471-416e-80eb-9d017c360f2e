package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import org.jooq.generated.enums.ReviewStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for post details.
 */
public class PostResponse {

    private Long id;
    private String caption;

    @JsonProperty("media_uris")
    private List<MediaItem> mediaUris;

    @JsonProperty("review_status")
    private ReviewStatus reviewStatus;

    @JsonProperty("reviewer_notes")
    private String reviewerNotes;

    private PostCreator creator;

    @JsonProperty("assigned_reviewers")
    private List<PostReviewer> assignedReviewers;

    @NotNull
    private PostPermissions permissions;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    public PostResponse() {}

    public PostResponse(Long id, String caption, List<MediaItem> mediaUris, ReviewStatus reviewStatus,
                       String reviewerNotes, PostCreator creator, List<PostReviewer> assignedReviewers,
                       PostPermissions permissions, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.caption = caption;
        this.mediaUris = mediaUris;
        this.reviewStatus = reviewStatus;
        this.reviewerNotes = reviewerNotes;
        this.creator = creator;
        this.assignedReviewers = assignedReviewers;
        this.permissions = permissions;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public List<MediaItem> getMediaUris() {
        return mediaUris;
    }

    public void setMediaUris(List<MediaItem> mediaUris) {
        this.mediaUris = mediaUris;
    }

    public ReviewStatus getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(ReviewStatus reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getReviewerNotes() {
        return reviewerNotes;
    }

    public void setReviewerNotes(String reviewerNotes) {
        this.reviewerNotes = reviewerNotes;
    }

    public PostCreator getCreator() {
        return creator;
    }

    public void setCreator(PostCreator creator) {
        this.creator = creator;
    }

    public List<PostReviewer> getAssignedReviewers() {
        return assignedReviewers;
    }

    public void setAssignedReviewers(List<PostReviewer> assignedReviewers) {
        this.assignedReviewers = assignedReviewers;
    }

    public PostPermissions getPermissions() {
        return permissions;
    }

    public void setPermissions(PostPermissions permissions) {
        this.permissions = permissions;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * Nested class representing the post creator.
     */
    public static class PostCreator {
        private Long id;
        private String name;
        private String email;

        public PostCreator() {}

        public PostCreator(Long id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }

    /**
     * Nested class representing a post reviewer with detailed review information.
     */
    public static class PostReviewer {
        private Long id;
        private String name;
        private String email;
        private ReviewStatus status;

        @JsonProperty("review_notes")
        private String reviewNotes;

        @JsonProperty("assigned_at")
        private LocalDateTime assignedAt;

        @JsonProperty("reviewed_at")
        private LocalDateTime reviewedAt;

        @JsonProperty("is_pending")
        private boolean isPending;

        public PostReviewer() {}

        public PostReviewer(Long id, String name, String email, ReviewStatus status) {
            this.id = id;
            this.name = name;
            this.email = email;
            this.status = status;
            this.isPending = status == ReviewStatus.pending;
        }

        public PostReviewer(Long id, String name, String email, ReviewStatus status,
                           String reviewNotes, LocalDateTime assignedAt, LocalDateTime reviewedAt) {
            this.id = id;
            this.name = name;
            this.email = email;
            this.status = status;
            this.reviewNotes = reviewNotes;
            this.assignedAt = assignedAt;
            this.reviewedAt = reviewedAt;
            this.isPending = status == ReviewStatus.pending;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public ReviewStatus getStatus() {
            return status;
        }

        public void setStatus(ReviewStatus status) {
            this.status = status;
            this.isPending = status == ReviewStatus.pending;
        }

        public String getReviewNotes() {
            return reviewNotes;
        }

        public void setReviewNotes(String reviewNotes) {
            this.reviewNotes = reviewNotes;
        }

        public LocalDateTime getAssignedAt() {
            return assignedAt;
        }

        public void setAssignedAt(LocalDateTime assignedAt) {
            this.assignedAt = assignedAt;
        }

        public LocalDateTime getReviewedAt() {
            return reviewedAt;
        }

        public void setReviewedAt(LocalDateTime reviewedAt) {
            this.reviewedAt = reviewedAt;
        }



        public boolean isPending() {
            return isPending;
        }

        public void setPending(boolean pending) {
            isPending = pending;
        }
    }

    /**
     * Nested class representing user permissions for the post.
     */
    public static class PostPermissions {
        @JsonProperty("can_edit")
        private boolean canEdit;

        @JsonProperty("can_review")
        private boolean canReview;

        @JsonProperty("can_comment")
        private boolean canComment;


        public PostPermissions() {}

        public PostPermissions(boolean canEdit, boolean canReview, boolean canComment) {
            this.canEdit = canEdit;
            this.canReview = canReview;
            this.canComment = canComment;
        }

        public boolean isCanEdit() {
            return canEdit;
        }

        public void setCanEdit(boolean canEdit) {
            this.canEdit = canEdit;
        }

        public boolean isCanReview() {
            return canReview;
        }

        public void setCanReview(boolean canReview) {
            this.canReview = canReview;
        }

        public boolean isCanComment() {
            return canComment;
        }

        public void setCanComment(boolean canComment) {
            this.canComment = canComment;
        }
    }

    @Override
    public String toString() {
        return "PostResponse{" +
                "id=" + id +
                ", caption='" + caption + '\'' +
                ", mediaUris=" + mediaUris +
                ", reviewStatus=" + reviewStatus +
                ", reviewerNotes='" + reviewerNotes + '\'' +
                ", creator=" + creator +
                ", assignedReviewers=" + assignedReviewers +
                ", permissions=" + permissions +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
