package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request DTO for generating multiple presigned upload URLs in a single request.
 * Supports batch upload operations for improved performance.
 */
public class BatchPresignedUploadRequest {

    @NotNull(message = "Files list is required")
    @NotEmpty(message = "At least one file must be specified")
    @Size(max = 10, message = "Maximum 10 files can be uploaded at once")
    @Valid
    private List<FileUploadRequest> files;

    public BatchPresignedUploadRequest() {}

    public BatchPresignedUploadRequest(List<FileUploadRequest> files) {
        this.files = files;
    }

    public List<FileUploadRequest> getFiles() {
        return files;
    }

    public void setFiles(List<FileUploadRequest> files) {
        this.files = files;
    }

    /**
     * Nested class representing individual file upload request.
     */
    public static class FileUploadRequest {
        
        @NotNull(message = "File name is required")
        @JsonProperty("file_name")
        private String fileName;

        @NotNull(message = "Content type is required")
        @JsonProperty("content_type")
        private String contentType;

        @JsonProperty("max_file_size")
        private Long maxFileSize;

        public FileUploadRequest() {}

        public FileUploadRequest(String fileName, String contentType, Long maxFileSize) {
            this.fileName = fileName;
            this.contentType = contentType;
            this.maxFileSize = maxFileSize;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getContentType() {
            return contentType;
        }

        public void setContentType(String contentType) {
            this.contentType = contentType;
        }

        public Long getMaxFileSize() {
            return maxFileSize;
        }

        public void setMaxFileSize(Long maxFileSize) {
            this.maxFileSize = maxFileSize;
        }
    }

    @Override
    public String toString() {
        return "BatchPresignedUploadRequest{" +
                "files=" + files +
                '}';
    }
}
