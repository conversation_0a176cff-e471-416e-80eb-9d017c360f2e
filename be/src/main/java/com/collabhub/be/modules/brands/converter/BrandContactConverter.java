package com.collabhub.be.modules.brands.converter;

import com.collabhub.be.modules.brands.dto.BrandContactRequest;
import com.collabhub.be.modules.brands.dto.BrandContactResponse;
import org.jooq.generated.tables.pojos.BrandContact;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Converter class for mapping between Brand Contact DTOs and jOOQ POJOs.
 * Handles conversion between different representations of brand contact data.
 */
@Component
public class BrandContactConverter {

    /**
     * Converts a request DTO to a jOOQ POJO for database insertion.
     *
     * @param request the request DTO
     * @param brandId the brand ID for the contact
     * @param accountId the account ID for multi-tenancy
     * @return the jOOQ POJO ready for database insertion
     */
    public BrandContact toBrandContact(BrandContactRequest request, Long brandId, Long accountId) {
        if (request == null) {
            return null;
        }

        BrandContact contact = new BrandContact();
        contact.setBrandId(brandId);
        contact.setAccountId(accountId);
        contact.setName(request.getName());
        contact.setEmail(request.getEmail());
        contact.setNotes(request.getNotes());
        contact.setCreatedAt(LocalDateTime.now());
        contact.setUpdatedAt(LocalDateTime.now());

        return contact;
    }

    /**
     * Updates an existing jOOQ POJO with data from a request DTO.
     *
     * @param contact the existing jOOQ POJO to update
     * @param request the request DTO
     * @return the updated jOOQ POJO
     */
    public BrandContact updateBrandContact(BrandContact contact, BrandContactRequest request) {
        if (contact == null || request == null) {
            return contact;
        }

        contact.setName(request.getName());
        contact.setEmail(request.getEmail());
        contact.setNotes(request.getNotes());
        contact.setUpdatedAt(LocalDateTime.now());

        return contact;
    }

    /**
     * Converts a jOOQ POJO to a response DTO for API responses.
     *
     * @param contact the jOOQ POJO
     * @return the response DTO
     */
    public BrandContactResponse toResponse(BrandContact contact) {
        if (contact == null) {
            return null;
        }

        return new BrandContactResponse(
                contact.getId(),
                contact.getBrandId(),
                contact.getAccountId(),
                contact.getName(),
                contact.getEmail(),
                contact.getNotes(),
                contact.getCreatedAt(),
                contact.getUpdatedAt()
        );
    }
}
