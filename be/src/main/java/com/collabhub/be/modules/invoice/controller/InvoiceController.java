package com.collabhub.be.modules.invoice.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.invoice.dto.*;
import org.jooq.generated.enums.InvoiceStatus;
import com.collabhub.be.modules.invoice.service.InvoiceService;
import com.collabhub.be.modules.invoice.service.InvoicePdfService;
import com.collabhub.be.modules.invoice.service.InvoiceEmailService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * REST controller for Invoice operations.
 * Handles CRUD operations for invoices with multi-tenancy and permission-based authorization.
 */
@RestController
@RequestMapping("/api/invoices")
@Tag(name = "Invoices", description = "Invoice management operations")
public class InvoiceController {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceController.class);

    private final InvoiceService invoiceService;
    private final InvoicePdfService pdfService;
    private final InvoiceEmailService emailService;
    private final JwtClaimsService jwtClaimsService;

    public InvoiceController(InvoiceService invoiceService,
                           InvoicePdfService pdfService,
                           InvoiceEmailService emailService,
                           JwtClaimsService jwtClaimsService) {
        this.invoiceService = invoiceService;
        this.pdfService = pdfService;
        this.emailService = emailService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Retrieves all invoices for the authenticated user's account.
     *
     * @param status optional status filter
     * @param fromDate optional start date filter
     * @param toDate optional end date filter
     * @param page page number (0-based)
     * @param size page size (default 20, max 100)
     * @param jwt the JWT token containing user information
     * @return page of invoices
     */
    @GetMapping
    @PreAuthorize("hasAuthority('invoice:read')")
    @Operation(summary = "Get all invoices", description = "Retrieves all invoices for the account with optional filtering")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoices retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<PageResponse<InvoiceListItemDto>> getAllInvoices(
            @Parameter(description = "Filter by invoice status") @RequestParam(required = false) InvoiceStatus status,
            @Parameter(description = "Filter from date (inclusive)") @RequestParam(required = false) LocalDate fromDate,
            @Parameter(description = "Filter to date (inclusive)") @RequestParam(required = false) LocalDate toDate,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)") @RequestParam(defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving invoices for account: {} with filters - status: {}, fromDate: {}, toDate: {}",
                    userContext.getAccountId(), status, fromDate, toDate);

        PageRequest pageRequest = PageRequest.of(page, size);
        PageResponse<InvoiceListItemDto> invoices = invoiceService.getInvoiceListItems(
                userContext.getAccountId(), status, fromDate, toDate, pageRequest);

        logger.debug("Successfully retrieved {} invoices for account: {}",
                    invoices.getNumberOfElements(), userContext.getAccountId());

        return ResponseEntity.ok(invoices);
    }

    /**
     * Generates the next invoice number for the account.
     *
     * @param jwt the JWT token containing user information
     * @return the next invoice number response
     */
    @GetMapping("/next-number")
    @PreAuthorize("hasAuthority('invoice:read')")
    @Operation(summary = "Generate next invoice number", description = "Generates the next invoice number based on existing invoices")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Next invoice number generated successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<NextInvoiceNumberResponse> getNextInvoiceNumber(
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Generating next invoice number for account: {}", userContext.getAccountId());

        NextInvoiceNumberResponse response = invoiceService.generateNextInvoiceNumber(userContext.getAccountId());

        logger.debug("Successfully generated next invoice number: {} for account: {}",
                    response.getNextInvoiceNumber(), userContext.getAccountId());

        return ResponseEntity.ok(response);
    }

    /**
     * Retrieves a specific invoice by ID.
     *
     * @param id the invoice ID
     * @param jwt the JWT token containing user information
     * @return the invoice
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('invoice:read')")
    @Operation(summary = "Get invoice by ID", description = "Retrieves a specific invoice by its ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    public ResponseEntity<InvoiceResponse> getInvoiceById(
            @Parameter(description = "Invoice ID") @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving invoice with ID: {} for account: {}", id, userContext.getAccountId());

        InvoiceResponse invoice = invoiceService.getInvoiceById(id, userContext.getAccountId());

        logger.debug("Successfully retrieved invoice: {} for account: {}", 
                    invoice.getInvoiceNumber(), userContext.getAccountId());

        return ResponseEntity.ok(invoice);
    }

    /**
     * Creates a new invoice.
     *
     * @param request the create request
     * @param jwt the JWT token containing user information
     * @return the created invoice
     */
    @PostMapping
    @PreAuthorize("hasAuthority('invoice:write')")
    @Operation(summary = "Create invoice", description = "Creates a new invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Invoice created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<InvoiceResponse> createInvoice(
            @Valid @RequestBody InvoiceCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Creating new invoice for account: {}", userContext.getAccountId());

        InvoiceResponse createdInvoice = invoiceService.createInvoice(request, userContext.getAccountId());

        logger.info("Successfully created invoice: {} for account: {}", 
                   createdInvoice.getInvoiceNumber(), userContext.getAccountId());

        return ResponseEntity.status(HttpStatus.CREATED).body(createdInvoice);
    }

    /**
     * Updates an existing invoice.
     *
     * @param id the invoice ID
     * @param request the update request
     * @param jwt the JWT token containing user information
     * @return the updated invoice
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('invoice:write')")
    @Operation(summary = "Update invoice", description = "Updates an existing invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    public ResponseEntity<InvoiceResponse> updateInvoice(
            @Parameter(description = "Invoice ID") @PathVariable Long id,
            @Valid @RequestBody InvoiceUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Updating invoice with ID: {} for account: {}", id, userContext.getAccountId());

        InvoiceResponse updatedInvoice = invoiceService.updateInvoice(id, request, userContext.getAccountId());

        logger.info("Successfully updated invoice: {} for account: {}", 
                   updatedInvoice.getInvoiceNumber(), userContext.getAccountId());

        return ResponseEntity.ok(updatedInvoice);
    }

    /**
     * Deletes an invoice (soft delete).
     *
     * @param id the invoice ID
     * @param jwt the JWT token containing user information
     * @return no content response
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('invoice:delete')")
    @Operation(summary = "Delete invoice", description = "Soft deletes an invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Invoice deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    public ResponseEntity<Void> deleteInvoice(
            @Parameter(description = "Invoice ID") @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Deleting invoice with ID: {} for account: {}", id, userContext.getAccountId());

        invoiceService.deleteInvoice(id, userContext.getAccountId());

        logger.info("Successfully deleted invoice with ID: {} for account: {}", id, userContext.getAccountId());

        return ResponseEntity.noContent().build();
    }

    /**
     * Updates invoice status.
     *
     * @param id the invoice ID
     * @param request the status update request
     * @param jwt the JWT token containing user information
     * @return the updated invoice
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasAuthority('invoice:write')")
    @Operation(summary = "Update invoice status", description = "Updates the status of an invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice status updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    public ResponseEntity<InvoiceResponse> updateInvoiceStatus(
            @Parameter(description = "Invoice ID") @PathVariable Long id,
            @Valid @RequestBody InvoiceStatusUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Updating status of invoice with ID: {} to {} for account: {}", 
                    id, request.getStatus(), userContext.getAccountId());

        InvoiceResponse updatedInvoice = invoiceService.updateInvoiceStatus(
                id, request.getStatus(), userContext.getAccountId());

        logger.info("Successfully updated status of invoice: {} to {} for account: {}", 
                   updatedInvoice.getInvoiceNumber(), request.getStatus(), userContext.getAccountId());

        return ResponseEntity.ok(updatedInvoice);
    }

    /**
     * Generates and downloads PDF for an invoice.
     *
     * @param id the invoice ID
     * @param template optional template name (default, branded, minimal)
     * @param jwt the JWT token containing user information
     * @return PDF file as byte array
     */
    @GetMapping("/{id}/pdf")
    @PreAuthorize("hasAuthority('invoice:read')")
    @Operation(summary = "Download invoice PDF", description = "Generates and downloads PDF for an invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "PDF generated successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    public ResponseEntity<byte[]> downloadInvoicePdf(
            @Parameter(description = "Invoice ID") @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Generating PDF for invoice ID: {} for account: {}",
                    id, userContext.getAccountId());

        // Generate PDF (service handles validation and generation)
        byte[] pdfBytes = pdfService.generateInvoicePdfWithValidation(id, userContext.getAccountId());

        logger.info("Successfully generated PDF for invoice ID: {} ({} bytes)",
                   id, pdfBytes.length);

        return ResponseEntity.ok()
                .header("Content-Type", InvoiceConstants.PDF_CONTENT_TYPE)
                .header("Content-Disposition", "attachment; filename=\"" + InvoiceConstants.PDF_FILENAME_PREFIX + id + InvoiceConstants.PDF_FILENAME_EXTENSION + "\"")
                .body(pdfBytes);
    }

    /**
     * Sends invoice email to all recipients.
     *
     * @param id the invoice ID
     * @param force whether to force send even if already sent
     * @param jwt the JWT token containing user information
     * @return success response
     */
    @PostMapping("/{id}/send")
    @PreAuthorize("hasAuthority('invoice:write')")
    @Operation(summary = "Send invoice email", description = "Sends invoice email to all recipients with PDF attachment")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice sent successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request, send failed, or duplicate send attempt"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    public ResponseEntity<InvoiceEmailResponse> sendInvoiceEmail(
            @Parameter(description = "Invoice ID") @PathVariable Long id,
            @Parameter(description = "Force send even if already sent") @RequestParam(defaultValue = "false") boolean force,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Sending invoice email for ID: {} (force: {}) for account: {}", id, force, userContext.getAccountId());

        // Get invoice data
        InvoiceResponse invoice = invoiceService.getInvoiceById(id, userContext.getAccountId());

        // Send email
        boolean success = emailService.sendInvoiceEmail(invoice, userContext.getAccountId(), force);

        if (success) {
            logger.info("Successfully sent invoice email for: {} to {} recipients",
                       invoice.getInvoiceNumber(), invoice.getRecipients().size());

            return ResponseEntity.ok(new InvoiceEmailResponse(
                    true,
                    "Invoice sent successfully to all recipients",
                    invoice.getRecipients().size()
            ));
        } else {
            logger.warn("Failed to send invoice email for: {}", invoice.getInvoiceNumber());

            return ResponseEntity.badRequest().body(new InvoiceEmailResponse(
                    false,
                    "Failed to send invoice to some or all recipients",
                    0
            ));
        }
    }

    /**
     * Retrieves delivery logs for a specific invoice.
     *
     * @param id the invoice ID
     * @param jwt the JWT token containing user information
     * @return list of delivery logs
     */
    @GetMapping("/{id}/delivery-logs")
    @PreAuthorize("hasAuthority('invoice:read')")
    @Operation(summary = "Get invoice delivery logs", description = "Retrieves delivery logs for a specific invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Delivery logs retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    public ResponseEntity<List<InvoiceDeliveryLogResponse>> getInvoiceDeliveryLogs(
            @Parameter(description = "Invoice ID") @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving delivery logs for invoice ID: {} for account: {}", id, userContext.getAccountId());

        // Verify invoice exists and user has access
        invoiceService.getInvoiceById(id, userContext.getAccountId());

        List<InvoiceDeliveryLogResponse> deliveryLogs = invoiceService.getInvoiceDeliveryLogs(id, userContext.getAccountId());

        logger.debug("Successfully retrieved {} delivery logs for invoice: {} for account: {}",
                    deliveryLogs.size(), id, userContext.getAccountId());

        return ResponseEntity.ok(deliveryLogs);
    }


}
