package com.collabhub.be.modules.notifications.dto;

import org.jooq.generated.enums.NotificationStatusEnum;

/**
 * Enum representing the status of a notification.
 * Maps to the database notification_status_enum for consistency.
 */
public enum NotificationStatus {
    /**
     * Notification has not been read by the user.
     */
    UNREAD(NotificationStatusEnum.UNREAD),

    /**
     * Notification has been read by the user.
     */
    READ(NotificationStatusEnum.READ);

    private final NotificationStatusEnum jooqEnum;

    NotificationStatus(NotificationStatusEnum jooqEnum) {
        this.jooqEnum = jooqEnum;
    }

    /**
     * Returns the corresponding jOOQ enum for database operations.
     */
    public NotificationStatusEnum toJooqEnum() {
        return jooqEnum;
    }

    /**
     * Converts from jOOQ enum to DTO enum.
     */
    public static NotificationStatus fromJooqEnum(NotificationStatusEnum jooqEnum) {
        if (jooqEnum == null) {
            return null;
        }
        
        for (NotificationStatus status : values()) {
            if (status.jooqEnum == jooqEnum) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("Unknown NotificationStatusEnum: " + jooqEnum);
    }
}
