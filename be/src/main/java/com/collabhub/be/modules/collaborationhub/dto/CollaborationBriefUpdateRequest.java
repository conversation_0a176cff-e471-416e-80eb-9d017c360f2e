package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for updating an existing collaboration brief.
 * Contains the fields that can be updated for a brief.
 * All briefs are visible to all participants within the hub.
 */
public class CollaborationBriefUpdateRequest {

    @NotBlank(message = "Brief title is required")
    @Size(max = 255, message = "Brief title must not exceed 255 characters")
    private String title;

    @Size(max = 10000, message = "Brief body must not exceed 10000 characters")
    private String body;

    public CollaborationBriefUpdateRequest() {}

    public CollaborationBriefUpdateRequest(String title, String body) {
        this.title = title;
        this.body = body;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    @Override
    public String toString() {
        return "CollaborationBriefUpdateRequest{" +
                "title='" + title + '\'' +
                ", body='" + (body != null ? body.substring(0, Math.min(body.length(), 50)) + "..." : null) + '\'' +
                '}';
    }
}
