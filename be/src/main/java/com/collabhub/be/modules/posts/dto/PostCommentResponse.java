package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.collabhub.be.modules.chat.dto.MentionDto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for post comment details.
 */
public class PostCommentResponse {

    private Long id;

    @JsonProperty("post_id")
    private Long postId;

    private String content;

    private CommentAuthor author;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    private CommentPermissions permissions;

    private List<MentionDto> mentions;

    public PostCommentResponse() {}

    public PostCommentResponse(Long id, Long postId, String content, CommentAuthor author,
                              LocalDateTime createdAt, LocalDateTime updatedAt, CommentPermissions permissions) {
        this.id = id;
        this.postId = postId;
        this.content = content;
        this.author = author;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.permissions = permissions;
    }

    public PostCommentResponse(Long id, Long postId, String content, CommentAuthor author,
                              LocalDateTime createdAt, LocalDateTime updatedAt, CommentPermissions permissions,
                              List<MentionDto> mentions) {
        this.id = id;
        this.postId = postId;
        this.content = content;
        this.author = author;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.permissions = permissions;
        this.mentions = mentions;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public CommentAuthor getAuthor() {
        return author;
    }

    public void setAuthor(CommentAuthor author) {
        this.author = author;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public CommentPermissions getPermissions() {
        return permissions;
    }

    public void setPermissions(CommentPermissions permissions) {
        this.permissions = permissions;
    }

    public List<MentionDto> getMentions() {
        return mentions;
    }

    public void setMentions(List<MentionDto> mentions) {
        this.mentions = mentions;
    }

    /**
     * Nested class representing the comment author.
     */
    public static class CommentAuthor {
        private Long id;
        private String name;
        private String email;

        public CommentAuthor() {}

        public CommentAuthor(Long id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }

    /**
     * Nested class representing comment permissions.
     */
    public static class CommentPermissions {
        @JsonProperty("can_edit")
        private boolean canEdit;

        @JsonProperty("can_delete")
        private boolean canDelete;

        public CommentPermissions() {}

        public CommentPermissions(boolean canEdit, boolean canDelete) {
            this.canEdit = canEdit;
            this.canDelete = canDelete;
        }

        public boolean isCanEdit() {
            return canEdit;
        }

        public void setCanEdit(boolean canEdit) {
            this.canEdit = canEdit;
        }

        public boolean isCanDelete() {
            return canDelete;
        }

        public void setCanDelete(boolean canDelete) {
            this.canDelete = canDelete;
        }
    }

    @Override
    public String toString() {
        return "PostCommentResponse{" +
                "id=" + id +
                ", postId=" + postId +
                ", content='" + content + '\'' +
                ", author=" + author +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", permissions=" + permissions +
                '}';
    }
}
