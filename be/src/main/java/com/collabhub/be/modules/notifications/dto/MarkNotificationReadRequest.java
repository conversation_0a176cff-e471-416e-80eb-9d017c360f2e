package com.collabhub.be.modules.notifications.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * Production-grade request DTO for marking a notification as read.
 *
 * <p>This DTO provides strongly-typed validation for notification read operations,
 * ensuring that only valid notification IDs are processed and providing clear
 * error messages for invalid requests.</p>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Schema(description = "Request to mark a notification as read with validation")
public class MarkNotificationReadRequest {

    @NotNull(message = "Notification ID cannot be null")
    @Positive(message = "Notification ID must be positive")
    @Schema(description = "Notification ID to mark as read", example = "123", required = true)
    private Long notificationId;

    // Constructors
    public MarkNotificationReadRequest() {}

    public MarkNotificationReadRequest(Long notificationId) {
        this.notificationId = notificationId;
    }

    // Getters and setters
    public Long getNotificationId() {
        return notificationId;
    }

    public void setNotificationId(Long notificationId) {
        this.notificationId = notificationId;
    }
}
