package com.collabhub.be.modules.auth.dto;

import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.model.Permission;
import java.util.Set;

/**
 * User context extracted from JWT token containing all relevant user information.
 * Provides a structured way to access user claims without directly accessing JWT.
 */
public class UserContext {

    private final Long userId;
    private final String email;
    private final String displayName;
    private final Role role;
    private final Long accountId;
    private final UserType userType;

    /**
     * Creates a new user context for internal users.
     * Internal users have userId and accountId.
     */
    public UserContext(Long userId, String email, String displayName, Role role, Long accountId) {
        if (userId == null || accountId == null) {
            throw new IllegalArgumentException("Internal users must have userId and accountId");
        }
        this.userId = userId;
        this.email = email;
        this.displayName = displayName;
        this.role = role;
        this.accountId = accountId;
        this.userType = UserType.INTERNAL;
    }

    /**
     * Creates a new user context for external participants.
     * External participants have null userId and accountId, access is determined by email and hub ID.
     */
    public UserContext(String email, String displayName, Role role) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("External users must have email");
        }
        this.userId = null;
        this.email = email;
        this.displayName = displayName;
        this.role = role;
        this.accountId = null;
        this.userType = UserType.EXTERNAL_PARTICIPANT;
    }

    public Long getUserId() {
        return userId;
    }

    public String getEmail() {
        return email;
    }

    public String getDisplayName() {
        return displayName;
    }

    public Role getRole() {
        return role;
    }

    public Long getAccountId() {
        return accountId;
    }

    public UserType getUserType() {
        return userType;
    }

    /**
     * @deprecated Use getUserType() == UserType.INTERNAL instead
     */
    @Deprecated
    public boolean isInternal() {
        return userType == UserType.INTERNAL;
    }

    /**
     * Checks if this user is an internal user.
     */
    public boolean isInternalUser() {
        return userType == UserType.INTERNAL;
    }

    /**
     * Checks if this user is an external participant.
     */
    public boolean isExternalUser() {
        return userType == UserType.EXTERNAL_PARTICIPANT || userType == UserType.EXTERNAL_ACCOUNTANT;
    }

    /**
     * Returns the permissions associated with this user's role.
     */
    public Set<Permission> getPermissions() {
        return role != null ? role.getPermissions() : Set.of();
    }



    /**
     * @deprecated Use isExternalUser() instead
     */
    @Deprecated
    public boolean isExternalParticipant() {
        return isExternalUser();
    }

    @Override
    public String toString() {
        return "UserContext{" +
                "userId=" + userId +
                ", email='" + email + '\'' +
                ", displayName='" + displayName + '\'' +
                ", role=" + role +
                ", accountId=" + accountId +
                ", userType=" + userType +
                '}';
    }


}
