package com.collabhub.be.modules.collaborationhub.controller;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.service.CollabHubPermissionService;
import com.collabhub.be.modules.collaborationhub.service.CollaborationHubService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.modules.media.dto.MediaDto;
import com.collabhub.be.modules.media.service.MediaService;
import com.collabhub.be.modules.posts.dto.*;
import com.collabhub.be.service.s3.S3StorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * REST controller for collaboration hub management.
 * Provides endpoints for CRUD operations on collaboration hubs with proper authorization.
 */
@RestController
@RequestMapping("/api/hubs")
@Tag(name = "Collaboration Hubs", description = "Collaboration hub management operations")
public class CollaborationHubController {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationHubController.class);

    private final CollaborationHubService hubService;
    private final JwtClaimsService jwtClaimsService;
    private final S3StorageService s3StorageService;
    private final MediaService mediaService;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final CollabHubPermissionService hubPermissionService;

    public CollaborationHubController(CollaborationHubService hubService,
                                    JwtClaimsService jwtClaimsService,
                                    S3StorageService s3StorageService,
                                    MediaService mediaService,
                                    CollaborationHubRepositoryImpl hubRepository,
                                    CollabHubPermissionService hubPermissionService) {
        this.hubService = hubService;
        this.jwtClaimsService = jwtClaimsService;
        this.s3StorageService = s3StorageService;
        this.mediaService = mediaService;
        this.hubRepository = hubRepository;
        this.hubPermissionService = hubPermissionService;
    }

    /**
     * Creates a new collaboration hub.
     *
     * @param request the hub creation request
     * @param jwt the JWT token for authentication
     * @return the created hub response
     */
    @PostMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_WRITE.permission)")
    @Operation(summary = "Create a new collaboration hub",
               description = "Creates a new collaboration hub and automatically adds the creator as an admin participant")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Hub created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "409", description = "Hub name already exists")
    })
    public ResponseEntity<CollaborationHubResponse> createHub(
            @Valid @RequestBody CollaborationHubCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Creating new collaboration hub: {} for account: {} by user: {}", 
                   request.getName(), userContext.getAccountId(), userContext.getUserId());

        CollaborationHubResponse response = hubService.createHub(
                request, userContext.getAccountId(), userContext.getUserId(), userContext.getEmail(), userContext.getDisplayName());

        logger.info("Successfully created collaboration hub with ID: {} for account: {}", 
                   response.getId(), userContext.getAccountId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Gets a paginated list of collaboration hubs.
     *
     * @param name optional name filter (case-insensitive partial match)
     * @param brandId optional brand filter
     * @param page page number (0-based)
     * @param size page size (max 100)
     * @param jwt the JWT token for authentication
     * @return paginated list of hubs
     */
    @GetMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_READ.permission)")
    @Operation(summary = "Get collaboration hubs",
               description = "Retrieves a paginated list of collaboration hubs with optional filtering")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Hubs retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<PageResponse<CollaborationHubListItemDto>> getHubs(
            @Parameter(description = "Filter hubs by name (case-insensitive partial match)")
            @RequestParam(value = "name", required = false) String name,
            @Parameter(description = "Filter hubs by brand ID")
            @RequestParam(value = "brandId", required = false) Long brandId,
            @Parameter(description = "Page number (0-based)")
            @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)")
            @RequestParam(value = "size", defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving collaboration hubs with filters: name='{}', brandId={}, page={}, size={}",
                    name, brandId, page, size);

        PageRequest pageRequest = new PageRequest(page, size);
        PageResponse<CollaborationHubListItemDto> response = hubService.getHubs(
                pageRequest, name, brandId);

        logger.debug("Retrieved {} collaboration hubs", response.getContent().size());
        return ResponseEntity.ok(response);
    }

    /**
     * Gets detailed information about a specific collaboration hub.
     *
     * @param id the hub ID
     * @param jwt the JWT token for authentication
     * @return the hub details
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_READ.permission)")
    @Operation(summary = "Get collaboration hub details",
               description = "Retrieves detailed information about a specific collaboration hub")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Hub details retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Hub not found")
    })
    public ResponseEntity<CollaborationHubResponse> getHubDetails(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving collaboration hub details for hub: {}", id);

        CollaborationHubResponse response = hubService.getHubDetails(id);

        logger.debug("Retrieved collaboration hub details for hub: {}", id);
        return ResponseEntity.ok(response);
    }

    /**
     * Updates an existing collaboration hub.
     *
     * @param id the hub ID
     * @param request the update request
     * @param jwt the JWT token for authentication
     * @return the updated hub response
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_WRITE.permission)")
    @Operation(summary = "Update collaboration hub",
               description = "Updates an existing collaboration hub (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Hub updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Hub not found"),
        @ApiResponse(responseCode = "409", description = "Hub name already exists")
    })
    public ResponseEntity<CollaborationHubResponse> updateHub(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long id,
            @Valid @RequestBody CollaborationHubUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.info("Updating collaboration hub: {}", id);

        CollaborationHubResponse response = hubService.updateHub(id, request);

        logger.info("Successfully updated collaboration hub: {}", id);
        return ResponseEntity.ok(response);
    }

    /**
     * Deletes a collaboration hub.
     *
     * @param id the hub ID
     * @param jwt the JWT token for authentication
     * @return no content response
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_DELETE.permission)")
    @Operation(summary = "Delete collaboration hub",
               description = "Deletes a collaboration hub and all associated data (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Hub deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Hub not found")
    })
    public ResponseEntity<Void> deleteHub(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        logger.info("Deleting collaboration hub: {}", id);

        hubService.deleteHub(id);

        logger.info("Successfully deleted collaboration hub: {}", id);
        return ResponseEntity.noContent().build();
    }

    // ========================================
    // Media Upload Endpoints (Hub-Scoped)
    // ========================================

    @Operation(summary = "Generate presigned upload URL",
               description = "Generates a presigned URL for direct S3 upload with enforced constraints. " +
                           "Works for both internal users and external participants with hub access.")
    @PostMapping("/{hubId}/media/presigned-upload-url")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<PresignedUploadResponse> generatePresignedUploadUrl(
            @Parameter(description = "Hub ID", required = true) @PathVariable Long hubId,
            @Parameter(description = "File name") @RequestParam("fileName") String fileName,
            @Parameter(description = "Content type") @RequestParam("contentType") String contentType,
            @Parameter(description = "Max file size in bytes (optional)") @RequestParam(value = "maxFileSize", required = false) Long maxFileSize,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Generating presigned URL for file: {} with type: {} for hub {}",
                    fileName, contentType, hubId);

        // Validate hub access for both internal and external users
        hubPermissionService.validateCanParticipantAccessHubContent(hubId);

        // Extract account ID from hub instead of user context
        Long accountId = extractAccountIdFromHub(hubId);

        PresignedUploadResponse response = s3StorageService.generatePresignedUploadUrl(
                accountId, S3Properties.ResourceType.POSTS, fileName, contentType, maxFileSize);

        logger.debug("Successfully generated presigned URL for file: {} in hub {}", fileName, hubId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Generate batch presigned upload URLs",
               description = "Generates multiple presigned URLs for concurrent S3 uploads with enforced constraints. " +
                           "Works for both internal users and external participants with hub access.")
    @PostMapping("/{hubId}/media/batch-presigned-upload-urls")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<BatchPresignedUploadResponse> generateBatchPresignedUploadUrls(
            @Parameter(description = "Hub ID", required = true) @PathVariable Long hubId,
            @Parameter(description = "Batch upload request") @Valid @RequestBody BatchPresignedUploadRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Generating {} presigned URLs for hub {}",
                    request.getFiles().size(), hubId);

        // Validate hub access for both internal and external users
        hubPermissionService.validateCanParticipantAccessHubContent(hubId);

        // Extract account ID from hub instead of user context
        Long accountId = extractAccountIdFromHub(hubId);

        List<PresignedUploadResponse> uploadUrls = new ArrayList<>();

        for (BatchPresignedUploadRequest.FileUploadRequest fileRequest : request.getFiles()) {
            try {
                PresignedUploadResponse uploadUrl = s3StorageService.generatePresignedUploadUrl(
                        accountId,
                        S3Properties.ResourceType.POSTS,
                        fileRequest.getFileName(),
                        fileRequest.getContentType(),
                        fileRequest.getMaxFileSize()
                );
                uploadUrls.add(uploadUrl);
            } catch (Exception e) {
                logger.error("Failed to generate presigned URL for file: {} in hub {} - {}",
                           fileRequest.getFileName(), hubId, e.getMessage(), e);
                throw new BadRequestException(ErrorCode.INVALID_INPUT,
                    "Failed to generate presigned URL for file: " + fileRequest.getFileName());
            }
        }

        BatchPresignedUploadResponse response = new BatchPresignedUploadResponse(uploadUrls);
        logger.debug("Successfully generated {} presigned URLs for hub {}",
                    uploadUrls.size(), hubId);

        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Create media record after presigned upload",
               description = "Creates a media record after successful presigned URL upload. " +
                           "Works for both internal users and external participants with hub access.")
    @PostMapping("/{hubId}/media/create-record")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<FileUploadResponse> createMediaRecord(
            @Parameter(description = "Hub ID", required = true) @PathVariable Long hubId,
            @Parameter(description = "Media creation request") @Valid @RequestBody MediaRecordCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Creating media record for URL: {} in hub {}",
                    request.getFileUrl(), hubId);

        // Validate hub access for both internal and external users
        hubPermissionService.validateCanParticipantAccessHubContent(hubId);

        // Extract account ID from hub instead of user context
        Long accountId = extractAccountIdFromHub(hubId);

        try {
            // Create media record from the uploaded file URL
            MediaDto mediaDto = mediaService.createMediaFromUrl(
                    request.getFileUrl(),
                    request.getFilename(),
                    request.getFileSize(),
                    request.getMimeType(),
                    accountId
            );

            // Determine file type for response
            FileType type = FileType.fromMimeType(request.getMimeType());

            FileUploadResponse response = new FileUploadResponse(
                    mediaDto.getUrl(),
                    request.getFilename(),
                    request.getFileSize(),
                    request.getMimeType(),
                    type
            );

            logger.debug("Successfully created media record for URL: {} with ID: {} in hub {}",
                        request.getFileUrl(), mediaDto.getId(), hubId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to create media record for URL: {} in hub {} - {}",
                        request.getFileUrl(), hubId, e.getMessage());
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Failed to create media record: " + e.getMessage());
        }
    }

    @Operation(summary = "Validate uploaded file",
               description = "Validates that a file was uploaded successfully via presigned URL. " +
                           "Works for both internal users and external participants with hub access.")
    @PostMapping("/{hubId}/media/validate")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<FileValidationResponse> validateUploadedFile(
            @Parameter(description = "Hub ID", required = true) @PathVariable Long hubId,
            @Parameter(description = "File URL to validate") @RequestParam("fileUrl") String fileUrl,
            @Parameter(description = "Expected content type") @RequestParam(value = "contentType", required = false) String contentType,
            @Parameter(description = "Max file size") @RequestParam(value = "maxFileSize", required = false) Long maxFileSize,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Validating uploaded file: {} in hub {}", fileUrl, hubId);

        // Validate hub access for both internal and external users
        hubPermissionService.validateCanParticipantAccessHubContent(hubId);

        // Extract account ID from hub instead of user context
        Long accountId = extractAccountIdFromHub(hubId);

        FileValidationResponse response = s3StorageService.validateUploadedFileDetailed(
                fileUrl, accountId, contentType, maxFileSize);

        logger.debug("File validation result for {} in hub {}: {}", fileUrl, hubId, response.getValid());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Generate presigned download URL",
               description = "Generates a presigned URL for secure file download. " +
                           "Works for both internal users and external participants with hub access.")
    @PostMapping("/{hubId}/media/download-url")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_READ.permission)")
    public ResponseEntity<Map<String, String>> generatePresignedDownloadUrl(
            @Parameter(description = "Hub ID", required = true) @PathVariable Long hubId,
            @Parameter(description = "File URL to download") @RequestParam("fileUrl") String fileUrl,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Generating presigned download URL for file: {} in hub {}", fileUrl, hubId);

        // Validate hub access for both internal and external users
        hubPermissionService.validateCanParticipantAccessHubContent(hubId);

        // Extract account ID from hub instead of user context
        Long accountId = extractAccountIdFromHub(hubId);

        String presignedUrl = s3StorageService.generatePresignedDownloadUrl(fileUrl, accountId);

        Map<String, String> response = Map.of("downloadUrl", presignedUrl);
        logger.debug("Successfully generated presigned download URL for file: {} in hub {}", fileUrl, hubId);
        return ResponseEntity.ok(response);
    }

    // ========================================
    // Helper Methods
    // ========================================

    /**
     * Extracts account ID from hub for multi-tenancy support.
     * This allows both internal users and external participants to access media endpoints.
     */
    private Long extractAccountIdFromHub(Long hubId) {
        Long accountId = hubRepository.getAccountIdForHub(hubId);
        if (accountId == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Unable to determine account for hub: " + hubId);
        }
        return accountId;
    }
}
