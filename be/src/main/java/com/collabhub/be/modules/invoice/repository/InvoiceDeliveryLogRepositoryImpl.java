package com.collabhub.be.modules.invoice.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.InvoiceDeliveryLogDao;
import org.jooq.generated.tables.pojos.InvoiceDeliveryLog;
import org.springframework.stereotype.Repository;

import java.util.List;

import static org.jooq.generated.Tables.INVOICE_DELIVERY_LOG;

/**
 * Repository for InvoiceDeliveryLog entity using jOOQ for database operations.
 * Provides type-safe database operations for invoice delivery audit logs.
 */
@Repository
public class InvoiceDeliveryLogRepositoryImpl extends InvoiceDeliveryLogDao {

    private final DSLContext dsl;

    public InvoiceDeliveryLogRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all delivery logs for a specific invoice.
     *
     * @param invoiceId the invoice ID
     * @return list of delivery logs ordered by creation date (newest first)
     */
    public List<InvoiceDeliveryLog> findByInvoiceId(Long invoiceId) {
        return dsl.select()
                .from(INVOICE_DELIVERY_LOG)
                .where(INVOICE_DELIVERY_LOG.INVOICE_ID.eq(invoiceId))
                .orderBy(INVOICE_DELIVERY_LOG.CREATED_AT.desc())
                .fetchInto(InvoiceDeliveryLog.class);
    }

    /**
     * Finds delivery logs for a specific invoice and recipient email.
     *
     * @param invoiceId the invoice ID
     * @param recipientEmail the recipient email
     * @return list of delivery logs for the specific recipient
     */
    public List<InvoiceDeliveryLog> findByInvoiceIdAndRecipient(Long invoiceId, String recipientEmail) {
        return dsl.select()
                .from(INVOICE_DELIVERY_LOG)
                .where(INVOICE_DELIVERY_LOG.INVOICE_ID.eq(invoiceId))
                .and(INVOICE_DELIVERY_LOG.RECIPIENT_EMAIL.eq(recipientEmail))
                .orderBy(INVOICE_DELIVERY_LOG.CREATED_AT.desc())
                .fetchInto(InvoiceDeliveryLog.class);
    }

    /**
     * Counts successful deliveries for an invoice.
     *
     * @param invoiceId the invoice ID
     * @return count of successful deliveries
     */
    public long countSuccessfulDeliveries(Long invoiceId) {
        return dsl.selectCount()
                .from(INVOICE_DELIVERY_LOG)
                .where(INVOICE_DELIVERY_LOG.INVOICE_ID.eq(invoiceId))
                .and(INVOICE_DELIVERY_LOG.DELIVERY_STATUS.eq("sent"))
                .fetchOne(0, Long.class);
    }

    /**
     * Finds failed delivery attempts for an invoice.
     *
     * @param invoiceId the invoice ID
     * @return list of failed delivery logs
     */
    public List<InvoiceDeliveryLog> findFailedDeliveries(Long invoiceId) {
        return dsl.select()
                .from(INVOICE_DELIVERY_LOG)
                .where(INVOICE_DELIVERY_LOG.INVOICE_ID.eq(invoiceId))
                .and(INVOICE_DELIVERY_LOG.DELIVERY_STATUS.in("failed", "error"))
                .orderBy(INVOICE_DELIVERY_LOG.CREATED_AT.desc())
                .fetchInto(InvoiceDeliveryLog.class);
    }
}
