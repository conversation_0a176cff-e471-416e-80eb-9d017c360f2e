package com.collabhub.be.modules.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Response DTO for email verification.
 * Returns confirmation that email verification was successful.
 */
public class EmailVerificationResponse {

    private String message;
    
    private String email;
    
    @JsonProperty("account_enabled")
    private boolean accountEnabled;

    public EmailVerificationResponse() {
    }

    public EmailVerificationResponse(String message, String email, boolean accountEnabled) {
        this.message = message;
        this.email = email;
        this.accountEnabled = accountEnabled;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isAccountEnabled() {
        return accountEnabled;
    }

    public void setAccountEnabled(boolean accountEnabled) {
        this.accountEnabled = accountEnabled;
    }
}
