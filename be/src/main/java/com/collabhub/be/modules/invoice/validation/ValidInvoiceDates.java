package com.collabhub.be.modules.invoice.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Custom validation annotation for invoice date constraints.
 * Validates that:
 * - Issue date is not in the future
 * - Due date is on or after issue date
 */
@Documented
@Constraint(validatedBy = InvoiceDateValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidInvoiceDates {

    String message() default "Invalid invoice dates";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
