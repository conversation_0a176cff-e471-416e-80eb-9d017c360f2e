package com.collabhub.be.modules.brands.converter;

import com.collabhub.be.modules.brands.dto.BrandListItemDto;
import org.jooq.generated.tables.pojos.Brand;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Converter class for mapping Brand entities to lightweight list item DTOs.
 * Optimized for list view performance by excluding heavy fields and relationships.
 */
@Component
public class BrandListItemConverter {

    /**
     * Converts a jOOQ Brand POJO to a lightweight list item DTO.
     *
     * @param brand the jOOQ POJO
     * @return the lightweight list item DTO
     */
    public BrandListItemDto toListItem(Brand brand) {
        if (brand == null) {
            return null;
        }

        return new BrandListItemDto(
                brand.getId(),
                brand.getName(),
                brand.getCompanyName(),
                brand.getEmail(),
                brand.getPhone(),
                brand.getWebsite()
        );
    }

    /**
     * Converts a list of jOOQ Brand POJOs to lightweight list item DTOs.
     *
     * @param brands the list of jOOQ POJOs
     * @return the list of lightweight list item DTOs
     */
    public List<BrandListItemDto> toListItems(List<Brand> brands) {
        if (brands == null) {
            return List.of();
        }

        return brands.stream()
                .map(this::toListItem)
                .toList();
    }
}
