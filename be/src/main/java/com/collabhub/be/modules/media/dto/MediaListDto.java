package com.collabhub.be.modules.media.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

/**
 * Lightweight DTO for media file list operations.
 * Contains essential information for media listing without full metadata.
 */
public class MediaListDto {

    private Long id;

    @JsonProperty("original_filename")
    private String originalFilename;

    @JsonProperty("file_size_bytes")
    private Long fileSizeBytes;

    @JsonProperty("mime_type")
    private String mimeType;

    private String url; // Generated S3 URL for frontend consumption

    private String type; // "image" or "video" for frontend compatibility

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    public MediaListDto() {}

    public MediaListDto(Long id, String originalFilename, Long fileSizeBytes, String mimeType,
                       String url, String type, LocalDateTime createdAt) {
        this.id = id;
        this.originalFilename = originalFilename;
        this.fileSizeBytes = fileSizeBytes;
        this.mimeType = mimeType;
        this.url = url;
        this.type = type;
        this.createdAt = createdAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }

    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "MediaListDto{" +
                "id=" + id +
                ", originalFilename='" + originalFilename + '\'' +
                ", fileSizeBytes=" + fileSizeBytes +
                ", mimeType='" + mimeType + '\'' +
                ", url='" + url + '\'' +
                ", type='" + type + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
