package com.collabhub.be.modules.media.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.model.Permission;
import com.collabhub.be.modules.media.converter.MediaConverter;
import com.collabhub.be.modules.media.dto.MediaDto;
import com.collabhub.be.modules.media.dto.MediaListDto;
import com.collabhub.be.modules.media.repository.ChatMessageMediaRepositoryImpl;
import com.collabhub.be.modules.media.repository.MediaRepositoryImpl;
import com.collabhub.be.modules.media.repository.PostMediaRepositoryImpl;
import com.collabhub.be.service.s3.S3StorageService;
import org.jooq.generated.tables.pojos.Media;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service for media management operations.
 * Handles centralized media storage, account usage tracking, and file operations.
 * Provides secure file upload, validation, and multi-tenant access control.
 */
@Service
@Validated
public class MediaService {

    private static final Logger logger = LoggerFactory.getLogger(MediaService.class);

    // Constants
    private static final int DEFAULT_MAX_RETRIES = 3;
    private static final long DEFAULT_INITIAL_DELAY_MS = 1000L;
    private static final int EXPONENTIAL_BACKOFF_MULTIPLIER = 2;
    private static final String QUERY_PARAMETER_SEPARATOR = "?";
    private static final String PATH_SEPARATOR = "/";
    private static final String ACCOUNT_PATH_SEPARATOR = "/";

    // Error messages
    private static final String MEDIA_NOT_FOUND_MESSAGE = "Media not found: %d";
    private static final String MEDIA_FILE_NOT_FOUND_MESSAGE = "Media file not found or invalid: %s";
    private static final String INVALID_FILE_URL_MESSAGE = "Invalid file URL format or file does not belong to account: %s";
    private static final String BUCKET_NOT_FOUND_MESSAGE = "Bucket name not found in URL: %s";
    private static final String S3_KEY_NOT_FOUND_MESSAGE = "No S3 key found after bucket name in URL: %s";
    private static final String S3_KEY_SECURITY_VIOLATION_MESSAGE = "S3 key does not belong to account %d: %s";
    private static final String S3_KEY_EXTRACTION_ERROR_MESSAGE = "Failed to extract S3 key from URL: %s for account: %d - Error: %s";

    // Log messages
    private static final String UPLOADING_MEDIA_LOG = "Uploading media file: {} for account {}";
    private static final String UPLOAD_SUCCESS_LOG = "Successfully uploaded and stored media file: {} with ID: {}";
    private static final String CREATING_MEDIA_FROM_URL_LOG = "Creating media record from URL: {} for account {}";
    private static final String MEDIA_RECORD_EXISTS_LOG = "Media record already exists for S3 key: {}";
    private static final String MEDIA_CREATION_SUCCESS_LOG = "Successfully created media record from URL: {} with ID: {}";
    private static final String FILE_VALIDATION_SUCCESS_LOG = "File validation successful on attempt {} for: {}";
    private static final String FILE_VALIDATION_RETRY_LOG = "File validation failed on attempt {} for: {}, retrying in {}ms";
    private static final String FILE_VALIDATION_INTERRUPTED_LOG = "File validation interrupted for: {}";
    private static final String FILE_VALIDATION_ERROR_LOG = "File validation error on attempt {} for: {} - {}";
    private static final String FILE_VALIDATION_FINAL_FAILURE_LOG = "File validation failed after {} attempts for: {}";
    private static final String EXTRACTING_S3_KEY_LOG = "Extracting S3 key from URL: {} for account: {}";
    private static final String REMOVED_QUERY_PARAMS_LOG = "Removed query parameters, clean URL: {}";
    private static final String USING_BUCKET_LOG = "Using bucket name: {}";
    private static final String EXTRACTED_S3_KEY_LOG = "Extracted S3 key: {}";
    private static final String BUCKET_NOT_FOUND_WARN_LOG = "Bucket name '{}' not found in URL: {}";
    private static final String NO_KEY_FOUND_WARN_LOG = "No key found after bucket name in URL: {}";
    private static final String S3_KEY_SECURITY_WARN_LOG = "S3 key '{}' does not start with account ID '{}'";
    private static final String S3_KEY_EXTRACTION_ERROR_LOG = "Failed to extract S3 key from URL: {} for account: {} - Error: {}";

    private final MediaRepositoryImpl mediaRepository;
    private final PostMediaRepositoryImpl postMediaRepository;
    private final ChatMessageMediaRepositoryImpl chatMessageMediaRepository;
    private final MediaConverter mediaConverter;
    private final S3StorageService s3StorageService;

    public MediaService(MediaRepositoryImpl mediaRepository,
                       PostMediaRepositoryImpl postMediaRepository,
                       ChatMessageMediaRepositoryImpl chatMessageMediaRepository,
                       MediaConverter mediaConverter,
                       S3StorageService s3StorageService) {
        this.mediaRepository = mediaRepository;
        this.postMediaRepository = postMediaRepository;
        this.chatMessageMediaRepository = chatMessageMediaRepository;
        this.mediaConverter = mediaConverter;
        this.s3StorageService = s3StorageService;
    }

    /**
     * Uploads a media file and creates a media record.
     */
    @Transactional
    public MediaDto uploadMedia(@NotNull MultipartFile file,
                               @NotNull Long accountId,
                               @NotBlank String resourceType) {
        logger.debug(UPLOADING_MEDIA_LOG, file.getOriginalFilename(), accountId);

        String fileUrl = uploadFileToS3(file, accountId, resourceType);
        String s3Key = extractS3KeyFromUrl(fileUrl, accountId);
        Media media = createMediaRecord(file, s3Key, accountId);

        logUploadSuccess(fileUrl, media.getId());
        return mediaConverter.toDto(media);
    }

    /**
     * Uploads file to S3 storage.
     */
    private String uploadFileToS3(MultipartFile file, Long accountId, String resourceType) {
        return s3StorageService.uploadFile(file, accountId, resourceType);
    }

    /**
     * Creates media record from uploaded file.
     */
    private Media createMediaRecord(MultipartFile file, String s3Key, Long accountId) {
        Media media = mediaConverter.createMediaFromUpload(
                file.getOriginalFilename(),
                file.getSize(),
                file.getContentType(),
                s3Key,
                s3StorageService.getBucketName(),
                accountId
        );
        return mediaRepository.createMedia(media);
    }

    /**
     * Logs successful upload.
     */
    private void logUploadSuccess(String fileUrl, Long mediaId) {
        logger.info(UPLOAD_SUCCESS_LOG, fileUrl, mediaId);
    }

    /**
     * Creates a media record from an existing S3 file URL.
     * Used for migrating existing media or when file is already uploaded.
     * Includes retry logic for eventual consistency issues with S3.
     */
    @Transactional
    public MediaDto createMediaFromUrl(@NotBlank String fileUrl,
                                      @NotBlank String originalFilename,
                                      @NotNull Long fileSizeBytes,
                                      @NotBlank String mimeType,
                                      @NotNull Long accountId) {
        logger.debug(CREATING_MEDIA_FROM_URL_LOG, fileUrl, accountId);

        String s3Key = extractS3KeyFromUrl(fileUrl, accountId);

        Optional<Media> existingMedia = findExistingMediaRecord(s3Key, accountId);
        if (existingMedia.isPresent()) {
            return handleExistingMedia(existingMedia.get(), s3Key);
        }

        validateFileExistence(fileUrl, accountId);
        Media media = createMediaRecordFromUrl(originalFilename, fileSizeBytes, mimeType, s3Key, accountId);

        logMediaCreationSuccess(fileUrl, media.getId());
        return mediaConverter.toDto(media);
    }

    /**
     * Finds existing media record by S3 key and account.
     */
    private Optional<Media> findExistingMediaRecord(String s3Key, Long accountId) {
        return mediaRepository.findByS3KeyAndAccountId(s3Key, accountId);
    }

    /**
     * Handles existing media record found.
     */
    private MediaDto handleExistingMedia(Media existingMedia, String s3Key) {
        logger.debug(MEDIA_RECORD_EXISTS_LOG, s3Key);
        return mediaConverter.toDto(existingMedia);
    }

    /**
     * Validates file existence with retry logic.
     */
    private void validateFileExistence(String fileUrl, Long accountId) {
        boolean fileExists = validateFileWithRetry(fileUrl, accountId, DEFAULT_MAX_RETRIES, DEFAULT_INITIAL_DELAY_MS);
        if (!fileExists) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                String.format(MEDIA_FILE_NOT_FOUND_MESSAGE, fileUrl));
        }
    }

    /**
     * Creates media record from URL parameters.
     */
    private Media createMediaRecordFromUrl(String originalFilename, Long fileSizeBytes, String mimeType,
                                          String s3Key, Long accountId) {
        Media media = mediaConverter.createMediaFromUpload(
                originalFilename,
                fileSizeBytes,
                mimeType,
                s3Key,
                s3StorageService.getBucketName(),
                accountId
        );
        return mediaRepository.createMedia(media);
    }

    /**
     * Logs successful media creation.
     */
    private void logMediaCreationSuccess(String fileUrl, Long mediaId) {
        logger.info(MEDIA_CREATION_SUCCESS_LOG, fileUrl, mediaId);
    }

    /**
     * Validates file existence with retry logic to handle S3 eventual consistency.
     */
    private boolean validateFileWithRetry(String fileUrl, Long accountId, int maxRetries, long delayMs) {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            ValidationResult result = attemptFileValidation(fileUrl, accountId, attempt);

            if (result.isValid()) {
                return true;
            }

            if (result.shouldRetry() && attempt < maxRetries) {
                delayMs = handleRetryDelay(fileUrl, attempt, delayMs);
                if (delayMs == -1) { // Interrupted
                    return false;
                }
            }
        }

        logFinalValidationFailure(maxRetries, fileUrl);
        return false;
    }

    /**
     * Attempts file validation for a single retry.
     */
    private ValidationResult attemptFileValidation(String fileUrl, Long accountId, int attempt) {
        try {
            boolean isValid = s3StorageService.validateUploadedFile(fileUrl, accountId, null, null);
            if (isValid) {
                logger.debug(FILE_VALIDATION_SUCCESS_LOG, attempt, fileUrl);
                return ValidationResult.valid();
            }
            return ValidationResult.invalid();
        } catch (Exception e) {
            logger.warn(FILE_VALIDATION_ERROR_LOG, attempt, fileUrl, e.getMessage());
            return ValidationResult.invalid();
        }
    }

    /**
     * Handles retry delay with exponential backoff.
     * @return new delay in ms, or -1 if interrupted
     */
    private long handleRetryDelay(String fileUrl, int attempt, long delayMs) {
        try {
            logger.debug(FILE_VALIDATION_RETRY_LOG, attempt, fileUrl, delayMs);
            Thread.sleep(delayMs);
            return delayMs * EXPONENTIAL_BACKOFF_MULTIPLIER;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn(FILE_VALIDATION_INTERRUPTED_LOG, fileUrl);
            return -1;
        }
    }

    /**
     * Logs final validation failure.
     */
    private void logFinalValidationFailure(int maxRetries, String fileUrl) {
        logger.error(FILE_VALIDATION_FINAL_FAILURE_LOG, maxRetries, fileUrl);
    }

    /**
     * Simple validation result holder.
     */
    private static class ValidationResult {
        private final boolean valid;
        private final boolean shouldRetry;

        private ValidationResult(boolean valid, boolean shouldRetry) {
            this.valid = valid;
            this.shouldRetry = shouldRetry;
        }

        static ValidationResult valid() {
            return new ValidationResult(true, false);
        }

        static ValidationResult invalid() {
            return new ValidationResult(false, true);
        }

        boolean isValid() {
            return valid;
        }

        boolean shouldRetry() {
            return shouldRetry;
        }
    }

    /**
     * Gets media by ID with account validation.
     */
    @Transactional(readOnly = true)
    public MediaDto getMediaById(@NotNull Long mediaId, @NotNull Long accountId) {
        Media media = findMediaByIdAndAccount(mediaId, accountId);
        return mediaConverter.toDto(media);
    }

    /**
     * Lists media files for an account with pagination.
     */
    @Transactional(readOnly = true)
    public PageResponse<MediaListDto> listMediaForAccount(@NotNull Long accountId, @Valid PageRequest pageRequest) {
        List<Media> mediaList = fetchMediaForAccount(accountId, pageRequest);
        long totalElements = countMediaForAccount(accountId);
        List<MediaListDto> mediaDtos = convertToListDtos(mediaList);

        return PageResponse.of(mediaDtos, pageRequest, totalElements);
    }

    /**
     * Finds media by ID and account or throws exception.
     */
    private Media findMediaByIdAndAccount(Long mediaId, Long accountId) {
        Optional<Media> media = mediaRepository.findByIdAndAccountId(mediaId, accountId);
        return media.orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
            String.format(MEDIA_NOT_FOUND_MESSAGE, mediaId)));
    }

    /**
     * Fetches media for account with pagination.
     */
    private List<Media> fetchMediaForAccount(Long accountId, PageRequest pageRequest) {
        int offset = pageRequest.getOffset();
        return mediaRepository.findByAccountId(accountId, offset, pageRequest.getSize());
    }

    /**
     * Counts total media for account.
     */
    private long countMediaForAccount(Long accountId) {
        return mediaRepository.countByAccountId(accountId);
    }

    /**
     * Converts media list to DTOs.
     */
    private List<MediaListDto> convertToListDtos(List<Media> mediaList) {
        return mediaConverter.toListDtos(mediaList);
    }

    /**
     * Calculates total storage usage for an account.
     */
    @Transactional(readOnly = true)
    public long calculateAccountStorageUsage(@NotNull Long accountId) {
        return mediaRepository.calculateAccountStorageUsage(accountId);
    }

    /**
     * Finds media files associated with a post.
     */
    @Transactional(readOnly = true)
    public List<Media> findMediaByPostId(@NotNull Long postId) {
        return mediaRepository.findByPostId(postId);
    }

    /**
     * Finds media files associated with a chat message.
     */
    @Transactional(readOnly = true)
    public List<Media> findMediaByChatMessageId(@NotNull Long chatMessageId) {
        return mediaRepository.findByChatMessageId(chatMessageId);
    }

    /**
     * Bulk loads media files for multiple posts to avoid N+1 queries.
     */
    @Transactional(readOnly = true)
    public Map<Long, List<Media>> bulkFindMediaByPostIds(@NotNull List<Long> postIds) {
        if (postIds.isEmpty()) {
            return Map.of();
        }
        return mediaRepository.bulkFindByPostIds(postIds);
    }

    /**
     * Bulk loads media files for multiple chat messages to avoid N+1 queries.
     */
    @Transactional(readOnly = true)
    public Map<Long, List<Media>> findMediaByChatMessageIds(@NotNull List<Long> chatMessageIds) {
        if (chatMessageIds.isEmpty()) {
            return Map.of();
        }
        return mediaRepository.bulkFindByChatMessageIds(chatMessageIds);
    }

    /**
     * Extracts S3 key from file URL.
     * Handles both regular URLs and presigned URLs with query parameters.
     * Supports both AWS S3 and MinIO URL formats.
     */
    private String extractS3KeyFromUrl(String fileUrl, Long accountId) {
        try {
            logger.debug(EXTRACTING_S3_KEY_LOG, fileUrl, accountId);

            String urlWithoutQuery = removeQueryParameters(fileUrl);
            String bucketName = getBucketName();
            String s3Key = extractKeyFromCleanUrl(urlWithoutQuery, bucketName);

            validateS3KeySecurity(s3Key, accountId);
            return s3Key;

        } catch (Exception e) {
            logger.error(S3_KEY_EXTRACTION_ERROR_LOG, fileUrl, accountId, e.getMessage());
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                String.format(INVALID_FILE_URL_MESSAGE, fileUrl));
        }
    }

    /**
     * Removes query parameters from URL.
     */
    private String removeQueryParameters(String fileUrl) {
        int queryIndex = fileUrl.indexOf('?');
        if (queryIndex != -1) {
            String urlWithoutQuery = fileUrl.substring(0, queryIndex);
            logger.debug(REMOVED_QUERY_PARAMS_LOG, urlWithoutQuery);
            return urlWithoutQuery;
        }
        return fileUrl;
    }

    /**
     * Gets bucket name from S3 service.
     */
    private String getBucketName() {
        String bucketName = s3StorageService.getBucketName();
        logger.debug(USING_BUCKET_LOG, bucketName);
        return bucketName;
    }

    /**
     * Extracts S3 key from clean URL using bucket name.
     */
    private String extractKeyFromCleanUrl(String urlWithoutQuery, String bucketName) {
        int bucketIndex = findBucketIndex(urlWithoutQuery, bucketName);
        String s3Key = extractKeyAfterBucket(urlWithoutQuery, bucketName, bucketIndex);

        logger.debug(EXTRACTED_S3_KEY_LOG, s3Key);
        return s3Key;
    }

    /**
     * Finds bucket index in URL.
     */
    private int findBucketIndex(String urlWithoutQuery, String bucketName) {
        int bucketIndex = urlWithoutQuery.indexOf(bucketName);
        if (bucketIndex == -1) {
            logger.warn(BUCKET_NOT_FOUND_WARN_LOG, bucketName, urlWithoutQuery);
            throw new IllegalArgumentException(String.format(BUCKET_NOT_FOUND_MESSAGE, urlWithoutQuery));
        }
        return bucketIndex;
    }

    /**
     * Extracts key after bucket name.
     */
    private String extractKeyAfterBucket(String urlWithoutQuery, String bucketName, int bucketIndex) {
        int keyStartIndex = bucketIndex + bucketName.length() + 1;
        if (keyStartIndex >= urlWithoutQuery.length()) {
            logger.warn(NO_KEY_FOUND_WARN_LOG, urlWithoutQuery);
            throw new IllegalArgumentException(String.format(S3_KEY_NOT_FOUND_MESSAGE, urlWithoutQuery));
        }
        return urlWithoutQuery.substring(keyStartIndex);
    }

    /**
     * Validates S3 key security (must belong to account).
     */
    private void validateS3KeySecurity(String s3Key, Long accountId) {
        if (!s3Key.startsWith(accountId + ACCOUNT_PATH_SEPARATOR)) {
            logger.warn(S3_KEY_SECURITY_WARN_LOG, s3Key, accountId);
            throw new IllegalArgumentException(String.format(S3_KEY_SECURITY_VIOLATION_MESSAGE, accountId, s3Key));
        }
    }
}
