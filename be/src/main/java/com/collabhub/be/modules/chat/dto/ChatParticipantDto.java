package com.collabhub.be.modules.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.HubParticipantRole;

/**
 * DTO representing a chat participant (simplified version of HubParticipantResponse).
 */
public class ChatParticipantDto {

    private Long id;

    private String name;

    private String email;

    private HubParticipantRole role;

    @JsonProperty("is_external")
    private Boolean isExternal;

    public ChatParticipantDto() {}

    public ChatParticipantDto(Long id, String name, String email, HubParticipantRole role, Boolean isExternal) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.role = role;
        this.isExternal = isExternal;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public HubParticipantRole getRole() {
        return role;
    }

    public void setRole(HubParticipantRole role) {
        this.role = role;
    }

    public Boolean getIsExternal() {
        return isExternal;
    }

    public void setIsExternal(Boolean isExternal) {
        this.isExternal = isExternal;
    }
}
