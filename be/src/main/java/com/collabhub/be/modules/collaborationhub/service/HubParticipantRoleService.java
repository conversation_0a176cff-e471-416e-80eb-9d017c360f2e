package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.modules.collaborationhub.converter.HubParticipantConverter;
import com.collabhub.be.modules.collaborationhub.dto.HubParticipantResponse;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.service.CollabHubPermissionService;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * Service for managing hub participant roles and role-related validations.
 * Handles role updates, admin protection logic, and role-based business rules.
 */
@Service
public class HubParticipantRoleService {

    private static final Logger logger = LoggerFactory.getLogger(HubParticipantRoleService.class);

    // Error Messages
    private static final String LAST_ADMIN_ROLE_CHANGE_MESSAGE = "Cannot change role of the last admin in the hub";
    private static final String INVALID_OPERATION_MESSAGE = "Cannot remove the last admin from the hub";
    private static final String ROLE_UPDATE_FAILED_MESSAGE = "Failed to update participant role";

    // Log Messages
    private static final String UPDATING_PARTICIPANT_ROLE_LOG = "Updating participant {} role to {} in hub {} by user {}";

    private final HubParticipantRepositoryImpl participantRepository;
    private final HubParticipantConverter participantConverter;
    private final CollabHubPermissionService participantPermissionService;

    public HubParticipantRoleService(HubParticipantRepositoryImpl participantRepository,
                                   HubParticipantConverter participantConverter,
                                   CollabHubPermissionService participantPermissionService) {
        this.participantRepository = participantRepository;
        this.participantConverter = participantConverter;
        this.participantPermissionService = participantPermissionService;
    }

    /**
     * Updates a participant's role with proper validation.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param newRole the new role
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return updated participant response
     */
    @Transactional
    public HubParticipantResponse updateParticipantRole(@NotNull Long hubId, @NotNull Long participantId,
                                                       @NotNull HubParticipantRole newRole,
                                                       @NotNull Long accountId, @NotNull Long userId) {
        logger.info(UPDATING_PARTICIPANT_ROLE_LOG, participantId, newRole, hubId, userId);

        // Validate access and permissions
        participantPermissionService.validateHubAdminAccess(hubId);
        HubParticipant participant = participantRepository.findById(participantId);

        // Validate role change is allowed
        validateRoleChange(participant, newRole, userId, hubId);

        // Update role in database
        updateParticipantRoleInDatabase(participantId, hubId, newRole);

        // Fetch updated participant and convert to response
        HubParticipant updatedParticipant = participantRepository.findById(participantId);
        String displayName = getParticipantDisplayName(updatedParticipant);
        return participantConverter.toResponse(updatedParticipant, displayName);
    }

    /**
     * Validates that a participant can be removed (not the last admin).
     *
     * @param participant the participant to remove
     * @param userId the requesting user ID
     * @param hubId the hub ID
     * @throws ConflictException if trying to remove the last admin
     */
    @Transactional(readOnly = true)
    public void validateLastAdminRemoval(HubParticipant participant, Long userId, Long hubId) {
        if (isLastAdminSelfOperation(participant, userId, hubId)) {
            throw new ConflictException(ErrorCode.INVALID_OPERATION, INVALID_OPERATION_MESSAGE);
        }
    }

    /**
     * Validates that a participant's role can be changed.
     *
     * @param participant the participant
     * @param newRole the new role
     * @param userId the requesting user ID
     * @param hubId the hub ID
     * @throws ConflictException if trying to change the last admin's role
     */
    @Transactional(readOnly = true)
    public void validateLastAdminRoleChange(HubParticipant participant, HubParticipantRole newRole, 
                                          Long userId, Long hubId) {
        // If changing from admin to non-admin, check if this is the last admin
        if (participant.getRole() == HubParticipantRole.admin && 
            newRole != HubParticipantRole.admin && 
            isLastAdminSelfOperation(participant, userId, hubId)) {
            throw new ConflictException(ErrorCode.INVALID_OPERATION, LAST_ADMIN_ROLE_CHANGE_MESSAGE);
        }
    }

    /**
     * Checks if this is the last admin in the hub.
     *
     * @param hubId the hub ID
     * @return true if there's only one admin left
     */
    @Transactional(readOnly = true)
    public boolean isLastAdmin(Long hubId) {
        // Use the existing repository method to find active participants and filter for admins
        long adminCount = participantRepository.findActiveParticipantsByHubId(hubId).stream()
                .filter(p -> p.getRole() == HubParticipantRole.admin)
                .count();
        return adminCount <= 1;
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Validates that a role change is allowed.
     */
    private void validateRoleChange(HubParticipant participant, HubParticipantRole newRole, Long userId, Long hubId) {
        // Skip validation if role is not changing
        if (participant.getRole() == newRole) {
            return;
        }

        // Validate last admin role change
        validateLastAdminRoleChange(participant, newRole, userId, hubId);
    }

    /**
     * Checks if this is a last admin performing an operation on themselves.
     */
    private boolean isLastAdminSelfOperation(HubParticipant participant, Long userId, Long hubId) {
        return participant.getRole() == HubParticipantRole.admin &&
               participant.getUserId() != null &&
               participant.getUserId().equals(userId) &&
               isLastAdmin(hubId);
    }

    /**
     * Updates participant role in the database.
     */
    private void updateParticipantRoleInDatabase(Long participantId, Long hubId, HubParticipantRole role) {
        // For now, use the DAO update method - in production this would be a proper repository method
        HubParticipant participant = participantRepository.findById(participantId);
        if (participant != null) {
            participant.setRole(role);
            participantRepository.update(participant);
        } else {
            throw new ConflictException(ErrorCode.INVALID_OPERATION, ROLE_UPDATE_FAILED_MESSAGE);
        }
    }

    /**
     * Gets participant display name.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        if (participant.getName() != null && !participant.getName().trim().isEmpty()) {
            return participant.getName();
        }

        // Extract from email if name not available
        String email = participant.getEmail();
        if (email != null && email.contains("@")) {
            return email.substring(0, email.indexOf("@"));
        }

        return email;
    }
}
