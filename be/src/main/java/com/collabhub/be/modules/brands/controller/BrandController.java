package com.collabhub.be.modules.brands.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.brands.dto.BrandContactResponse;
import com.collabhub.be.modules.brands.dto.BrandCreateRequest;
import com.collabhub.be.modules.brands.dto.BrandListItemDto;
import com.collabhub.be.modules.brands.dto.BrandResponse;
import com.collabhub.be.modules.brands.dto.BrandUpdateRequest;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.modules.brands.service.BrandService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for brand management operations.
 * Provides endpoints for CRUD operations on brands and their contacts.
 */
@RestController
@RequestMapping("/api/brands")
@Tag(name = "Brands", description = "Brand management operations")
public class BrandController {

    private static final Logger logger = LoggerFactory.getLogger(BrandController.class);
    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int MAX_PAGE_SIZE = 100;

    private final BrandService brandService;
    private final JwtClaimsService jwtClaimsService;

    public BrandController(BrandService brandService, JwtClaimsService jwtClaimsService) {
        this.brandService = brandService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Creates a new brand with associated contacts.
     *
     * @param request the brand creation request
     * @param jwt the JWT token for authentication
     * @return the created brand response
     */
    @PostMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRAND_WRITE.permission)")
    @Operation(summary = "Create a new brand", description = "Creates a new brand with associated contacts")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Brand created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "409", description = "Brand name already exists")
    })
    public ResponseEntity<BrandResponse> createBrand(
            @Valid @RequestBody BrandCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Creating new brand: {} for account: {}", request.getName(), userContext.getAccountId());

        BrandResponse response = brandService.createBrand(request, userContext.getAccountId());

        logger.info("Successfully created brand with ID: {} for account: {}", response.getId(), userContext.getAccountId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Retrieves brands with filtering and pagination for the current account.
     *
     * @param nameFilter optional name filter (case-insensitive partial match)
     * @param page the page number (0-based, default: 0)
     * @param size the number of items per page (default: 20, max: 100)
     * @param jwt the JWT token for authentication
     * @return paginated brand responses
     */
    @GetMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRAND_READ.permission)")
    @Operation(summary = "Get brands with pagination", description = "Retrieves active brands for the current account with filtering and pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Brands retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<PageResponse<BrandListItemDto>> getBrands(
            @Parameter(description = "Filter brands by name (case-insensitive partial match)")
            @RequestParam(value = "name", required = false) String nameFilter,
            @Parameter(description = "Page number (0-based)")
            @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)")
            @RequestParam(value = "size", defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving brands for account: {} with filter '{}', page {}, size {}",
                    userContext.getAccountId(), nameFilter, page, size);

        int validatedSize = Math.min(size, MAX_PAGE_SIZE);
        PageRequest pageRequest = PageRequest.of(page, validatedSize);
        PageResponse<BrandListItemDto> brands = brandService.getBrandListItems(userContext.getAccountId(), nameFilter, pageRequest);

        logger.debug("Retrieved {} brands out of {} total for account: {}",
                    brands.getNumberOfElements(), brands.getTotalElements(), userContext.getAccountId());
        return ResponseEntity.ok(brands);
    }

    /**
     * Retrieves a specific brand by ID.
     *
     * @param id the brand ID
     * @param jwt the JWT token for authentication
     * @return the brand response
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRAND_READ.permission)")
    @Operation(summary = "Get brand by ID", description = "Retrieves a specific brand with its contacts")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Brand retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Brand not found")
    })
    public ResponseEntity<BrandResponse> getBrandById(
            @Parameter(description = "Brand ID", required = true)
            @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving brand with ID: {} for account: {}", id, userContext.getAccountId());

        BrandResponse brand = brandService.getBrandById(id, userContext.getAccountId());

        logger.debug("Successfully retrieved brand: {} for account: {}", brand.getName(), userContext.getAccountId());
        return ResponseEntity.ok(brand);
    }

    /**
     * Updates an existing brand and its contacts.
     *
     * @param id the brand ID
     * @param request the brand update request
     * @param jwt the JWT token for authentication
     * @return the updated brand response
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRAND_WRITE.permission)")
    @Operation(summary = "Update brand", description = "Updates an existing brand and its contacts")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Brand updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Brand not found"),
        @ApiResponse(responseCode = "409", description = "Brand name already exists")
    })
    public ResponseEntity<BrandResponse> updateBrand(
            @Parameter(description = "Brand ID", required = true)
            @PathVariable Long id,
            @Valid @RequestBody BrandUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Updating brand with ID: {} for account: {}", id, userContext.getAccountId());

        BrandResponse response = brandService.updateBrand(id, request, userContext.getAccountId());

        logger.info("Successfully updated brand: {} for account: {}", response.getName(), userContext.getAccountId());
        return ResponseEntity.ok(response);
    }

    /**
     * Retrieves contacts for a specific brand.
     *
     * @param id the brand ID
     * @param jwt the JWT token for authentication
     * @return list of brand contacts
     */
    @GetMapping("/{id}/contacts")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRAND_READ.permission)")
    @Operation(summary = "Get brand contacts", description = "Retrieves all contacts for a specific brand")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Brand contacts retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Brand not found")
    })
    public ResponseEntity<List<BrandContactResponse>> getBrandContacts(
            @Parameter(description = "Brand ID", required = true)
            @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving contacts for brand with ID: {} for account: {}", id, userContext.getAccountId());

        List<BrandContactResponse> contacts = brandService.getBrandContacts(id, userContext.getAccountId());

        logger.debug("Successfully retrieved {} contacts for brand with ID: {} for account: {}",
                    contacts.size(), id, userContext.getAccountId());
        return ResponseEntity.ok(contacts);
    }

    /**
     * Soft deletes a brand and all its contacts.
     *
     * @param id the brand ID
     * @param jwt the JWT token for authentication
     * @return no content response
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRAND_DELETE.permission)")
    @Operation(summary = "Delete brand", description = "Soft deletes a brand and all its contacts")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Brand deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Brand not found")
    })
    public ResponseEntity<Void> deleteBrand(
            @Parameter(description = "Brand ID", required = true)
            @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Deleting brand with ID: {} for account: {}", id, userContext.getAccountId());

        brandService.deleteBrand(id, userContext.getAccountId());

        logger.info("Successfully deleted brand with ID: {} for account: {}", id, userContext.getAccountId());
        return ResponseEntity.noContent().build();
    }
}
