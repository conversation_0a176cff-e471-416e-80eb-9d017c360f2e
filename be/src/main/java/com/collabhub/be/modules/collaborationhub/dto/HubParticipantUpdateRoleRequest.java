package com.collabhub.be.modules.collaborationhub.dto;

import org.jooq.generated.enums.HubParticipantRole;

import jakarta.validation.constraints.NotNull;

/**
 * Request DTO for updating a participant's role in a collaboration hub.
 */
public class HubParticipantUpdateRoleRequest {

    @NotNull(message = "Role is required")
    private HubParticipantRole role;

    public HubParticipantUpdateRoleRequest() {}

    public HubParticipantUpdateRoleRequest(HubParticipantRole role) {
        this.role = role;
    }

    public HubParticipantRole getRole() {
        return role;
    }

    public void setRole(HubParticipantRole role) {
        this.role = role;
    }

    @Override
    public String toString() {
        return "HubParticipantUpdateRoleRequest{" +
                "role=" + role +
                '}';
    }
}
