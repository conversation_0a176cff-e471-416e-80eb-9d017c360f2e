package com.collabhub.be.modules.invoice.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.bankdetails.repository.BankDetailsRepositoryImpl;
import com.collabhub.be.modules.brands.repository.BrandRepositoryImpl;
import com.collabhub.be.modules.companies.repository.AccountCompanyRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.InvoiceCreateRequest;
import com.collabhub.be.modules.invoice.dto.InvoiceUpdateRequest;
import com.collabhub.be.modules.invoice.repository.InvoiceRepositoryImpl;
import org.jooq.generated.enums.InvoiceStatus;
import org.jooq.generated.tables.pojos.Invoice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.EnumSet;
import java.util.Optional;
import java.util.Set;

/**
 * Service for comprehensive invoice validation.
 * Handles business rules, data integrity, and status transition validation.
 */
@Service
public class InvoiceValidationService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceValidationService.class);

    private final InvoiceRepositoryImpl invoiceRepository;
    private final AccountCompanyRepositoryImpl accountCompanyRepository;
    private final BrandRepositoryImpl brandRepository;
    private final BankDetailsRepositoryImpl bankDetailsRepository;

    // Define valid status transitions
    private static final Set<InvoiceStatus> DRAFT_ALLOWED_TRANSITIONS = EnumSet.of(
            InvoiceStatus.sent, InvoiceStatus.paid
    );
    
    private static final Set<InvoiceStatus> SENT_ALLOWED_TRANSITIONS = EnumSet.of(
            InvoiceStatus.paid, InvoiceStatus.overdue
    );
    
    private static final Set<InvoiceStatus> OVERDUE_ALLOWED_TRANSITIONS = EnumSet.of(
            InvoiceStatus.paid
    );
    
    private static final Set<InvoiceStatus> PAID_ALLOWED_TRANSITIONS = EnumSet.noneOf(InvoiceStatus.class);

    // Define which statuses allow modifications
    private static final Set<InvoiceStatus> MODIFIABLE_STATUSES = EnumSet.of(
            InvoiceStatus.draft
    );

    public InvoiceValidationService(InvoiceRepositoryImpl invoiceRepository,
                                   AccountCompanyRepositoryImpl accountCompanyRepository,
                                   BrandRepositoryImpl brandRepository,
                                   BankDetailsRepositoryImpl bankDetailsRepository) {
        this.invoiceRepository = invoiceRepository;
        this.accountCompanyRepository = accountCompanyRepository;
        this.brandRepository = brandRepository;
        this.bankDetailsRepository = bankDetailsRepository;
    }

    /**
     * Validates invoice creation request.
     *
     * @param request the create request
     * @param accountId the account ID for multi-tenancy
     * @throws BadRequestException if validation fails
     */
    @Transactional(readOnly = true)
    public void validateCreateRequest(InvoiceCreateRequest request, Long accountId) {
        logger.debug("Validating invoice creation request for account: {}", accountId);

        // Validate invoice number uniqueness
        validateInvoiceNumberUniqueness(request.getInvoiceNumber(), accountId, null);

        // Validate dates
        validateDates(request.getIssueDate(), request.getDueDate());

        // Validate required fields
        validateRequiredFields(request);

        logger.debug("Invoice creation validation passed for account: {}", accountId);
    }

    /**
     * Validates invoice update request.
     *
     * @param request the update request
     * @param existingInvoice the existing invoice
     * @param accountId the account ID for multi-tenancy
     * @throws BadRequestException if validation fails
     */
    @Transactional(readOnly = true)
    public void validateUpdateRequest(InvoiceUpdateRequest request, Invoice existingInvoice, Long accountId) {
        logger.debug("Validating invoice update request for invoice: {} in account: {}", 
                    existingInvoice.getId(), accountId);

        // Check if invoice can be modified based on current status (only DRAFT invoices can be edited)
        validateInvoiceModifiable(existingInvoice);

        // Validate issuer, recipient, and bank details if being changed
        if (request.getIssuerId() != null) {
            validateIssuerExists(request.getIssuerId(), accountId);
        }

        if (request.getRecipientId() != null) {
            validateRecipientExists(request.getRecipientId(), accountId);
        }

        if (request.getBankDetailsId() != null) {
            validateBankDetailsExists(request.getBankDetailsId(), accountId);
        }

        // Validate invoice number uniqueness if being changed
        if (request.getInvoiceNumber() != null &&
            !request.getInvoiceNumber().equals(existingInvoice.getInvoiceNumber())) {
            validateInvoiceNumberUniqueness(request.getInvoiceNumber(), accountId, existingInvoice.getId());
        }

        // Validate dates if being changed
        LocalDate issueDate = request.getIssueDate() != null ? request.getIssueDate() : existingInvoice.getIssueDate();
        LocalDate dueDate = request.getDueDate() != null ? request.getDueDate() : existingInvoice.getDueDate();
        validateDates(issueDate, dueDate);

        logger.debug("Invoice update validation passed for invoice: {} in account: {}", 
                    existingInvoice.getId(), accountId);
    }

    /**
     * Validates status transition.
     *
     * @param currentStatus the current status
     * @param newStatus the new status
     * @throws BadRequestException if transition is not allowed
     */
    public void validateStatusTransition(InvoiceStatus currentStatus, InvoiceStatus newStatus) {
        logger.debug("Validating status transition from {} to {}", currentStatus, newStatus);

        if (currentStatus == newStatus) {
            return; // No change, always valid
        }

        Set<InvoiceStatus> allowedTransitions = getAllowedTransitions(currentStatus);
        
        if (!allowedTransitions.contains(newStatus)) {
            logger.warn("Invalid status transition attempted: {} -> {}", currentStatus, newStatus);
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, 
                    String.format("Cannot transition from %s to %s", currentStatus, newStatus));
        }

        logger.debug("Status transition validation passed: {} -> {}", currentStatus, newStatus);
    }

    /**
     * Validates that an invoice can be modified based on its current status.
     *
     * @param invoice the invoice to check
     * @throws BadRequestException if invoice cannot be modified
     */
    public void validateInvoiceModifiable(Invoice invoice) {
        if (!MODIFIABLE_STATUSES.contains(invoice.getStatus())) {
            logger.warn("Attempted to modify invoice {} with non-modifiable status: {}",
                       invoice.getId(), invoice.getStatus());
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR,
                    String.format("Cannot modify invoice with status: %s. Only draft invoices can be modified.",
                                 invoice.getStatus()));
        }
    }

    /**
     * Validates that an invoice has required issuer and recipient data for sending or PDF generation.
     * This validation ensures that the invoice contains all necessary information to generate a complete PDF.
     *
     * @param invoice the invoice to validate
     * @throws BadRequestException if required issuer or recipient data is missing
     */
    @Transactional(readOnly = true)
    public void validateInvoiceForSendingOrPdf(Invoice invoice) {
        logger.debug("Validating invoice {} for sending/PDF generation", invoice.getId());

        // Validate issuer is present
        if (invoice.getIssuerId() == null) {
            logger.warn("Invoice {} missing issuer ID", invoice.getId());
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR,
                    InvoiceConstants.INVOICE_MISSING_ISSUER_MESSAGE);
        }

        // Validate recipient is present
        if (invoice.getRecipientId() == null) {
            logger.warn("Invoice {} missing recipient ID", invoice.getId());
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR,
                    InvoiceConstants.INVOICE_MISSING_RECIPIENT_MESSAGE);
        }

        logger.debug("Invoice {} validation passed for sending/PDF generation", invoice.getId());
    }

    /**
     * Validates invoice number uniqueness within account.
     *
     * @param invoiceNumber the invoice number to check
     * @param accountId the account ID
     * @param excludeInvoiceId invoice ID to exclude from check (for updates)
     * @throws BadRequestException if invoice number already exists
     */
    private void validateInvoiceNumberUniqueness(String invoiceNumber, Long accountId, Long excludeInvoiceId) {
        Optional<Invoice> existingInvoice = invoiceRepository.findByInvoiceNumberAndAccountId(invoiceNumber, accountId);
        
        if (existingInvoice.isPresent() && 
            (excludeInvoiceId == null || !existingInvoice.get().getId().equals(excludeInvoiceId))) {
            logger.warn("Duplicate invoice number attempted: {} for account: {}", invoiceNumber, accountId);
            throw new BadRequestException(ErrorCode.DUPLICATE_RESOURCE, 
                    "Invoice number already exists: " + invoiceNumber);
        }
    }

    /**
     * Validates issue date and due date constraints.
     *
     * @param issueDate the issue date
     * @param dueDate the due date
     * @throws BadRequestException if dates are invalid
     */
    private void validateDates(LocalDate issueDate, LocalDate dueDate) {
        LocalDate today = LocalDate.now();

        // Issue date cannot be in the future
        if (issueDate != null && issueDate.isAfter(today)) {
            logger.warn("Issue date in future attempted: {}", issueDate);
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, 
                    "Issue date cannot be in the future");
        }

        // Due date must be >= issue date
        if (issueDate != null && dueDate != null && dueDate.isBefore(issueDate)) {
            logger.warn("Due date before issue date attempted: issue={}, due={}", issueDate, dueDate);
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, 
                    "Due date must be on or after issue date");
        }
    }

    /**
     * Validates required fields for invoice creation.
     *
     * @param request the create request
     * @throws BadRequestException if required fields are missing
     */
    private void validateRequiredFields(InvoiceCreateRequest request) {
        if (request.getItems() == null || request.getItems().isEmpty()) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, 
                    "At least one invoice item is required");
        }

        if (request.getRecipients() == null || request.getRecipients().isEmpty()) {
            throw new BadRequestException(ErrorCode.VALIDATION_ERROR, 
                    "At least one recipient is required");
        }
    }

    /**
     * Validates that issuer exists and belongs to the account.
     *
     * @param issuerId the issuer ID
     * @param accountId the account ID
     * @throws BadRequestException if issuer doesn't exist or doesn't belong to account
     */
    private void validateIssuerExists(Long issuerId, Long accountId) {
        logger.debug("Validating issuer exists: {} for account: {}", issuerId, accountId);

        if (issuerId == null) {
            return; // Null issuer is allowed for some invoice types
        }

        boolean exists = accountCompanyRepository.existsByIdAndAccountId(issuerId, accountId);
        if (!exists) {
            logger.warn("Issuer not found or access denied: issuerId={}, accountId={}", issuerId, accountId);
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Issuer company not found or access denied");
        }

        logger.debug("Issuer validation successful: {} for account: {}", issuerId, accountId);
    }

    /**
     * Validates that recipient exists and belongs to the account.
     *
     * @param recipientId the recipient ID
     * @param accountId the account ID
     * @throws BadRequestException if recipient doesn't exist or doesn't belong to account
     */
    private void validateRecipientExists(Long recipientId, Long accountId) {
        logger.debug("Validating recipient exists: {} for account: {}", recipientId, accountId);

        if (recipientId == null) {
            return; // Null recipient is allowed for some invoice types
        }

        boolean exists = brandRepository.existsByIdAndAccountId(recipientId, accountId);
        if (!exists) {
            logger.warn("Recipient not found or access denied: recipientId={}, accountId={}", recipientId, accountId);
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Recipient brand not found or access denied");
        }

        logger.debug("Recipient validation successful: {} for account: {}", recipientId, accountId);
    }

    /**
     * Validates that bank details exist and belong to the account.
     *
     * @param bankDetailsId the bank details ID
     * @param accountId the account ID
     * @throws BadRequestException if bank details don't exist or don't belong to account
     */
    private void validateBankDetailsExists(Long bankDetailsId, Long accountId) {
        logger.debug("Validating bank details exist: {} for account: {}", bankDetailsId, accountId);

        if (bankDetailsId == null) {
            return; // Null bank details is allowed for some invoice types
        }

        boolean exists = bankDetailsRepository.existsByIdAndAccountId(bankDetailsId, accountId);
        if (!exists) {
            logger.warn("Bank details not found or access denied: bankDetailsId={}, accountId={}", bankDetailsId, accountId);
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Bank details not found or access denied");
        }

        logger.debug("Bank details validation successful: {} for account: {}", bankDetailsId, accountId);
    }

    /**
     * Gets allowed status transitions for a given current status.
     *
     * @param currentStatus the current status
     * @return set of allowed target statuses
     */
    private Set<InvoiceStatus> getAllowedTransitions(InvoiceStatus currentStatus) {
        return switch (currentStatus) {
            case draft -> DRAFT_ALLOWED_TRANSITIONS;
            case sent -> SENT_ALLOWED_TRANSITIONS;
            case overdue -> OVERDUE_ALLOWED_TRANSITIONS;
            case paid -> PAID_ALLOWED_TRANSITIONS;
        };
    }
}
