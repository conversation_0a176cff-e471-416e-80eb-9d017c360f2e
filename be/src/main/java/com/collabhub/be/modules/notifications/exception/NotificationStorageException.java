package com.collabhub.be.modules.notifications.exception;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.InternalServerErrorException;

/**
 * Exception thrown when notification storage operations fail.
 *
 * <p>This exception is typically thrown when:</p>
 * <ul>
 *   <li>Database operations fail during notification creation</li>
 *   <li>Notification metadata serialization fails</li>
 *   <li>Bulk notification operations encounter errors</li>
 *   <li>Storage constraints are violated</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public class NotificationStorageException extends InternalServerErrorException {

    private static final String DEFAULT_MESSAGE = "Failed to store notification";

    /**
     * Constructs a new NotificationStorageException with a default message.
     */
    public NotificationStorageException() {
        super(ErrorCode.NOTIFICATION_STORAGE_FAILED, DEFAULT_MESSAGE);
    }

    /**
     * Constructs a new NotificationStorageException with the specified detail message.
     *
     * @param message the detail message
     */
    public NotificationStorageException(String message) {
        super(ErrorCode.NOTIFICATION_STORAGE_FAILED, message);
    }

    /**
     * Constructs a new NotificationStorageException with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public NotificationStorageException(String message, Throwable cause) {
        super(ErrorCode.NOTIFICATION_STORAGE_FAILED, message, cause);
    }

    /**
     * Constructs a new NotificationStorageException with the specified cause.
     *
     * @param cause the cause
     */
    public NotificationStorageException(Throwable cause) {
        super(ErrorCode.NOTIFICATION_STORAGE_FAILED, DEFAULT_MESSAGE, cause);
    }
}
