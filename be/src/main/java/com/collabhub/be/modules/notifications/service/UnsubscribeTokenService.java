package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.model.TokenType;
import com.collabhub.be.modules.auth.repository.VerificationTokenRepository;
import org.jooq.generated.tables.pojos.VerificationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotBlank;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;

/**
 * Service for managing unsubscribe tokens for email notifications.
 * Provides secure token generation and validation for one-click unsubscribe functionality.
 * Follows OWASP security best practices with proper token expiration and single-use enforcement.
 */
@Service
@Transactional
public class UnsubscribeTokenService {

    private static final Logger logger = LoggerFactory.getLogger(UnsubscribeTokenService.class);

    // Security constants
    private static final int TOKEN_LENGTH = 32;
    private static final int TOKEN_LOG_LENGTH = 8;
    private static final String TOKEN_LOG_SUFFIX = "...";

    // Log messages
    private static final String CREATING_UNSUBSCRIBE_TOKEN_LOG = "Creating unsubscribe token for email: {}";
    private static final String CREATED_UNSUBSCRIBE_TOKEN_LOG = "Created unsubscribe token for email: {}, expires at: {}";
    private static final String VALIDATING_UNSUBSCRIBE_TOKEN_LOG = "Validating unsubscribe token: {}";
    private static final String VALIDATED_UNSUBSCRIBE_TOKEN_SUCCESS_LOG = "Successfully validated unsubscribe token for email: {}";
    private static final String REVOKING_EXISTING_TOKENS_LOG = "Revoking existing unsubscribe tokens for email: {}";

    // Error messages
    private static final String INVALID_EMAIL_MESSAGE = "Email address cannot be null or empty";
    private static final String INVALID_TOKEN_MESSAGE = "Unsubscribe token cannot be null or empty";
    private static final String INVALID_EXPIRED_TOKEN_MESSAGE = "Invalid or expired unsubscribe token";

    private final VerificationTokenRepository verificationTokenRepository;
    private final AuthProperties authProperties;
    private final SecureRandom secureRandom;

    public UnsubscribeTokenService(VerificationTokenRepository verificationTokenRepository,
                                 AuthProperties authProperties) {
        this.verificationTokenRepository = verificationTokenRepository;
        this.authProperties = authProperties;
        this.secureRandom = new SecureRandom();
    }

    /**
     * Creates an unsubscribe token for the specified email address.
     * Revokes any existing unsubscribe tokens for the same email to ensure single active token.
     *
     * @param email the email address to create unsubscribe token for
     * @return the generated unsubscribe token
     * @throws BadRequestException if email is invalid
     */
    public String createUnsubscribeToken(@NotBlank String email) {
        logger.info(CREATING_UNSUBSCRIBE_TOKEN_LOG, email);

        validateEmail(email);

        // Revoke any existing unsubscribe tokens for this email
        revokeExistingUnsubscribeTokens(email);

        // Generate new secure token
        String token = generateSecureToken();
        LocalDateTime expiresAt = LocalDateTime.now()
                .plus(authProperties.getVerificationToken().getUnsubscribeTokenTtl());

        // Create verification token record
        VerificationToken verificationToken = verificationTokenRepository.createUnsubscribeToken(
                email, token, expiresAt);

        logger.info(CREATED_UNSUBSCRIBE_TOKEN_LOG, email, expiresAt);
        return token;
    }

    /**
     * Validates an unsubscribe token and returns the associated email address.
     * Marks the token as used to prevent reuse.
     *
     * @param token the unsubscribe token to validate
     * @return validation result with email address
     * @throws NotFoundException if token is invalid or expired
     * @throws BadRequestException if token format is invalid
     */
    public UnsubscribeTokenValidationResult validateUnsubscribeToken(@NotBlank String token) {
        logger.info(VALIDATING_UNSUBSCRIBE_TOKEN_LOG, truncateTokenForLogging(token));

        validateToken(token);
        VerificationToken verificationToken = findValidUnsubscribeToken(token);

        // Mark token as used (single-use enforcement)
        markTokenAsUsed(verificationToken);

        logger.info(VALIDATED_UNSUBSCRIBE_TOKEN_SUCCESS_LOG, verificationToken.getEmail());

        return new UnsubscribeTokenValidationResult(verificationToken.getEmail());
    }

    /**
     * Revokes all existing unsubscribe tokens for the specified email address.
     *
     * @param email the email address to revoke tokens for
     */
    private void revokeExistingUnsubscribeTokens(String email) {
        logger.debug(REVOKING_EXISTING_TOKENS_LOG, email);
        verificationTokenRepository.revokeTokensByEmailAndType(email, TokenType.UNSUBSCRIBE_TOKEN);
    }

    /**
     * Generates a cryptographically secure random token.
     *
     * @return base64-encoded secure token
     */
    private String generateSecureToken() {
        byte[] tokenBytes = new byte[TOKEN_LENGTH];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }

    /**
     * Finds and validates an unsubscribe token.
     *
     * @param token the token to find
     * @return the verification token if valid
     * @throws NotFoundException if token is invalid or expired
     */
    private VerificationToken findValidUnsubscribeToken(String token) {
        Optional<VerificationToken> tokenOpt = verificationTokenRepository
                .findByTokenAndType(token, TokenType.UNSUBSCRIBE_TOKEN);

        if (tokenOpt.isEmpty()) {
            throw new NotFoundException(ErrorCode.INVALID_TOKEN, INVALID_EXPIRED_TOKEN_MESSAGE);
        }

        VerificationToken verificationToken = tokenOpt.get();

        // Check if token is for unsubscribe (has email but no user_id)
        if (verificationToken.getEmail() == null) {
            throw new NotFoundException(ErrorCode.INVALID_TOKEN, INVALID_EXPIRED_TOKEN_MESSAGE);
        }

        // Check if token is already used
        if (verificationToken.getRevoked()) {
            throw new NotFoundException(ErrorCode.INVALID_TOKEN, INVALID_EXPIRED_TOKEN_MESSAGE);
        }

        // Check if token is expired
        if (verificationToken.getExpiresAt().isBefore(LocalDateTime.now())) {
            throw new NotFoundException(ErrorCode.INVALID_TOKEN, INVALID_EXPIRED_TOKEN_MESSAGE);
        }

        return verificationToken;
    }

    /**
     * Marks a token as used by setting the revoked flag.
     *
     * @param verificationToken the token to mark as used
     */
    private void markTokenAsUsed(VerificationToken verificationToken) {
        verificationTokenRepository.revokeToken(verificationToken.getToken());
    }

    /**
     * Validates email address format and content.
     *
     * @param email the email to validate
     * @throws BadRequestException if email is invalid
     */
    private void validateEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.VALIDATION_FAILED, INVALID_EMAIL_MESSAGE);
        }
    }

    /**
     * Validates token format and content.
     *
     * @param token the token to validate
     * @throws BadRequestException if token is invalid
     */
    private void validateToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.VALIDATION_FAILED, INVALID_TOKEN_MESSAGE);
        }
    }

    /**
     * Truncates token for safe logging without exposing full token value.
     *
     * @param token the token to truncate
     * @return truncated token for logging
     */
    private String truncateTokenForLogging(String token) {
        return token.substring(0, Math.min(TOKEN_LOG_LENGTH, token.length())) + TOKEN_LOG_SUFFIX;
    }

    /**
     * Result of unsubscribe token validation.
     * Contains the email address associated with the token.
     */
    public record UnsubscribeTokenValidationResult(
            String email
    ) {
    }
}
