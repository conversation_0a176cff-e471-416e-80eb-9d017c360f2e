package com.collabhub.be.modules.posts.util;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.media.service.MediaService;
import com.collabhub.be.modules.media.dto.MediaDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Utility class for handling media-related operations in posts.
 * Centralizes media validation, processing, and MIME type detection logic
 * to eliminate code duplication and improve maintainability.
 */
@Component
public class PostMediaUtil {

    private static final Logger logger = LoggerFactory.getLogger(PostMediaUtil.class);

    // MIME type mappings for common file extensions
    private static final Map<String, String> EXTENSION_TO_MIME_TYPE = Map.of(
            ".jpg", PostConstants.MIME_TYPE_JPEG,
            ".jpeg", PostConstants.MIME_TYPE_JPEG,
            ".png", PostConstants.MIME_TYPE_PNG,
            ".gif", PostConstants.MIME_TYPE_GIF,
            ".mp4", PostConstants.MIME_TYPE_MP4,
            ".mov", PostConstants.MIME_TYPE_QUICKTIME,
            ".avi", PostConstants.MIME_TYPE_AVI
    );

    private final MediaService mediaService;

    public PostMediaUtil(MediaService mediaService) {
        this.mediaService = mediaService;
    }

    /**
     * Creates or validates media records from URLs and returns their IDs.
     * Handles URL validation, filename extraction, and MIME type detection.
     *
     * @param mediaUris list of media URLs
     * @param accountId account ID for multi-tenancy
     * @return list of media IDs
     * @throws BadRequestException if any media URL is invalid
     */
    public List<Long> createOrValidateMediaRecords(List<String> mediaUris, Long accountId) {
        logger.debug("Creating/validating {} media records for account {}", mediaUris.size(), accountId);

        return mediaUris.stream()
                .map(uri -> processMediaUri(uri, accountId))
                .toList();
    }

    /**
     * Processes a single media URI and returns the media ID.
     *
     * @param uri the media URI
     * @param accountId account ID for multi-tenancy
     * @return media ID
     * @throws BadRequestException if the URI is invalid
     */
    public Long processMediaUri(String uri, Long accountId) {
        try {
            logger.debug("Processing media URI: {}", uri);

            String filename = extractFilenameFromUrl(uri);
            String mimeType = determineMimeTypeFromUrl(uri);

            MediaDto mediaDto = mediaService.createMediaFromUrl(
                    uri,
                    filename,
                    PostConstants.UNKNOWN_FILE_SIZE,
                    mimeType,
                    accountId
            );

            logger.debug("Successfully processed media URI: {} -> ID: {}", uri, mediaDto.getId());
            return mediaDto.getId();
        } catch (Exception e) {
            logger.error("Failed to create/validate media record for URL: {} - Error: {}", uri, e.getMessage(), e);
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                PostConstants.INVALID_MEDIA_FILE_MESSAGE + ": " + uri);
        }
    }

    /**
     * Extracts filename from URL with fallback handling.
     *
     * @param url the URL to extract filename from
     * @return extracted filename or default filename
     */
    public String extractFilenameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return PostConstants.DEFAULT_FILENAME;
        }

        try {
            String[] parts = url.split("/");
            String filename = parts[parts.length - 1];
            
            // Remove query parameters if present
            int queryIndex = filename.indexOf('?');
            if (queryIndex != -1) {
                filename = filename.substring(0, queryIndex);
            }
            
            return filename.isEmpty() ? PostConstants.DEFAULT_FILENAME : filename;
        } catch (Exception e) {
            logger.warn("Failed to extract filename from URL: {} - Error: {}", url, e.getMessage());
            return PostConstants.DEFAULT_FILENAME;
        }
    }

    /**
     * Determines MIME type from URL extension with fallback handling.
     *
     * @param url the URL to analyze
     * @return detected MIME type or default MIME type
     */
    public String determineMimeTypeFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return PostConstants.DEFAULT_MIME_TYPE;
        }

        String lowerUrl = url.toLowerCase();
        
        // Check each supported extension
        for (Map.Entry<String, String> entry : EXTENSION_TO_MIME_TYPE.entrySet()) {
            if (lowerUrl.endsWith(entry.getKey())) {
                return entry.getValue();
            }
        }

        logger.debug("Unknown file extension for URL: {}, using default MIME type", url);
        return PostConstants.DEFAULT_MIME_TYPE;
    }

    /**
     * Validates that a list of media URIs is not null or empty.
     *
     * @param mediaUris list of media URIs to validate
     * @return true if the list contains valid URIs
     */
    public boolean hasValidMediaUris(List<String> mediaUris) {
        return mediaUris != null && !mediaUris.isEmpty();
    }

    /**
     * Checks if a URL appears to be a valid media URL based on extension.
     *
     * @param url the URL to check
     * @return true if the URL has a supported media extension
     */
    public boolean isValidMediaUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        String lowerUrl = url.toLowerCase();
        return EXTENSION_TO_MIME_TYPE.keySet().stream()
                .anyMatch(lowerUrl::endsWith);
    }

    /**
     * Gets the file extension from a URL.
     *
     * @param url the URL to analyze
     * @return file extension (including the dot) or empty string if not found
     */
    public String getFileExtension(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "";
        }

        String filename = extractFilenameFromUrl(url);
        int lastDotIndex = filename.lastIndexOf('.');
        
        return lastDotIndex != -1 ? filename.substring(lastDotIndex) : "";
    }

    /**
     * Checks if a MIME type represents an image.
     *
     * @param mimeType the MIME type to check
     * @return true if the MIME type is an image type
     */
    public boolean isImageMimeType(String mimeType) {
        return mimeType != null && mimeType.startsWith("image/");
    }

    /**
     * Checks if a MIME type represents a video.
     *
     * @param mimeType the MIME type to check
     * @return true if the MIME type is a video type
     */
    public boolean isVideoMimeType(String mimeType) {
        return mimeType != null && mimeType.startsWith("video/");
    }
}
