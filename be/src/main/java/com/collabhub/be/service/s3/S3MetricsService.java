package com.collabhub.be.service.s3;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * Service for collecting and recording S3 operation metrics.
 * Provides comprehensive monitoring of S3 operations for performance analysis.
 */
@Service
@ConditionalOnClass(MeterRegistry.class)
public class S3MetricsService {

    private static final Logger logger = LoggerFactory.getLogger(S3MetricsService.class);

    private final MeterRegistry meterRegistry;

    public S3MetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        logger.info("S3 metrics service initialized with MeterRegistry");
    }

    /**
     * Records a successful upload operation.
     */
    public void recordUploadSuccess(Duration duration, Long accountId, String resourceType, long fileSize) {
        Tags tags = Tags.of(
            "status", "success",
            "account_id", String.valueOf(accountId),
            "resource_type", resourceType,
            "file_size_category", categorizeFileSize(fileSize)
        );
        
        Counter.builder("s3.uploads.total")
            .tags(tags)
            .register(meterRegistry)
            .increment();
        Timer.builder("s3.upload.duration")
            .tags(tags)
            .register(meterRegistry)
            .record(duration);
        
        logger.debug("Recorded successful upload metrics: duration={}, account={}, type={}, size={}", 
            duration, accountId, resourceType, fileSize);
    }

    /**
     * Records a failed upload operation.
     */
    public void recordUploadFailure(Duration duration, Long accountId, String resourceType, String errorType) {
        Tags tags = Tags.of(
            "status", "failure",
            "account_id", String.valueOf(accountId),
            "resource_type", resourceType,
            "error_type", errorType
        );
        
        Counter.builder("s3.uploads.total")
            .tags(tags)
            .register(meterRegistry)
            .increment();
        if (duration != null) {
            Timer.builder("s3.upload.duration")
                .tags(tags)
                .register(meterRegistry)
                .record(duration);
        }
        
        logger.debug("Recorded failed upload metrics: duration={}, account={}, type={}, error={}", 
            duration, accountId, resourceType, errorType);
    }

    /**
     * Records a successful download operation.
     */
    public void recordDownloadSuccess(Duration duration, Long accountId, String resourceType) {
        Tags tags = Tags.of(
            "status", "success",
            "account_id", String.valueOf(accountId),
            "resource_type", resourceType
        );
        
        Counter.builder("s3.downloads.total")
            .tags(tags)
            .register(meterRegistry)
            .increment();
        Timer.builder("s3.download.duration")
            .tags(tags)
            .register(meterRegistry)
            .record(duration);
        
        logger.debug("Recorded successful download metrics: duration={}, account={}, type={}", 
            duration, accountId, resourceType);
    }

    /**
     * Records a failed download operation.
     */
    public void recordDownloadFailure(Duration duration, Long accountId, String resourceType, String errorType) {
        Tags tags = Tags.of(
            "status", "failure",
            "account_id", String.valueOf(accountId),
            "resource_type", resourceType,
            "error_type", errorType
        );
        
        Counter.builder("s3.downloads.total")
            .tags(tags)
            .register(meterRegistry)
            .increment();
        if (duration != null) {
            Timer.builder("s3.download.duration")
                .tags(tags)
                .register(meterRegistry)
                .record(duration);
        }
        
        logger.debug("Recorded failed download metrics: duration={}, account={}, type={}, error={}", 
            duration, accountId, resourceType, errorType);
    }

    /**
     * Records a file validation operation.
     */
    public void recordValidation(Duration duration, boolean success, String validationType) {
        Tags tags = Tags.of(
            "status", success ? "success" : "failure",
            "validation_type", validationType
        );
        
        Counter.builder("s3.validations.total")
            .tags(tags)
            .register(meterRegistry)
            .increment();
        Timer.builder("s3.validation.duration")
            .tags(tags)
            .register(meterRegistry)
            .record(duration);
        
        logger.debug("Recorded validation metrics: duration={}, success={}, type={}", 
            duration, success, validationType);
    }

    /**
     * Records presigned URL generation.
     */
    public void recordPresignedUrlGeneration(Duration duration, boolean success, String urlType, Long accountId) {
        Tags tags = Tags.of(
            "status", success ? "success" : "failure",
            "url_type", urlType,
            "account_id", String.valueOf(accountId)
        );
        
        Counter.builder("s3.presigned_urls.total")
            .description("Total number of presigned URLs generated")
            .tags(tags)
            .register(meterRegistry)
            .increment();
            
        Timer.builder("s3.presigned_url.generation.duration")
            .description("Presigned URL generation duration")
            .tags(tags)
            .register(meterRegistry)
            .record(duration);
        
        logger.debug("Recorded presigned URL metrics: duration={}, success={}, type={}, account={}", 
            duration, success, urlType, accountId);
    }

    /**
     * Records storage usage metrics.
     */
    public void recordStorageUsage(Long accountId, String resourceType, long totalBytes, int fileCount) {
        Tags tags = Tags.of(
            "account_id", String.valueOf(accountId),
            "resource_type", resourceType
        );
        
        meterRegistry.gauge("s3.storage.bytes", tags, totalBytes);
        meterRegistry.gauge("s3.storage.files", tags, fileCount);
        
        logger.debug("Recorded storage usage metrics: account={}, type={}, bytes={}, files={}", 
            accountId, resourceType, totalBytes, fileCount);
    }

    private String categorizeFileSize(long fileSize) {
        if (fileSize < 1024 * 1024) { // < 1MB
            return "small";
        } else if (fileSize < 10 * 1024 * 1024) { // < 10MB
            return "medium";
        } else if (fileSize < 100 * 1024 * 1024) { // < 100MB
            return "large";
        } else {
            return "xlarge";
        }
    }
}
