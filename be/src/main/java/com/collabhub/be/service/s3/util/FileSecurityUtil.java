package com.collabhub.be.service.s3.util;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.service.s3.constants.FileValidationConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Map;

/**
 * Utility class for file security validation.
 * Provides methods for validating file content, detecting malicious files, and security checks.
 */
public final class FileSecurityUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileSecurityUtil.class);

    /**
     * Validates file content against declared MIME type using magic numbers.
     */
    public static void validateFileContent(MultipartFile file, String contentType) {
        if (file == null || contentType == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "File and content type are required");
        }

        byte[] magicNumber = getMagicNumberForContentType(contentType);
        if (magicNumber == null) {
            logger.debug("No magic number validation available for content type: {}", contentType);
            return;
        }

        validateMagicNumber(file, magicNumber, contentType);
    }

    /**
     * Validates filename for security issues.
     */
    public static void validateFilenameSecurity(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.FILE_NAME_INVALID, 
                FileValidationConstants.ERROR_SUSPICIOUS_FILENAME);
        }

        validateFilenameLength(filename);
        validateSuspiciousPatterns(filename);
        validateDangerousExtensions(filename);
    }

    /**
     * Validates file size for security thresholds.
     */
    public static void validateFileSizeSecurity(long fileSize) {
        if (fileSize < FileValidationConstants.MIN_FILE_SIZE) {
            throw new BadRequestException(ErrorCode.FILE_CONTENT_INVALID, 
                FileValidationConstants.ERROR_FILE_TOO_SMALL);
        }

        if (fileSize > FileValidationConstants.SUSPICIOUS_FILE_SIZE_THRESHOLD) {
            logger.warn("File size exceeds security threshold: {} bytes", fileSize);
            throw new BadRequestException(ErrorCode.FILE_SIZE_EXCEEDED, 
                FileValidationConstants.ERROR_FILE_TOO_LARGE);
        }
    }

    /**
     * Checks if file extension is dangerous.
     */
    public static boolean isDangerousExtension(String filename) {
        if (filename == null) {
            return false;
        }

        String extension = extractFileExtension(filename).toLowerCase();
        return FileValidationConstants.DANGEROUS_EXTENSIONS.contains(extension);
    }

    /**
     * Sanitizes filename for safe storage.
     */
    public static String sanitizeFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "unnamed_file";
        }

        // Remove dangerous characters and patterns
        String sanitized = filename.replaceAll("[/\\\\:*?\"<>|]", "_")
                                  .replaceAll("\\.\\.", "_")
                                  .replaceAll("^\\.", "_")
                                  .trim();

        // Limit filename length
        if (sanitized.length() > FileValidationConstants.MAX_FILENAME_VALIDATION_LENGTH) {
            String extension = extractFileExtension(sanitized);
            String nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf('.'));
            sanitized = nameWithoutExt.substring(0, 
                FileValidationConstants.MAX_FILENAME_VALIDATION_LENGTH - extension.length()) + extension;
        }

        return sanitized.isEmpty() ? "unnamed_file" : sanitized;
    }

    private static byte[] getMagicNumberForContentType(String contentType) {
        byte[] magicNumber = FileValidationConstants.MAGIC_NUMBERS.get(contentType);
        if (magicNumber == null) {
            magicNumber = FileValidationConstants.EXTENDED_MAGIC_NUMBERS.get(contentType);
        }
        return magicNumber;
    }

    private static void validateMagicNumber(MultipartFile file, byte[] expectedMagicNumber, String contentType) {
        try {
            byte[] fileHeader = new byte[Math.max(expectedMagicNumber.length, 
                FileValidationConstants.MAGIC_NUMBER_READ_SIZE)];
            
            try (InputStream inputStream = file.getInputStream()) {
                int bytesRead = inputStream.read(fileHeader);
                if (bytesRead < expectedMagicNumber.length) {
                    throw new BadRequestException(ErrorCode.FILE_CONTENT_INVALID,
                        FileValidationConstants.ERROR_FILE_TOO_SMALL);
                }

                byte[] actualMagicNumber = Arrays.copyOf(fileHeader, expectedMagicNumber.length);
                if (!Arrays.equals(actualMagicNumber, expectedMagicNumber)) {
                    logger.warn("Magic number mismatch for content type {}: expected {}, got {}", 
                        contentType, Arrays.toString(expectedMagicNumber), Arrays.toString(actualMagicNumber));
                    throw new BadRequestException(ErrorCode.FILE_CONTENT_INVALID,
                        FileValidationConstants.ERROR_MAGIC_NUMBER_MISMATCH);
                }
            }
        } catch (IOException e) {
            logger.error("Failed to validate file content: {}", e.getMessage());
            throw new BadRequestException(ErrorCode.FILE_CONTENT_INVALID,
                "Failed to validate file content");
        }
    }

    private static void validateFilenameLength(String filename) {
        if (filename.length() > FileValidationConstants.MAX_FILENAME_VALIDATION_LENGTH) {
            throw new BadRequestException(ErrorCode.FILE_NAME_INVALID,
                "Filename is too long");
        }
    }

    private static void validateSuspiciousPatterns(String filename) {
        String upperFilename = filename.toUpperCase();
        for (String pattern : FileValidationConstants.SUSPICIOUS_PATTERNS) {
            if (upperFilename.contains(pattern)) {
                throw new BadRequestException(ErrorCode.FILE_NAME_INVALID,
                    FileValidationConstants.ERROR_SUSPICIOUS_FILENAME);
            }
        }
    }

    private static void validateDangerousExtensions(String filename) {
        if (isDangerousExtension(filename)) {
            throw new BadRequestException(ErrorCode.FILE_TYPE_NOT_ALLOWED,
                FileValidationConstants.ERROR_DANGEROUS_EXTENSION);
        }
    }

    private static String extractFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    private FileSecurityUtil() {
        // Private constructor to prevent instantiation
    }
}
