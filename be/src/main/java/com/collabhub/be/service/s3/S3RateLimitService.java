package com.collabhub.be.service.s3;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.service.s3.constants.S3Constants;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Enhanced S3 rate limiting service using Resilience4j.
 * Provides better performance, metrics, and configuration flexibility compared to Guava.
 * Implements per-account rate limiting for uploads and downloads with different strategies.
 */
@Service
public class S3RateLimitService {

    private static final Logger logger = LoggerFactory.getLogger(S3RateLimitService.class);

    private final RateLimiterRegistry rateLimiterRegistry;
    private final ConcurrentHashMap<String, RateLimiter> rateLimiters = new ConcurrentHashMap<>();

    // Rate limiter configuration constants
    private static final String UPLOAD_RATE_LIMITER_PREFIX = "s3-upload-";
    private static final String DOWNLOAD_RATE_LIMITER_PREFIX = "s3-download-";
    private static final String BATCH_UPLOAD_RATE_LIMITER_PREFIX = "s3-batch-upload-";

    public S3RateLimitService() {
        this.rateLimiterRegistry = RateLimiterRegistry.ofDefaults();
    }

    /**
     * Checks if upload is allowed for the given account.
     * Uses Resilience4j rate limiter with configurable limits and timeout.
     */
    public void checkUploadRateLimit(Long accountId) {
        validateAccountId(accountId);

        RateLimiter rateLimiter = getOrCreateUploadRateLimiter(accountId);
        
        boolean permitted = rateLimiter.acquirePermission(1);
        if (!permitted) {
            logger.warn("Upload rate limit exceeded for account: {} (limit: {} per minute)", 
                accountId, S3Constants.MAX_UPLOADS_PER_MINUTE);
            throw new BadRequestException(ErrorCode.UPLOAD_RATE_LIMIT_EXCEEDED,
                "Upload rate limit exceeded. Please try again later.");
        }

        logger.debug("Upload rate limit check passed for account: {}", accountId);
    }

    /**
     * Checks if download is allowed for the given account.
     * Uses separate rate limiter configuration for downloads.
     */
    public void checkDownloadRateLimit(Long accountId) {
        validateAccountId(accountId);

        RateLimiter rateLimiter = getOrCreateDownloadRateLimiter(accountId);
        
        boolean permitted = rateLimiter.acquirePermission(1);
        if (!permitted) {
            logger.warn("Download rate limit exceeded for account: {} (limit: {} per minute)", 
                accountId, S3Constants.MAX_DOWNLOADS_PER_MINUTE);
            throw new BadRequestException(ErrorCode.UPLOAD_RATE_LIMIT_EXCEEDED,
                "Download rate limit exceeded. Please try again later.");
        }

        logger.debug("Download rate limit check passed for account: {}", accountId);
    }

    /**
     * Checks if batch upload is allowed for the given account.
     * Uses burst-capable rate limiter for batch operations.
     */
    public void checkBatchUploadRateLimit(Long accountId, int fileCount) {
        validateAccountId(accountId);
        validateFileCount(fileCount);

        RateLimiter rateLimiter = getOrCreateBatchUploadRateLimiter(accountId);
        
        boolean permitted = rateLimiter.acquirePermission(fileCount);
        if (!permitted) {
            logger.warn("Batch upload rate limit exceeded for account: {} (requested: {} files, limit: {} per minute)", 
                accountId, fileCount, S3Constants.MAX_UPLOADS_PER_MINUTE);
            throw new BadRequestException(ErrorCode.UPLOAD_RATE_LIMIT_EXCEEDED,
                String.format("Batch upload rate limit exceeded for %d files. Please try again later.", fileCount));
        }

        logger.debug("Batch upload rate limit check passed for account: {} ({} files)", accountId, fileCount);
    }

    /**
     * Gets current upload rate limit metrics for an account.
     */
    public RateLimitMetrics getUploadRateMetrics(Long accountId) {
        if (accountId == null) {
            return new RateLimitMetrics(0, 0, 0);
        }

        String key = UPLOAD_RATE_LIMITER_PREFIX + accountId;
        RateLimiter rateLimiter = rateLimiters.get(key);
        
        if (rateLimiter == null) {
            return new RateLimitMetrics(S3Constants.MAX_UPLOADS_PER_MINUTE, S3Constants.MAX_UPLOADS_PER_MINUTE, 0);
        }

        RateLimiter.Metrics metrics = rateLimiter.getMetrics();
        return new RateLimitMetrics(
            S3Constants.MAX_UPLOADS_PER_MINUTE,
            metrics.getAvailablePermissions(),
            metrics.getNumberOfWaitingThreads()
        );
    }

    /**
     * Gets current download rate limit metrics for an account.
     */
    public RateLimitMetrics getDownloadRateMetrics(Long accountId) {
        if (accountId == null) {
            return new RateLimitMetrics(0, 0, 0);
        }

        String key = DOWNLOAD_RATE_LIMITER_PREFIX + accountId;
        RateLimiter rateLimiter = rateLimiters.get(key);
        
        if (rateLimiter == null) {
            return new RateLimitMetrics(S3Constants.MAX_DOWNLOADS_PER_MINUTE, S3Constants.MAX_DOWNLOADS_PER_MINUTE, 0);
        }

        RateLimiter.Metrics metrics = rateLimiter.getMetrics();
        return new RateLimitMetrics(
            S3Constants.MAX_DOWNLOADS_PER_MINUTE,
            metrics.getAvailablePermissions(),
            metrics.getNumberOfWaitingThreads()
        );
    }

    /**
     * Clears rate limiters for an account (useful for testing or admin operations).
     */
    public void clearRateLimiters(Long accountId) {
        if (accountId == null) {
            return;
        }

        String uploadKey = UPLOAD_RATE_LIMITER_PREFIX + accountId;
        String downloadKey = DOWNLOAD_RATE_LIMITER_PREFIX + accountId;
        String batchKey = BATCH_UPLOAD_RATE_LIMITER_PREFIX + accountId;

        rateLimiters.remove(uploadKey);
        rateLimiters.remove(downloadKey);
        rateLimiters.remove(batchKey);
        
        logger.info("Cleared rate limiters for account: {}", accountId);
    }

    /**
     * Gets or creates upload rate limiter for an account.
     */
    private RateLimiter getOrCreateUploadRateLimiter(Long accountId) {
        String key = UPLOAD_RATE_LIMITER_PREFIX + accountId;
        return rateLimiters.computeIfAbsent(key, k -> {
            logger.debug("Creating upload rate limiter for account: {} (limit: {} per minute)", 
                accountId, S3Constants.MAX_UPLOADS_PER_MINUTE);
            
            RateLimiterConfig config = RateLimiterConfig.custom()
                .limitForPeriod(S3Constants.MAX_UPLOADS_PER_MINUTE)
                .limitRefreshPeriod(Duration.ofMinutes(1))
                .timeoutDuration(Duration.ofSeconds(1))
                .build();
                
            return rateLimiterRegistry.rateLimiter(k, config);
        });
    }

    /**
     * Gets or creates download rate limiter for an account.
     */
    private RateLimiter getOrCreateDownloadRateLimiter(Long accountId) {
        String key = DOWNLOAD_RATE_LIMITER_PREFIX + accountId;
        return rateLimiters.computeIfAbsent(key, k -> {
            logger.debug("Creating download rate limiter for account: {} (limit: {} per minute)", 
                accountId, S3Constants.MAX_DOWNLOADS_PER_MINUTE);
            
            RateLimiterConfig config = RateLimiterConfig.custom()
                .limitForPeriod(S3Constants.MAX_DOWNLOADS_PER_MINUTE)
                .limitRefreshPeriod(Duration.ofMinutes(1))
                .timeoutDuration(Duration.ofSeconds(1))
                .build();
                
            return rateLimiterRegistry.rateLimiter(k, config);
        });
    }

    /**
     * Gets or creates batch upload rate limiter for an account.
     * Uses burst-capable configuration for batch operations.
     */
    private RateLimiter getOrCreateBatchUploadRateLimiter(Long accountId) {
        String key = BATCH_UPLOAD_RATE_LIMITER_PREFIX + accountId;
        return rateLimiters.computeIfAbsent(key, k -> {
            logger.debug("Creating batch upload rate limiter for account: {} (limit: {} per minute)", 
                accountId, S3Constants.MAX_UPLOADS_PER_MINUTE);
            
            RateLimiterConfig config = RateLimiterConfig.custom()
                .limitForPeriod(S3Constants.MAX_UPLOADS_PER_MINUTE)
                .limitRefreshPeriod(Duration.ofMinutes(1))
                .timeoutDuration(Duration.ofSeconds(5)) // Longer timeout for batch operations
                .build();
                
            return rateLimiterRegistry.rateLimiter(k, config);
        });
    }

    private void validateAccountId(Long accountId) {
        if (accountId == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Account ID is required");
        }
    }

    private void validateFileCount(int fileCount) {
        if (fileCount <= 0) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "File count must be positive");
        }
    }

    /**
     * Rate limit metrics data class.
     */
    public static class RateLimitMetrics {
        private final int limitPerPeriod;
        private final int availablePermissions;
        private final int waitingThreads;

        public RateLimitMetrics(int limitPerPeriod, int availablePermissions, int waitingThreads) {
            this.limitPerPeriod = limitPerPeriod;
            this.availablePermissions = availablePermissions;
            this.waitingThreads = waitingThreads;
        }

        public int getLimitPerPeriod() { return limitPerPeriod; }
        public int getAvailablePermissions() { return availablePermissions; }
        public int getWaitingThreads() { return waitingThreads; }
    }
}
