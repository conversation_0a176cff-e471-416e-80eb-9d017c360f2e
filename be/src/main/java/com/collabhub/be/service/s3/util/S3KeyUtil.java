package com.collabhub.be.service.s3.util;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

/**
 * Utility class for S3 key operations.
 * Provides methods for generating, extracting, and validating S3 keys.
 */
public final class S3KeyUtil {

    private static final Logger logger = LoggerFactory.getLogger(S3KeyUtil.class);

    /**
     * Generates a unique S3 key for a file.
     * Format: {accountId}/{resourceType}/{uuid}.{extension}
     */
    public static String generateS3Key(Long accountId, String resourceType, String originalFilename) {
        if (accountId == null || resourceType == null || resourceType.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, 
                "Account ID and resource type are required");
        }

        String sanitizedFilename = FileSecurityUtil.sanitizeFilename(originalFilename);
        String extension = extractFileExtension(sanitizedFilename);
        String uniqueFilename = UUID.randomUUID().toString() + extension;
        
        return String.format("%d/%s/%s", accountId, resourceType.trim(), uniqueFilename);
    }

    /**
     * Extracts S3 key from a file URL.
     * Handles both regular URLs and presigned URLs with query parameters.
     */
    public static String extractKeyFromUrl(String fileUrl, String bucketName) {
        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "File URL is required");
        }

        if (bucketName == null || bucketName.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Bucket name is required");
        }

        try {
            String urlWithoutQuery = removeQueryParameters(fileUrl);
            return extractKeyFromCleanUrl(urlWithoutQuery, bucketName);
        } catch (Exception e) {
            logger.error("Failed to extract S3 key from URL: {} - {}", fileUrl, e.getMessage());
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Invalid file URL format");
        }
    }

    /**
     * Validates that an S3 key belongs to the specified account.
     */
    public static void validateKeyAccountOwnership(String s3Key, Long accountId) {
        if (s3Key == null || accountId == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, 
                "S3 key and account ID are required");
        }

        if (!s3Key.startsWith(accountId + "/")) {
            logger.warn("S3 key does not belong to account {}: {}", accountId, s3Key);
            throw new BadRequestException(ErrorCode.ACCESS_DENIED, 
                "File does not belong to the specified account");
        }
    }

    /**
     * Extracts account ID from S3 key.
     */
    public static Long extractAccountIdFromKey(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "S3 key is required");
        }

        String[] parts = s3Key.split("/");
        if (parts.length < 2) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Invalid S3 key format");
        }

        try {
            return Long.parseLong(parts[0]);
        } catch (NumberFormatException e) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Invalid account ID in S3 key");
        }
    }

    /**
     * Extracts resource type from S3 key.
     */
    public static String extractResourceTypeFromKey(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "S3 key is required");
        }

        String[] parts = s3Key.split("/");
        if (parts.length < 3) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Invalid S3 key format");
        }

        return parts[1];
    }

    /**
     * Extracts filename from S3 key.
     */
    public static String extractFilenameFromKey(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "S3 key is required");
        }

        String[] parts = s3Key.split("/");
        if (parts.length < 3) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Invalid S3 key format");
        }

        return parts[parts.length - 1];
    }

    /**
     * Validates S3 key format.
     */
    public static boolean isValidS3KeyFormat(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            return false;
        }

        String[] parts = s3Key.split("/");
        if (parts.length != 3) {
            return false;
        }

        // Validate account ID part
        try {
            Long.parseLong(parts[0]);
        } catch (NumberFormatException e) {
            return false;
        }

        // Validate resource type part
        if (parts[1].trim().isEmpty()) {
            return false;
        }

        // Validate filename part
        if (parts[2].trim().isEmpty()) {
            return false;
        }

        return true;
    }

    private static String extractFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    private static String removeQueryParameters(String url) {
        int queryIndex = url.indexOf('?');
        return queryIndex != -1 ? url.substring(0, queryIndex) : url;
    }

    private static String extractKeyFromCleanUrl(String urlWithoutQuery, String bucketName) {
        int bucketIndex = urlWithoutQuery.indexOf(bucketName);
        if (bucketIndex == -1) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, 
                "Bucket name not found in URL");
        }

        int keyStartIndex = bucketIndex + bucketName.length() + 1;
        if (keyStartIndex >= urlWithoutQuery.length()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, 
                "No S3 key found after bucket name in URL");
        }

        return urlWithoutQuery.substring(keyStartIndex);
    }

    private S3KeyUtil() {
        // Private constructor to prevent instantiation
    }
}
