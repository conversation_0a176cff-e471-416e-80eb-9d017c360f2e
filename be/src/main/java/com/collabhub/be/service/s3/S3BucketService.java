package com.collabhub.be.service.s3;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.InternalServerErrorException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.service.s3.constants.S3Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.model.TransitionStorageClass;

import java.time.Duration;
import java.time.Instant;

/**
 * Service for managing S3 bucket operations.
 * Handles bucket creation, lifecycle policies, and bucket configuration.
 */
@Service
public class S3BucketService {

    private static final Logger logger = LoggerFactory.getLogger(S3BucketService.class);

    private final S3Client s3Client;
    private final S3Properties s3Properties;
    private final S3MetricsService metricsService;

    public S3BucketService(S3Client s3Client, S3Properties s3Properties, S3MetricsService metricsService) {
        this.s3Client = s3Client;
        this.s3Properties = s3Properties;
        this.metricsService = metricsService;
    }

    /**
     * Ensures the shared bucket exists, creating it if necessary.
     * This method is account-agnostic since we use a single shared bucket.
     */
    public void ensureBucketExists() {
        String bucketName = s3Properties.getBucketName();
        Instant start = Instant.now();
        boolean success = false;

        try {
            if (bucketExists(bucketName)) {
                logger.debug(S3Constants.LOG_BUCKET_EXISTS, bucketName);
                success = true;
                return;
            }

            createBucket(bucketName);
            configureBucketLifecycle(bucketName);
            success = true;
            
        } catch (Exception e) {
            logger.error("Error ensuring bucket exists: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR,
                "Failed to verify bucket existence: " + e.getMessage());
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            metricsService.recordPresignedUrlGeneration(duration, success, "bucket_ensure", 0L);
        }
    }

    /**
     * Creates the S3 bucket with appropriate configuration.
     */
    public void createBucket(String bucketName) {
        try {
            CreateBucketRequest.Builder requestBuilder = CreateBucketRequest.builder()
                .bucket(bucketName);

            // For regions other than us-east-1, we need to specify the location constraint
            if (!s3Properties.getRegion().equals("us-east-1")) {
                requestBuilder.createBucketConfiguration(
                    CreateBucketConfiguration.builder()
                        .locationConstraint(BucketLocationConstraint.fromValue(s3Properties.getRegion()))
                        .build()
                );
            }

            s3Client.createBucket(requestBuilder.build());
            logger.info(S3Constants.LOG_BUCKET_CREATED, bucketName);

        } catch (Exception e) {
            logger.error("Failed to create bucket: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, 
                "Failed to create bucket: " + e.getMessage());
        }
    }

    /**
     * Configures lifecycle policies for cost optimization.
     */
    public void configureBucketLifecycle(String bucketName) {
        try {
            LifecycleRule rule = LifecycleRule.builder()
                .id("cost-optimization")
                .status(ExpirationStatus.ENABLED)
                .filter(LifecycleRuleFilter.builder()
                    .prefix("") // Apply to all objects
                    .build())
                .transitions(
                    // Transition to Standard-IA after 30 days
                    Transition.builder()
                        .days(30)
                        .storageClass(TransitionStorageClass.STANDARD_IA)
                        .build(),
                    // Transition to Glacier after 90 days
                    Transition.builder()
                        .days(90)
                        .storageClass(TransitionStorageClass.GLACIER)
                        .build(),
                    // Transition to Deep Archive after 365 days
                    Transition.builder()
                        .days(365)
                        .storageClass(TransitionStorageClass.DEEP_ARCHIVE)
                        .build()
                )
                .build();

            BucketLifecycleConfiguration lifecycleConfig = BucketLifecycleConfiguration.builder()
                .rules(rule)
                .build();

            PutBucketLifecycleConfigurationRequest lifecycleRequest = 
                PutBucketLifecycleConfigurationRequest.builder()
                    .bucket(bucketName)
                    .lifecycleConfiguration(lifecycleConfig)
                    .build();

            s3Client.putBucketLifecycleConfiguration(lifecycleRequest);
            logger.info("Configured lifecycle policy for bucket: {}", bucketName);

        } catch (Exception e) {
            logger.warn("Failed to configure lifecycle policy for bucket {}: {}", bucketName, e.getMessage());
            // Don't throw exception as this is not critical for basic functionality
        }
    }

    /**
     * Configures bucket CORS for web access.
     */
    public void configureBucketCors(String bucketName) {
        try {
            CORSRule corsRule = CORSRule.builder()
                .allowedHeaders("*")
                .allowedMethods("GET", "PUT", "POST", "DELETE")
                .allowedOrigins("*") // In production, specify actual origins
                .maxAgeSeconds(3000)
                .build();

            CORSConfiguration corsConfiguration = CORSConfiguration.builder()
                .corsRules(corsRule)
                .build();

            PutBucketCorsRequest corsRequest = PutBucketCorsRequest.builder()
                .bucket(bucketName)
                .corsConfiguration(corsConfiguration)
                .build();

            s3Client.putBucketCors(corsRequest);
            logger.info("Configured CORS policy for bucket: {}", bucketName);

        } catch (Exception e) {
            logger.warn("Failed to configure CORS policy for bucket {}: {}", bucketName, e.getMessage());
        }
    }

    /**
     * Configures bucket versioning.
     */
    public void configureBucketVersioning(String bucketName, boolean enabled) {
        try {
            VersioningConfiguration versioningConfig = VersioningConfiguration.builder()
                .status(enabled ? BucketVersioningStatus.ENABLED : BucketVersioningStatus.SUSPENDED)
                .build();

            PutBucketVersioningRequest versioningRequest = PutBucketVersioningRequest.builder()
                .bucket(bucketName)
                .versioningConfiguration(versioningConfig)
                .build();

            s3Client.putBucketVersioning(versioningRequest);
            logger.info("Configured versioning for bucket: {} (enabled: {})", bucketName, enabled);

        } catch (Exception e) {
            logger.warn("Failed to configure versioning for bucket {}: {}", bucketName, e.getMessage());
        }
    }

    /**
     * Gets bucket location.
     */
    public String getBucketLocation(String bucketName) {
        try {
            GetBucketLocationRequest locationRequest = GetBucketLocationRequest.builder()
                .bucket(bucketName)
                .build();

            GetBucketLocationResponse locationResponse = s3Client.getBucketLocation(locationRequest);
            return locationResponse.locationConstraintAsString();

        } catch (Exception e) {
            logger.error("Failed to get bucket location for {}: {}", bucketName, e.getMessage());
            return "unknown";
        }
    }

    /**
     * Checks if bucket exists.
     */
    public boolean bucketExists(String bucketName) {
        try {
            HeadBucketRequest headBucketRequest = HeadBucketRequest.builder()
                .bucket(bucketName)
                .build();
                
            s3Client.headBucket(headBucketRequest);
            return true;
            
        } catch (NoSuchBucketException e) {
            return false;
        } catch (Exception e) {
            logger.error("Error checking bucket existence for {}: {}", bucketName, e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR,
                "Failed to check bucket existence: " + e.getMessage());
        }
    }

    /**
     * Gets bucket policy.
     */
    public String getBucketPolicy(String bucketName) {
        try {
            GetBucketPolicyRequest policyRequest = GetBucketPolicyRequest.builder()
                .bucket(bucketName)
                .build();

            GetBucketPolicyResponse policyResponse = s3Client.getBucketPolicy(policyRequest);
            return policyResponse.policy();

        } catch (Exception e) {
            logger.warn("Failed to get bucket policy for {}: {}", bucketName, e.getMessage());
            return null;
        }
    }

    /**
     * Sets bucket policy for security.
     */
    public void setBucketPolicy(String bucketName, String policyJson) {
        try {
            PutBucketPolicyRequest policyRequest = PutBucketPolicyRequest.builder()
                .bucket(bucketName)
                .policy(policyJson)
                .build();

            s3Client.putBucketPolicy(policyRequest);
            logger.info("Set bucket policy for: {}", bucketName);

        } catch (Exception e) {
            logger.error("Failed to set bucket policy for {}: {}", bucketName, e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR,
                "Failed to set bucket policy: " + e.getMessage());
        }
    }
}
