package com.collabhub.be.service.s3.constants;

import java.util.Map;
import java.util.Set;

/**
 * Constants for file validation operations.
 * Contains magic numbers, allowed/blocked extensions, and validation rules.
 */
public final class FileValidationConstants {

    // Magic numbers for file type validation
    public static final Map<String, byte[]> MAGIC_NUMBERS = Map.of(
        // Image magic numbers
        "image/jpeg", new byte[]{(byte) 0xFF, (byte) 0xD8, (byte) 0xFF},
        "image/png", new byte[]{(byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A},
        "image/gif", new byte[]{0x47, 0x49, 0x46, 0x38},
        "image/webp", new byte[]{0x52, 0x49, 0x46, 0x46}, // RIFF header
        
        // Video magic numbers
        "video/mp4", new byte[]{0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70}, // ftyp
        "video/quicktime", new byte[]{0x00, 0x00, 0x00, 0x14, 0x66, 0x74, 0x79, 0x70}, // ftyp
        "video/x-msvideo", new byte[]{0x52, 0x49, 0x46, 0x46} // RIFF header for AVI
    );

    // Additional magic numbers for enhanced validation
    public static final Map<String, byte[]> EXTENDED_MAGIC_NUMBERS = Map.of(
        // Additional video formats
        "video/webm", new byte[]{0x1A, 0x45, (byte) 0xDF, (byte) 0xA3},
        "video/x-flv", new byte[]{0x46, 0x4C, 0x56, 0x01},
        
        // Document formats (if needed in future)
        "application/pdf", new byte[]{0x25, 0x50, 0x44, 0x46},
        
        // Archive formats (for security scanning)
        "application/zip", new byte[]{0x50, 0x4B, 0x03, 0x04},
        "application/x-rar-compressed", new byte[]{0x52, 0x61, 0x72, 0x21}
    );

    // Dangerous file extensions that should always be blocked
    public static final Set<String> DANGEROUS_EXTENSIONS = Set.of(
        ".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js",
        ".jar", ".php", ".asp", ".jsp", ".sh", ".ps1", ".py", ".rb",
        ".pl", ".cgi", ".htaccess", ".htpasswd", ".ini", ".dll", ".so",
        ".app", ".deb", ".rpm", ".dmg", ".pkg", ".msi", ".reg"
    );

    // Suspicious file patterns
    public static final Set<String> SUSPICIOUS_PATTERNS = Set.of(
        "..", "/", "\\", "<", ">", ":", "\"", "|", "?", "*",
        "CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4",
        "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2",
        "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
    );

    // File size validation thresholds
    public static final long MIN_FILE_SIZE = 1; // 1 byte minimum
    public static final long SUSPICIOUS_FILE_SIZE_THRESHOLD = 500 * 1024 * 1024; // 500MB
    
    // Content validation settings
    public static final int MAGIC_NUMBER_READ_SIZE = 16; // Read first 16 bytes for validation
    public static final int MAX_FILENAME_VALIDATION_LENGTH = 1000; // Prevent DoS attacks
    
    // Validation error messages
    public static final String ERROR_FILE_TOO_SMALL = "File content is too small or corrupted";
    public static final String ERROR_MAGIC_NUMBER_MISMATCH = "File content does not match declared content type";
    public static final String ERROR_SUSPICIOUS_FILENAME = "Filename contains suspicious patterns";
    public static final String ERROR_DANGEROUS_EXTENSION = "File extension is blocked for security reasons";
    public static final String ERROR_FILE_TOO_LARGE = "File size exceeds security threshold";
    
    private FileValidationConstants() {
        // Private constructor to prevent instantiation
    }
}
