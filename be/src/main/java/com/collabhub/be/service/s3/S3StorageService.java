package com.collabhub.be.service.s3;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.InternalServerErrorException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.posts.dto.FileValidationResponse;
import com.collabhub.be.modules.posts.dto.PresignedUploadResponse;
import com.collabhub.be.service.s3.util.S3KeyUtil;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;

import java.time.Duration;
import java.time.Instant;

/**
 * Main S3 storage service that coordinates all S3 operations.
 * Acts as a facade for the various specialized S3 services.
 */
@Service
@Validated
public class S3StorageService {

    private static final Logger logger = LoggerFactory.getLogger(S3StorageService.class);

    private final S3Client s3Client;
    private final S3Properties s3Properties;
    private final S3ValidationService validationService;
    private final S3UrlService urlService;
    private final S3BucketService bucketService;
    private final S3MultipartUploadService multipartUploadService;
    private final S3RateLimitService rateLimitService;
    private final S3MetricsService metricsService;

    public S3StorageService(S3Client s3Client, S3Properties s3Properties,
                           S3ValidationService validationService, S3UrlService urlService,
                           S3BucketService bucketService, S3MultipartUploadService multipartUploadService,
                           S3RateLimitService rateLimitService, S3MetricsService metricsService) {
        this.s3Client = s3Client;
        this.s3Properties = s3Properties;
        this.validationService = validationService;
        this.urlService = urlService;
        this.bucketService = bucketService;
        this.multipartUploadService = multipartUploadService;
        this.rateLimitService = rateLimitService;
        this.metricsService = metricsService;
    }

    /**
     * Uploads a file to S3 and returns the file URL.
     * Files are organized by account and resource type: {accountId}/{resourceType}/{filename}
     */
    public String uploadFile(@Valid @NotNull MultipartFile file, @NotNull @Positive Long accountId, 
                           @NotBlank String resourceType) {
        Instant start = Instant.now();
        boolean success = false;
        
        try {
            // Rate limiting check
            rateLimitService.checkUploadRateLimit(accountId);
            
            // Validate file
            validationService.validateFileForUpload(file);
            
            // Upload using appropriate method based on file size
            String fileUrl = multipartUploadService.uploadFile(file, accountId, resourceType);
            
            success = true;
            return fileUrl;
            
        } catch (Exception e) {
            Duration duration = Duration.between(start, Instant.now());
            metricsService.recordUploadFailure(duration, accountId, resourceType, e.getClass().getSimpleName());
            throw e;
        }
    }

    /**
     * Deletes a file from S3.
     * Validates that the file belongs to the specified account before deletion.
     */
    public void deleteFile(@NotBlank String fileUrl, @NotNull @Positive Long accountId) {
        Instant start = Instant.now();
        boolean success = false;
        
        try {
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.extractKeyFromUrl(fileUrl, bucketName);
            
            // Validate account ownership
            S3KeyUtil.validateKeyAccountOwnership(key, accountId);
            
            DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build();
                
            s3Client.deleteObject(deleteRequest);
            
            success = true;
            logger.info("Successfully deleted file: {} from bucket: {} (account: {})", 
                key, bucketName, accountId);
                
        } catch (Exception e) {
            logger.error("Failed to delete file from S3: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, 
                "Failed to delete file: " + e.getMessage());
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            String resourceType = extractResourceTypeFromUrl(fileUrl);
            if (success) {
                metricsService.recordDownloadSuccess(duration, accountId, resourceType);
            } else {
                metricsService.recordDownloadFailure(duration, accountId, resourceType, "deletion_failed");
            }
        }
    }

    /**
     * Generates a presigned URL for file download.
     */
    public String generatePresignedDownloadUrl(@NotBlank String fileUrl, @NotNull @Positive Long accountId) {
        // Rate limiting check
        rateLimitService.checkDownloadRateLimit(accountId);
        
        return urlService.generatePresignedDownloadUrl(fileUrl, accountId);
    }

    /**
     * Generates a presigned URL for file download with custom duration.
     */
    public String generatePresignedDownloadUrl(@NotBlank String fileUrl, @NotNull @Positive Long accountId, 
                                             @NotNull Duration duration) {
        // Rate limiting check
        rateLimitService.checkDownloadRateLimit(accountId);
        
        return urlService.generatePresignedDownloadUrl(fileUrl, accountId, duration);
    }

    /**
     * Generates a presigned URL for file upload with enforced constraints.
     */
    public PresignedUploadResponse generatePresignedUploadUrl(@NotNull @Positive Long accountId,
                                                            @NotBlank String resourceType,
                                                            @NotBlank String fileName,
                                                            @NotBlank String contentType,
                                                            Long maxFileSize) {
        // Rate limiting check
        rateLimitService.checkUploadRateLimit(accountId);
        
        return urlService.generatePresignedUploadUrl(accountId, resourceType, fileName, contentType, maxFileSize);
    }

    /**
     * Validates that an uploaded file exists and meets constraints.
     */
    public boolean validateUploadedFile(@NotBlank String fileUrl, @NotNull @Positive Long accountId,
                                      String expectedContentType, Long maxFileSize) {
        return validationService.validateUploadedFile(fileUrl, accountId, expectedContentType, maxFileSize);
    }

    /**
     * Enhanced file validation that returns detailed validation information.
     */
    public FileValidationResponse validateUploadedFileDetailed(@NotBlank String fileUrl, 
                                                              @NotNull @Positive Long accountId,
                                                              String expectedContentType, 
                                                              Long maxFileSize) {
        return validationService.validateUploadedFileDetailed(fileUrl, accountId, expectedContentType, maxFileSize);
    }

    /**
     * Ensures the shared bucket exists.
     */
    public void ensureBucketExists() {
        bucketService.ensureBucketExists();
    }

    /**
     * Generates a file URL from bucket name and key.
     */
    public String generateFileUrl(@NotBlank String bucketName, @NotBlank String key) {
        return urlService.generateFileUrl(bucketName, key);
    }

    /**
     * Gets the configured S3 bucket name.
     */
    public String getBucketName() {
        return s3Properties.getBucketName();
    }

    /**
     * Generates batch presigned upload URLs.
     */
    public PresignedUploadResponse[] generateBatchPresignedUploadUrls(@NotNull @Positive Long accountId,
                                                                     @NotBlank String resourceType,
                                                                     String[] fileNames,
                                                                     String[] contentTypes,
                                                                     Long[] maxFileSizes) {
        // Rate limiting check for batch operations
        rateLimitService.checkBatchUploadRateLimit(accountId, fileNames.length);
        
        return urlService.generateBatchPresignedUploadUrls(accountId, resourceType, fileNames, contentTypes, maxFileSizes);
    }

    /**
     * Gets upload rate limit metrics for an account.
     */
    public S3RateLimitService.RateLimitMetrics getUploadRateMetrics(@NotNull @Positive Long accountId) {
        return rateLimitService.getUploadRateMetrics(accountId);
    }

    /**
     * Gets download rate limit metrics for an account.
     */
    public S3RateLimitService.RateLimitMetrics getDownloadRateMetrics(@NotNull @Positive Long accountId) {
        return rateLimitService.getDownloadRateMetrics(accountId);
    }

    /**
     * Records storage usage metrics for monitoring.
     */
    public void recordStorageUsage(@NotNull @Positive Long accountId, @NotBlank String resourceType, 
                                 long totalBytes, int fileCount) {
        metricsService.recordStorageUsage(accountId, resourceType, totalBytes, fileCount);
    }

    private String extractResourceTypeFromUrl(String fileUrl) {
        try {
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.extractKeyFromUrl(fileUrl, bucketName);
            return S3KeyUtil.extractResourceTypeFromKey(key);
        } catch (Exception e) {
            return "unknown";
        }
    }
}
