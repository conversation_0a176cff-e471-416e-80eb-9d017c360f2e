package com.collabhub.be.service.s3;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.InternalServerErrorException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.posts.dto.PresignedUploadResponse;
import com.collabhub.be.service.s3.constants.S3Constants;
import com.collabhub.be.service.s3.util.S3KeyUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.time.Duration;
import java.time.Instant;

/**
 * Service for generating and managing S3 presigned URLs.
 * Handles both upload and download presigned URL generation with caching.
 */
@Service
@Validated
public class S3UrlService {

    private static final Logger logger = LoggerFactory.getLogger(S3UrlService.class);

    private final S3Client s3Client;
    private final S3Presigner s3Presigner;
    private final S3Properties s3Properties;
    private final S3ValidationService validationService;
    private final S3MetricsService metricsService;

    public S3UrlService(S3Client s3Client, S3Presigner s3Presigner, S3Properties s3Properties,
                       S3ValidationService validationService, S3MetricsService metricsService) {
        this.s3Client = s3Client;
        this.s3Presigner = s3Presigner;
        this.s3Properties = s3Properties;
        this.validationService = validationService;
        this.metricsService = metricsService;
    }

    /**
     * Generates a presigned URL for file download with caching.
     * Uses content-type-aware expiration times for optimal security/UX balance.
     */
    @Cacheable(value = "presignedDownloadUrls", key = "#fileUrl + '_' + #accountId")
    public String generatePresignedDownloadUrl(@NotBlank String fileUrl, @NotNull @Positive Long accountId) {
        Duration duration = determineOptimalDuration(fileUrl);
        return generatePresignedDownloadUrlInternal(fileUrl, accountId, duration);
    }

    /**
     * Generates a presigned URL for file download with custom duration.
     */
    public String generatePresignedDownloadUrl(@NotBlank String fileUrl, @NotNull @Positive Long accountId, 
                                             @NotNull Duration duration) {
        return generatePresignedDownloadUrlInternal(fileUrl, accountId, duration);
    }

    /**
     * Generates a presigned URL for file upload with enforced constraints.
     */
    public PresignedUploadResponse generatePresignedUploadUrl(@NotNull @Positive Long accountId,
                                                            @NotBlank String resourceType,
                                                            @NotBlank String fileName,
                                                            @NotBlank String contentType,
                                                            Long maxFileSize) {
        Instant start = Instant.now();
        boolean success = false;
        
        try {
            validationService.validateContentType(contentType);
            
            long fileSizeLimit = maxFileSize != null ? maxFileSize : S3Constants.DEFAULT_MAX_FILE_SIZE;
            
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.generateS3Key(accountId, resourceType, fileName);
            
            PutObjectRequest putRequest = createPutObjectRequest(bucketName, key, contentType, fileSizeLimit);
            PutObjectPresignRequest presignRequest = createPutObjectPresignRequest(putRequest, S3Constants.UPLOAD_PRESIGNED_URL_DURATION);
            
            String presignedUrl = s3Presigner.presignPutObject(presignRequest).url().toString();
            String finalUrl = generateFileUrl(bucketName, key);
            
            success = true;
            logger.info("Generated presigned upload URL for file: {} with constraints: type={}, maxSize={} (account: {}, resourceType: {})",
                       key, contentType, fileSizeLimit, accountId, resourceType);
            
            return new PresignedUploadResponse(presignedUrl, finalUrl, key, contentType, fileSizeLimit);
            
        } catch (Exception e) {
            logger.error("Failed to generate presigned upload URL: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, 
                S3Constants.ERROR_UPLOAD_FAILED);
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            metricsService.recordPresignedUrlGeneration(duration, success, "upload", accountId);
        }
    }

    /**
     * Generates a file URL from bucket name and key.
     */
    public String generateFileUrl(@NotBlank String bucketName, @NotBlank String key) {
        if (s3Properties.getEndpoint() != null) {
            // For MinIO or custom S3 endpoint
            return s3Properties.getEndpoint() + "/" + bucketName + "/" + key;
        } else {
            // For AWS S3
            return "https://" + bucketName + ".s3." + s3Properties.getRegion() + ".amazonaws.com/" + key;
        }
    }

    /**
     * Determines optimal presigned URL duration based on file type and content.
     * Implements industry best practices for security vs UX balance.
     */
    private Duration determineOptimalDuration(String fileUrl) {
        if (fileUrl == null) {
            return S3Constants.DEFAULT_PRESIGNED_URL_DURATION;
        }

        String lowerUrl = fileUrl.toLowerCase();

        // Video files get longer duration due to larger size and slower loading
        if (lowerUrl.contains(".mp4") || lowerUrl.contains(".mov") ||
            lowerUrl.contains(".avi") || lowerUrl.contains(".webm") ||
            lowerUrl.contains("video/")) {
            return S3Constants.VIDEO_PRESIGNED_URL_DURATION;
        }

        // Images and other content get standard duration
        return S3Constants.DEFAULT_PRESIGNED_URL_DURATION;
    }

    /**
     * Generates multiple presigned upload URLs for batch operations.
     */
    public PresignedUploadResponse[] generateBatchPresignedUploadUrls(@NotNull @Positive Long accountId,
                                                                     @NotBlank String resourceType,
                                                                     String[] fileNames,
                                                                     String[] contentTypes,
                                                                     Long[] maxFileSizes) {
        if (fileNames.length != contentTypes.length || 
            (maxFileSizes != null && fileNames.length != maxFileSizes.length)) {
            throw new IllegalArgumentException("Array lengths must match");
        }

        PresignedUploadResponse[] responses = new PresignedUploadResponse[fileNames.length];
        
        for (int i = 0; i < fileNames.length; i++) {
            Long maxFileSize = maxFileSizes != null ? maxFileSizes[i] : null;
            responses[i] = generatePresignedUploadUrl(accountId, resourceType, 
                fileNames[i], contentTypes[i], maxFileSize);
        }
        
        return responses;
    }

    private String generatePresignedDownloadUrlInternal(String fileUrl, Long accountId, Duration duration) {
        Instant start = Instant.now();
        boolean success = false;
        
        try {
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.extractKeyFromUrl(fileUrl, bucketName);
            
            logger.debug("Generating presigned URL - Original URL: {}, Bucket: {}, Key: {}", 
                fileUrl, bucketName, key);
            
            S3KeyUtil.validateKeyAccountOwnership(key, accountId);
            
            verifyObjectExists(bucketName, key);
            
            GetObjectRequest getRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build();
                
            GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(duration)
                .getObjectRequest(getRequest)
                .build();
                
            String presignedUrl = s3Presigner.presignGetObject(presignRequest).url().toString();
            
            success = true;
            logger.debug(S3Constants.LOG_DOWNLOAD_URL_GENERATED, key, accountId);
            return presignedUrl;
            
        } catch (ForbiddenException e) {
            throw e; // Re-throw access denied exceptions
        } catch (Exception e) {
            logger.error("Failed to generate presigned download URL for fileUrl: {}, error: {}", 
                fileUrl, e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, 
                S3Constants.ERROR_DOWNLOAD_URL_FAILED);
        } finally {
            Duration operationDuration = Duration.between(start, Instant.now());
            String resourceType = extractResourceTypeFromUrl(fileUrl);
            metricsService.recordPresignedUrlGeneration(operationDuration, success, "download", accountId);
        }
    }

    private void verifyObjectExists(String bucketName, String key) {
        HeadObjectRequest headRequest = HeadObjectRequest.builder()
            .bucket(bucketName)
            .key(key)
            .build();
            
        s3Client.headObject(headRequest);
    }

    private PutObjectRequest createPutObjectRequest(String bucketName, String key, String contentType, long fileSizeLimit) {
        return PutObjectRequest.builder()
            .bucket(bucketName)
            .key(key)
            .contentType(contentType)
            .contentLength(fileSizeLimit)
            .build();
    }

    private PutObjectPresignRequest createPutObjectPresignRequest(PutObjectRequest putRequest, Duration duration) {
        return PutObjectPresignRequest.builder()
            .signatureDuration(duration)
            .putObjectRequest(putRequest)
            .build();
    }

    private String extractResourceTypeFromUrl(String fileUrl) {
        try {
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.extractKeyFromUrl(fileUrl, bucketName);
            return S3KeyUtil.extractResourceTypeFromKey(key);
        } catch (Exception e) {
            return "unknown";
        }
    }
}
