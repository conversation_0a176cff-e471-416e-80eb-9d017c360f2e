package com.collabhub.be.service.s3;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.InternalServerErrorException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.service.s3.constants.S3Constants;
import com.collabhub.be.service.s3.util.S3KeyUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Service for handling multipart uploads to S3.
 * Provides efficient upload of large files with progress tracking and parallel processing.
 */
@Service
@Validated
public class S3MultipartUploadService {

    private static final Logger logger = LoggerFactory.getLogger(S3MultipartUploadService.class);

    private final S3Client s3Client;
    private final S3Properties s3Properties;
    private final S3BucketService bucketService;
    private final S3MetricsService metricsService;
    private final ExecutorService executorService;

    public S3MultipartUploadService(S3Client s3Client, S3Properties s3Properties, 
                                   S3BucketService bucketService, S3MetricsService metricsService) {
        this.s3Client = s3Client;
        this.s3Properties = s3Properties;
        this.bucketService = bucketService;
        this.metricsService = metricsService;
        this.executorService = Executors.newFixedThreadPool(4); // Configurable thread pool
    }

    /**
     * Uploads a file using multipart upload if it exceeds the threshold.
     */
    public String uploadFile(@NotNull MultipartFile file, @NotNull @Positive Long accountId, 
                           @NotBlank String resourceType) {
        if (file.getSize() > S3Constants.MULTIPART_UPLOAD_THRESHOLD) {
            return uploadWithMultipart(file, accountId, resourceType);
        } else {
            return uploadDirectly(file, accountId, resourceType);
        }
    }

    /**
     * Uploads a file using multipart upload.
     */
    public String uploadWithMultipart(@NotNull MultipartFile file, @NotNull @Positive Long accountId, 
                                    @NotBlank String resourceType) {
        Instant start = Instant.now();
        boolean success = false;
        String uploadId = null;
        
        try {
            bucketService.ensureBucketExists();
            
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.generateS3Key(accountId, resourceType, file.getOriginalFilename());
            
            // Initiate multipart upload
            uploadId = initiateMultipartUpload(bucketName, key, file.getContentType());
            
            // Upload parts
            List<CompletedPart> completedParts = uploadParts(file, bucketName, key, uploadId);
            
            // Complete multipart upload
            completeMultipartUpload(bucketName, key, uploadId, completedParts);
            
            String fileUrl = generateFileUrl(bucketName, key);
            success = true;
            
            logger.info(S3Constants.LOG_UPLOAD_SUCCESS, key, bucketName, accountId, resourceType);
            return fileUrl;
            
        } catch (Exception e) {
            if (uploadId != null) {
                abortMultipartUpload(s3Properties.getBucketName(), 
                    S3KeyUtil.generateS3Key(accountId, resourceType, file.getOriginalFilename()), uploadId);
            }
            logger.error(S3Constants.LOG_UPLOAD_FAILURE, e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, 
                String.format(S3Constants.ERROR_UPLOAD_FAILED, e.getMessage()));
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            metricsService.recordUploadSuccess(duration, accountId, resourceType, file.getSize());
        }
    }

    /**
     * Uploads a file directly (for smaller files).
     */
    public String uploadDirectly(@NotNull MultipartFile file, @NotNull @Positive Long accountId, 
                               @NotBlank String resourceType) {
        Instant start = Instant.now();
        boolean success = false;
        
        try {
            bucketService.ensureBucketExists();
            
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.generateS3Key(accountId, resourceType, file.getOriginalFilename());
            
            PutObjectRequest putRequest = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .contentType(file.getContentType())
                .contentLength(file.getSize())
                .build();
                
            s3Client.putObject(putRequest, RequestBody.fromInputStream(file.getInputStream(), file.getSize()));
            
            String fileUrl = generateFileUrl(bucketName, key);
            success = true;
            
            logger.info(S3Constants.LOG_UPLOAD_SUCCESS, key, bucketName, accountId, resourceType);
            return fileUrl;
            
        } catch (IOException e) {
            logger.error("Failed to read file content: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, "Failed to read file content");
        } catch (Exception e) {
            logger.error(S3Constants.LOG_UPLOAD_FAILURE, e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, 
                String.format(S3Constants.ERROR_UPLOAD_FAILED, e.getMessage()));
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            metricsService.recordUploadSuccess(duration, accountId, resourceType, file.getSize());
        }
    }

    private String initiateMultipartUpload(String bucketName, String key, String contentType) {
        CreateMultipartUploadRequest createRequest = CreateMultipartUploadRequest.builder()
            .bucket(bucketName)
            .key(key)
            .contentType(contentType)
            .build();
            
        CreateMultipartUploadResponse createResponse = s3Client.createMultipartUpload(createRequest);
        String uploadId = createResponse.uploadId();
        
        logger.debug("Initiated multipart upload for key: {} with uploadId: {}", key, uploadId);
        return uploadId;
    }

    private List<CompletedPart> uploadParts(MultipartFile file, String bucketName, String key, String uploadId) 
            throws IOException {
        List<CompletedPart> completedParts = new ArrayList<>();
        List<CompletableFuture<CompletedPart>> futures = new ArrayList<>();
        
        byte[] fileBytes = file.getBytes();
        int partNumber = 1;
        int offset = 0;
        
        while (offset < fileBytes.length) {
            int partSize = Math.min(S3Constants.MULTIPART_UPLOAD_PART_SIZE, fileBytes.length - offset);
            byte[] partData = new byte[partSize];
            System.arraycopy(fileBytes, offset, partData, 0, partSize);
            
            final int currentPartNumber = partNumber;

            CompletableFuture<CompletedPart> future = CompletableFuture.supplyAsync(() ->
                uploadPart(bucketName, key, uploadId, currentPartNumber, partData), executorService);
                
            futures.add(future);
            
            offset += partSize;
            partNumber++;
        }
        
        // Wait for all parts to complete
        for (CompletableFuture<CompletedPart> future : futures) {
            try {
                completedParts.add(future.get());
            } catch (Exception e) {
                logger.error("Failed to upload part: {}", e.getMessage());
                throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, 
                    "Failed to upload file part: " + e.getMessage());
            }
        }
        
        // Sort by part number
        completedParts.sort((a, b) -> Integer.compare(a.partNumber(), b.partNumber()));
        
        logger.debug("Uploaded {} parts for key: {}", completedParts.size(), key);
        return completedParts;
    }

    private CompletedPart uploadPart(String bucketName, String key, String uploadId, int partNumber, byte[] partData) {
        try {
            UploadPartRequest uploadPartRequest = UploadPartRequest.builder()
                .bucket(bucketName)
                .key(key)
                .uploadId(uploadId)
                .partNumber(partNumber)
                .build();
                
            UploadPartResponse uploadPartResponse = s3Client.uploadPart(uploadPartRequest, 
                RequestBody.fromInputStream(new ByteArrayInputStream(partData), partData.length));
                
            logger.debug("Uploaded part {} for key: {} (size: {} bytes)", partNumber, key, partData.length);
            
            return CompletedPart.builder()
                .partNumber(partNumber)
                .eTag(uploadPartResponse.eTag())
                .build();
                
        } catch (Exception e) {
            logger.error("Failed to upload part {} for key {}: {}", partNumber, key, e.getMessage());
            throw new RuntimeException("Failed to upload part: " + e.getMessage(), e);
        }
    }

    private void completeMultipartUpload(String bucketName, String key, String uploadId, 
                                       List<CompletedPart> completedParts) {
        CompletedMultipartUpload completedUpload = CompletedMultipartUpload.builder()
            .parts(completedParts)
            .build();
            
        CompleteMultipartUploadRequest completeRequest = CompleteMultipartUploadRequest.builder()
            .bucket(bucketName)
            .key(key)
            .uploadId(uploadId)
            .multipartUpload(completedUpload)
            .build();
            
        s3Client.completeMultipartUpload(completeRequest);
        logger.debug("Completed multipart upload for key: {} with uploadId: {}", key, uploadId);
    }

    private void abortMultipartUpload(String bucketName, String key, String uploadId) {
        try {
            AbortMultipartUploadRequest abortRequest = AbortMultipartUploadRequest.builder()
                .bucket(bucketName)
                .key(key)
                .uploadId(uploadId)
                .build();
                
            s3Client.abortMultipartUpload(abortRequest);
            logger.warn("Aborted multipart upload for key: {} with uploadId: {}", key, uploadId);
        } catch (Exception e) {
            logger.error("Failed to abort multipart upload for key: {} with uploadId: {}: {}", 
                key, uploadId, e.getMessage());
        }
    }

    private String generateFileUrl(String bucketName, String key) {
        if (s3Properties.getEndpoint() != null) {
            return s3Properties.getEndpoint() + "/" + bucketName + "/" + key;
        } else {
            return "https://" + bucketName + ".s3." + s3Properties.getRegion() + ".amazonaws.com/" + key;
        }
    }
}
