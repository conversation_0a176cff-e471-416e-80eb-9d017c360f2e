package com.collabhub.be.service.s3.constants;

import java.time.Duration;

/**
 * Constants for S3 operations.
 * Centralizes all configuration values and constants used in S3 services.
 */
public final class S3Constants {

    // Presigned URL durations - Industry best practices for security vs UX balance
    public static final Duration DEFAULT_PRESIGNED_URL_DURATION = Duration.ofMinutes(10); // Images and general content
    public static final Duration VIDEO_PRESIGNED_URL_DURATION = Duration.ofMinutes(30);   // Videos (larger files, slower networks)
    public static final Duration UPLOAD_PRESIGNED_URL_DURATION = Duration.ofMinutes(15);  // Upload operations
    public static final Duration EXTENDED_PRESIGNED_URL_DURATION = Duration.ofHours(1);   // Special cases only
    public static final Duration SHORT_PRESIGNED_URL_DURATION = Duration.ofMinutes(5);    // High-security scenarios
    
    // File size limits
    public static final long DEFAULT_MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    public static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
    public static final long MAX_VIDEO_SIZE = 100 * 1024 * 1024; // 100MB
    public static final long MAX_DOCUMENT_SIZE = 20 * 1024 * 1024; // 20MB
    
    // Multipart upload thresholds
    public static final long MULTIPART_UPLOAD_THRESHOLD = 5 * 1024 * 1024; // 5MB
    public static final int MULTIPART_UPLOAD_PART_SIZE = 5 * 1024 * 1024; // 5MB
    public static final int MAX_MULTIPART_UPLOAD_PARTS = 10000; // AWS limit
    
    // Rate limiting
    public static final int MAX_UPLOADS_PER_MINUTE = 60;
    public static final int MAX_DOWNLOADS_PER_MINUTE = 300;
    
    // Filename constraints
    public static final int MAX_FILENAME_LENGTH = 255;
    
    // Retry configuration
    public static final int MAX_RETRY_ATTEMPTS = 3;
    public static final Duration RETRY_BACKOFF_DELAY = Duration.ofMillis(500);
    public static final double RETRY_BACKOFF_MULTIPLIER = 2.0;
    
    // Cache configuration - TTL shorter than URL expiration for safety
    public static final Duration PRESIGNED_URL_CACHE_TTL = Duration.ofMinutes(5);           // Safe margin for 10min URLs
    public static final Duration VIDEO_URL_CACHE_TTL = Duration.ofMinutes(10);              // Safe margin for 30min video URLs
    public static final Duration UPLOAD_URL_CACHE_TTL = Duration.ofMinutes(5);              // Safe margin for 15min upload URLs
    
    // Log message templates
    public static final String LOG_UPLOAD_SUCCESS = "Successfully uploaded file: {} to bucket: {} (account: {}, type: {})";
    public static final String LOG_UPLOAD_FAILURE = "Failed to upload file to S3: {}";
    public static final String LOG_DOWNLOAD_URL_GENERATED = "Generated presigned download URL for file: {} (account: {})";
    public static final String LOG_BUCKET_CREATED = "Successfully created bucket: {}";
    public static final String LOG_BUCKET_EXISTS = "Bucket {} already exists";
    public static final String LOG_FILE_VALIDATION_SUCCESS = "File validation successful for: {} (account: {})";
    public static final String LOG_FILE_VALIDATION_FAILURE = "File validation failed for: {} (account: {}) - {}";
    
    // Error messages
    public static final String ERROR_FILE_REQUIRED = "File is required";
    public static final String ERROR_FILENAME_REQUIRED = "Filename is required";
    public static final String ERROR_CONTENT_TYPE_REQUIRED = "Content type is required";
    public static final String ERROR_FILE_SIZE_EXCEEDED = "File size exceeds maximum allowed size: %d MB";
    public static final String ERROR_FILE_TYPE_NOT_ALLOWED = "Unsupported file type. Allowed types: %s";
    public static final String ERROR_FILE_EXTENSION_BLOCKED = "File extension is not allowed for security reasons";
    public static final String ERROR_FILENAME_INVALID = "Filename contains invalid characters";
    public static final String ERROR_FILE_CONTENT_INVALID = "File content does not match declared content type";
    public static final String ERROR_FILE_ACCESS_DENIED = "File access denied";
    public static final String ERROR_UPLOAD_FAILED = "Failed to upload file: %s";
    public static final String ERROR_DOWNLOAD_URL_FAILED = "Failed to generate download URL";
    
    private S3Constants() {
        // Private constructor to prevent instantiation
    }
}
