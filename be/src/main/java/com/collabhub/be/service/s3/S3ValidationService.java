package com.collabhub.be.service.s3;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.posts.dto.FileValidationResponse;
import com.collabhub.be.service.s3.constants.S3Constants;
import com.collabhub.be.service.s3.util.FileSecurityUtil;
import com.collabhub.be.service.s3.util.S3KeyUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;

import java.time.Duration;
import java.time.Instant;

/**
 * Service for validating files and S3 operations.
 * Handles file content validation, security checks, and uploaded file verification.
 */
@Service
@Validated
public class S3ValidationService {

    private static final Logger logger = LoggerFactory.getLogger(S3ValidationService.class);

    private final S3Client s3Client;
    private final S3Properties s3Properties;
    private final S3MetricsService metricsService;

    public S3ValidationService(S3Client s3Client, S3Properties s3Properties, S3MetricsService metricsService) {
        this.s3Client = s3Client;
        this.s3Properties = s3Properties;
        this.metricsService = metricsService;
    }

    /**
     * Validates a multipart file before upload.
     */
    public void validateFileForUpload(@NotNull MultipartFile file) {
        Instant start = Instant.now();
        boolean success = false;

        try {
            validateFileBasics(file);
            validateFilename(file.getOriginalFilename());
            validateContentType(file.getContentType());
            validateFileSize(file.getSize(), file.getContentType());
            validateFileContent(file, file.getContentType());

            success = true;
            logger.debug("File validation successful for: {}", file.getOriginalFilename());
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            metricsService.recordValidation(duration, success, "upload_validation");
        }
    }

    /**
     * Validates content type.
     */
    public void validateContentType(String contentType) {
        if (contentType == null || !s3Properties.isAllowedMimeType(contentType)) {
            throw new BadRequestException(ErrorCode.FILE_TYPE_NOT_ALLOWED,
                String.format(S3Constants.ERROR_FILE_TYPE_NOT_ALLOWED, s3Properties.getAllowedMimeTypes()));
        }
    }

    /**
     * Validates that an uploaded file exists and meets constraints.
     */
    public boolean validateUploadedFile(@NotBlank String fileUrl, @NotNull @Positive Long accountId, 
                                       String expectedContentType, Long maxFileSize) {
        Instant start = Instant.now();
        boolean success = false;
        
        try {
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.extractKeyFromUrl(fileUrl, bucketName);
            
            S3KeyUtil.validateKeyAccountOwnership(key, accountId);
            
            HeadObjectResponse headResponse = getObjectMetadata(bucketName, key);
            
            validateContentTypeMatch(headResponse.contentType(), expectedContentType, key);
            validateFileSizeLimit(headResponse.contentLength(), maxFileSize, key);
            
            success = true;
            logger.debug(S3Constants.LOG_FILE_VALIDATION_SUCCESS, key, accountId);
            return true;
            
        } catch (Exception e) {
            logger.warn(S3Constants.LOG_FILE_VALIDATION_FAILURE, fileUrl, accountId, e.getMessage());
            return false;
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            metricsService.recordValidation(duration, success, "uploaded_file_validation");
        }
    }

    /**
     * Enhanced file validation that returns detailed validation information.
     */
    public FileValidationResponse validateUploadedFileDetailed(@NotBlank String fileUrl,
                                                              @NotNull @Positive Long accountId,
                                                              String expectedContentType,
                                                              Long maxFileSize) {
        Instant start = Instant.now();
        boolean success = false;

        try {
            String bucketName = s3Properties.getBucketName();
            String key = S3KeyUtil.extractKeyFromUrl(fileUrl, bucketName);

            FileValidationResponse response = performDetailedValidation(fileUrl, key, bucketName, accountId, expectedContentType, maxFileSize);
            success = response.getValid();
            return response;

        } catch (Exception e) {
            logger.error("Failed to validate uploaded file: {}", e.getMessage());
            return createFailureResponse(fileUrl, "Validation failed: " + e.getMessage(),
                expectedContentType, maxFileSize);
        } finally {
            Duration duration = Duration.between(start, Instant.now());
            metricsService.recordValidation(duration, success, "detailed_validation");
        }
    }

    private void validateFileBasics(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, S3Constants.ERROR_FILE_REQUIRED);
        }
    }

    private void validateFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.FILE_NAME_INVALID, S3Constants.ERROR_FILENAME_REQUIRED);
        }

        FileSecurityUtil.validateFilenameSecurity(filename);
        
        if (!s3Properties.isAllowedExtension(filename)) {
            throw new BadRequestException(ErrorCode.FILE_TYPE_NOT_ALLOWED,
                String.format(S3Constants.ERROR_FILE_TYPE_NOT_ALLOWED, s3Properties.getAllowedExtensions()));
        }
    }



    private void validateFileSize(long fileSize, String contentType) {
        FileSecurityUtil.validateFileSizeSecurity(fileSize);
        
        long maxSize = s3Properties.getMaxFileSizeForType(contentType);
        if (fileSize > maxSize) {
            String fileType = s3Properties.isImageType(contentType) ? "image" : "video";
            throw new BadRequestException(ErrorCode.FILE_SIZE_EXCEEDED,
                String.format(S3Constants.ERROR_FILE_SIZE_EXCEEDED, maxSize / 1024 / 1024));
        }
    }

    private void validateFileContent(MultipartFile file, String contentType) {
        FileSecurityUtil.validateFileContent(file, contentType);
    }

    private HeadObjectResponse getObjectMetadata(String bucketName, String key) {
        HeadObjectRequest headRequest = HeadObjectRequest.builder()
            .bucket(bucketName)
            .key(key)
            .build();
            
        return s3Client.headObject(headRequest);
    }

    private void validateContentTypeMatch(String actualContentType, String expectedContentType, String key) {
        if (expectedContentType != null && !expectedContentType.equals(actualContentType)) {
            logger.warn("Content type mismatch for file {}: expected {}, got {}", 
                key, expectedContentType, actualContentType);
        }
    }

    private void validateFileSizeLimit(long actualSize, Long maxFileSize, String key) {
        if (maxFileSize != null && actualSize > maxFileSize) {
            logger.warn("File size exceeds limit for file {}: {} > {}", key, actualSize, maxFileSize);
        }
    }

    private FileValidationResponse performDetailedValidation(String fileUrl, String key, String bucketName,
                                                           Long accountId, String expectedContentType, Long maxFileSize) {
        try {
            S3KeyUtil.validateKeyAccountOwnership(key, accountId);
            
            HeadObjectResponse headResponse = getObjectMetadata(bucketName, key);
            
            boolean fileExists = true;
            boolean sizeCheckPassed = maxFileSize == null || headResponse.contentLength() <= maxFileSize;
            boolean contentTypeCheckPassed = expectedContentType == null || 
                expectedContentType.equals(headResponse.contentType());
            
            boolean isValid = sizeCheckPassed && contentTypeCheckPassed;
            String message = isValid ? "File validation successful" : buildValidationErrorMessage(
                sizeCheckPassed, contentTypeCheckPassed, expectedContentType, headResponse.contentType(),
                maxFileSize, headResponse.contentLength());
            
            FileValidationResponse.ValidationDetails details = 
                new FileValidationResponse.ValidationDetails(sizeCheckPassed, contentTypeCheckPassed, 
                    fileExists, maxFileSize, expectedContentType);
            
            return new FileValidationResponse(isValid, fileUrl, message, headResponse.contentLength(),
                headResponse.contentType(), details);
                
        } catch (NoSuchKeyException e) {
            return createFileNotFoundResponse(fileUrl, expectedContentType, maxFileSize);
        }
    }

    private String buildValidationErrorMessage(boolean sizeCheckPassed, boolean contentTypeCheckPassed,
                                             String expectedContentType, String actualContentType,
                                             Long maxFileSize, long actualSize) {
        if (!contentTypeCheckPassed) {
            return "Content type mismatch: expected " + expectedContentType + ", got " + actualContentType;
        }
        if (!sizeCheckPassed) {
            return "File size exceeds limit: " + actualSize + " > " + maxFileSize;
        }
        return "Validation failed";
    }

    private FileValidationResponse createFileNotFoundResponse(String fileUrl, String expectedContentType, Long maxFileSize) {
        FileValidationResponse.ValidationDetails details = 
            new FileValidationResponse.ValidationDetails(false, false, false, maxFileSize, expectedContentType);
        return new FileValidationResponse(false, fileUrl, "File not found", null, null, details);
    }

    private FileValidationResponse createFailureResponse(String fileUrl, String message, 
                                                        String expectedContentType, Long maxFileSize) {
        FileValidationResponse.ValidationDetails details = 
            new FileValidationResponse.ValidationDetails(false, false, false, maxFileSize, expectedContentType);
        return new FileValidationResponse(false, fileUrl, message, null, null, details);
    }
}
