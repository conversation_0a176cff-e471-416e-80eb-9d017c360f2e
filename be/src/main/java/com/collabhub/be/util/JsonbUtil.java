package com.collabhub.be.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.JSONB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for handling jOOQ JSONB type conversions.
 * Provides consistent methods for parsing JSONB objects to POJOs and vice versa.
 */
public class JsonbUtil {

    private static final Logger logger = LoggerFactory.getLogger(JsonbUtil.class);

    /**
     * Parses a JSONB object to a specific type using ObjectMapper.
     * Handles different input types (String, JSONB, Object) gracefully.
     *
     * @param jsonb the JSONB object to parse
     * @param targetClass the target class to convert to
     * @param objectMapper the ObjectMapper instance to use
     * @param <T> the target type
     * @return the parsed object, or null if parsing fails
     */
    public static <T> T parseJsonb(Object jsonb, Class<T> targetClass, ObjectMapper objectMapper) {
        if (jsonb == null) {
            return null;
        }

        try {
            if (jsonb instanceof String) {
                return objectMapper.readValue((String) jsonb, targetClass);
            } else if (jsonb instanceof JSONB) {
                // Handle jOOQ JSONB type by extracting the JSON string
                String jsonString = ((JSONB) jsonb).data();
                return objectMapper.readValue(jsonString, targetClass);
            } else {
                return objectMapper.convertValue(jsonb, targetClass);
            }
        } catch (Exception e) {
            logger.warn("Failed to parse JSONB to {}: {}", targetClass.getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * Parses a JSONB object to a specific type using TypeReference.
     * Useful for generic types like List<T> or Map<String, Object>.
     *
     * @param jsonb the JSONB object to parse
     * @param typeReference the TypeReference for the target type
     * @param objectMapper the ObjectMapper instance to use
     * @param <T> the target type
     * @return the parsed object, or null if parsing fails
     */
    public static <T> T parseJsonb(Object jsonb, TypeReference<T> typeReference, ObjectMapper objectMapper) {
        if (jsonb == null) {
            return null;
        }

        try {
            if (jsonb instanceof String) {
                return objectMapper.readValue((String) jsonb, typeReference);
            } else if (jsonb instanceof JSONB) {
                // Handle jOOQ JSONB type by extracting the JSON string
                String jsonString = ((JSONB) jsonb).data();
                return objectMapper.readValue(jsonString, typeReference);
            } else {
                return objectMapper.convertValue(jsonb, typeReference);
            }
        } catch (Exception e) {
            logger.warn("Failed to parse JSONB to {}: {}", typeReference.getType(), e.getMessage());
            return null;
        }
    }

    /**
     * Converts an object to JSONB format for database storage.
     *
     * @param object the object to convert
     * @param objectMapper the ObjectMapper instance to use
     * @return the JSONB representation, or null if conversion fails
     */
    public static JSONB toJsonb(Object object, ObjectMapper objectMapper) {
        if (object == null) {
            return null;
        }

        try {
            String jsonString = objectMapper.writeValueAsString(object);
            return JSONB.valueOf(jsonString);
        } catch (Exception e) {
            logger.error("Failed to convert object to JSONB: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Safely extracts JSON string from a JSONB object.
     *
     * @param jsonb the JSONB object
     * @return the JSON string, or null if extraction fails
     */
    public static String extractJsonString(Object jsonb) {
        if (jsonb == null) {
            return null;
        }

        try {
            if (jsonb instanceof String) {
                return (String) jsonb;
            } else if (jsonb instanceof JSONB) {
                return ((JSONB) jsonb).data();
            } else {
                return jsonb.toString();
            }
        } catch (Exception e) {
            logger.warn("Failed to extract JSON string from JSONB: {}", e.getMessage());
            return null;
        }
    }
}
