version: '3.8'

services:
  postgres:
    image: 'postgres:latest'
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    volumes:
      - pgdata:/var/lib/postgresql/data

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - '9000:9000'  # S3 API
      - '9001:9001'  # Web UI
    volumes:
      - minio-data:/data

  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - '1025:1025'  # SMTP
      - '8025:8025'  # Web UI

volumes:
  pgdata:
  minio-data:
