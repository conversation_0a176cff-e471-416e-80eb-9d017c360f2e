# Flyway Gradle Tasks

This document describes the available Flyway Gradle tasks for database migration management in the Collaboration Hub project.

## Prerequisites

- PostgreSQL database running on `localhost:5432`
- Database credentials: `postgres/postgres`
- Database name: `postgres`

## Available Tasks

### Standard Flyway Tasks

These are provided by the Flyway Gradle plugin:

- **`./gradlew flywayMigrate`** - Apply pending migrations
- **`./gradlew flywayInfo`** - Show migration status and information
- **`./gradlew flywayValidate`** - Validate applied migrations against available ones
- **`./gradlew flywayClean`** - Drop all objects in configured schemas (⚠️ DEVELOPMENT ONLY)
- **`./gradlew flywayBaseline`** - Baseline an existing database
- **`./gradlew flywayRepair`** - Repair migration metadata table

### Custom Development Tasks

These are custom tasks created for improved development workflow:

#### `./gradlew flywayMigrateAndGenerateJooq`
- Runs Flyway migration
- Automatically generates jOOQ classes after successful migration
- **Use this for normal development workflow**

#### `./gradlew flywayCleanAndMigrate`
- Cleans database (drops all objects)
- Runs all migrations from scratch
- ⚠️ **DEVELOPMENT ONLY** - Will destroy all data

#### `./gradlew flywayStatus`
- Shows detailed migration status
- Enhanced output with formatting
- Useful for checking current state

#### `./gradlew flywayDevReset`
- Complete database reset workflow:
  1. Clean database
  2. Run all migrations
  3. Generate jOOQ classes
- ⚠️ **DEVELOPMENT ONLY** - Will destroy all data
- Shows warning before execution

## Safety Features

### Production Protection
- `flywayClean` is automatically disabled in production environments
- Checks `SPRING_PROFILES_ACTIVE` environment variable
- Throws error if attempting to clean in production

### Build Integration
- `flywayValidate` runs automatically before `build` task
- `jooqCodegen` automatically depends on `flywayMigrate`
- Ensures database schema is always validated and jOOQ classes are up-to-date

## Common Workflows

### Adding a New Migration
1. Create new migration file: `V014__Your_migration_name.sql`
2. Run: `./gradlew flywayMigrateAndGenerateJooq`
3. Commit both migration file and updated jOOQ classes

### Starting Fresh (Development)
```bash
./gradlew flywayDevReset
```

### Checking Migration Status
```bash
./gradlew flywayStatus
```

### Production Deployment
```bash
# Only run migrate in production, never clean
./gradlew flywayMigrate
```

## Configuration

Flyway configuration is in `build.gradle.kts`:

```kotlin
flyway {
    url = "*****************************************"
    user = "postgres"
    password = "postgres"
    schemas = arrayOf("public")
    locations = arrayOf("classpath:db/migration")
    baselineOnMigrate = true
    validateOnMigrate = true
    outOfOrder = false
    cleanDisabled = false
}
```

## Troubleshooting

### Migration Checksum Mismatch
```bash
./gradlew flywayRepair
```

### Database Out of Sync
```bash
# Development only
./gradlew flywayDevReset
```

### jOOQ Classes Out of Date
```bash
./gradlew flywayMigrateAndGenerateJooq
```

## Environment Variables

- `SPRING_PROFILES_ACTIVE` - Used for production safety checks
- Set to `prod` or `production` to enable production protections

## Best Practices

1. **Always use `flywayMigrateAndGenerateJooq`** for development
2. **Never use `flywayClean` in production**
3. **Validate migrations** before committing: `./gradlew flywayValidate`
4. **Keep migrations immutable** - never modify existing migration files
5. **Test migrations** on a copy of production data before deployment
