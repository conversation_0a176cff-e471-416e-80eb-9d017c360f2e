# File Upload Security Documentation

## Overview

This document outlines the comprehensive security measures implemented for file uploads in the Collaboration Hub application, following OWASP best practices for secure file upload functionality.

## Security Measures Implemented

### 1. File Size Limits

**Implementation:**
- Images: Maximum 10MB (configurable via `app.s3.max-image-size-bytes`)
- Videos: Maximum 100MB (configurable via `app.s3.max-video-size-bytes`)
- Overall limit: 100MB (configurable via `app.s3.max-file-size`)

**Configuration:**
```properties
app.s3.max-image-size-bytes=********    # 10MB
app.s3.max-video-size-bytes=*********   # 100MB
```

**Error Handling:**
- `FILE_SIZE_EXCEEDED` error code for oversized files
- Specific error messages indicating file type and size limits

### 2. File Type Validation

**MIME Type Whitelist:**
- Images: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
- Videos: `video/mp4`, `video/quicktime`, `video/x-msvideo`

**File Extension Validation:**
- Allowed: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`, `.mp4`, `.mov`, `.avi`
- Blocked: `.exe`, `.bat`, `.cmd`, `.com`, `.pif`, `.scr`, `.vbs`, `.js`, `.jar`, `.php`, `.asp`, `.jsp`, `.sh`, `.ps1`, `.py`, `.rb`

**Error Handling:**
- `FILE_TYPE_NOT_ALLOWED` error code for invalid file types
- Detailed error messages listing allowed types

### 3. Filename Sanitization

**Security Measures:**
- Removes path separators (`/`, `\`)
- Removes dangerous characters (`:`, `*`, `?`, `"`, `<`, `>`, `|`)
- Prevents directory traversal (`..`)
- Limits filename length to 255 characters
- Generates UUID-based filenames to prevent conflicts

**Implementation:**
```java
public String sanitizeFilename(String filename) {
    // Remove dangerous characters and path separators
    String sanitized = filename.replaceAll("[/\\\\:*?\"<>|]", "_")
                              .replaceAll("\\.\\.", "_")
                              .replaceAll("^\\.", "_")
                              .trim();
    // Limit length and preserve extension
    // ...
}
```

### 4. Content Validation (Magic Number Verification)

**Magic Number Validation:**
- JPEG: `FF D8 FF`
- PNG: `89 50 4E 47 0D 0A 1A 0A`
- GIF: `47 49 46 38`
- WebP: `52 49 46 46` (RIFF header)
- MP4: `00 00 00 18 66 74 79 70` (ftyp)
- QuickTime: `00 00 00 14 66 74 79 70` (ftyp)
- AVI: `52 49 46 46` (RIFF header)

**Error Handling:**
- `FILE_CONTENT_INVALID` error code for content mismatches
- Validates actual file content matches declared MIME type

### 5. Multi-Tenancy Security

**Account Isolation:**
- Files organized by account: `{accountId}/{resourceType}/{filename}`
- Validation ensures files belong to requesting account
- Prevents cross-account file access

**S3 Key Structure:**
```
bucket-name/
├── account-1/
│   ├── posts/
│   │   ├── uuid-1.jpg
│   │   └── uuid-2.mp4
│   └── invoices/
│       └── uuid-3.pdf
└── account-2/
    └── posts/
        └── uuid-4.png
```

### 6. Rate Limiting Considerations

**Current Implementation:**
- Spring Security provides basic authentication-based rate limiting
- File size limits prevent resource exhaustion

**Recommendations for Production:**
- Implement Redis-based rate limiting per user/account
- Configure maximum uploads per time window
- Monitor unusual upload patterns

### 7. Storage Security

**S3 Configuration:**
- Single shared bucket with folder-based multi-tenancy
- Presigned URLs with 15-minute expiration
- Server-side validation of all uploads

**Cost Optimization:**
- Strict file size limits based on content type
- UUID-based filenames prevent enumeration
- Automatic cleanup of orphaned files (to be implemented)

## Error Codes

| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `FILE_UPLOAD_FAILED` | General upload failure | 500 |
| `FILE_SIZE_EXCEEDED` | File exceeds size limits | 400 |
| `FILE_TYPE_NOT_ALLOWED` | Invalid file type/extension | 400 |
| `FILE_NAME_INVALID` | Invalid filename | 400 |
| `FILE_CONTENT_INVALID` | Content doesn't match type | 400 |
| `FILE_VALIDATION_FAILED` | Validation failure | 400 |
| `FILE_NOT_FOUND` | File not found | 404 |
| `FILE_DELETION_FAILED` | Deletion failure | 500 |
| `UPLOAD_RATE_LIMIT_EXCEEDED` | Too many uploads | 429 |
| `MALICIOUS_FILE_DETECTED` | Potential malware | 400 |

## API Endpoints

### Upload Media File
```http
POST /api/posts/media/upload
Content-Type: multipart/form-data

file: [binary data]
```

### Generate Presigned URL
```http
POST /api/posts/media/presigned-url
Content-Type: application/x-www-form-urlencoded

fileName=example.jpg&contentType=image/jpeg&maxFileSize=********
```

### Validate Uploaded File
```http
POST /api/posts/media/validate
Content-Type: application/x-www-form-urlencoded

fileUrl=https://...&contentType=image/jpeg&maxFileSize=********
```

## Security Recommendations

### Immediate Actions Required
1. **Virus Scanning Integration**: Implement antivirus scanning for uploaded files
2. **Content Security Policy**: Add CSP headers to prevent XSS via uploaded files
3. **File Quarantine**: Implement temporary quarantine for uploaded files

### Production Considerations
1. **CDN Integration**: Use CloudFront or similar CDN for file delivery
2. **Monitoring**: Implement comprehensive logging and alerting
3. **Backup Strategy**: Regular backups of uploaded files
4. **Compliance**: Ensure GDPR/privacy compliance for user uploads

### Advanced Security Features
1. **Image Processing**: Sanitize images to remove EXIF data and potential exploits
2. **Sandboxing**: Process uploads in isolated environments
3. **Machine Learning**: Implement ML-based malware detection
4. **Audit Trail**: Complete audit trail for all file operations

## Configuration Examples

### Development (MinIO)
```properties
app.s3.endpoint=http://localhost:9000
app.s3.bucket-name=collabhub-dev
app.s3.max-image-size-bytes=5242880    # 5MB for dev
app.s3.max-video-size-bytes=********   # 50MB for dev
```

### Production (AWS S3)
```properties
app.s3.endpoint=
app.s3.bucket-name=collabhub-prod
app.s3.max-image-size-bytes=********   # 10MB
app.s3.max-video-size-bytes=*********  # 100MB
```

## Testing

### Security Test Cases
1. Upload files exceeding size limits
2. Upload files with blocked extensions
3. Upload files with mismatched content types
4. Attempt directory traversal in filenames
5. Test cross-account file access
6. Validate presigned URL expiration
7. Test malformed file uploads

### Performance Test Cases
1. Concurrent upload stress testing
2. Large file upload performance
3. S3 storage cost monitoring
4. Rate limiting effectiveness

## Compliance

This implementation addresses the following security standards:
- **OWASP Top 10**: Addresses injection, broken access control, and security misconfiguration
- **NIST Cybersecurity Framework**: Implements identify, protect, and detect functions
- **ISO 27001**: Supports information security management requirements
- **GDPR**: Provides data protection and privacy controls
