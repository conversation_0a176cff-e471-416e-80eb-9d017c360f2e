buildscript {
    dependencies {
        classpath("org.flywaydb:flyway-database-postgresql:11.11.0")
    }
}

plugins {
	java
	id("org.springframework.boot") version "3.5.0"
	id("io.spring.dependency-management") version "1.1.7"
    id("org.jooq.jooq-codegen-gradle") version "3.19.23"
    id("org.flywaydb.flyway") version "11.11.0"
}

group = "com.collabhub"
version = "0.0.1-SNAPSHOT"

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom(configurations.annotationProcessor.get())
	}
}

repositories {
	mavenCentral()
}

sourceSets {
    main {
        java {
            srcDirs("build/generated-sources/jooq")
        }
    }
}

dependencies {
    // AWS SDK BOM for version management
    implementation(platform("software.amazon.awssdk:bom:2.28.11"))

	implementation("org.springframework.boot:spring-boot-starter-actuator")
	implementation("org.springframework.boot:spring-boot-starter-jooq")
	implementation("org.springframework.boot:spring-boot-starter-mail")
	implementation("org.springframework.boot:spring-boot-starter-oauth2-authorization-server")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")
    implementation("org.springframework.boot:spring-boot-starter-security")
	implementation("org.springframework.boot:spring-boot-starter-thymeleaf")
	implementation("org.springframework.boot:spring-boot-starter-validation")
	implementation("org.springframework.boot:spring-boot-starter-web")
	implementation("org.springframework.boot:spring-boot-starter-websocket")
	implementation("org.flywaydb:flyway-core")
	implementation("org.flywaydb:flyway-database-postgresql")
	implementation("org.thymeleaf.extras:thymeleaf-extras-springsecurity6")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.8")

    // PDF Generation
    implementation("com.itextpdf:itext-core:8.0.5")
    implementation("com.itextpdf:html2pdf:5.0.5")

    // AWS SDK for S3 (versions managed by BOM)
    implementation("software.amazon.awssdk:s3")
    implementation("software.amazon.awssdk:auth")

    // Micrometer for metrics (included in Spring Boot Actuator, but explicit for annotations)
    implementation("io.micrometer:micrometer-core")



    // Caffeine cache for better caching performance
    implementation("com.github.ben-manes.caffeine:caffeine")

    // Resilience4j for rate limiting and circuit breaker patterns
    implementation("io.github.resilience4j:resilience4j-ratelimiter:2.2.0")
    implementation("io.github.resilience4j:resilience4j-spring-boot3:2.2.0")

    developmentOnly("org.springframework.boot:spring-boot-devtools")
    developmentOnly("org.springframework.boot:spring-boot-docker-compose")
	runtimeOnly("org.postgresql:postgresql")
    jooqCodegen("org.postgresql:postgresql")
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
	testImplementation("org.springframework.boot:spring-boot-starter-test")
	testImplementation("org.springframework.boot:spring-boot-testcontainers")
	testImplementation("org.springframework.security:spring-security-test")
	testImplementation("org.testcontainers:junit-jupiter")
	testImplementation("org.testcontainers:postgresql")
	testRuntimeOnly("org.junit.platform:junit-platform-launcher")
}

tasks.withType<Test> {
	useJUnitPlatform()
}

jooq {
    configuration {
        jdbc {
            url = "*****************************************"
            user = "postgres"
            password = "postgres"
        }
        generator {
            name = "org.jooq.codegen.DefaultGenerator"
            database {
                name = "org.jooq.meta.postgres.PostgresDatabase"
                inputSchema = "public"
            }
            generate {
                isDeprecated = false
                isRecords = true
                isSpringDao = true
                isDaos = true
                isSpringAnnotations = true
                nonnullAnnotationType = "jakarta.validation.constraints.NotNull"
                isNonnullAnnotation = true
            }
        }
    }
}

// Flyway configuration
flyway {
    url = "*****************************************"
    user = "postgres"
    password = "postgres"
    driver = "org.postgresql.Driver"
    schemas = arrayOf("public")
    locations = arrayOf("filesystem:src/main/resources/db/migration")
    baselineOnMigrate = true
    validateOnMigrate = true
    cleanDisabled = false
}

// Task to run migrations before jOOQ code generation
tasks.named("jooqCodegen") {
    dependsOn("flywayMigrate")
}

// Custom task for development database setup
tasks.register("setupDevDatabase") {
    group = "database"
    description = "Sets up the development database with migrations"
    dependsOn("flywayMigrate")
    doLast {
        println("Development database setup completed!")
    }
}

// Custom task for cleaning and rebuilding database
tasks.register("rebuildDatabase") {
    group = "database"
    description = "Cleans and rebuilds the database with all migrations"
    dependsOn("flywayClean", "flywayMigrate")
    tasks.findByName("flywayMigrate")?.mustRunAfter("flywayClean")
    doLast {
        println("Database rebuilt successfully!")
    }
}