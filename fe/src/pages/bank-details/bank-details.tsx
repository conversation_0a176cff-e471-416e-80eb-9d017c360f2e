import { useState } from "react"
import { CreditCard, Plus, AlertCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CardSkeletonGrid } from "@/components/ui/card-skeleton"
import { ResponsiveCardGrid } from "@/components/ui/responsive-card-grid"
import { BankDetailCard } from '@/components/bank-details/bank-detail-card.tsx';
import { EditBankDetailDialog } from "@/components/bank-details/edit-bank-detail-dialog.tsx"
import { DeleteBankDetailDialog } from "@/components/bank-details/delete-bank-detail-dialog.tsx"
import { useBankDetails } from "@/hooks/bank-details"
import { usePermissions } from "@/hooks/use-permissions"
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { BankDetail } from '@/components/bank-details/types';

export default function BankDetailsPage() {
  const [editingBankDetail, setEditingBankDetail] = useState<BankDetail | null>(null)
  const [deletingBankDetail, setDeletingBankDetail] = useState<BankDetail | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const { t, keys } = useTranslations()
  const { canManageBankDetails } = usePermissions()

  const { data: bankDetails, isLoading, error, isError, refetch } = useBankDetails()

  const handleCreateNew = () => {
    setIsCreating(true)
    setEditingBankDetail(null)
  }

  const handleEdit = (bankDetail: BankDetail) => {
    setIsCreating(false)
    setEditingBankDetail(bankDetail)
  }

  const handleDelete = (bankDetail: BankDetail) => {
    setDeletingBankDetail(bankDetail)
  }

  const handleCloseDialog = () => {
    setEditingBankDetail(null)
    setIsCreating(false)
  }

  const handleCloseDeleteDialog = () => {
    setDeletingBankDetail(null)
  }

  const handleSuccess = () => {
    // Optionally show a success message or perform other actions
    // The queries will automatically refetch due to cache invalidation
  }

  if (isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{t(keys.bankDetails.title)}</h1>
            <p className="text-muted-foreground">{t(keys.bankDetails.description)}</p>
          </div>
          {canManageBankDetails() && (
            <Button onClick={handleCreateNew} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {t(keys.bankDetails.createNew)}
            </Button>
          )}
        </div>

        <CardSkeletonGrid count={6} />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{t(keys.bankDetails.title)}</h1>
            <p className="text-muted-foreground">{t(keys.bankDetails.description)}</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error?.error.code || t(keys.bankDetails.error)}
          </AlertDescription>
        </Alert>

        <div className="flex justify-center">
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const hasBankDetails = bankDetails && bankDetails.length > 0

  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{t(keys.bankDetails.title)}</h1>
          <p className="text-muted-foreground">{t(keys.bankDetails.description)}</p>
        </div>
        {canManageBankDetails() && (
          <Button onClick={handleCreateNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {t(keys.bankDetails.createNew)}
          </Button>
        )}
      </div>

      {hasBankDetails ? (
        <ResponsiveCardGrid>
          {bankDetails.map((bankDetail) => (
            <BankDetailCard
              key={bankDetail.id}
              bankDetail={bankDetail}
              onEdit={() => handleEdit(bankDetail)}
              onDelete={() => handleDelete(bankDetail)}
            />
          ))}
        </ResponsiveCardGrid>
      ) : (
        <div className="flex flex-col items-center justify-center min-h-[500px] text-center max-w-2xl mx-auto px-4">
          <div className="mb-6 p-4 bg-muted rounded-full">
            <CreditCard className="h-12 w-12 text-muted-foreground" />
          </div>
          <h2 className="text-2xl font-semibold mb-3">{t(keys.bankDetails.noBankDetails)}</h2>
          <p className="text-muted-foreground text-lg mb-8 leading-relaxed">
            {t(keys.bankDetails.noBankDetailsDescription)}
          </p>
          {canManageBankDetails() && (
            <Button onClick={handleCreateNew} className="flex items-center gap-2" size="lg">
              <Plus className="h-5 w-5" />
              {t(keys.bankDetails.createNew)}
            </Button>
          )}
        </div>
      )}

      <EditBankDetailDialog
        bankDetail={isCreating ? null : editingBankDetail}
        open={isCreating || !!editingBankDetail}
        onClose={handleCloseDialog}
        onSuccess={handleSuccess}
      />

      <DeleteBankDetailDialog
        bankDetail={deletingBankDetail}
        open={!!deletingBankDetail}
        onClose={handleCloseDeleteDialog}
        onSuccess={handleSuccess}
      />
    </div>
  )
}
