import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

import NotificationPreferences from '@/components/notification-preferences';
import { useAuth } from '@/hooks/auth';
import { useTranslations } from '@/lib/i18n';

/**
 * Settings page for user preferences and account management.
 * Contains notification preferences and other user settings.
 */
export default function SettingsPage() {
  const { user } = useAuth();
  const { t, keys } = useTranslations();

  return (
    <div className="container mx-auto py-4 px-4 sm:px-6 lg:px-8 space-y-4 sm:space-y-6 max-w-4xl">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">{t(keys.settings.title)}</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          {t(keys.settings.description)}
        </p>
      </div>

      {/* User Profile Section */}
      <Card>
        <CardHeader>
          <CardTitle>{t(keys.settings.profile.title)}</CardTitle>
          <CardDescription>
            {t(keys.settings.profile.description)}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="space-y-1">
              <Label className="text-sm font-medium">{t(keys.settings.profile.name)}</Label>
              <p className="text-sm text-muted-foreground break-words">{user?.display_name || t(keys.settings.profile.notSet)}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-sm font-medium">{t(keys.settings.profile.email)}</Label>
              <p className="text-sm text-muted-foreground break-all font-mono">{user?.email}</p>
            </div>
            <div className="space-y-1 sm:col-span-2">
              <Label className="text-sm font-medium">{t(keys.settings.profile.accountType)}</Label>
              <div className="flex flex-wrap items-center gap-2">
                <Badge variant={user?.internal ? 'default' : 'secondary'} className="text-xs">
                  {user?.internal ? t(keys.settings.profile.internalUser) : t(keys.settings.profile.externalParticipant)}
                </Badge>
                {!user?.internal && (
                  <span className="text-xs text-muted-foreground">{t(keys.settings.profile.multiAccountAccess)}</span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Preferences Section */}
      <NotificationPreferences />
    </div>
  );
}

// Re-export for easier imports
export { SettingsPage };
