import { LoginForm } from '@/components/login-form';
import { Link, Navigate } from 'react-router-dom';
import { useAuth, AuthStatus } from '@/hooks/auth';
import { ROUTES } from '@/router/routes';
import { useTranslations } from '@/lib/i18n/typed-translations';

export default function LoginPage() {
  const { status } = useAuth();
  const { t, keys } = useTranslations();

  // Redirect authenticated users to dashboard
  if (status === AuthStatus.AUTHENTICATED) {
    return <Navigate to={ROUTES.DASHBOARD} replace />;
  }

  return (
    <div className="flex flex-col gap-6">
      <LoginForm />
      <div className="text-center text-sm">
        {t(keys.auth.login.noAccount)}{' '}
        <Link
          to={ROUTES.SIGNUP}
          className="underline underline-offset-4 hover:text-primary"
        >
          {t(keys.auth.login.signUp)}
        </Link>
      </div>
    </div>
  );
}