import { useEffect, useState } from 'react';
import { useSearchParams, Navigate } from 'react-router';
import { useEmailVerification } from '@/hooks/auth/use-email-verification';
import { useAuth, AuthStatus } from '@/hooks/auth';
import { LoadingScreen } from '@/components/loading-screen';
import { ROUTES } from '@/router/routes';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

/**
 * Email Verification Page
 * 
 * Handles the email verification flow for new user registrations.
 * Extracts the verification token from URL parameters, verifies the email,
 * and redirects users to the login page upon successful verification.
 * 
 * URL format: /auth/verify-email?token=abc123
 */
export default function VerifyEmailPage() {
  const [searchParams] = useSearchParams();
  const { status, isAuthenticated } = useAuth();

  const {
    verifyEmail,
    isPending,
    error,
    isError,
    isSuccess,
    reset
  } = useEmailVerification();

  const [verificationAttempted, setVerificationAttempted] = useState(false);
  const token = searchParams.get('token');

  // Attempt verification when component mounts and token is available
  useEffect(() => {
    if (token && !verificationAttempted && status !== AuthStatus.LOADING) {
      setVerificationAttempted(true);
      verifyEmail({
        body: { token }
      });
    }
  }, [token, verificationAttempted, status, verifyEmail]);

  // Redirect authenticated users to dashboard
  if (isAuthenticated) {
    return <Navigate to={ROUTES.DASHBOARD} replace />;
  }

  // Show loading screen while auth status is being determined
  if (status === AuthStatus.LOADING) {
    return <LoadingScreen />;
  }

  // Handle missing token
  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Invalid Verification Link
            </CardTitle>
            <CardDescription>
              The verification link appears to be incomplete or corrupted.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => window.location.href = ROUTES.LOGIN}
              className="w-full"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show loading state during verification
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Verifying Your Email
            </CardTitle>
            <CardDescription>
              Please wait while we verify your email address...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Show success state
  if (isSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Email Verified Successfully!
            </CardTitle>
            <CardDescription>
              Your email address has been verified. You can now log in to your account.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center text-sm text-gray-600 mb-4">
              Redirecting to login page in a few seconds...
            </div>
            <Button
              onClick={() => window.location.href = ROUTES.LOGIN}
              className="w-full"
            >
              Go to Login Now
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state
  if (isError) {
    const errorMessage = error?.error?.message || "Failed to verify email address.";
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Verification Failed
            </CardTitle>
            <CardDescription>
              We couldn't verify your email address.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {errorMessage}
              </AlertDescription>
            </Alert>
            <div className="flex flex-col space-y-2">
              <Button
                onClick={() => {
                  reset();
                  setVerificationAttempted(false);
                }}
                variant="outline"
                className="w-full"
              >
                Try Again
              </Button>
              <Button
                onClick={() => window.location.href = ROUTES.LOGIN}
                className="w-full"
              >
                Go to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Fallback loading state
  return <LoadingScreen />;
}
