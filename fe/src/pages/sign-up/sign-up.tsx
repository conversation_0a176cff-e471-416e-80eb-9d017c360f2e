import { Link, Navigate } from 'react-router-dom';
import { SignUpForm } from '@/components/sign-up-form';
import { useAuth, AuthStatus } from '@/hooks/auth';
import { ROUTES } from '@/router/routes';
import { useTranslations } from '@/lib/i18n/typed-translations';

export default function SignUpPage() {
  const { status } = useAuth();
  const { t, keys } = useTranslations();

  // Redirect authenticated users to dashboard
  if (status === AuthStatus.AUTHENTICATED) {
    return <Navigate to={ROUTES.DASHBOARD} replace />;
  }

  return (
    <div className="flex flex-col gap-6">
      <SignUpForm />
      <div className="text-center text-sm">
        {t(keys.auth.register.hasAccount)}{' '}
        <Link
          to={ROUTES.LOGIN}
          className="underline underline-offset-4 hover:text-primary"
        >
          {t(keys.auth.register.signIn)}
        </Link>
      </div>
    </div>
  );
}