import { useCallback, useRef, useEffect } from 'react';

/**
 * Custom hook for debouncing multiple rapid updates.
 * Provides instant visual feedback while batching API calls.
 */
export function useDebouncedUpdates<T>(
  updateFn: (updates: T[]) => void,
  delay: number = 500
) {
  const pendingUpdatesRef = useRef<Map<string, T>>(new Map());
  const timeoutRef = useRef<NodeJS.Timeout>();

  const flush = useCallback(() => {
    if (pendingUpdatesRef.current.size > 0) {
      const updates = Array.from(pendingUpdatesRef.current.values());
      updateFn(updates);
      pendingUpdatesRef.current.clear();
    }
  }, [updateFn]);

  const addUpdate = useCallback((key: string, update: T) => {
    pendingUpdatesRef.current.set(key, update);
    
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set new timeout
    timeoutRef.current = setTimeout(flush, delay);
  }, [flush, delay]);

  const clearUpdates = useCallback(() => {
    pendingUpdatesRef.current.clear();
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const hasPendingUpdates = useCallback(() => {
    return pendingUpdatesRef.current.size > 0;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    addUpdate,
    clearUpdates,
    hasPendingUpdates,
    flush
  };
}
