import { useInfiniteQuery } from '@tanstack/react-query';
import { fetchClient } from '@/lib/api/client';

/**
 * Custom hook for fetching chat messages with infinite scroll pagination.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically:
 * - Scopes messages to the current account (multi-tenancy)
 * - Validates user access to the channel
 * - Returns messages with sender details, mentions, and attachments
 * - Supports pagination using 'before' cursor for infinite scroll
 * 
 * @param channelId - Channel ID to fetch messages for
 * @param options - Query options including enabled and staleTime
 */
export function useChatMessages(
  channelId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return useInfiniteQuery({
    queryKey: ['get', '/api/chats/{channelId}/messages', { channelId }],
    queryFn: async ({ pageParam }) => {
      const response = await fetchClient.GET('/api/chats/{channelId}/messages', {
        params: {
          path: { channelId },
          query: {
            limit: 50, // Load 50 messages per page
            before: pageParam as number | undefined,
          },
        },
      });

      if (response.error) {
        throw new Error(response.error.error?.message || 'Failed to fetch messages');
      }

      return response.data!;
    },
    initialPageParam: undefined as number | undefined,
    getNextPageParam: (lastPage) => {
      // If we have messages and there are more, use the oldest message ID as cursor
      if (lastPage.messages && lastPage.messages.length > 0 && lastPage.has_more) {
        const oldestMessage = lastPage.messages[lastPage.messages.length - 1];
        return oldestMessage.id;
      }
      return undefined;
    },
    enabled: options?.enabled !== false && !!channelId,
    // Cache data for 30 seconds (messages change frequently)
    staleTime: options?.staleTime ?? 30 * 1000,
    // Enable automatic refetching on window focus for new messages
    refetchOnWindowFocus: true,
  });
}

/**
 * Utility function to flatten infinite query pages into a single array of messages.
 * Messages are returned in chronological order (oldest first).
 */
export function flattenMessages(data: ReturnType<typeof useChatMessages>['data']) {
  if (!data?.pages) return [];
  
  // Flatten all pages and reverse to get chronological order
  const allMessages = data.pages.flatMap(page => page.messages || []);
  return allMessages.reverse();
}
