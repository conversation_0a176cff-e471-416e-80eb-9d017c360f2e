import { useAuth } from '@/contexts/auth-context';

/**
 * Custom hook for downloading chat attachments.
 * Generates presigned download URLs for secure file access.
 * 
 * The backend validates that the user has access to the file
 * and generates a temporary presigned URL for download.
 */
export function useDownloadAttachment() {
  const { accessToken } = useAuth();

  const downloadAttachment = async (fileUrl: string, filename: string) => {
    try {
      // Generate presigned download URL
      const response = await fetch('/api/posts/media/download-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: new URLSearchParams({
          fileUrl: fileUrl,
        }),
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.error?.message) {
            errorMessage = errorData.error.message;
          }
        } catch {
          // If response is not JSON, use the default error message
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      const downloadUrl = result.downloadUrl;

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

    } catch (error) {
      // Error handling for attachment download
      console.error('Failed to download attachment:', error);
      throw error;
    }
  };

  return { downloadAttachment };
}
