import { useCallback, useMemo } from 'react';

/**
 * Shared utilities hook for chat components.
 * Provides memoized utility functions to prevent unnecessary re-renders.
 */
export function useChatUtils() {
  // Memoized function to get user initials
  const getInitials = useCallback((name?: string) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }, []);

  // Memoized function to get role badge colors
  const getRoleBadgeColor = useCallback((role?: string) => {
    switch (role) {
      case 'admin': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'reviewer': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'content_creator': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }, []);

  // Memoized function to format file sizes
  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // Memoized function to format time display
  const formatTime = useCallback((dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  }, []);

  // Memoized function to get channel icon type
  const getChannelIcon = useCallback((scope: string) => {
    switch (scope) {
      case 'general': return 'hash';
      case 'custom': return 'lock';
      default: return 'hash';
    }
  }, []);

  return useMemo(() => ({
    getInitials,
    getRoleBadgeColor,
    formatFileSize,
    formatTime,
    getChannelIcon
  }), [getInitials, getRoleBadgeColor, formatFileSize, formatTime, getChannelIcon]);
}
