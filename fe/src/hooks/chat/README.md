# Chat Hooks

This directory contains React hooks for managing real-time chat functionality using openapi-react-query and WebSocket connections.

## Available Hooks

### Channel Management
- `useChatChannels(hubId, options?)` - Fetch channels for a hub with permissions and unread counts
- `useChatChannel(hubId, channelId, options?)` - Fetch specific channel details

### Message Management
- `useChatMessages(channelId, options?)` - Fetch messages with infinite scroll pagination
- `useSendMessage(channelId)` - Send new messages with attachments and mentions
- `useUpdateMessage(channelId, messageId)` - Edit existing messages
- `useDeleteMessage(channelId, messageId)` - Delete messages
- `flattenMessages(data)` - Utility to flatten infinite query pages

### File Upload
- `useUploadChatAttachment()` - Upload files for chat attachments

## Features

- **Real-time messaging** via WebSocket integration
- **Infinite scroll pagination** for message history
- **File attachments** with progress tracking (10MB images, 100MB videos)
- **Message editing and deletion** with proper permissions
- **Mention parsing** and highlighting
- **Type-safe API calls** using OpenAPI-generated types
- **Automatic cache invalidation** on mutations
- **Multi-tenancy support** (automatic account scoping)
- **Permission-based access control** (only shows accessible channels)

## Usage

```typescript
import { 
  useChatChannels, 
  useChatMessages, 
  useSendMessage,
  useUploadChatAttachment,
  flattenMessages 
} from '@/hooks/chat';

// Fetch channels for a hub
const { data: channelsResponse, isLoading } = useChatChannels(hubId);
const channels = channelsResponse?.content || [];

// Fetch messages with infinite scroll
const {
  data,
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage,
} = useChatMessages(channelId);

const messages = flattenMessages(data);

// Send a message
const sendMessageMutation = useSendMessage(channelId);
const handleSend = () => {
  sendMessageMutation.mutate({
    params: { path: { channelId } },
    body: {
      content: 'Hello world!',
      attachment_uris: ['https://s3.../file.jpg']
    }
  });
};

// Upload attachment
const { uploadFile, isLoading: isUploading, progress } = useUploadChatAttachment();
const handleFileUpload = async (file: File) => {
  try {
    const result = await uploadFile(file);
    console.log('Uploaded:', result.url);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

## WebSocket Integration

The chat hooks work seamlessly with the WebSocket system:

1. **Real-time updates**: New messages are received via WebSocket and automatically update the UI
2. **Optimistic updates**: Messages sent via WebSocket appear immediately
3. **Fallback support**: If WebSocket is disconnected, falls back to REST API
4. **Cache invalidation**: WebSocket events trigger React Query cache updates

## File Upload Constraints

- **Images**: Maximum 10MB, supported types: JPEG, PNG, GIF, WebP
- **Videos**: Maximum 100MB, supported types: MP4, QuickTime, AVI
- **Documents**: Maximum 10MB, supported types: PDF, DOC, DOCX, TXT
- Progress tracking during upload
- Automatic file type validation
- Proper error handling with user-friendly messages

## Permission System

The chat system respects the backend permission model:

- **Channel access**: Users can only see channels they have permission to access
- **Message operations**: Only message authors can edit/delete their messages
- **File uploads**: Requires appropriate chat write permissions
- **Multi-tenancy**: All operations are automatically scoped to the user's account

## Error Handling

All hooks include comprehensive error handling:

- Network errors with retry logic
- Permission errors with clear messages
- File upload errors with specific validation messages
- WebSocket connection errors with fallback behavior
- Optimistic update rollback on failures
