import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching channel members/participants.
 * Since chat channels are scoped to hub participants, this fetches hub participants
 * and filters them based on channel access rules.
 *
 * The backend automatically:
 * - Scopes participants to the current account (multi-tenancy)
 * - Validates user access to the hub
 * - Returns participants with their roles and details
 *
 * @param hubId - Hub ID
 * @param channelId - Channel ID (used for future filtering if needed)
 * @param options - Query options including enabled and staleTime
 */
export function useChannelMembers(
  hubId: number,
  channelId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/participants', {
    params: {
      path: { hubId },
      query: {
        page: 0,
        size: 100, // Most hubs won't have more than 100 participants
      },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId && !!channelId,
    // Cache data for 1 minute (participant list changes less frequently)
    staleTime: options?.staleTime ?? 1 * 60 * 1000,
    // Enable automatic refetching on window focus for fresh status
    refetchOnWindowFocus: true,
  });
}
