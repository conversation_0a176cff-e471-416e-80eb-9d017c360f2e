import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';

/**
 * Custom hook for uploading chat attachment files.
 * Uses native fetch API for multipart/form-data uploads.
 * 
 * Reuses the same upload endpoint as posts but for chat attachments.
 * Includes progress tracking and proper error handling.
 * 
 * File constraints (enforced by backend):
 * - Images: max 10MB, types: jpeg, png, gif, webp
 * - Videos: max 100MB, types: mp4, quicktime, avi
 * - Documents: max 10MB, types: pdf, doc, docx, txt
 */
export function useUploadChatAttachment() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const { accessToken } = useAuth();

  const uploadFile = useCallback(async (file: File) => {
    if (!accessToken) {
      throw new Error('Authentication required');
    }

    setIsLoading(true);
    setError(null);
    setProgress(0);

    try {
      // Validate file size and type on frontend
      const maxSize = file.type.startsWith('video/') ? 100 * 1024 * 1024 : 10 * 1024 * 1024;
      if (file.size > maxSize) {
        const maxSizeMB = maxSize / (1024 * 1024);
        throw new Error(`File size exceeds ${maxSizeMB}MB limit`);
      }

      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/quicktime', 'video/x-msvideo',
        'application/pdf', 'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
      ];

      if (!allowedTypes.includes(file.type)) {
        throw new Error('File type not supported');
      }

      const formData = new FormData();
      formData.append('file', file);

      // Use XMLHttpRequest for progress tracking
      const response = await new Promise<Response>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentComplete = (event.loaded / event.total) * 100;
            setProgress(Math.round(percentComplete));
          }
        });

        xhr.addEventListener('load', () => {
          resolve(new Response(xhr.response, {
            status: xhr.status,
            statusText: xhr.statusText,
            headers: new Headers(xhr.getAllResponseHeaders().split('\r\n').reduce((headers, line) => {
              const [key, value] = line.split(': ');
              if (key && value) headers[key] = value;
              return headers;
            }, {} as Record<string, string>))
          }));
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed'));
        });

        xhr.open('POST', '/api/posts/media/upload');
        xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
        xhr.send(formData);
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.error?.message) {
            errorMessage = errorData.error.message;
          }
        } catch {
          // If response is not JSON, use the default error message
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
      return {
        url: result.url,
        filename: result.filename,
        size: result.size,
        mimeType: result.mime_type,
        type: result.type,
      };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
      setProgress(0);
    }
  }, [accessToken]);

  return {
    uploadFile,
    isLoading,
    error,
    progress,
  };
}
