import { useMemo } from 'react';
import type { MentionDto } from '@/lib/types/api';

interface ParsedMention {
  text: string;
  isMention: boolean;
  mention?: { name: string; email?: string };
}

/**
 * Hook for parsing mentions in message content.
 * Provides optimized mention parsing with memoization to prevent unnecessary re-computation.
 */
export function useMentionParser() {
  // Memoized function to create mention lookup map
  const createMentionMap = useMemo(() => (mentions: Array<{ name: string; email?: string }>) => {
    const mentionMap = new Map<string, { name: string; email?: string }>();
    
    mentions.forEach(mention => {
      // Map by full email address (primary)
      if (mention.email) {
        const email = mention.email.toLowerCase().trim();
        mentionMap.set(email, mention);

        // Map by email prefix (part before @)
        const emailPrefix = mention.email.split('@')[0].toLowerCase();
        mentionMap.set(emailPrefix, mention);
      }

      // Map by participant name (fallback for manually typed mentions)
      if (mention.name) {
        const fullName = mention.name.toLowerCase().trim();
        mentionMap.set(fullName, mention);

        // Map by first name only
        const firstName = fullName.split(' ')[0];
        if (firstName !== fullName) {
          mentionMap.set(firstName, mention);
        }
      }
    });

    return mentionMap;
  }, []);

  // Memoized function to parse content with mentions
  const parseContent = useMemo(() => (
    content: string, 
    mentions?: Array<{ name: string; email?: string }>
  ): ParsedMention[] => {
    if (!mentions || mentions.length === 0) {
      return [{ text: content, isMention: false }];
    }

    const parts: ParsedMention[] = [];
    let lastIndex = 0;

    const mentionMap = createMentionMap(mentions);

    // Find all @mentions in the content - detect full emails and email prefixes
    const mentionRegex = /@([a-zA-Z0-9._-]+(?:@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})?)/g;
    let match;

    while ((match = mentionRegex.exec(content)) !== null) {
      // Add text before mention
      if (match.index > lastIndex) {
        parts.push({
          text: content.slice(lastIndex, match.index),
          isMention: false
        });
      }

      // Extract the full email address or email prefix from mention
      const mentionIdentifier = match[1].toLowerCase().trim();
      const mentionData = mentionMap.get(mentionIdentifier);

      if (mentionData) {
        // Add the mention with display name instead of email/prefix
        parts.push({
          text: `@${mentionData.name || 'Unknown'}`,
          isMention: true,
          mention: mentionData
        });
      } else {
        // Not a valid mention, treat as regular text
        parts.push({
          text: match[0],
          isMention: false
        });
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      parts.push({
        text: content.slice(lastIndex),
        isMention: false
      });
    }

    return parts;
  }, [createMentionMap]);

  // Memoized function to convert mentions to display format (for channel list previews)
  const convertMentionsToDisplayFormat = useMemo(() => (
    content: string, 
    mentions: MentionDto[]
  ): string => {
    let result = content;

    // Create a map for mention lookup
    const mentionMap = new Map<string, MentionDto>();

    mentions.forEach(mention => {
      // Map by full email address (primary)
      if (mention.email) {
        const email = mention.email.toLowerCase().trim();
        mentionMap.set(email, mention);

        // Map by email prefix (part before @)
        const emailPrefix = mention.email.split('@')[0].toLowerCase();
        mentionMap.set(emailPrefix, mention);
      }
    });

    // Find and replace mentions in content
    const mentionRegex = /@([a-zA-Z0-9._-]+(?:@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})?)/g;

    result = result.replace(mentionRegex, (match, identifier) => {
      const mentionIdentifier = identifier.toLowerCase().trim();
      const mentionData = mentionMap.get(mentionIdentifier);

      if (mentionData && mentionData.name) {
        return `@${mentionData.name}`;
      }

      // Return original if no match found
      return match;
    });

    return result;
  }, []);

  return {
    parseContent,
    convertMentionsToDisplayFormat
  };
}
