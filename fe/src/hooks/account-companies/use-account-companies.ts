import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching all account companies.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns active (non-deleted) companies.
 */
export function useAccountCompanies() {
  return $api.useQuery('get', '/api/account-companies', {
    params: {},
  }, {
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
    // Cache data for 5 minutes
    staleTime: 5 * 60 * 1000,
  });
}
