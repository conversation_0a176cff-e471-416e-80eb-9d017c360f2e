import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * Custom hook for creating a new account company.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates the account companies list query on success
 * to ensure the UI shows the newly created company.
 */
export function useCreateAccountCompany() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/account-companies', {
    onSuccess: () => {
      // Invalidate and refetch account companies list
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/account-companies',
          {
            params: {
              query: {
                page: 0,
                size: 20
              }
            }
          }
        ],
      });

      // Show success toast notification
      toast.success('Company created successfully');
    },
    onError: (error) => {
      console.error('Failed to create account company:', error);
      toast.error('Failed to create company');
    },
  });
}
