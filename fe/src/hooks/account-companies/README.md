# Account Companies Hooks

This directory contains React hooks for account companies CRUD operations using `openapi-react-query` and the existing API client.

## Overview

The account companies hooks provide:
- **Type-safe API calls** using OpenAPI-generated types
- **Automatic multi-tenancy** scoping handled by the backend
- **React Query integration** for caching, loading states, and error handling
- **Automatic cache invalidation** on mutations to keep data fresh

## Hooks

### `useAccountCompanies`

Fetches all account companies for the current account.

```typescript
import { useAccountCompanies } from '@/hooks/account-companies';

function CompanyList() {
  const { data: companies, isLoading, error } = useAccountCompanies();

  if (isLoading) return <div>Loading companies...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <ul>
      {companies?.map((company) => (
        <li key={company.id}>{company.company_name}</li>
      ))}
    </ul>
  );
}
```

### `useAccountCompany`

Fetches a specific account company by ID.

```typescript
import { useAccountCompany } from '@/hooks/account-companies';

function CompanyDetails({ companyId }: { companyId: number }) {
  const { data: company, isLoading, error } = useAccountCompany(companyId);

  if (isLoading) return <div>Loading company...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!company) return <div>Company not found</div>;

  return (
    <div>
      <h1>{company.company_name}</h1>
      <p>Email: {company.email}</p>
      <p>Phone: {company.phone}</p>
      <p>Website: {company.website}</p>
    </div>
  );
}
```

### `useCreateAccountCompany`

Creates a new account company.

```typescript
import { useCreateAccountCompany, type AccountCompanyCreateRequest } from '@/hooks/account-companies';

function CreateCompanyForm() {
  const { mutate: createCompany, isPending, error } = useCreateAccountCompany();

  const handleSubmit = (data: AccountCompanyCreateRequest) => {
    createCompany({
      body: data,
    }, {
      onSuccess: (newCompany) => {
        console.log('Company created:', newCompany.company_name);
        // Navigate to company details or show success message
      },
    });
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      const formData = new FormData(e.currentTarget);
      handleSubmit({
        company_name: formData.get('company_name') as string,
        email: formData.get('email') as string,
        phone: formData.get('phone') as string,
        website: formData.get('website') as string,
      });
    }}>
      <input name="company_name" placeholder="Company Name" required />
      <input name="email" type="email" placeholder="Email" />
      <input name="phone" placeholder="Phone" />
      <input name="website" placeholder="Website" />
      <button type="submit" disabled={isPending}>
        {isPending ? 'Creating...' : 'Create Company'}
      </button>
      {error && <div>Error: {error.message}</div>}
    </form>
  );
}
```

### `useUpdateAccountCompany`

Updates an existing account company.

```typescript
import { useUpdateAccountCompany, type AccountCompanyUpdateRequest } from '@/hooks/account-companies';

function EditCompanyForm({ companyId, initialData }: { 
  companyId: number; 
  initialData: AccountCompanyUpdateRequest;
}) {
  const { mutate: updateCompany, isPending, error } = useUpdateAccountCompany();

  const handleSubmit = (data: AccountCompanyUpdateRequest) => {
    updateCompany({
      params: { path: { id: companyId } },
      body: data,
    }, {
      onSuccess: (updatedCompany) => {
        console.log('Company updated:', updatedCompany.company_name);
        // Show success message or navigate
      },
    });
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      const formData = new FormData(e.currentTarget);
      handleSubmit({
        company_name: formData.get('company_name') as string,
        email: formData.get('email') as string,
        phone: formData.get('phone') as string,
        website: formData.get('website') as string,
      });
    }}>
      <input name="company_name" defaultValue={initialData.company_name} placeholder="Company Name" />
      <input name="email" type="email" defaultValue={initialData.email} placeholder="Email" />
      <input name="phone" defaultValue={initialData.phone} placeholder="Phone" />
      <input name="website" defaultValue={initialData.website} placeholder="Website" />
      <button type="submit" disabled={isPending}>
        {isPending ? 'Updating...' : 'Update Company'}
      </button>
      {error && <div>Error: {error.message}</div>}
    </form>
  );
}
```

### `useDeleteAccountCompany`

Soft deletes an account company.

```typescript
import { useDeleteAccountCompany } from '@/hooks/account-companies';

function DeleteCompanyButton({ companyId, companyName }: { 
  companyId: number; 
  companyName: string;
}) {
  const { mutate: deleteCompany, isPending, error } = useDeleteAccountCompany();

  const handleDelete = () => {
    if (confirm(`Are you sure you want to delete ${companyName}?`)) {
      deleteCompany({
        params: { path: { id: companyId } },
      }, {
        onSuccess: () => {
          console.log('Company deleted successfully');
          // Show success message or navigate
        },
      });
    }
  };

  return (
    <div>
      <button onClick={handleDelete} disabled={isPending}>
        {isPending ? 'Deleting...' : 'Delete Company'}
      </button>
      {error && <div>Error: {error.message}</div>}
    </div>
  );
}
```

## Features

- **Type Safety**: Full TypeScript support with OpenAPI-generated types
- **Error Handling**: Comprehensive error states and validation error extraction
- **Loading States**: Built-in loading indicators for all operations
- **Cache Management**: Automatic cache invalidation on mutations
- **Multi-tenancy**: Backend automatically scopes data to current account
- **Optimistic Updates**: React Query handles optimistic updates and rollbacks

## Error Handling

All hooks provide comprehensive error handling:

- **Network errors**: Connection issues, timeouts
- **Validation errors**: Field-specific validation messages from backend
- **Authentication errors**: Invalid or expired tokens
- **Authorization errors**: Insufficient permissions
- **Not Found errors**: Company doesn't exist or access denied

Errors are properly typed and can be handled in your components using the `error` property returned by each hook.

## Cache Management

The hooks automatically manage React Query cache:

- **GET operations**: Cache data with appropriate stale times
- **Mutations**: Invalidate related queries on success
- **Optimistic updates**: UI updates immediately, rolls back on error
- **Background refetching**: Keep data fresh automatically

## Multi-tenancy

All operations are automatically scoped to the current user's account:

- Backend extracts account ID from JWT token
- No need to pass account ID in frontend
- Data isolation between different accounts
- Proper authorization checks on all operations
