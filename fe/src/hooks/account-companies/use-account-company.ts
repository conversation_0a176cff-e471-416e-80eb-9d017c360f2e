import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a specific account company by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically validates that the company belongs to the current account.
 * 
 * @param id - The account company ID to fetch
 * @param enabled - Whether the query should be enabled (default: true when id is provided)
 */
export function useAccountCompany(id: number | undefined, enabled = true) {
  return $api.useQuery('get', '/api/account-companies/{id}', {
    params: {
      path: { id: id! },
    },
  }, {
    // Only run the query if we have an ID and it's enabled
    enabled: enabled && id !== undefined,
    // Cache data for 10 minutes since individual companies change less frequently
    staleTime: 10 * 60 * 1000,
  });
}
