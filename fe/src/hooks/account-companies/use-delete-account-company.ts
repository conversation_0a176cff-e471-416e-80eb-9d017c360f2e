import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for soft deleting an account company.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI reflects the deletion (company will no longer appear in lists).
 */
export function useDeleteAccountCompany() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/account-companies/{id}', {
    onSuccess: (_data, variables) => {
      // Invalidate and refetch account companies list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/account-companies'],
      });

      // Remove the specific company from cache if we have the ID
      if (variables.params?.path?.id) {
        queryClient.removeQueries({
          queryKey: ['get', '/api/account-companies/{id}', { params: { path: { id: variables.params.path.id } } }],
        });
      }
    },
  });
}
