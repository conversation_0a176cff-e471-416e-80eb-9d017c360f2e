import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for updating an existing account company.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated data.
 */
export function useUpdateAccountCompany() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/account-companies/{id}', {
    onSuccess: (_data, variables) => {
      // Invalidate and refetch account companies list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/account-companies'],
      });

      // Invalidate the specific company query if we have the ID
      if (variables.params?.path?.id) {
        queryClient.invalidateQueries({
          queryKey: ['get', '/api/account-companies/{id}', { params: { path: { id: variables.params.path.id } } }],
        });
      }
    },
  });
}
