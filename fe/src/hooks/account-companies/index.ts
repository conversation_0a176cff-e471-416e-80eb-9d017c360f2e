/**
 * Account Companies hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for account companies CRUD operations
 * using the openapi-react-query pattern with proper TypeScript typing.
 *
 * Usage:
 * - useAccountCompanies: Fetch all account companies for the current account
 * - useAccountCompany: Fetch a specific account company by ID
 * - useCreateAccountCompany: Create a new account company
 * - useUpdateAccountCompany: Update an existing account company
 * - useDeleteAccountCompany: Soft delete an account company
 *
 * All hooks include proper error handling, loading states, and automatic
 * multi-tenancy scoping handled by the backend.
 */

// Export all account companies hooks
export { useAccountCompanies } from './use-account-companies';
export { useAccountCompany } from './use-account-company';
export { useCreateAccountCompany } from './use-create-account-company';
export { useUpdateAccountCompany } from './use-update-account-company';
export { useDeleteAccountCompany } from './use-delete-account-company';

// Re-export types from decoupled API types
export type {
  AccountCompanyResponse,
  AccountCompanyCreateRequest,
  AccountCompanyUpdateRequest
} from '@/lib/types/api';
