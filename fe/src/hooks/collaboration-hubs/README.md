# Collaboration Hub Hooks

This directory contains React hooks for managing collaboration hubs using openapi-react-query.

## Available Hooks

- `useCollaborationHubs(name?, brandId?, page?, size?, options?)` - Fetch hubs with pagination and filtering
- `useCollaborationHub(id, options?)` - Fetch a single hub by ID
- `useCreateCollaborationHub()` - Create a new collaboration hub
- `useUpdateCollaborationHub()` - Update an existing collaboration hub (admin only)

## Features

- Type-safe API calls using OpenAPI-generated types
- Automatic cache invalidation on mutations
- Multi-tenancy support (automatic account scoping)
- Pagination support for hub listing
- Name and brand-based filtering for hub search
- Optimistic UI updates through React Query
- Role-based access control (only shows hubs where user is participant)

## Usage

```typescript
import { useCollaborationHubs, useCreateCollaborationHub, useUpdateCollaborationHub } from '@/hooks/collaboration-hubs';

// Fetch hubs with optional filtering
const { data: hubs, isLoading } = useCollaborationHubs('campaign', 123, 0, 20);

// Create new hub
const createMutation = useCreateCollaborationHub();
const handleCreate = () => {
  createMutation.mutate({
    body: {
      name: 'New Campaign Hub',
      brandId: 123,
      description: 'Workspace for new campaign'
    }
  });
};

// Update existing hub
const updateMutation = useUpdateCollaborationHub();
const handleUpdate = (hubId: number) => {
  updateMutation.mutate({
    params: { path: { id: hubId } },
    body: {
      name: 'Updated Hub Name',
      description: 'Updated description'
    }
  });
};
```

## Data Structure

The hooks work with the following main data types:

- `CollaborationHubListItemDto` - Lightweight hub info for list view
- `CollaborationHubResponse` - Full hub details with participants and stats
- `CollaborationHubCreateRequest` - Data required to create a new hub

## Permissions

All hooks respect the backend permission system:
- `hub:read` - Required to list and view hubs
- `hub:write` - Required to create new hubs
- Users can only see hubs where they are participants
