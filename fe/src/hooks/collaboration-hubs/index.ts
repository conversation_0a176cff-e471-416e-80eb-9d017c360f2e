/**
 * Collaboration Hub hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for collaboration hub CRUD operations
 * and participant management using the openapi-react-query pattern with proper TypeScript typing.
 *
 * Hub Management:
 * - useCollaborationHubs: Fetch paginated hubs with filtering
 * - useCollaborationHub: Fetch a specific hub by ID
 * - useCreateCollaborationHub: Create a new hub
 * - useUpdateCollaborationHub: Update an existing hub
 *
 * Participant Management:
 * - useHubParticipants: Fetch hub participants with filtering
 * - useInviteParticipants: Invite new participants to a hub
 * - useUpdateParticipantRole: Update a participant's role
 * - useRemoveParticipant: Remove a participant from a hub
 * - useResendInvitation: Resend invitation to external participants
 *
 * All hooks include proper error handling, loading states, and automatic
 * multi-tenancy scoping handled by the backend.
 */

// Hub Management
export { useCollaborationHubs } from './use-collaboration-hubs';
export { useCollaborationHub } from './use-collaboration-hub';
export { useCreateCollaborationHub } from './use-create-collaboration-hub';
export { useUpdateCollaborationHub } from './use-update-collaboration-hub';
export { useDeleteCollaborationHub } from './use-delete-collaboration-hub';

// Participant Management
export { useHubParticipants } from './use-hub-participants';
export { useHubPermissions } from './use-hub-permissions';
export { useInviteParticipants } from './use-invite-participants';
export { useUpdateParticipantRole } from './use-update-participant-role';
export { useRemoveParticipant } from './use-remove-participant';
export { useResendInvitation } from './use-resend-invitation';
