import { $api } from '@/lib/api/client';
import type { PathsApiHubsHubIdParticipantsGetParametersQueryRole } from '@/lib/api/v1';

/**
 * Custom hook for fetching hub participants with filtering and pagination.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * The backend automatically applies role-based visibility and excludes removed participants:
 * - Admins and reviewers can see all active participants
 * - Content creators can only see admins and reviewers (not other creators)
 * - Only non-removed participants are returned
 *
 * @param hubId - Hub ID to fetch participants from
 * @param options - Query options including enabled, staleTime, and filters
 */
export function useHubParticipants(
  hubId: number | null,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    role?: PathsApiHubsHubIdParticipantsGetParametersQueryRole;
    page?: number;
    size?: number;
  }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/participants', {
    params: {
      path: { hubId: hubId! },
      query: {
        role: options?.role,
        page: options?.page ?? 0,
        size: options?.size ?? 20,
      },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId,
    // Cache data for 2 minutes (participants can change more frequently)
    staleTime: options?.staleTime ?? 2 * 60 * 1000,
  });
}
