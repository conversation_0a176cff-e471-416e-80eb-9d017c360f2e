import { useAuth } from '@/contexts/auth-context'
import { $api } from '@/lib/api/client'

/**
 * Custom hook for determining user permissions within a specific collaboration hub.
 * 
 * This hook combines account-level permissions with hub-specific participant roles
 * to determine what actions the current user can perform in a hub.
 * 
 * Permission Logic:
 * - Account ADMIN users: Full admin permissions for all hubs in their account
 * - Hub participants with "admin" role: Full admin permissions for the specific hub
 * - Other hub participants: Read-only access to view participants
 * - Non-participants: No access (handled by backend)
 * 
 * @param hubId - The collaboration hub ID to check permissions for
 * @returns Object containing permission flags and user context
 */
export function useHubPermissions(hubId: number | null) {
  const { user } = useAuth()
  
  // Fetch all hub participants to find current user's role
  const { data: participantsResponse, isLoading } = $api.useQuery('get', '/api/hubs/{hubId}/participants', {
    params: {
      path: { hubId: hubId! },
      query: {
        size: 100, // Get all participants (reasonable limit)
      },
    },
  }, {
    enabled: !!hubId,
    // Cache data for 5 minutes (participants don't change frequently)
    staleTime: 5 * 60 * 1000,
  })

  // Find current user's participant record in this hub
  const currentUserParticipant = participantsResponse?.content?.find(participant =>
    participant.email === user?.email
  )

  // Account-level ADMIN users have full permissions for hubs in their account
  const isAccountAdmin = user?.role === 'ADMIN'
  
  // Hub-level admin users have full permissions for this specific hub
  const isHubAdmin = currentUserParticipant?.role === 'admin'
  
  // Users with admin permissions (either account or hub level)
  const hasAdminPermissions = isAccountAdmin || isHubAdmin
  
  // Determine specific permissions
  const canInviteParticipants = hasAdminPermissions
  const canManageParticipants = hasAdminPermissions
  const canViewParticipants = !!currentUserParticipant || isAccountAdmin
  const canEditParticipantRoles = hasAdminPermissions
  const canRemoveParticipants = hasAdminPermissions
  
  return {
    // Permission flags
    canInviteParticipants,
    canManageParticipants,
    canViewParticipants,
    canEditParticipantRoles,
    canRemoveParticipants,
    
    // User context
    currentUserParticipant,
    isAccountAdmin,
    isHubAdmin,
    hasAdminPermissions,
    
    // Loading state
    isLoading,
    
    // Helper for button text
    getParticipantButtonText: () => {
      return hasAdminPermissions ? 'invite' : 'viewMembers'
    }
  }
}
