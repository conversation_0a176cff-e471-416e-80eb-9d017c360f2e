import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for updating a participant's role in a collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the hub participants list and hub details queries on success
 * to ensure the UI shows the updated participant role.
 * 
 * The backend automatically:
 * - Validates admin permissions for role updates
 * - Updates participant role in the database
 * - Maintains audit trail of role changes
 */
export function useUpdateParticipantRole(hubId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/hubs/{hubId}/participants/{participantId}/role', {
    onSuccess: () => {
      // Invalidate all participants queries for this hub (catches all query parameter variations)
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/hubs/{hubId}/participants' &&
                 queryKey.length >= 3 &&
                 (queryKey[2] as { params?: { path?: { hubId?: number } } })?.params?.path?.hubId === hubId;
        }
      });

      // Invalidate hub details to update participant information
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}', { params: { path: { id: hubId } } }]
      });

      // Also invalidate the general hubs list to update participant counts
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs']
      });
    },
  });
}
