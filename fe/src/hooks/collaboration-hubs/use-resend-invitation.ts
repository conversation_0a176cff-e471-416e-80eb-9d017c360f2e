import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for resending invitation to an external participant.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the hub participants list on success
 * to ensure the UI shows updated invitation status.
 * 
 * The backend automatically:
 * - Validates admin permissions for resending invitations
 * - Generates new magic link token
 * - Sends invitation email with updated link
 * - Updates invitation timestamps
 */
export function useResendInvitation(hubId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/hubs/{hubId}/participants/{participantId}/resend-invite', {
    onSuccess: () => {
      // Invalidate all participants queries for this hub (catches all query parameter variations)
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/hubs/{hubId}/participants' &&
                 queryKey.length >= 3 &&
                 (queryKey[2] as { params?: { path?: { hubId?: number } } })?.params?.path?.hubId === hubId;
        }
      });
    },
  });
}
