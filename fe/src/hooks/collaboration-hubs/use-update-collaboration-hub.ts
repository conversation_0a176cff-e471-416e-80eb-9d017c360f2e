import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for updating an existing collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated hub data.
 *
 * Only admins can update collaboration hubs.
 */
export function useUpdateCollaborationHub() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('put', '/api/hubs/{id}', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch collaboration hubs list
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/hubs',
          {
            params: {
              query: {
                name: null,
                page: 0,
                size: 20
              }
            }
          }
        ],
      });

      // Invalidate and refetch the specific hub
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/hubs/{id}',
          {
            params: {
              path: { id: variables.params.path.id },
              query: {}
            }
          }
        ],
      });

      // Show success toast notification
      toast.success(t(keys.collaborationHubs.editDialog.successMessage));
    },
    onError: (error) => {
      console.error('Failed to update collaboration hub:', error);
      toast.error(t(keys.collaborationHubs.editDialog.errorMessage));
    },
  });
}
