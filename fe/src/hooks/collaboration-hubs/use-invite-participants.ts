import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for inviting participants to a collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the hub participants list and hub details queries on success
 * to ensure the UI shows the newly invited participants.
 * 
 * The backend automatically:
 * - Validates participant permissions
 * - Sends invitation emails with magic links
 * - Creates participant records with proper roles
 * - <PERSON>les duplicate invitation prevention
 */
export function useInviteParticipants(hubId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/hubs/{hubId}/participants', {
    onSuccess: () => {
      // Invalidate all participants queries for this hub (catches all query parameter variations)
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/hubs/{hubId}/participants' &&
                 queryKey.length >= 3 &&
                 (queryKey[2] as { params?: { path?: { hubId?: number } } })?.params?.path?.hubId === hubId;
        }
      });

      // Invalidate hub details to update participant count
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}', { params: { path: { id: hubId } } }]
      });

      // Also invalidate the general hubs list to update participant counts
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs']
      });
    },
  });
}
