import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a single collaboration hub by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Returns the hub with all participants, statistics, and brand information.
 * Only returns hubs where the user is a participant.
 * 
 * @param id - Hub ID to fetch
 * @param options - Query options including enabled and staleTime
 */
export function useCollaborationHub(
  id: number, 
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{id}', {
    params: {
      path: { id },
    },
  }, {
    enabled: options?.enabled !== false && !!id,
    // Cache data for 5 minutes
    staleTime: options?.staleTime ?? 5 * 60 * 1000,
  });
}
