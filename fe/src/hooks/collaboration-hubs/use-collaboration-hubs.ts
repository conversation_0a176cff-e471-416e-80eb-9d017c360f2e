import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching collaboration hubs with pagination and filtering.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns hubs where the user is a participant.
 * 
 * @param name - Optional name filter for case-insensitive partial matching
 * @param brandId - Optional brand filter to show hubs for specific brand
 * @param page - Page number (0-based, default: 0)
 * @param size - Page size (default: 20, max: 100)
 * @param options - Additional query options (enabled, staleTime)
 */
export function useCollaborationHubs(
  name?: string, 
  brandId?: number, 
  page: number = 0, 
  size: number = 20,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs', {
    params: {
      query: {
        name,
        brandId,
        page,
        size,
      },
    },
  }, {
    enabled: options?.enabled !== false,
    // Cache data for 2 minutes (hubs change less frequently than other data)
    staleTime: options?.staleTime ?? 2 * 60 * 1000,
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
  });
}
