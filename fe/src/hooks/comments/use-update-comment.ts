import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for updating an existing comment.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates the comments list query on success
 * to ensure the UI shows the updated comment.
 *
 * The backend automatically:
 * - Validates user permissions (only comment author and admins can edit)
 * - Scopes to the current account (multi-tenancy)
 * - Updates the comment's updated_at timestamp
 */
export function useUpdateComment() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('put', '/api/comments/{commentId}', {
    onSuccess: (data, variables) => {
      const commentId = variables.params.path.commentId;

      // Invalidate specific comment query
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/comments/{commentId}',
          {
            params: {
              path: { commentId },
              query: {}
            }
          }
        ],
      });

      // Invalidate comments list for the post (we need to get postId from the response)
      if (data?.post_id) {
        queryClient.invalidateQueries({
          queryKey: [
            'get',
            '/api/posts/{postId}/comments',
            {
              params: {
                path: { postId: data.post_id },
                query: { page: 0, size: 20 }
              }
            }
          ],
        });
      }

      // Invalidate all comments queries to be safe
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/posts/{postId}/comments';
        }
      });

      // Show success toast notification
      toast.success(t(keys.collaborationHubs.posts.comments.commentUpdated));
    },
    onError: (error) => {
      console.error('Failed to update comment:', error);
      toast.error(t(keys.collaborationHubs.posts.comments.failedToUpdate));
    },
  });
}
