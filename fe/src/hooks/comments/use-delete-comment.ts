import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for deleting a comment.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the comments list query and post details on success
 * to ensure the UI removes the deleted comment and updates comment count.
 * 
 * The backend automatically:
 * - Validates user permissions (only comment author and admins can delete)
 * - Scopes to the current account (multi-tenancy)
 * - Updates the post's comment count
 */
export function useDeleteComment() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/comments/{commentId}', {
    onSuccess: (_, variables) => {
      const commentId = variables.params.path.commentId;
      
      // Remove specific comment from cache
      queryClient.removeQueries({
        queryKey: [
          'get',
          '/api/comments/{commentId}',
          {
            params: {
              path: { commentId },
              query: {}
            }
          }
        ],
      });

      // Invalidate all comments queries to update lists and counts
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/posts/{postId}/comments';
        }
      });

      // Invalidate post details to update comment count
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/posts/{postId}';
        }
      });

      // Invalidate posts list to update comment count in list view
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey as unknown[];
          return queryKey.length >= 2 &&
                 queryKey[0] === 'get' &&
                 queryKey[1] === '/api/hubs/{hubId}/posts';
        }
      });
    },
  });
}
