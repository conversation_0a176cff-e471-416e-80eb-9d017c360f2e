/**
 * Comment hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for comment CRUD operations
 * using the openapi-react-query pattern with proper TypeScript typing.
 *
 * Usage:
 * - useComments: Fetch paginated comments for a post
 * - useCommentsInfinite: Fetch comments with infinite scrolling
 * - useComment: Fetch a specific comment by ID
 * - useCreateComment: Create a new comment on a post
 * - useUpdateComment: Update an existing comment
 * - useDeleteComment: Delete a comment
 *
 * All hooks include proper error handling, loading states, and automatic
 * multi-tenancy scoping handled by the backend.
 */

export { useComments } from './use-comments';
export { useCommentsInfinite } from './use-comments-infinite';
export { useComment } from './use-comment';
export { useCreateComment } from './use-create-comment';
export { useUpdateComment } from './use-update-comment';
export { useDeleteComment } from './use-delete-comment';
