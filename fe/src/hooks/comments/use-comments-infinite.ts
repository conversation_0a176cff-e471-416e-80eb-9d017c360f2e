import { useInfiniteQuery } from '@tanstack/react-query';
import { fetchClient } from '@/lib/api/client';

/**
 * Custom hook for fetching comments with infinite scrolling for a post.
 * Uses React Query's infinite query with openapi-fetch for pagination.
 *
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Comments are ordered by creation date (newest first).
 * Works for both internal users and external participants.
 *
 * @param postId - Post ID to fetch comments from
 * @param size - Page size (default: 20, max: 100)
 * @param options - Additional query options (enabled, staleTime)
 */
export function useCommentsInfinite(
  postId: number | null,
  size: number = 20,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return useInfiniteQuery({
    queryKey: ['get', '/api/posts/{postId}/comments', { postId, size }],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await fetchClient.GET('/api/posts/{postId}/comments', {
        params: {
          path: { postId: postId! },
          query: {
            page: pageParam as number,
            size,
          },
        },
      });

      if (response.error) {
        throw new Error(response.error.error?.message || 'Failed to fetch comments');
      }

      return response.data!;
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      // Check if there are more pages available
      if (lastPage.has_next) {
        return (lastPage.current_page ?? 0) + 1;
      }
      return undefined;
    },
    enabled: options?.enabled !== false && !!postId,
    // Cache data for 30 seconds (comments change frequently)
    staleTime: options?.staleTime ?? 30 * 1000,
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
  });
}
