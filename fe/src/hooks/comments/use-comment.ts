import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a specific comment by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * 
 * @param commentId - Comment ID to fetch
 * @param options - Additional query options (enabled, staleTime)
 */
export function useComment(
  commentId: number | null,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/comments/{commentId}', {
    params: {
      path: { commentId: commentId! },
    },
  }, {
    enabled: options?.enabled !== false && !!commentId,
    // Cache data for 1 minute
    staleTime: options?.staleTime ?? 1 * 60 * 1000,
  });
}
