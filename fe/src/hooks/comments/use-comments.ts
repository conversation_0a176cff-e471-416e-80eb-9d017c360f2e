import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching comments for a post with pagination.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Comments are ordered by creation date (newest first).
 * Works for both internal users and external participants.
 *
 * @param postId - Post ID to fetch comments from
 * @param page - Page number (0-based, default: 0)
 * @param size - Page size (default: 20, max: 100)
 * @param options - Additional query options (enabled, staleTime)
 */
export function useComments(
  postId: number | null,
  page: number = 0,
  size: number = 20,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/posts/{postId}/comments', {
    params: {
      path: { postId: postId! },
      query: {
        page,
        size,
      },
    },
  }, {
    enabled: options?.enabled !== false && !!postId,
    // Cache data for 30 seconds (comments change frequently)
    staleTime: options?.staleTime ?? 30 * 1000,
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
  });
}
