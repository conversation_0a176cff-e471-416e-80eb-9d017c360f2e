# Comment Hooks

React Query hooks for managing post comments in the Collaboration Hub frontend.

## Overview

These hooks provide a clean, type-safe interface for comment CRUD operations using the openapi-react-query pattern. All hooks automatically handle multi-tenancy scoping, error handling, and cache invalidation.

## Available Hooks

### `useComments(postId, page?, size?, options?)`

Fetches paginated comments for a specific post.

**Parameters:**
- `postId: number | null` - Post ID to fetch comments from
- `page: number` - Page number (0-based, default: 0)
- `size: number` - Page size (default: 20, max: 100)
- `options?: { enabled?: boolean; staleTime?: number }` - Query options

**Returns:** React Query result with `PostCommentListResponse`

**Example:**
```tsx
const { data: comments, isLoading, error } = useComments(postId, 0, 20);
```

### `useCommentsInfinite(postId, size?, options?)`

Fetches comments with infinite scrolling for a specific post.

**Parameters:**
- `postId: number | null` - Post ID to fetch comments from
- `size: number` - Page size (default: 20, max: 100)
- `options?: { enabled?: boolean; staleTime?: number }` - Query options

**Returns:** React Query infinite result with flattened comments

**Example:**
```tsx
const {
  data,
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage
} = useCommentsInfinite(postId, 20);

const allComments = data?.pages.flatMap(page => page.comments) || [];
```

### `useComment(commentId, options?)`

Fetches a specific comment by ID.

**Parameters:**
- `commentId: number | null` - Comment ID to fetch
- `options?: { enabled?: boolean; staleTime?: number }` - Query options

**Returns:** React Query result with `PostCommentResponse`

### `useCreateComment()`

Creates a new comment on a post.

**Returns:** React Query mutation for creating comments

**Example:**
```tsx
const createComment = useCreateComment();

const handleSubmit = async (content: string) => {
  await createComment.mutateAsync({
    params: { path: { postId } },
    body: { content }
  });
};
```

### `useUpdateComment()`

Updates an existing comment.

**Returns:** React Query mutation for updating comments

**Example:**
```tsx
const updateComment = useUpdateComment();

const handleUpdate = async (commentId: number, content: string) => {
  await updateComment.mutateAsync({
    params: { path: { commentId } },
    body: { content }
  });
};
```

### `useDeleteComment()`

Deletes a comment.

**Returns:** React Query mutation for deleting comments

**Example:**
```tsx
const deleteComment = useDeleteComment();

const handleDelete = async (commentId: number) => {
  await deleteComment.mutateAsync({
    params: { path: { commentId } }
  });
};
```

## Features

- **Type Safety**: Full TypeScript support with generated API types
- **Automatic Cache Management**: Intelligent cache invalidation on mutations
- **Multi-tenancy**: Automatic account scoping handled by backend
- **Permission Handling**: Backend validates user permissions for all operations
- **Optimistic Updates**: UI updates immediately with proper error handling
- **Real-time Data**: Automatic refetching on window focus for fresh data

## Error Handling

All hooks include proper error handling. Use the standard React Query error patterns:

```tsx
const { data, isLoading, error } = useComments(postId);

if (error) {
  // Handle error state
  return <div>Error loading comments: {error.message}</div>;
}
```

## Cache Behavior

- **Comments List**: 30-second stale time with automatic refetch on focus
- **Individual Comments**: 1-minute stale time
- **Mutations**: Automatically invalidate related queries for consistency
