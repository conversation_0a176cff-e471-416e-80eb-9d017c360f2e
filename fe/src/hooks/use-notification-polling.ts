import { useEffect, useRef } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useUnreadNotificationCount } from '@/hooks/use-notifications'
import { useAuth } from '@/hooks/auth'

/**
 * Hook for real-time notification updates using polling.
 * Automatically polls for new notifications every 90 seconds when the user is active.
 * Pauses polling when the tab is not visible to save resources.
 */
export function useNotificationPolling() {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isVisibleRef = useRef(true)
  
  // Only enable polling for internal users
  const shouldPoll = user?.internal === true
  
  const startPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
    
    intervalRef.current = setInterval(() => {
      // Only poll if tab is visible and user should receive notifications
      if (isVisibleRef.current && shouldPoll) {
        // Invalidate notification queries to trigger refetch
        queryClient.invalidateQueries({
          queryKey: ['get', '/api/notifications']
        })
        queryClient.invalidateQueries({
          queryKey: ['get', '/api/notifications/unread-count']
        })
      }
    }, 90000) // Poll every 90 seconds (1.5 minutes)
  }
  
  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }
  
  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden
      
      if (document.hidden) {
        // Tab is hidden, stop polling to save resources
        stopPolling()
      } else if (shouldPoll) {
        // Tab is visible again, resume polling
        startPolling()
        
        // Immediately check for new notifications when tab becomes visible
        queryClient.invalidateQueries({
          queryKey: ['get', '/api/notifications']
        })
        queryClient.invalidateQueries({
          queryKey: ['get', '/api/notifications/unread-count']
        })
      }
    }
    
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [shouldPoll, queryClient])
  
  // Start/stop polling based on user authentication
  useEffect(() => {
    if (shouldPoll) {
      startPolling()
    } else {
      stopPolling()
    }
    
    return () => {
      stopPolling()
    }
  }, [shouldPoll])
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling()
    }
  }, [])
  
  return {
    isPolling: intervalRef.current !== null,
    startPolling,
    stopPolling,
  }
}

/**
 * Hook for detecting new notifications and showing toast notifications.
 * Compares current notification count with previous count to detect new notifications.
 */
export function useNewNotificationDetection() {
  const { user } = useAuth()
  const previousUnreadCountRef = useRef<number | null>(null)
  const { data: unreadCount } = useUnreadNotificationCount()
  
  useEffect(() => {
    // Only for internal users
    if (!user?.internal) {
      return
    }
    
    const currentCount = unreadCount || 0
    const previousCount = previousUnreadCountRef.current
    
    // If we have a previous count and current count is higher, show notification
    if (previousCount !== null && currentCount > previousCount) {
      const newNotificationCount = currentCount - previousCount
      
      // Show browser notification if permission is granted
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('New Notification', {
          body: `You have ${newNotificationCount} new notification${newNotificationCount > 1 ? 's' : ''}`,
          icon: '/favicon.ico',
          tag: 'collaboration-hub-notification', // Prevents duplicate notifications
        })
      }
      
      // Could also show toast notification here if desired
      // toast.info(`You have ${newNotificationCount} new notification${newNotificationCount > 1 ? 's' : ''}`)
    }
    
    // Update the previous count
    previousUnreadCountRef.current = currentCount
  }, [unreadCount, user?.internal])
  
  // Request notification permission on mount
  useEffect(() => {
    if (user?.internal && 'Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [user?.internal])
}

/**
 * Combined hook that enables both polling and new notification detection.
 * This is the main hook to use for real-time notification updates.
 */
export function useRealTimeNotifications() {
  const polling = useNotificationPolling()
  useNewNotificationDetection()
  
  return polling
}
