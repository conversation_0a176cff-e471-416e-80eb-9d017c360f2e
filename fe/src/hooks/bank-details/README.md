# Bank Details Hooks

This directory contains React hooks for bank details CRUD operations using `openapi-react-query` and the existing API client.

## Overview

The bank details hooks provide:
- **Type-safe API calls** using OpenAPI-generated types
- **Automatic multi-tenancy** scoping handled by the backend
- **React Query integration** for caching, loading states, and error handling
- **Automatic cache invalidation** on mutations to keep data fresh

## Hooks

### `useBankDetails`

Fetches all bank details for the current account.

```typescript
import { useBankDetails } from '@/hooks/bank-details';

function BankDetailsPage() {
  const { data: bankDetails, isLoading, error, isError, refetch } = useBankDetails();

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error: {error?.message}</div>;

  return (
    <div>
      {bankDetails?.map((bankDetail) => (
        <div key={bankDetail.id}>{bankDetail.name}</div>
      ))}
    </div>
  );
}
```

### `useBankDetail`

Fetches a specific bank detail by ID.

```typescript
import { useBankDetail } from '@/hooks/bank-details';

function BankDetailView({ id }: { id: number }) {
  const { data: bankDetail, isLoading, error } = useBankDetail(id);

  if (isLoading) return <div>Loading...</div>;
  if (!bankDetail) return <div>Bank detail not found</div>;

  return <div>{bankDetail.name}</div>;
}
```

### `useCreateBankDetail`

Creates a new bank detail.

```typescript
import { useCreateBankDetail, type BankDetailsCreateRequest } from '@/hooks/bank-details';

function CreateBankDetailForm() {
  const { mutate: createBankDetail, isPending, error } = useCreateBankDetail();

  const handleSubmit = (data: BankDetailsCreateRequest) => {
    createBankDetail({
      body: data,
    }, {
      onSuccess: (newBankDetail) => {
        console.log('Bank detail created:', newBankDetail.name);
        // Navigate to bank detail details or show success message
      },
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button type="submit" disabled={isPending}>
        {isPending ? 'Creating...' : 'Create Bank Detail'}
      </button>
    </form>
  );
}
```

### `useUpdateBankDetail`

Updates an existing bank detail.

```typescript
import { useUpdateBankDetail, type BankDetailsUpdateRequest } from '@/hooks/bank-details';

function EditBankDetailForm({ bankDetailId }: { bankDetailId: number }) {
  const { mutate: updateBankDetail, isPending, error } = useUpdateBankDetail();

  const handleSubmit = (data: BankDetailsUpdateRequest) => {
    updateBankDetail({
      params: { path: { id: bankDetailId } },
      body: data,
    }, {
      onSuccess: (updatedBankDetail) => {
        console.log('Bank detail updated:', updatedBankDetail.name);
        // Show success message or navigate
      },
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button type="submit" disabled={isPending}>
        {isPending ? 'Updating...' : 'Update Bank Detail'}
      </button>
    </form>
  );
}
```

### `useDeleteBankDetail`

Soft deletes a bank detail.

```typescript
import { useDeleteBankDetail } from '@/hooks/bank-details';

function DeleteBankDetailButton({ bankDetailId }: { bankDetailId: number }) {
  const { mutate: deleteBankDetail, isPending } = useDeleteBankDetail();

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this bank detail?')) {
      deleteBankDetail({
        params: { path: { id: bankDetailId } },
      }, {
        onSuccess: () => {
          console.log('Bank detail deleted');
          // Show success message
        },
      });
    }
  };

  return (
    <button onClick={handleDelete} disabled={isPending}>
      {isPending ? 'Deleting...' : 'Delete'}
    </button>
  );
}
```

## Error Handling

All hooks return error information that can be used for user feedback:

```typescript
const { data, isLoading, error, isError } = useBankDetails();

if (isError) {
  // error.message contains the error message
  // error.status contains the HTTP status code (if available)
  console.error('Failed to load bank details:', error);
}
```

## Cache Management

The hooks automatically manage React Query cache:

- **List queries** are invalidated when creating, updating, or deleting bank details
- **Individual queries** are invalidated when updating the specific bank detail
- **Stale time** is set to 5 minutes for optimal performance
- **Refetch on window focus** ensures data freshness when users return to the tab

## Multi-tenancy

All operations are automatically scoped to the current user's account. The backend handles multi-tenancy, so you don't need to pass account IDs in the frontend.
