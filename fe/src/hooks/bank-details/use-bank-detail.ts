import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a specific bank detail by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns active (non-deleted) bank details.
 *
 * @param id - Bank detail ID to fetch
 * @param enabled - Whether the query should be enabled (default: true)
 */
export function useBankDetail(id: number, enabled: boolean = true) {
  return $api.useQuery('get', '/api/bank-details/{id}', {
    params: { path: { id } },
  }, {
    enabled: enabled && !!id,
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
    // Cache data for 5 minutes
    staleTime: 5 * 60 * 1000,
  });
}
