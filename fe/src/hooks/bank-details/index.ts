/**
 * Bank Details hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for bank details CRUD operations
 * using the openapi-react-query pattern with proper TypeScript typing.
 *
 * Usage:
 * - useBankDetails: Fetch all bank details for the current account
 * - useBankDetail: Fetch a specific bank detail by ID
 * - useCreateBankDetail: Create a new bank detail
 * - useUpdateBankDetail: Update an existing bank detail
 * - useDeleteBankDetail: Soft delete a bank detail
 *
 * All hooks include proper error handling, loading states, and automatic
 * multi-tenancy scoping handled by the backend.
 */

// Export all bank details hooks
export { useBankDetails } from './use-bank-details';
export { useBankDetail } from './use-bank-detail';
export { useCreateBankDetail } from './use-create-bank-detail';
export { useUpdateBankDetail } from './use-update-bank-detail';
export { useDeleteBankDetail } from './use-delete-bank-detail';

// Re-export types from decoupled API types
export type {
  BankDetailsResponse,
  BankDetailsCreateRequest,
  BankDetailsUpdateRequest
} from '@/lib/types/api';
