import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching all bank details.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns active (non-deleted) bank details.
 */
export function useBankDetails() {
  return $api.useQuery('get', '/api/bank-details', {
    params: {},
  }, {
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
    // Cache data for 5 minutes
    staleTime: 5 * 60 * 1000,
  });
}
