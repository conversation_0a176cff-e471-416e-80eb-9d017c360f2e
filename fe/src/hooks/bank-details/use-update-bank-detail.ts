import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * Custom hook for updating an existing bank detail.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated data.
 */
export function useUpdateBankDetail() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/bank-details/{id}', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch bank details list
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/bank-details',
          {
            params: {
              query: {
                page: 0,
                size: 20
              }
            }
          }
        ],
      });

      // Invalidate the specific bank detail query if it exists
      if (variables.params?.path?.id) {
        queryClient.invalidateQueries({
          queryKey: [
            'get',
            '/api/bank-details/{id}',
            {
              params: {
                path: { id: variables.params.path.id },
                query: {}
              }
            }
          ],
        });
      }

      // Show success toast notification
      toast.success('Bank detail updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update bank detail:', error);
      toast.error('Failed to update bank detail');
    },
  });
}
