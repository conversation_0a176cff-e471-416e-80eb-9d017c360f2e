import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';

/**
 * Custom hook for uploading media files for posts.
 * Uses native fetch API for multipart/form-data uploads.
 *
 * Supports both direct upload and presigned URL upload patterns.
 * Includes progress tracking and proper error handling.
 * 
 * File constraints (enforced by backend):
 * - Images: max 10MB, types: jpeg, png, gif, webp
 * - Videos: max 100MB, types: mp4, quicktime, avi
 */
export function useUploadMedia() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const { accessToken } = useAuth();

  const uploadMedia = useCallback(async (file: File): Promise<{
    url: string;
    filename: string;
    size: number;
    mimeType: string;
    type: string;
  }> => {
    setIsLoading(true);
    setError(null);
    setProgress(0);

    try {
      // Validate file before upload
      const maxImageSize = 10 * 1024 * 1024; // 10MB
      const maxVideoSize = 100 * 1024 * 1024; // 100MB
      const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      const allowedVideoTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo'];

      const isImage = allowedImageTypes.includes(file.type);
      const isVideo = allowedVideoTypes.includes(file.type);

      if (!isImage && !isVideo) {
        throw new Error('File type not supported. Please upload images (JPEG, PNG, GIF, WebP) or videos (MP4, MOV, AVI).');
      }

      if (isImage && file.size > maxImageSize) {
        throw new Error('Image file size must be less than 10MB.');
      }

      if (isVideo && file.size > maxVideoSize) {
        throw new Error('Video file size must be less than 100MB.');
      }

      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Upload using XMLHttpRequest for progress tracking
      const response = await new Promise<Response>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentComplete = (event.loaded / event.total) * 100;
            setProgress(Math.round(percentComplete));
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(new Response(xhr.responseText, {
              status: xhr.status,
              statusText: xhr.statusText,
              headers: new Headers({
                'Content-Type': 'application/json',
              }),
            }));
          } else {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Network error occurred during upload'));
        });

        xhr.open('POST', '/api/posts/media/upload');
        xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
        xhr.send(formData);
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.error?.message) {
            errorMessage = errorData.error.message;
          }
        } catch {
          // If response is not JSON, use the default error message
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
      return {
        url: result.url,
        filename: result.filename,
        size: result.size,
        mimeType: result.mime_type,
        type: result.type,
      };

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload media';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
      setProgress(0);
    }
  }, [accessToken]);

  const reset = useCallback(() => {
    setError(null);
    setProgress(0);
  }, []);

  return {
    uploadMedia,
    isLoading,
    error,
    progress,
    reset,
  };
}
