import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for updating an existing post.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated post data.
 * 
 * Only post creators and hub admins can update posts (enforced by backend).
 */
export function useUpdatePost() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('put', '/api/posts/{postId}', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch the specific post
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/posts/{postId}', { params: { path: { postId: variables.params.path.postId } } }],
      });

      // Invalidate posts list queries (we don't know which hub this post belongs to)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/posts'],
      });

      // Invalidate hub details to update stats if needed
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}'],
      });

      // Show success toast notification
      toast.success(t(keys.collaborationHubs.posts.postUpdated));
    },
  });
}
