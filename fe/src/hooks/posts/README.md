# Posts Hooks

This directory contains React hooks for managing posts using openapi-react-query.

## Available Hooks

- `usePosts(hubId, status?, page?, size?, options?)` - Fetch posts with pagination and filtering
- `usePost(postId, options?)` - Fetch a single post by ID
- `useCreatePost()` - Create a new post in a collaboration hub
- `useUpdatePost()` - Update an existing post
- `useDeletePost()` - Delete a post (soft deletion)
- `useUploadMedia()` - Upload media files for posts
- `useHubParticipants(hubId, role?, options?)` - Fetch hub participants for reviewer selection

## Features

- Type-safe API calls using OpenAPI-generated types
- Automatic cache invalidation on mutations
- Multi-tenancy support (automatic account scoping)
- Pagination support for post listing
- Status-based filtering for post search
- Progress tracking for media uploads
- Role-based participant filtering for reviewer selection
- Optimistic UI updates through React Query
- Permission-based access control (only shows posts from accessible hubs)

## Usage

```typescript
import { 
  usePosts, 
  useCreatePost, 
  useUploadMedia,
  useHubParticipants 
} from '@/hooks/posts';

// Fetch posts with optional filtering
const { data: posts, isLoading } = usePosts(hubId, 'pending', 0, 20);

// Create new post
const createMutation = useCreatePost();
const handleCreate = () => {
  createMutation.mutate({
    params: { path: { hubId: 123 } },
    body: {
      caption: 'New post caption',
      media_uris: ['https://s3.../image1.jpg'],
      reviewer_notes: 'Please review for brand guidelines',
      reviewer_ids: [456, 789]
    }
  });
};

// Upload media with progress tracking
const { uploadMedia, isLoading: isUploading, progress } = useUploadMedia();
const handleFileUpload = async (file: File) => {
  try {
    const result = await uploadMedia(file);
    console.log('Uploaded:', result.url);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};

// Fetch reviewers for post assignment
const { data: reviewers } = useHubParticipants(hubId, 'reviewer');
```

## File Upload Constraints

The `useUploadMedia` hook enforces the following constraints:

- **Images**: Maximum 10MB, supported types: JPEG, PNG, GIF, WebP
- **Videos**: Maximum 100MB, supported types: MP4, QuickTime, AVI
- Progress tracking during upload
- Automatic file type validation
- Proper error handling with user-friendly messages

## Reviewer Selection

The `useHubParticipants` hook is optimized for reviewer selection:

- Filters participants to only show those who can review posts
- Includes: `reviewer`, `reviewer_creator`, and `admin` roles
- Excludes: `content_creator` role (unless they also have reviewer permissions)
- Applies role-based visibility rules from the backend
- Returns active participants only

## Error Handling

All hooks include comprehensive error handling:

- Network errors with retry capabilities
- Validation errors with field-specific messages
- Permission errors with appropriate user feedback
- File upload errors with size/type validation messages

## Cache Management

- Posts are cached for 1 minute (frequent updates during review process)
- Individual posts are cached for 2 minutes
- Participants are cached for 5 minutes (less frequent changes)
- Automatic cache invalidation on mutations
- Optimistic updates for better user experience
