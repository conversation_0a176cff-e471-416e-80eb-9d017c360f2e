import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for deleting a post.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI removes the deleted post.
 * 
 * The backend performs soft deletion and removes associated media files from S3.
 * Only post creators and hub admins can delete posts (enforced by backend).
 */
export function useDeletePost() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/posts/{postId}', {
    onSuccess: (_, variables) => {
      // Invalidate posts list queries (we don't know which hub this post belongs to)
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/posts'],
      });
      
      // Remove the specific post from cache
      queryClient.removeQueries({
        queryKey: ['get', '/api/posts/{postId}', { params: { path: { postId: variables.params.path.postId } } }],
      });
      
      // Invalidate hub details to update stats
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}'],
      });
    },
  });
}
