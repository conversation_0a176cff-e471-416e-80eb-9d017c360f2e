import { useInfiniteQuery } from '@tanstack/react-query';
import { fetchClient } from '@/lib/api/client';
import type { PathsApiHubsHubIdPostsGetParametersQueryStatus } from '@/lib/api/v1';

/**
 * Custom hook for fetching posts with infinite scrolling in a collaboration hub.
 * Uses React Query's infinite query with openapi-fetch for pagination.
 *
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns posts from hubs where the user is a participant.
 *
 * @param hubId - Hub ID to fetch posts from
 * @param filter - Optional post filter ('all', 'assigned_to_me', 'created_by_me', 'needs_review')
 * @param status - Optional status filter ('pending', 'approved', 'rework')
 * @param size - Page size (default: 20, max: 100)
 * @param options - Additional query options (enabled, staleTime)
 */
export function usePostsInfinite(
  hubId: number | null,
  filter?: string,
  status?: PathsApiHubsHubIdPostsGetParametersQueryStatus,
  size: number = 20,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return useInfiniteQuery({
    queryKey: ['get', '/api/hubs/{hubId}/posts', { hubId, filter, status, size }],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await fetchClient.GET('/api/hubs/{hubId}/posts', {
        params: {
          path: { hubId: hubId! },
          query: {
            filter: filter && filter !== 'all' ? filter : undefined,
            status,
            page: pageParam as number,
            size,
          },
        },
      });

      if (response.error) {
        throw new Error(response.error.error?.message || 'Failed to fetch posts');
      }

      return response.data!;
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      // Check if there are more pages available
      if (lastPage.has_next) {
        return (lastPage.page ?? 0) + 1;
      }
      return undefined;
    },
    enabled: options?.enabled !== false && !!hubId,
    // Cache data for 1 minute (posts change frequently during review process)
    staleTime: options?.staleTime ?? 1 * 60 * 1000,
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
  });
}
