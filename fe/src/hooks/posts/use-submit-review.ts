import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for submitting or updating a post review.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated review status and post data.
 * 
 * Only assigned reviewers and hub admins can review posts (enforced by backend).
 * Uses upsert behavior - one review per reviewer per post.
 */
export function useSubmitReview() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/posts/{postId}/reviews', {
    onSuccess: (_data, variables) => {
      const postId = variables.params.path.postId;
      
      // Invalidate and refetch the specific post to show updated review status
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/posts/{postId}', { params: { path: { postId } } }],
      });
      
      // Invalidate posts list queries to update review status in lists
      // We don't know which hub this post belongs to, so invalidate all posts lists
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/posts'],
      });
      
      // Invalidate hub details to update review statistics if needed
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}'],
      });
    },
  });
}
