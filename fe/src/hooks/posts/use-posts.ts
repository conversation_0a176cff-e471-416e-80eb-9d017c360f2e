import { $api } from '@/lib/api/client';
import type { PathsApiHubsHubIdPostsGetParametersQueryStatus } from '@/lib/api/v1';

/**
 * Custom hook for fetching posts in a collaboration hub with pagination and filtering.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns posts from hubs where the user is a participant.
 *
 * @param hubId - Hub ID to fetch posts from
 * @param filter - Optional post filter ('all', 'assigned_to_me', 'created_by_me', 'needs_review')
 * @param status - Optional status filter ('pending', 'approved', 'rework')
 * @param page - Page number (0-based, default: 0)
 * @param size - Page size (default: 20, max: 100)
 * @param options - Additional query options (enabled, staleTime)
 */
export function usePosts(
  hubId: number | null,
  filter?: string,
  status?: PathsApiHubsHubIdPostsGetParametersQueryStatus,
  page: number = 0,
  size: number = 20,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/posts', {
    params: {
      path: { hubId: hubId! },
      query: {
        filter: filter && filter !== 'all' ? filter : undefined,
        status,
        page,
        size,
      },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId,
    // Cache data for 3 minutes (safe margin for 10min URL expiration)
    staleTime: options?.staleTime ?? 3 * 60 * 1000,
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
  });
}
