import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a single post by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Returns the post with all media, reviewers, and permissions.
 * Only returns posts from hubs where the user is a participant.
 * 
 * @param postId - Post ID to fetch
 * @param options - Query options including enabled and staleTime
 */
export function usePost(
  postId: number | null, 
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/posts/{postId}', {
    params: {
      path: { postId: postId! },
    },
  }, {
    enabled: options?.enabled !== false && !!postId,
    // Cache data for 3 minutes (safe margin for 10min URL expiration)
    staleTime: options?.staleTime ?? 3 * 60 * 1000,
  });
}
