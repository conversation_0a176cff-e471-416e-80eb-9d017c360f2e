import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching hub participants with role filtering.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * This hook is specifically designed for post reviewer selection,
 * filtering participants to only show those with reviewer roles.
 *
 * The backend automatically applies role-based visibility and excludes removed participants:
 * - Admins and reviewers can see all active participants
 * - Content creators can only see admins and reviewers (not other creators)
 * - Only non-removed participants are returned
 *
 * @param hubId - Hub ID to fetch participants from
 * @param role - Optional role filter ('reviewer', 'reviewer_creator', 'admin')
 * @param options - Additional query options (enabled, staleTime)
 */
export function useHubParticipants(
  hubId: number | null,
  _role?: 'reviewer' | 'reviewer_creator' | 'admin', // Unused but kept for API compatibility
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/participants', {
    params: {
      path: { hubId: hubId! },
      query: {
        size: 100, // Get all participants (reasonable limit)
      },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId,
    // Cache data for 5 minutes (participants don't change frequently)
    staleTime: options?.staleTime ?? 5 * 60 * 1000,
    // Transform data to extract only reviewers for post assignment
    select: (data) => {
      if (!data?.content) return [];

      // Filter to only include participants who can review posts
      return data.content.filter(participant =>
        participant.role === 'reviewer' ||
        participant.role === 'reviewer_creator' ||
        participant.role === 'admin'
      );
    },
  });
}
