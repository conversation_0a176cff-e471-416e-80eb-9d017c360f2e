# Brands Hooks

This directory contains React hooks for managing brands using openapi-react-query.

## Available Hooks

- `useBrands(nameFilter?, page?, size?)` - Fetch brands with pagination and filtering
- `useBrand(id, enabled?)` - Fetch a single brand by ID
- `useCreateBrand()` - Create a new brand
- `useUpdateBrand()` - Update an existing brand
- `useDeleteBrand()` - Soft delete a brand

## Features

- Type-safe API calls using OpenAPI-generated types
- Automatic cache invalidation on mutations
- Multi-tenancy support (automatic account scoping)
- Pagination support for brand listing
- Name-based filtering for brand search
- Optimistic UI updates through React Query

## Usage

```typescript
import { useBrands, useCreateBrand } from '@/hooks/brands';

// List brands with pagination
const { data: brandsPage, isLoading } = useBrands('search term', 0, 20);

// Create a new brand
const createBrand = useCreateBrand();
await createBrand.mutateAsync({
  body: {
    name: 'Brand Name',
    company_name: 'Company Name',
    // ... other fields
  }
});
```
