import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching contacts for a specific brand.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns contacts for brands that belong to the current account.
 * 
 * @param brandId - The brand ID to fetch contacts for
 * @param enabled - Whether the query should be enabled (default: true when brandId is provided)
 */
export function useBrandContacts(brandId?: number, enabled?: boolean) {
  return $api.useQuery('get', '/api/brands/{id}/contacts', {
    params: {
      path: {
        id: brandId!,
      },
    },
  }, {
    // Only run query when brandId is provided, enabled is true, and brandId is a valid number
    enabled: enabled !== false && brandId !== undefined && brandId > 0,
    // Cache data for 5 minutes
    staleTime: 5 * 60 * 1000,
    // Keep data fresh when component is focused
    refetchOnWindowFocus: true,
  });
}
