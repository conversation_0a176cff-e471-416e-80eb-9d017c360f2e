import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching brands with pagination and filtering.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns active (non-deleted) brands.
 * 
 * @param nameFilter - Optional name filter for case-insensitive partial matching
 * @param page - Page number (0-based, default: 0)
 * @param size - Page size (default: 20, max: 100)
 */
export function useBrands(nameFilter?: string, page: number = 0, size: number = 20) {
  return $api.useQuery('get', '/api/brands', {
    params: {
      query: {
        name: nameFilter,
        page,
        size,
      },
    },
  }, {
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
    // Cache data for 5 minutes
    staleTime: 5 * 60 * 1000,
  });
}
