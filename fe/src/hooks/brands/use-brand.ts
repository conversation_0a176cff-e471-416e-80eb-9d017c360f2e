import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a single brand by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Returns the brand with all associated contacts.
 * 
 * @param id - Brand ID to fetch
 * @param enabled - Whether the query should be enabled (default: true)
 */
export function useBrand(id: number, enabled: boolean = true) {
  return $api.useQuery('get', '/api/brands/{id}', {
    params: {
      path: { id },
    },
  }, {
    enabled: enabled && !!id,
    // Cache data for 5 minutes
    staleTime: 5 * 60 * 1000,
  });
}
