import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching the next invoice number.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * This hook fetches the next suggested invoice number based on existing invoices
 * in the account. The backend automatically scopes results to the current account.
 * 
 * @param options - Query options including enabled and staleTime
 */
export function useNextInvoiceNumber(options?: { enabled?: boolean; staleTime?: number }) {
  return $api.useQuery('get', '/api/invoices/next-number', {}, {
    enabled: options?.enabled !== false,
    staleTime: 0,
    refetchOnWindowFocus: true
  });
}
