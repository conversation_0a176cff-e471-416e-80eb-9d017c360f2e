# Invoice Hooks

This directory contains React hooks for invoice management operations using the openapi-react-query pattern.

## Available Hooks

### Data Fetching Hooks

- **`useInvoices(params?)`** - Fetch paginated invoices with filtering
  - Supports status, date range, and pagination filters
  - Automatically scoped to current account (multi-tenancy)
  - Returns `PageResponseInvoiceResponse` from backend

- **`useInvoice(id)`** - Fetch a specific invoice by ID
  - Returns full invoice details including line items and recipients
  - Only runs query when ID is provided

### Mutation Hooks

- **`useCreateInvoice()`** - Create a new invoice
  - Automatically invalidates invoices list on success
  - Accepts `InvoiceCreateRequest` body

- **`useUpdateInvoice()`** - Update an existing invoice
  - Invalidates both list and specific invoice queries on success
  - Accepts `InvoiceUpdateRequest` body

- **`useDeleteInvoice()`** - Soft delete an invoice
  - Invalidates invoices list on success
  - Performs soft delete (backend implementation)

- **`useUpdateInvoiceStatus()`** - Update invoice status
  - Invalidates related queries on success
  - Accepts `InvoiceStatusUpdateRequest` body

- **`useInvoicePdf()`** - Download invoice PDF
  - Uses native fetch API for binary file downloads (avoids JSON parsing issues)
  - Returns `{ downloadPdf, isLoading, error }` object
  - Automatically handles file download in browser
  - Includes proper authentication headers

- **`useSendInvoice()`** - Send invoice email
  - Sends invoice to all recipients via email
  - Invalidates related queries on success

## Usage Examples

```tsx
import { 
  useInvoices, 
  useCreateInvoice, 
  useUpdateInvoiceStatus 
} from '@/hooks/invoices';

// Fetch invoices with filters
const { data: invoices, isLoading } = useInvoices({
  status: 'SENT',
  page: 0,
  size: 20
});

// Create new invoice
const createMutation = useCreateInvoice();
const handleCreate = () => {
  createMutation.mutate({
    body: {
      issuer_id: 1,
      recipient_id: 2,
      // ... other fields
    }
  });
};

// Update invoice status
const statusMutation = useUpdateInvoiceStatus();
const handleStatusUpdate = (invoiceId: number, newStatus: string) => {
  statusMutation.mutate({
    params: { path: { id: invoiceId } },
    body: { status: newStatus }
  });
};

// Download invoice PDF
const { downloadPdf, isLoading: isPdfLoading, error: pdfError } = useInvoicePdf();
const handleDownload = async (invoiceId: number) => {
  try {
    await downloadPdf({
      id: invoiceId,
      template: 'default' // optional: 'default', 'branded', 'minimal'
    });
  } catch (error) {
    console.error('Failed to download PDF:', error);
  }
};
```

## Type Safety

All hooks use TypeScript types generated from the OpenAPI specification:

- `InvoiceResponse` - Full invoice data
- `InvoiceCreateRequest` - Create invoice payload
- `InvoiceUpdateRequest` - Update invoice payload
- `InvoiceStatusUpdateRequest` - Status update payload
- `PageResponseInvoiceResponse` - Paginated response

## Error Handling

All hooks include automatic error handling through React Query:

- Network errors are automatically retried
- Error states are available via `isError` and `error` properties
- Loading states are available via `isLoading` and `isPending` properties

## Cache Management

The hooks automatically manage React Query cache:

- List queries are invalidated when mutations succeed
- Specific invoice queries are invalidated when that invoice is updated
- Stale time is configured appropriately for each hook type

## Multi-tenancy

All hooks automatically include multi-tenancy support:

- Backend automatically scopes all operations to the current account
- No additional account ID parameters needed in frontend
- JWT token provides account context to backend
