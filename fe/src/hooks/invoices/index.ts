/**
 * Invoice hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for invoice CRUD operations
 * using the openapi-react-query pattern with proper TypeScript typing.
 *
 * Usage:
 * - useInvoices: Fetch paginated invoices with filtering
 * - useInvoice: Fetch a specific invoice by ID
 * - useCreateInvoice: Create a new invoice
 * - useUpdateInvoice: Update an existing invoice
 * - useDeleteInvoice: Soft delete an invoice
 * - useUpdateInvoiceStatus: Update invoice status
 * - useInvoicePdf: Download invoice PDF
 * - useSendInvoice: Send invoice email
 *
 * All hooks include proper error handling, loading states, and automatic
 * multi-tenancy scoping handled by the backend.
 */

// Export all invoice hooks
export { useInvoices } from './use-invoices';
export { useInvoice } from './use-invoice';
export { useCreateInvoice } from './use-create-invoice';
export { useUpdateInvoice } from './use-update-invoice';
export { useDeleteInvoice } from './use-delete-invoice';
export { useUpdateInvoiceStatus } from './use-update-invoice-status';
export { useInvoicePdf } from './use-invoice-pdf';
export { useSendInvoice } from './use-send-invoice';
export { useInvoiceDeliveryStatus, useHasInvoiceBeenSent, hasBeenSentSuccessfully } from './use-invoice-delivery-status';
export { useNextInvoiceNumber } from './use-next-invoice-number';

// Re-export types from decoupled API types
export type {
  InvoiceResponse,
  InvoiceCreateRequest,
  InvoiceUpdateRequest,
  InvoiceItemRequest,
  InvoiceItemResponse,
  InvoiceRecipientRequest,
  InvoiceRecipientResponse,
  InvoiceEmailResponse,
  NextInvoiceNumberResponse,
  PageResponseInvoiceListItemDto
} from '@/lib/types/api';

// Additional types not in the main API types file
export type InvoiceStatusUpdateRequest = {
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'voided';
};

// Status enums from OpenAPI (matching backend enum values)
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue';
export type RecipientType = 'original' | 'copy';
export type RecipientSource = 'manual' | 'brand_contact';
