import { $api } from '@/lib/api/client';
import type { PathsApiInvoicesGetParametersQueryStatus } from '@/lib/api/v1';

/**
 * Custom hook for fetching paginated invoices with filtering.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Supports filtering by status, date range, and pagination.
 */
export function useInvoices(params?: {
  status?: PathsApiInvoicesGetParametersQueryStatus;
  fromDate?: string;
  toDate?: string;
  page?: number;
  size?: number;
}) {
  return $api.useQuery('get', '/api/invoices', {
    params: {
      query: {
        status: params?.status,
        fromDate: params?.fromDate,
        toDate: params?.toDate,
        page: params?.page || 0,
        size: params?.size || 20,
      },
    },
  }, {
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
    // Cache data for 2 minutes (invoices change less frequently than other data)
    staleTime: 2 * 60 * 1000,
  });
}
