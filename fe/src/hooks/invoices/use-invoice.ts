import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a specific invoice by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Returns full invoice details including line items and recipients.
 *
 * @param id - The invoice ID to fetch, or null/undefined to disable the query
 */
export function useInvoice(id: number | null | undefined) {
  return $api.useQuery('get', '/api/invoices/{id}', {
    params: {
      path: { id: id! },
    },
  }, {
    // Only run query if ID is provided and is a valid number
    enabled: id !== null && id !== undefined && id > 0,
    // Cache data for 5 minutes
    staleTime: 5 * 60 * 1000,
  });
}
