import { $api } from '@/lib/api/client';

/**
 * Custom hook for checking invoice delivery status.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * This hook fetches delivery logs to determine if an invoice has been sent successfully.
 */
export function useInvoiceDeliveryStatus(invoiceId: number | null, options?: { enabled?: boolean; staleTime?: number }) {
  return $api.useQuery('get', '/api/invoices/{id}/delivery-logs', {
    params: { path: { id: invoiceId! } },
    ...options,
  }, {
    enabled: Boolean(invoiceId) && (options?.enabled !== false),
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Helper function to determine if an invoice has been sent successfully
 * based on delivery logs.
 */
export function hasBeenSentSuccessfully(deliveryLogs?: Array<{ delivery_status: string }>) {
  if (!deliveryLogs || deliveryLogs.length === 0) {
    return false;
  }

  return deliveryLogs.some(log => log.delivery_status === 'sent');
}

/**
 * Simplified hook to check if an invoice has been sent successfully.
 * Returns a boolean indicating whether the invoice has successful delivery logs.
 */
export function useHasInvoiceBeenSent(invoiceId: number | null, options?: { enabled?: boolean; staleTime?: number }) {
  const { data: deliveryLogs, ...rest } = useInvoiceDeliveryStatus(invoiceId, options);

  return {
    ...rest,
    data: hasBeenSentSuccessfully(deliveryLogs),
    deliveryLogs,
  };
}
