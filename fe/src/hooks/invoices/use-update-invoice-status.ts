import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for updating invoice status.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated status.
 */
export function useUpdateInvoiceStatus() {
  const queryClient = useQueryClient();

  return $api.useMutation('patch', '/api/invoices/{id}/status', {
    onSuccess: (_data, variables) => {
      // Invalidate and refetch invoices list
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/invoices',
          {
            params: {
              query: {
                status: null,
                startDate: null,
                endDate: null,
                page: 0,
                size: 20
              }
            }
          }
        ],
      });

      // Invalidate specific invoice query
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/invoices/{id}',
          {
            params: {
              path: { id: variables.params.path.id },
              query: {}
            }
          }
        ],
      });
    },
  });
}
