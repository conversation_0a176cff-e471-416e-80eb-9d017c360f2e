import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for soft deleting an invoice.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the invoices list query on success
 * to ensure the UI removes the deleted invoice.
 */
export function useDeleteInvoice() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/invoices/{id}', {
    onSuccess: () => {
      // Invalidate and refetch invoices list
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/invoices',
          {
            params: {
              query: {
                status: null,
                startDate: null,
                endDate: null,
                page: 0,
                size: 20
              }
            }
          }
        ],
      });
    },
  });
}
