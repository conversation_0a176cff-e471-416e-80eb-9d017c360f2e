import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';

/**
 * Custom hook for downloading invoice PDF.
 * Uses native fetch API for binary file downloads to avoid JSON parsing issues.
 *
 * Returns an object with downloadPdf function and loading state.
 * The backend handles PDF generation and returns the file as bytes.
 */
export function useInvoicePdf() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { accessToken } = useAuth();

  const downloadPdf = useCallback(async (params: {
    id: number;
  }) => {
    setIsLoading(true);
    setError(null);

    try {
      // Build URL
      const url = '/api/invoices/' + params.id + '/pdf';

      // Prepare headers
      const headers: Record<string, string> = {
        'Accept': 'application/pdf',
      };

      // Add authorization header if token is available
      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      // Use native fetch for binary data to avoid JSON parsing
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include', // Include cookies for refresh token
        headers,
      });

      if (!response.ok) {
        // Try to get error message from response
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch {
          // If response is not JSON, use the default error message
        }
        throw new Error(errorMessage);
      }

      // Get the response as ArrayBuffer for binary data
      const arrayBuffer = await response.arrayBuffer();

      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        throw new Error('No PDF data received');
      }

      // Create blob from ArrayBuffer
      const blob = new Blob([arrayBuffer], { type: 'application/pdf' });

      // Create download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `Invoice_${params.id}.pdf`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      window.URL.revokeObjectURL(downloadUrl);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to download PDF';
      setError(errorMessage);
      // Error handling for PDF download
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [accessToken]);

  return {
    downloadPdf,
    isLoading,
    error,
  };
}
