import { useEffect, useCallback } from 'react';
import { useAuth, AuthStatus } from '@/contexts/auth-context';
import useRefresh from '@/hooks/auth/use-refresh.ts';

/**
 * Custom hook for handling authentication initialization on app startup.
 * This hook attempts to refresh the access token using the refresh token cookie
 * and manages the authentication state accordingly.
 */
export function useAuthInitialization() {
  const {
    status,
    hasInitialized,
    updateAuthState,
    setInitialized,
    clearAuth
  } = useAuth();

  const { mutateAsync } = useRefresh()

  // Initialize authentication by attempting token refresh
  const initializeAuth = useCallback(async () => {
    // Skip if already initialized
    if (hasInitialized) {
      return;
    }

    try {
      // Attempt to refresh the token using the refresh token cookie
      // Use the API client directly to avoid interference with the refresh hook's error handling
      const response = await mutateAsync({});

      if (response && response.access_token && response.user) {
        // Success - update authentication state with user info
        updateAuthState(response);
      } else {
        // Invalid response - clear auth state
        clearAuth();
      }

      // Mark as initialized
      setInitialized();
    } catch (__error) {
      // No valid refresh token or refresh failed - clear auth state and mark as initialized
      clearAuth();
      setInitialized();
    }
  }, [hasInitialized, mutateAsync, setInitialized, updateAuthState, clearAuth]);

  // Run initialization on mount
  useEffect(() => {
    void initializeAuth();
  }, [initializeAuth]);

  return {
    isInitializing: status === AuthStatus.LOADING && !hasInitialized,
    hasInitialized,
    status,
  };
}

