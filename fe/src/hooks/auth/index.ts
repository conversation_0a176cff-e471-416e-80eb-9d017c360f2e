/**
 * Authentication hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for authentication operations
 * using the cookie-based refresh token flow implemented in the backend.
 *
 * Usage:
 * - useLogin: Handle user login with email/password
 * - useRegister: Handle user registration
 * - useEmailVerification: Handle email verification via verification tokens
 * - useRefresh: Handle token refresh (reads refresh token from HTTP-only cookies)
 * - useLogout: Handle user logout and cookie cleanup
 * - useAuthInitialization: Handle app initialization and automatic token refresh
 * - useAuth: Access authentication state and context
 * - useIsAuthenticated: Check if user is authenticated
 * - useCurrentUser: Get current user information
 */

// Export all authentication hooks
export { useLogin } from './use-login';
export { useLogout } from './use-logout';
export { useRefresh } from './use-refresh';
export { useRegister } from './use-register';
export { useEmailVerification } from './use-email-verification';
export { useAuthInitialization } from './use-auth-initialization';
export { useMagicLinkAuth } from './use-magic-link-auth';

// Re-export auth context hooks
export {
  useAuth,
  useIsAuthenticated,
  useCurrentUser,
  AuthStatus,
  type User,
  type AuthenticationResponse,
  type AuthState,
  type AuthContextType,
} from '@/contexts/auth-context';
