import { $api } from '@/lib/api/client';
import { useNavigate } from 'react-router';
import { ROUTES } from '@/router/routes';

/**
 * Custom hook for email verification functionality.
 * Handles user email verification via verification tokens sent in registration emails.
 */
export function useEmailVerification() {
  const navigate = useNavigate();

  const { mutate, mutateAsync, isPending, error, isError, isSuccess, reset } = $api.useMutation(
    'post',
    '/api/auth/verify-email',
    {
      onSuccess: (data) => {
        // Email verification successful - redirect to login page
        // User can now log in with their verified account
        setTimeout(() => {
          navigate(ROUTES.LOGIN, { 
            replace: true,
            state: { 
              message: data.message || 'Email verified successfully! You can now log in.',
              type: 'success'
            }
          });
        }, 2000); // Show success message for 2 seconds before redirect
      },
    }
  );

  return {
    verifyEmail: mutate,
    verifyEmailAsync: mutateAsync,
    isPending,
    error,
    isError,
    isSuccess,
    reset,
  };
}
