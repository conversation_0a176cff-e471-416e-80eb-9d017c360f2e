import { $api } from '@/lib/api/client';
import { useAuth } from '@/contexts/auth-context';

/**
 * Custom hook for user logout functionality.
 * Uses openapi-react-query for the logout API call and manages authentication state.
 * Even if the API call fails, the local state will be cleared for security.
 */
export function useLogout() {
  const { clearAuth } = useAuth();

  const mutation = $api.useMutation('post', '/api/auth/logout', {
    onSettled: () => {
      // Always clear local authentication state regardless of API call result
      // This ensures the user is logged out locally even if the server call fails
      clearAuth();

    },
    onError: (__error) => {
      // Error occurred but still continue with logout
    },
  });

  const logoutAsync = async (): Promise<void> => {
    try {
      await mutation.mutateAsync({});
    } catch (__error) {
      // Error is already handled in onError
    }
  };

  return {
    logout: () => mutation.mutate({}),
    logoutAsync,
    isLoading: mutation.isPending,
    isPending: mutation.isPending,
    error: mutation.error,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
    reset: mutation.reset,
  };
}

