import { $api } from '@/lib/api/client';


/**
 * Custom hook for user registration functionality.
 * Uses React Query directly until OpenAPI types include registration endpoint.
 */
export function useRegister() {
  const { mutate, isPending, error, isError, isSuccess, data } = $api.useMutation('post', '/api/auth/register');

  return {
   mutate,
    isPending,
    error,
    isError,
    isSuccess,
    data,
  };
}
