import { $api } from '@/lib/api/client';
import { useAuth } from '@/contexts/auth-context';

/**
 * Custom hook for user login functionality.
 * Uses openapi-react-query for the login API call and manages authentication state.
 */
export function useLogin() {
  const { updateAuthState } = useAuth();

  const { mutate,
    isPending,
    error,
    isError,
    isSuccess,
    reset,
  } = $api.useMutation('post', '/api/auth/login', {
    onSuccess: (data) => {
      // Success - update authentication state
      updateAuthState(data);
    },
  });

  return {
    login: mutate,
    isPending: isPending,
    error: error,
    isError: isError,
    isSuccess: isSuccess,
    reset:reset,
  };
}

