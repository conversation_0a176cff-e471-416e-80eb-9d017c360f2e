import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for deleting a collaboration hub brief.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI removes the deleted brief.
 * 
 * Only brief creators and hub admins can delete briefs (enforced by backend).
 */
export function useDeleteBrief() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/hubs/{hubId}/briefs/{briefId}', {
    onSuccess: (_, variables) => {
      // Invalidate briefs list for this hub
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/briefs', { params: { path: { hubId: variables.params.path.hubId } } }],
      });
      
      // Remove the specific brief from cache
      queryClient.removeQueries({
        queryKey: ['get', '/api/hubs/{hubId}/briefs/{briefId}', { 
          params: { 
            path: { 
              hubId: variables.params.path.hubId, 
              briefId: variables.params.path.briefId 
            } 
          } 
        }],
      });
      
      // Invalidate hub details to update stats if needed
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}', { params: { path: { id: variables.params.path.hubId } } }],
      });
    },
  });
}
