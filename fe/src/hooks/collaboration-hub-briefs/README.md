# Collaboration Hub Briefs Hooks

This directory contains React hooks for managing collaboration hub briefs using openapi-react-query.

## Available Hooks

- `useBriefs(hubId, title?, page?, size?, options?)` - Fetch briefs with pagination and filtering
- `useBrief(hubId, briefId, options?)` - Fetch a single brief by ID
- `useCreateBrief()` - Create a new collaboration hub brief
- `useUpdateBrief()` - Update an existing brief
- `useDeleteBrief()` - Delete a brief

## Features

- Type-safe API calls using OpenAPI-generated types
- Automatic cache invalidation on mutations
- Multi-tenancy support (automatic account scoping)
- Pagination support for brief listing
- Title-based filtering for brief search
- Optimistic UI updates through React Query
- Role-based access control (only shows briefs from accessible hubs)

## Usage

```typescript
import { useBriefs, useCreateBrief, useUpdateBrief, useDeleteBrief } from '@/hooks/collaboration-hub-briefs';

// Fetch briefs with optional filtering
const { data: briefs, isLoading } = useBriefs(hubId, 'guidelines', 0, 20);

// Create new brief
const createMutation = useCreateBrief();
const handleCreate = () => {
  createMutation.mutate({
    params: { path: { hubId: 123 } },
    body: {
      title: 'Brand Guidelines',
      body: 'Complete brand guidelines for the campaign...',
      accessTags: ['admin', 'reviewer']
    }
  });
};

// Update existing brief
const updateMutation = useUpdateBrief();
const handleUpdate = () => {
  updateMutation.mutate({
    params: { path: { hubId: 123, briefId: 456 } },
    body: {
      title: 'Updated Guidelines',
      body: 'Updated content...',
      accessTags: ['admin']
    }
  });
};

// Delete brief
const deleteMutation = useDeleteBrief();
const handleDelete = () => {
  deleteMutation.mutate({
    params: { path: { hubId: 123, briefId: 456 } }
  });
};
```

## Error Handling

All hooks include comprehensive error handling:

```typescript
const { data, isLoading, error } = useBriefs(hubId);

if (error) {
  // Handle error (automatically typed)
  console.error('Failed to fetch briefs:', error);
}
```

## Cache Management

The hooks automatically manage React Query cache:

- **Create**: Invalidates briefs list and hub details
- **Update**: Invalidates specific brief, briefs list, and hub details  
- **Delete**: Removes specific brief from cache, invalidates briefs list and hub details

## Multi-tenancy

All operations are automatically scoped to the current user's account. The backend ensures users can only access briefs from hubs where they are participants.

## Permissions

Brief operations respect role-based permissions:

- **Create**: All hub participants can create briefs
- **Read**: All hub participants can read briefs (with access tag filtering)
- **Update**: Only brief creators and hub admins can update briefs
- **Delete**: Only brief creators and hub admins can delete briefs
