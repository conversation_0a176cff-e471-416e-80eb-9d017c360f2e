/**
 * Collaboration Hub Briefs hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for brief CRUD operations
 * using the openapi-react-query pattern with proper TypeScript typing.
 *
 * Brief Management:
 * - useBriefs: Fetch paginated briefs with filtering
 * - useBrief: Fetch a specific brief by ID
 * - useCreateBrief: Create a new brief
 * - useUpdateBrief: Update an existing brief
 * - useDeleteBrief: Delete a brief
 *
 * All hooks include proper error handling, loading states, and automatic
 * multi-tenancy scoping handled by the backend.
 */

export { useBriefs } from './use-briefs';
export { useBrief } from './use-brief';
export { useCreateBrief } from './use-create-brief';
export { useUpdateBrief } from './use-update-brief';
export { useDeleteBrief } from './use-delete-brief';
