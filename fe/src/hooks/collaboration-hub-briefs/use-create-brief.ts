import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for creating a new collaboration hub brief.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the briefs list query on success
 * to ensure the UI shows the newly created brief.
 * 
 * The backend automatically:
 * - Scopes the brief to the current account (multi-tenancy)
 * - Sets the creator to the current user
 * - Validates brief content and access tags
 */
export function useCreateBrief() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('post', '/api/hubs/{hubId}/briefs', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch briefs list for this hub
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/briefs', { params: { path: { hubId: variables.params.path.hubId } } }],
      });

      // Invalidate hub details to update stats if needed
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}', { params: { path: { id: variables.params.path.hubId } } }],
      });

      // Show success toast notification
      toast.success(t(keys.collaborationHubs.briefs.dialog.createSuccess));
    },
  });
}
