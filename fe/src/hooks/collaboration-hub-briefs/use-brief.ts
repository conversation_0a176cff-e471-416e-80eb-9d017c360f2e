import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a single collaboration hub brief by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Returns the brief with all details including creator information.
 * Only returns briefs from hubs where the user is a participant.
 * 
 * @param hubId - Hub ID the brief belongs to
 * @param briefId - Brief ID to fetch
 * @param options - Query options including enabled and staleTime
 */
export function useBrief(
  hubId: number | null,
  briefId: number | null, 
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/briefs/{briefId}', {
    params: {
      path: { hubId: hubId!, briefId: briefId! },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId && !!briefId,
    // Cache data for 5 minutes
    staleTime: options?.staleTime ?? 5 * 60 * 1000,
  });
}
