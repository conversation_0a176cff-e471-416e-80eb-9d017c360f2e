import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';


/**
 * Custom hook for updating an existing collaboration hub brief.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated brief data.
 * 
 * Only brief creators and hub admins can update briefs (enforced by backend).
 */
export function useUpdateBrief() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/hubs/{hubId}/briefs/{briefId}', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch the specific brief
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/briefs/{briefId}', { 
          params: { 
            path: { 
              hubId: variables.params.path.hubId, 
              briefId: variables.params.path.briefId 
            } 
          } 
        }],
      });
      
      // Invalidate briefs list for this hub
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/briefs', { params: { path: { hubId: variables.params.path.hubId } } }],
      });
      
      // Invalidate hub details to update stats if needed
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}', { params: { path: { id: variables.params.path.hubId } } }],
      });
    },
  });
}
