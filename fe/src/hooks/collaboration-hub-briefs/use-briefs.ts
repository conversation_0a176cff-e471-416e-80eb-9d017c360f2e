import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching collaboration hub briefs with pagination and filtering.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Only returns briefs from hubs where the user is a participant.
 * 
 * @param hubId - Hub ID to fetch briefs from
 * @param title - Optional title filter for case-insensitive partial matching
 * @param page - Page number (0-based, default: 0)
 * @param size - Page size (default: 20, max: 100)
 * @param options - Additional query options (enabled, staleTime)
 */
export function useBriefs(
  hubId: number | null,
  title?: string,
  page: number = 0,
  size: number = 20,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/briefs', {
    params: {
      path: { hubId: hubId! },
      query: {
        title,
        page,
        size,
      },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId,
    // Cache data for 2 minutes (briefs change less frequently than posts)
    staleTime: options?.staleTime ?? 2 * 60 * 1000,
    // Enable automatic refetching on window focus for fresh data
    refetchOnWindowFocus: true,
  });
}
