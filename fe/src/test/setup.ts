import '@testing-library/jest-dom'

// Mock window.getSelection for tests
Object.defineProperty(window, 'getSelection', {
  writable: true,
  value: () => ({
    rangeCount: 1,
    getRangeAt: () => ({
      startContainer: document.createElement('div'),
      startOffset: 0,
      endContainer: document.createElement('div'),
      endOffset: 0,
      setStart: () => {},
      setEnd: () => {},
      collapse: () => {},
      deleteContents: () => {},
      insertNode: () => {},
      setStartAfter: () => {}
    }),
    removeAllRanges: () => {},
    addRange: () => {}
  })
})

// Mock document.createRange for tests
Object.defineProperty(document, 'createRange', {
  writable: true,
  value: () => ({
    startContainer: document.createElement('div'),
    startOffset: 0,
    endContainer: document.createElement('div'),
    endOffset: 0,
    setStart: () => {},
    setEnd: () => {},
    collapse: () => {},
    deleteContents: () => {},
    insertNode: () => {},
    setStartAfter: () => {},
    selectNodeContents: () => {}
  })
})

// Mock document.createTreeWalker for tests
Object.defineProperty(document, 'createTreeWalker', {
  writable: true,
  value: () => ({
    nextNode: () => null
  })
})
