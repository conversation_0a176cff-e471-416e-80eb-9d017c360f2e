/**
 * Decoupled API types extracted from OpenAPI components.
 * 
 * This file provides standalone TypeScript types that are decoupled from the
 * OpenAPI components structure. This separation provides:
 * - Better type safety and independence from API schema changes
 * - Cleaner imports without deep component access
 * - Flexibility for frontend-specific type customizations
 * - Easier maintenance and refactoring
 */

import type { components } from '@/lib/api/v1';

// ============================================================================
// ERROR TYPES
// ============================================================================

export type ErrorDetail = components['schemas']['ErrorDetail'];
export type ErrorInfo = components['schemas']['ErrorInfo'];
export type ErrorResponse = components['schemas']['ErrorResponse'];

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export type AuthenticationResponse = components['schemas']['AuthenticationResponse'];

// Enhanced UserInfo type with permissions field
export interface UserInfo extends Omit<components['schemas']['UserInfo'], 'permissions'> {
  permissions: components['schemas']['Permission'][];
}

// ============================================================================
// COLLABORATION HUB TYPES
// ============================================================================

export type CollaborationHubListItemDto = components['schemas']['CollaborationHubListItemDto'];
export type CollaborationHubResponse = components['schemas']['CollaborationHubResponse'];
export type CollaborationHubCreateRequest = components['schemas']['CollaborationHubCreateRequest'];
export type CollaborationHubUpdateRequest = components['schemas']['CollaborationHubUpdateRequest'];
export type PageResponseCollaborationHubListItemDto = components['schemas']['PageResponseCollaborationHubListItemDto'];

// ============================================================================
// HUB PARTICIPANT TYPES
// ============================================================================

export type HubParticipantResponse = components['schemas']['HubParticipantResponse'];
export type HubParticipantInviteRequest = components['schemas']['HubParticipantInviteRequest'];
export type HubParticipantInviteResponse = components['schemas']['HubParticipantInviteResponse'];
export type HubParticipantUpdateRoleRequest = components['schemas']['HubParticipantUpdateRoleRequest'];
export type ParticipantInviteItem = components['schemas']['ParticipantInviteItem'];
export type InvitedParticipant = components['schemas']['InvitedParticipant'];
export type HubParticipantListResponse = components['schemas']['HubParticipantListResponse'];
// export type HubStatsDto = components['schemas']['HubStatsDto']; // Not available in current API

// ============================================================================
// COLLABORATION HUB BRIEF TYPES
// ============================================================================

export type CollaborationBriefResponse = components['schemas']['CollaborationBriefResponse'];
export type CollaborationBriefListItemDto = components['schemas']['CollaborationBriefListItemDto'];
export type CollaborationBriefCreateRequest = components['schemas']['CollaborationBriefCreateRequest'];
export type CollaborationBriefUpdateRequest = components['schemas']['CollaborationBriefUpdateRequest'];
export type PageResponseCollaborationBriefListItemDto = components['schemas']['PageResponseCollaborationBriefListItemDto'];

// Frontend-specific types for the brief form
export interface BriefFormData {
  title: string;
  body?: string;
}

// ============================================================================
// POST TYPES
// ============================================================================

export type PostResponse = components['schemas']['PostResponse'];
export type PostListItemResponse = components['schemas']['PostListItemResponse'];
export type PostCreateRequest = components['schemas']['PostCreateRequest'];
export type PostUpdateRequest = components['schemas']['PostUpdateRequest'];
export type PostCreator = components['schemas']['PostCreator'];
export type PostReviewer = components['schemas']['PostReviewer'];
export type MediaItem = components['schemas']['MediaItem'];
export type PostListResponse = components['schemas']['PostListResponse'];

// ============================================================================
// COMMENT TYPES
// ============================================================================

export type PostCommentResponse = components['schemas']['PostCommentResponse'];
export type PostCommentCreateRequest = components['schemas']['PostCommentCreateRequest'];
export type PostCommentUpdateRequest = components['schemas']['PostCommentUpdateRequest'];
export type PostCommentListResponse = components['schemas']['PostCommentListResponse'];
export type PostCommentItem = components['schemas']['PostCommentItem'];
export type CommentAuthor = components['schemas']['CommentAuthor'];
export type CommentPermissions = components['schemas']['CommentPermissions'];

// ============================================================================
// MEDIA UPLOAD TYPES
// ============================================================================

export type FileValidationResponse = components['schemas']['FileValidationResponse'];
export type ValidationDetails = components['schemas']['ValidationDetails'];

// ============================================================================
// CHAT TYPES
// ============================================================================

export type ChatChannelResponse = components['schemas']['ChatChannelResponse'];
export type ChatChannelInfo = components['schemas']['ChatChannelInfo'];
export type ChatMessageResponse = components['schemas']['ChatMessageResponse'];
export type ChatMessageUpdateRequest = components['schemas']['ChatMessageUpdateRequest'];
export type ChatMessageListResponse = components['schemas']['ChatMessageListResponse'];
export type ChatParticipantDto = components['schemas']['ChatParticipantDto'];
export type MentionDto = components['schemas']['MentionDto'];
export type AttachmentDto = components['schemas']['AttachmentDto'];
export type PageResponseChatChannelResponse = components['schemas']['PageResponseChatChannelResponse'];
export type PageResponseChatMessageResponse = components['schemas']['PageResponseChatMessageResponse'];

// ============================================================================
// BRAND TYPES
// ============================================================================

export type BrandResponse = components['schemas']['BrandResponse'];
export type BrandListItemDto = components['schemas']['BrandListItemDto'];
export type BrandCreateRequest = components['schemas']['BrandCreateRequest'];
export type BrandUpdateRequest = components['schemas']['BrandUpdateRequest'];
export type BrandContactResponse = components['schemas']['BrandContactResponse'];
export type BrandContactRequest = components['schemas']['BrandContactRequest'];
export type PageResponseBrandListItemDto = components['schemas']['PageResponseBrandListItemDto'];

// ============================================================================
// INVOICE TYPES
// ============================================================================

export type InvoiceResponse = components['schemas']['InvoiceResponse'];
export type InvoiceListItemDto = components['schemas']['InvoiceListItemDto'];
export type InvoiceCreateRequest = components['schemas']['InvoiceCreateRequest'];
export type InvoiceUpdateRequest = components['schemas']['InvoiceUpdateRequest'];
export type InvoiceItemRequest = components['schemas']['InvoiceItemRequest'];
export type InvoiceItemResponse = components['schemas']['InvoiceItemResponse'];
export type InvoiceRecipientRequest = components['schemas']['InvoiceRecipientRequest'];
export type InvoiceRecipientResponse = components['schemas']['InvoiceRecipientResponse'];
export type InvoiceEmailResponse = components['schemas']['InvoiceEmailResponse'];
export type NextInvoiceNumberResponse = components['schemas']['NextInvoiceNumberResponse'];
export type PageResponseInvoiceListItemDto = components['schemas']['PageResponseInvoiceListItemDto'];

// ============================================================================
// BANK DETAILS TYPES
// ============================================================================

export type BankDetailsResponse = components['schemas']['BankDetailsResponse'];
export type BankDetailsCreateRequest = components['schemas']['BankDetailsCreateRequest'];
export type BankDetailsUpdateRequest = components['schemas']['BankDetailsUpdateRequest'];

// ============================================================================
// ACCOUNT COMPANY TYPES
// ============================================================================

export type AccountCompanyResponse = components['schemas']['AccountCompanyResponse'];
export type AccountCompanyCreateRequest = components['schemas']['AccountCompanyCreateRequest'];
export type AccountCompanyUpdateRequest = components['schemas']['AccountCompanyUpdateRequest'];

// ============================================================================
// PERMISSION ENUM
// ============================================================================

// Re-export Permission enum from generated API types to ensure consistency
export { Permission } from '@/lib/api/v1';

// ============================================================================
// ENUMS - Re-export from OpenAPI generated enums
// ============================================================================

// Import and re-export all enums from the OpenAPI generated file
export {
  // User and Role enums
  UserInfoRole,

  // Collaboration Hub enums
  CollaborationHubResponseMyRole,
  CollaborationHubListItemDtoMyRole,

  // Hub Participant enums
  HubParticipantResponseRole,
  HubParticipantUpdateRoleRequestRole,
  ParticipantInviteItemRole,
  ParticipantFiltersAvailableRoles,

  // Post enums
  PostListItemResponseReview_status,
  PostListItemResponseMy_review_status,

  // Chat enums
  ChatParticipantDtoRole,
  ChatChannelResponseScope,
  ChatChannelInfoScope,

  // Invoice enums
  InvoiceResponseStatus,
  InvoiceListItemDtoStatus,
  InvoiceRecipientRequestType,
  InvoiceRecipientRequestSource,
  InvoiceRecipientResponseType,
  InvoiceRecipientResponseSource
} from '@/lib/api/v1';
