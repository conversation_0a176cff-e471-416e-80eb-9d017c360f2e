import React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { useQueryClient } from '@tanstack/react-query';
import {
  ChatWebSocketClient,
  type TypingIndicator,
  type UserPresence,
} from './chat-websocket-client';

export interface WebSocketContextValue {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  subscribeToChannel: (channelId: number) => void;
  unsubscribeFromChannel: (channelId: number) => void;
  sendMessage: (channelId: number, content: string, attachmentUris?: string[]) => void;
  sendTypingIndicator: (channelId: number) => void;
  typingUsers: Record<number, Array<{ participantId: number; name: string; email: string; timestamp: Date }>>;
}

const WebSocketContext = createContext<WebSocketContextValue | null>(null);

export interface WebSocketProviderProps {
  children: React.ReactNode;
}

export function WebSocketProvider({ children }: WebSocketProviderProps) {
  const { accessToken, isAuthenticated, user } = useAuth();
  const queryClient = useQueryClient();
  const clientRef = useRef<ChatWebSocketClient | null>(null);
  
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [typingUsers, setTypingUsers] = useState<Record<number, Array<{ participantId: number; name: string; email: string; timestamp: Date }>>>({});

  // Cleanup typing indicators after 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setTypingUsers(prev => {
        const updated = { ...prev };
        let hasChanges = false;

        Object.keys(updated).forEach(channelIdStr => {
          const channelId = parseInt(channelIdStr);
          const filtered = updated[channelId].filter(typingUser =>
            now.getTime() - typingUser.timestamp.getTime() < 3000
          );

          if (filtered.length !== updated[channelId].length) {
            updated[channelId] = filtered;
            hasChanges = true;
          }
        });

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleMessage = useCallback((channelId: number) => {
    // Invalidate messages query to trigger refetch and show new message
    queryClient.invalidateQueries({
      queryKey: ['get', '/api/chats/{channelId}/messages', { channelId }],
    });

    // Also invalidate channel list to update last message
    queryClient.invalidateQueries({
      queryKey: ['get', '/api/hubs/{hubId}/chats'],
    });
  }, [queryClient]);

  const handleTyping = useCallback((channelId: number, typing: TypingIndicator) => {
    // Don't show typing indicator for current user
    if (typing.participant_email === user?.email) {
      return;
    }

    setTypingUsers(prev => ({
      ...prev,
      [channelId]: [
        ...(prev[channelId] || []).filter(typingUser => typingUser.participantId !== typing.participant_id),
        {
          participantId: typing.participant_id,
          name: typing.participant_name || typing.participant_email.split('@')[0], // Use name if available, fallback to email prefix
          email: typing.participant_email,
          timestamp: new Date(),
        }
      ]
    }));
  }, [user?.email]);

  const handleUserPresence = useCallback((_channelId: number, _presence: UserPresence) => {
    // Handle user presence updates (could be used for online status, etc.)
  }, []);

  const handleConnect = useCallback(() => {
    setIsConnected(true);
    setIsConnecting(false);
    setError(null);
  }, []);

  const handleDisconnect = useCallback(() => {
    setIsConnected(false);
    setIsConnecting(false);
  }, []);

  const handleError = useCallback((error: unknown) => {
    const errorMessage = error instanceof Error ? error.message : 'WebSocket connection error';
    setError(errorMessage);
    setIsConnecting(false);
  }, []);

  // Initialize WebSocket connection when authenticated
  useEffect(() => {
    if (!isAuthenticated || !accessToken) {
      // Clean up existing connection
      if (clientRef.current) {
        clientRef.current.disconnect();
        clientRef.current = null;
      }
      setIsConnected(false);
      setIsConnecting(false);
      return;
    }

    if (clientRef.current) {
      // Update token if client already exists
      clientRef.current.updateAccessToken(accessToken);
      return;
    }

    setIsConnecting(true);
    setError(null);

    // Create new WebSocket client
    clientRef.current = new ChatWebSocketClient({
      accessToken,
      onMessage: handleMessage,
      onTyping: handleTyping,
      onUserPresence: handleUserPresence,
      onConnect: handleConnect,
      onDisconnect: handleDisconnect,
      onError: handleError,
    });

    clientRef.current.connect();

    return () => {
      if (clientRef.current) {
        clientRef.current.disconnect();
        clientRef.current = null;
      }
    };
  }, [isAuthenticated, accessToken, handleMessage, handleTyping, handleUserPresence, handleConnect, handleDisconnect, handleError]);

  const subscribeToChannel = useCallback((channelId: number) => {
    if (clientRef.current && isConnected) {
      clientRef.current.subscribeToChannel(channelId);
    }
  }, [isConnected]);

  const unsubscribeFromChannel = useCallback((channelId: number) => {
    if (clientRef.current) {
      clientRef.current.unsubscribeFromChannel(channelId);
    }
  }, []);

  const sendMessage = useCallback((channelId: number, content: string, attachmentUris: string[] = []) => {
    if (clientRef.current && isConnected) {
      clientRef.current.sendMessage(channelId, content, attachmentUris);
    } else {
      throw new Error('WebSocket not connected');
    }
  }, [isConnected]);

  const sendTypingIndicator = useCallback((channelId: number) => {
    if (clientRef.current && isConnected) {
      clientRef.current.sendTypingIndicator(channelId);
    }
  }, [isConnected]);

  const value: WebSocketContextValue = {
    isConnected,
    isConnecting,
    error,
    subscribeToChannel,
    unsubscribeFromChannel,
    sendMessage,
    sendTypingIndicator,
    typingUsers,
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
}

export function useWebSocket(): WebSocketContextValue {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
}
