/**
 * WebSocket utilities for real-time chat functionality.
 * 
 * Provides STOMP-based WebSocket client with JWT authentication
 * and React context for managing WebSocket connections.
 */

export { ChatWebSocketClient } from './chat-websocket-client';
export { WebSocketProvider, useWebSocket } from './websocket-context';
export type { 
  ChatMessage, 
  TypingIndicator, 
  UserPresence, 
  WebSocketMessage,
  ChatWebSocketClientConfig 
} from './chat-websocket-client';
export type { WebSocketContextValue } from './websocket-context';
