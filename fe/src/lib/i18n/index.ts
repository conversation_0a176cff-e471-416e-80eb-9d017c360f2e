import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import enTranslations from './resources/en.json';

// Import the translation resources
const resources = {
  en: {
    translation: enTranslations,
  },
};

// Initialize i18next
i18n
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources,
    lng: 'en', // default language
    fallbackLng: 'en',

    // Namespace configuration
    defaultNS: 'translation',
    ns: ['translation'],

    // Enable returning objects for nested access
    returnObjects: true,

    // Interpolation configuration
    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // React-specific configuration
    react: {
      useSuspense: false, // Disable suspense for now to avoid loading states
    },

    // Development configuration
    debug: import.meta.env.DEV,

    // Key separator configuration
    keySeparator: '.', // Use dot notation for nested keys
    nsSeparator: false, // Disable namespace separator
  });

// Export the i18n instance
export default i18n;

// Re-export typed translation utilities for convenience
export {
  useTypedTranslations,
  useTranslations,
  getTranslationKey,
  type TypedTranslations,
  type UseTranslationsReturn
} from './typed-translations';

// Re-export types
export type { TranslationResources } from './types';
