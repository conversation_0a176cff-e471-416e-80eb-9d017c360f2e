import createFetchClient from 'openapi-fetch';
import createClient from 'openapi-react-query';
import type { paths } from './v1';
// Note: components are now imported from @/lib/types/api when needed

/**
 * API client configuration for the Collaboration Hub backend.
 * Uses openapi-react-query with openapi-fetch and TypeScript types generated from OpenAPI spec.
 */


// Create the fetch client
export const fetchClient = createFetchClient<paths>({
  credentials: 'include', // Important: Include cookies for refresh token
});

// Create the React Query client
export const $api = createClient(fetchClient);

/**
 * Set the authorization header for authenticated requests.
 * This should be called whenever we have a valid access token.
 */
export function setAuthToken(token: string | null) {
  if (token) {
    fetchClient.use({
      onRequest({ request }) {
        request.headers.set('Authorization', `Bearer ${token}`);
        return request;
      },
    });
  } else {
    // Remove authorization header if no token
    fetchClient.use({
      onRequest({ request }) {
        request.headers.delete('Authorization');
        return request;
      },
    });
  }
}

// Error response type for error handling
import type { ErrorInfo } from '@/lib/types/api';
type ErrorResponse = ErrorInfo;

/**
 * Check if an error is an authentication error (401 Unauthorized).
 */
export function isAuthError(error: ErrorResponse): boolean {
  return error && typeof error === 'object' && 'status' in error && error.status === 401;
}

/**
 * Check if an error is a validation error (400 Bad Request).
 */
export function isValidationError(error: ErrorResponse): boolean {
  return error && typeof error === 'object' && 'status' in error && error.status === 400;
}
