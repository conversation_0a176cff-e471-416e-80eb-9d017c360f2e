/**
 * Filter System Initialization
 *
 * Initializes the high-performance event-driven filter system
 * Call this once when the app starts
 */

import { initializeFilterFromUrl } from '@/hooks/posts/use-post-filters'
import type { PostFilterType } from '@/components/posts/post-filter-chips'

interface FilterEventBus {
  getCurrentFilter: () => Promise<PostFilterType>;
  setFilter: (filter: string) => Promise<void>;
  subscribe: (callback: (filter: PostFilterType) => void) => Promise<() => void>;
}

declare global {
  interface Window {
    filterEventBus?: FilterEventBus;
  }
}

/**
 * Initialize the filter system
 * Should be called once when the app starts
 */
export function initializeFilterSystem(): void {
  // Initialize filter from URL on app start
  initializeFilterFromUrl()
  
  // Make filter event bus globally accessible for debugging
  if (typeof window !== 'undefined') {
    // This is for development/debugging purposes
    window.filterEventBus = {
      getCurrentFilter: async () => {
        const { PostFilterEventBus } = await import('@/hooks/posts/use-post-filters')
        return PostFilterEventBus.getInstance().getCurrentFilter()
      },
      setFilter: async (filter: string) => {
        const { PostFilterEventBus } = await import('@/hooks/posts/use-post-filters')
        PostFilterEventBus.getInstance().setFilter(filter as PostFilterType)
      },
      subscribe: async (callback: (filter: PostFilterType) => void) => {
        const { PostFilterEventBus } = await import('@/hooks/posts/use-post-filters')
        return PostFilterEventBus.getInstance().subscribe(callback)
      }
    }
  }
}
