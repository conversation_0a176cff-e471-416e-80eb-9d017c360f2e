import React from 'react';
import { UnifiedInput } from '@/components/ui/unified-input';

interface MessageInputProps {
  channelId: number;
  hubId: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  // Editing mode props
  isEditing?: boolean;
  editingContent?: string;
  onSaveEdit?: (content: string) => void;
  onCancelEdit?: () => void;
}

export const MessageInput = React.memo<MessageInputProps>(({
  channelId,
  hubId,
  placeholder,
  disabled,
  className,
  isEditing = false,
  editingContent = '',
  onSaveEdit,
  onCancelEdit
}) => {
  return (
    <UnifiedInput
      variant="chat"
      hubId={hubId}
      channelId={channelId}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      isEditing={isEditing}
      editingContent={editingContent}
      onSaveEdit={onSaveEdit}
      onCancelEdit={onCancelEdit}
    />
  );
});

MessageInput.displayName = 'MessageInput';
