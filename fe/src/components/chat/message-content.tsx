import React, { useState, useCallback } from 'react';
import { UserProfilePopup } from '@/components/ui/user-profile-popup';
import { useMentionParser } from '@/hooks/chat';

interface MessageContentProps {
  content: string;
  mentions?: Array<{
    name: string;
    email?: string;
  }>;
}

export const MessageContent = React.memo<MessageContentProps>(({ content, mentions }) => {
  const [selectedMentionIndex, setSelectedMentionIndex] = useState<number | null>(null);
  const { parseContent } = useMentionParser();

  const handleMentionClick = useCallback((index: number) => {
    setSelectedMentionIndex(index);
  }, []);

  const handlePopupClose = useCallback((open: boolean) => {
    if (!open) {
      setSelectedMentionIndex(null);
    }
  }, []);

  // Parse content with mentions
  const parts = parseContent(content, mentions);

  return (
    <div className="whitespace-pre-wrap break-words">
      {parts.map((part, index) =>
        part.isMention && part.mention ? (
          <UserProfilePopup
            key={index}
            user={part.mention}
            open={selectedMentionIndex === index}
            onOpenChange={handlePopupClose}
            trigger={
              <span
                className="text-blue-600 dark:text-blue-400 cursor-pointer hover:underline"
                title={`${part.mention.name} (${part.mention.email || ''})`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleMentionClick(index);
                }}
              >
                {part.text}
              </span>
            }
          />
        ) : part.isMention ? (
          <span key={index} className="text-blue-600 dark:text-blue-400">
            {part.text}
          </span>
        ) : (
          <span key={index}>{part.text}</span>
        )
      )}
    </div>
  );
});

MessageContent.displayName = 'MessageContent';
