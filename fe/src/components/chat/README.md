# Chat Components

This directory contains React components for the real-time chat interface in collaboration hubs.

## Components

### MessageList
Main chat interface component with infinite scroll and real-time updates.

**Features:**
- Infinite scroll pagination for message history
- Real-time message updates via WebSocket
- Auto-scroll to bottom for new messages
- Load more messages when scrolling to top
- Typing indicators
- Message editing interface
- Empty state handling

**Props:**
```typescript
interface MessageListProps {
  channelId: number;
  channelName?: string;
  className?: string;
}
```

### MessageInput
Message composition component with file upload and emoji support.

**Features:**
- Real-time typing indicators
- File attachment with drag-and-drop
- Progress tracking for uploads
- Emoji picker (placeholder)
- Mention support (future)
- Send via WebSocket or REST API fallback
- Mobile-responsive design

**Props:**
```typescript
interface MessageInputProps {
  channelId: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}
```

### MessageItem
Individual message display component with actions.

**Features:**
- Message content with mention highlighting
- File attachment previews
- Edit/delete actions for own messages
- Role badges for senders
- Timestamp display with edit indicators
- Avatar display
- Mobile-responsive layout

**Props:**
```typescript
interface MessageItemProps {
  message: ChatMessage;
  channelId: number;
  onEdit?: (messageId: number, content: string) => void;
  className?: string;
}
```

### ChannelList
Channel sidebar component with unread counts.

**Features:**
- Channel list with icons based on scope
- Unread message counts
- Last message previews
- Participant counts
- Channel selection
- Loading and error states
- Mobile-responsive design

**Props:**
```typescript
interface ChannelListProps {
  hubId: number;
  selectedChannelId?: number;
  onChannelSelect: (channelId: number) => void;
  className?: string;
}
```

## Usage

```typescript
import { MessageList, MessageInput, MessageItem, ChannelList } from '@/components/chat';

// Full chat interface
function ChatInterface({ hubId }: { hubId: number }) {
  const [selectedChannelId, setSelectedChannelId] = useState<number | null>(null);

  return (
    <div className="flex h-full">
      {/* Channel sidebar */}
      <div className="w-80">
        <ChannelList
          hubId={hubId}
          selectedChannelId={selectedChannelId || undefined}
          onChannelSelect={setSelectedChannelId}
        />
      </div>

      {/* Chat area */}
      <div className="flex-1">
        {selectedChannelId && (
          <MessageList
            channelId={selectedChannelId}
            channelName="General"
          />
        )}
      </div>
    </div>
  );
}
```

## Design Patterns

### Mobile Responsiveness
- **Desktop**: Side-by-side channel list and chat area
- **Mobile**: Full-screen chat with slide-out channel selector
- **Breakpoint**: Uses `useIsMobile()` hook with 768px breakpoint

### Real-time Updates
- **WebSocket integration**: Automatic subscription to channel events
- **Optimistic updates**: Messages appear immediately when sent
- **Cache invalidation**: React Query cache updates on WebSocket events
- **Fallback behavior**: REST API fallback when WebSocket disconnected

### File Handling
- **Upload progress**: Visual progress bars during file upload
- **File previews**: Thumbnails and metadata for attachments
- **Download links**: Direct links to view/download files
- **Error handling**: Clear error messages for failed uploads

### Accessibility
- **Keyboard navigation**: Full keyboard support for all interactions
- **Screen readers**: Proper ARIA labels and semantic HTML
- **Focus management**: Logical tab order and focus indicators
- **Color contrast**: Meets WCAG guidelines for text and backgrounds

## Styling

All components use:
- **shadcn/ui components** for consistent design system
- **Tailwind CSS** for responsive styling
- **CSS Grid/Flexbox** for layouts
- **Custom ScrollArea** for consistent scrolling behavior
- **Dark/light theme** support via CSS variables

## State Management

- **React Query**: Server state management and caching
- **WebSocket Context**: Real-time connection management
- **Local state**: Component-specific UI state
- **Form state**: react-hook-form for message composition

## Performance Optimizations

- **Infinite scroll**: Only loads visible messages
- **Virtual scrolling**: Efficient rendering of large message lists
- **Debounced typing**: Reduces WebSocket traffic for typing indicators
- **Image lazy loading**: Defers loading of attachment previews
- **Memoization**: React.memo and useMemo for expensive operations

## Error Boundaries

Each component includes proper error handling:
- **Network errors**: Retry buttons and error messages
- **Permission errors**: Clear feedback for unauthorized actions
- **Upload errors**: Specific validation and size limit messages
- **WebSocket errors**: Connection status indicators and fallbacks
