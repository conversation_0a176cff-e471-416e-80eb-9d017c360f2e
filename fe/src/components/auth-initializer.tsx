import { useEffect } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { AuthStatus } from '@/contexts/auth-context';
import { useAuthInitialization } from '@/hooks/auth/use-auth-initialization';
import { LoadingScreen } from './loading-screen';
import { ROUTES } from '@/router/routes';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useRealTimeNotifications } from '@/hooks/use-notification-polling';


/**
 * Authentication layout component that wraps the entire app.
 * Handles automatic token refresh on app initialization and manages routing logic.
 * 
 * Flow:
 * 1. On mount, attempt to refresh JWT access token using refresh token cookie
 * 2. If refresh succeeds: store user info and allow access to requested route
 * 3. If refresh fails: clear auth state and redirect to login if on protected route
 * 4. Show loading screen until initialization is complete
 */
export function AuthInitializer() {
  const { status, hasInitialized, isInitializing } = useAuthInitialization();
  const navigate = useNavigate();
  const { t, keys } = useTranslations();

  // Enable real-time notification updates for authenticated users
  useRealTimeNotifications();

  // Handle routing logic after initialization
  useEffect(() => {
    // Don't do anything until initialization is complete
    if (!hasInitialized || isInitializing) {
      return;
    }

    if (status === AuthStatus.UNAUTHENTICATED) {
        navigate(ROUTES.LOGIN, { replace: true });
      }
  }, [status, hasInitialized, isInitializing, navigate]);

  // Show loading screen during initialization
  if (isInitializing || !hasInitialized) {
    return <LoadingScreen message={t(keys.auth.initializing)} />;
  }

  return <Outlet />;
}

export default AuthInitializer;
