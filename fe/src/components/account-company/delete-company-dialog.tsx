"use client"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog.tsx"
import { Alert, AlertDescription } from "@/components/ui/alert.tsx"
import { AlertCircle } from "lucide-react"
import { useDeleteAccountCompany } from "@/hooks/account-companies"
import { useTranslations } from '@/lib/i18n/typed-translations'

type Company = {
  id: number;
  phone?: string;
  email?: string;
  website?: string;
  account_id: number;
  company_name: string;
  address_street?: string;
  address_city?: string;
  address_postal_code?: string;
  address_country?: string;
  vat_number?: string;
  registration_number?: string;
  created_at: string;
  updated_at: string;
}


interface DeleteCompanyDialogProps {
  company: Company | null
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function DeleteCompanyDialog({ company, open, onClose, onSuccess }: DeleteCompanyDialogProps) {
  const { mutate: deleteCompany, isPending, error, isError } = useDeleteAccountCompany()
  const { t, keys } = useTranslations()

  const handleConfirm = () => {
    if (!company) return

    deleteCompany({
      params: { path: { id: company.id } },
    }, {
      onSuccess: () => {
        onClose()
        onSuccess?.()
      },
    })
  }

  return (
    <AlertDialog open={open} onOpenChange={onClose}>
      <AlertDialogContent className="w-[calc(100vw-1rem)] max-w-md mx-2 sm:mx-auto">
        <AlertDialogHeader>
          <AlertDialogTitle>{t(keys.accountCompanies.confirmDelete)}</AlertDialogTitle>
          <AlertDialogDescription>
            {t(keys.accountCompanies.deleteDescription)} <strong>{company?.company_name}</strong>
          </AlertDialogDescription>
        </AlertDialogHeader>

        {isError && error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error?.error?.message || t(keys.accountCompanies.error)}
            </AlertDescription>
          </Alert>
        )}

        <AlertDialogFooter className="flex flex-col-reverse gap-2 sm:flex-row sm:gap-2">
          <AlertDialogCancel disabled={isPending} className="min-h-[44px]">
            {t(keys.accountCompanies.cancel)}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 min-h-[44px]"
          >
            {isPending ? t(keys.accountCompanies.deleting) : t(keys.accountCompanies.delete)}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
