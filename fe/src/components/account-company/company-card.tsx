import { Building2, Mail, Phone, MapPin, Globe, MoreHorizontal, Pencil, Trash2 } from "lucide-react"
import { Card, CardContent, CardHeader } from "@/components/ui/card.tsx"
import { Button } from "@/components/ui/button.tsx"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu.tsx"
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { CompanyDisplayData } from '@/components/account-company/types';

interface CompanyCardProps {
  company: CompanyDisplayData
  onEdit: () => void
  onDelete: () => void
}

export function CompanyCard({ company, onEdit, onDelete }: CompanyCardProps) {
  const { t, keys } = useTranslations();

  return (
    <Card className="w-full hover:shadow-lg transition-shadow duration-200 relative">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Building2 className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-lg leading-tight">{company.company_name}</h3>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={onEdit}>
                <Pencil className="h-4 w-4 mr-2" />
                {t(keys.accountCompanies.edit)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onDelete}
                className="text-destructive focus:text-destructive focus:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {t(keys.accountCompanies.delete)}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Contact Information */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Mail className="h-4 w-4" />
            {company.email ? (
              <a href={`mailto:${company.email}`} className="hover:text-primary transition-colors">
                {company.email}
              </a>
            ) : (
              <span>—</span>
            )}
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-4 w-4" />
            {company.phone ? (
              <a href={`tel:${company.phone}`} className="hover:text-primary transition-colors">
                {company.phone}
              </a>
            ) : (
              <span>—</span>
            )}
          </div>
        </div>

        {/* Location */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <MapPin className="h-4 w-4" />
          <span>
            {company.address_city || company.address_country
              ? [company.address_city, company.address_country].filter(Boolean).join(", ")
              : "—"
            }
          </span>
        </div>

        {/* Website */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Globe className="h-4 w-4" />
          {company.website ? (
            <a
              href={company.website}
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary transition-colors truncate"
            >
              {company.website.replace(/^https?:\/\//, "")}
            </a>
          ) : (
            <span>—</span>
          )}
        </div>
      </CardContent>
    </Card>
  )
}