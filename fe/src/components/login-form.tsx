import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useLogin } from "@/hooks/auth";
import { useNavigate } from 'react-router';
import { ROUTES } from '@/router/routes';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useMemo } from 'react';

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export function LoginForm() {
  const navigate = useNavigate();
  const { login, isPending, error, isError } = useLogin();
  const { t, keys } = useTranslations();

  const schema = useMemo(() => yup.object({
    email: yup.string().email(t(keys.validation.email.invalid)).required(t(keys.validation.required)),
    password: yup.string().required(t(keys.validation.required)),
  }), [t, keys]);

  type LoginFormValues = yup.InferType<typeof schema>;

  const form = useForm<LoginFormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = (data: LoginFormValues) => {
    login({
      body: {
        ...data
      }
    }, {
      onSuccess: () => {
        navigate(ROUTES.DASHBOARD);
      },
    });
  };

  return (
    <div className="flex flex-col gap-6">
      <Card>
        <CardHeader>
          <CardTitle>{t(keys.auth.login.title)}</CardTitle>
          <CardDescription>
            {t(keys.auth.login.description)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.auth.login.email)}</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="text" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>{t(keys.auth.login.password)}</FormLabel>
                      <button
                        type="button"
                        className="text-sm underline-offset-4 hover:underline"
                      >
                        {t(keys.auth.login.forgotPassword)}
                      </button>
                    </div>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {isError && error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {t(keys.auth.login.error)}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex flex-col gap-3">
                <Button type="submit" className="w-full" disabled={isPending}>
                  {isPending ? t(keys.auth.login.submitting) : t(keys.auth.login.submit)}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
