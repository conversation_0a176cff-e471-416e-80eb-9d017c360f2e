# Authentication System

This directory contains the authentication components and layout for the Collaboration Hub frontend.

## Overview

The authentication system implements automatic token refresh on app initialization with proper routing logic. It uses:

- **Cookie-based refresh tokens** (HTTP-only, secure)
- **JWT access tokens** stored in localStorage
- **Automatic token refresh** on app startup
- **Route protection** and redirection logic
- **Loading states** during authentication initialization

## Components

### `AuthLayout`

The main authentication layout component that wraps the entire app and handles:

- Automatic token refresh on app mount
- Loading screen during initialization
- Routing logic based on authentication state
- Redirection between public and protected routes

**Usage:**
```tsx
<AuthLayout>
  <YourAppContent />
</AuthLayout>
```

### `ProtectedRoute`

A route guard component that protects authenticated-only routes:

```tsx
<ProtectedRoute>
  <DashboardPage />
</ProtectedRoute>
```

### `LoadingScreen`

A reusable loading screen component for authentication states:

```tsx
<LoadingScreen message="Initializing authentication..." />
```

## Authentication Flow

### 1. App Initialization

1. **AuthLayout** mounts and triggers **useAuthInitialization**
2. Hook attempts to refresh JWT using refresh token cookie
3. If refresh succeeds:
   - Store user info and access token
   - Mark as authenticated
   - Allow access to requested route
4. If refresh fails:
   - Clear any existing auth state
   - Mark as unauthenticated
   - Redirect to login if on protected route

### 2. Route Protection

- **Public routes** (`/login`, `/signup`): Accessible without authentication
- **Protected routes** (`/app/*`): Require authentication
- **Authenticated users** on login page: Redirected to dashboard
- **Unauthenticated users** on protected routes: Redirected to login

### 3. Loading States

- Show loading screen until authentication initialization completes
- Prevent route rendering during auth check
- Smooth user experience with proper loading indicators

## Integration

The authentication system is integrated into the router configuration:

```tsx
export const router = createBrowserRouter([
  {
    path: '/',
    element: <AuthLayout><CenteredLayout /></AuthLayout>,
    children: [
      { path: 'login', element: <LoginPage /> },
      { path: 'signup', element: <SignUpPage /> },
    ],
  },
  {
    path: '/app',
    element: (
      <AuthLayout>
        <ProtectedRoute>
          <SidebarLayout />
        </ProtectedRoute>
      </AuthLayout>
    ),
    children: [
      { path: 'dashboard', element: <DashboardPage /> },
    ],
  },
]);
```

## Security Features

- **HTTP-only cookies** for refresh tokens (not accessible via JavaScript)
- **Automatic token rotation** on refresh
- **Secure cookie attributes** (SameSite, Secure in production)
- **Proper error handling** for expired/invalid tokens
- **State cleanup** on logout or auth errors

## Error Handling

- **Network errors**: Graceful fallback to unauthenticated state
- **Invalid tokens**: Automatic cleanup and redirect to login
- **Expired refresh tokens**: Clear state and require re-authentication
- **API errors**: Proper error logging and user feedback

## Development Notes

- The system is designed to work seamlessly with the backend's cookie-based refresh token flow
- Loading states prevent flash of unauthenticated content
- Route protection works at multiple levels (layout + route guards)
- Authentication state is managed centrally via React Context
- All components are fully typed with TypeScript
