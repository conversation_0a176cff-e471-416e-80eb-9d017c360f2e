import { useSidebar } from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"

/**
 * Responsive card grid that adapts to sidebar state for optimal card layout.
 * 
 * Grid behavior:
 * - Mobile (< 768px): Always 1 column (sidebar is overlay)
 * - Medium (768px-1279px): 
 *   - Sidebar closed: 2 columns (more space available)
 *   - Sidebar open: 1 column (limited space due to sidebar)
 * - Large (≥ 1280px): Always 3 columns (enough space even with sidebar)
 * 
 * This prevents cramped layouts when sidebar reduces available content width.
 */
interface ResponsiveCardGridProps {
  children: React.ReactNode
  className?: string
}

export function ResponsiveCardGrid({ children, className }: ResponsiveCardGridProps) {
  const { open: sidebarOpen, isMobile } = useSidebar()

  // On mobile, sidebar is overlay so it doesn't affect layout
  // On desktop, we need to consider sidebar state for medium screens
  const gridClasses = cn(
    "grid gap-6",
    // Base: 1 column on mobile
    "grid-cols-1",
    // Medium screens: conditional based on sidebar state
    !isMobile && sidebarOpen 
      ? "md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3" // Sidebar open: delay 2-col until lg
      : "md:grid-cols-2 xl:grid-cols-3", // Sidebar closed: normal responsive
    className
  )

  return (
    <div className={gridClasses}>
      {children}
    </div>
  )
}

/**
 * Responsive skeleton grid with the same sidebar-aware behavior.
 */
interface ResponsiveSkeletonGridProps {
  count?: number
  className?: string
  children: React.ReactNode
}

export function ResponsiveSkeletonGrid({ 
  count = 6, 
  className,
  children 
}: ResponsiveSkeletonGridProps) {
  const { open: sidebarOpen, isMobile } = useSidebar()

  const gridClasses = cn(
    "grid gap-6",
    "grid-cols-1",
    !isMobile && sidebarOpen 
      ? "md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3"
      : "md:grid-cols-2 xl:grid-cols-3",
    className
  )

  return (
    <div className={gridClasses}>
      {Array.from({ length: count }, (_, i) => (
        <div key={i}>{children}</div>
      ))}
    </div>
  )
}
