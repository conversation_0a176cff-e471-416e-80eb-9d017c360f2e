import React from 'react'
import { User, Mail, ExternalLink, Crown, Camera, Eye, Edit } from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { useTranslations } from '@/lib/i18n/typed-translations'

interface UserProfilePopupProps {
  user: {
    name: string
    email?: string
    participant_id?: number
    is_external?: boolean
    role?: string
  }
  trigger: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function UserProfilePopup({ user, trigger, open, onOpenChange }: UserProfilePopupProps) {
  const { t, keys } = useTranslations()

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleIcon = (role?: string, isExternal?: boolean) => {
    // Prioritize functional role icons over participant type
    switch (role?.toLowerCase()) {
      case 'admin':
        return <Crown className="h-4 w-4 text-yellow-600" />
      case 'content_creator':
        return <Camera className="h-4 w-4 text-green-600" />
      case 'reviewer':
        return <Eye className="h-4 w-4 text-blue-600" />
      case 'reviewer_creator':
        return <Edit className="h-4 w-4 text-purple-600" />
      case 'external_participant':
        // For system-level external participant role, show external icon only if no functional role
        return isExternal ? <ExternalLink className="h-4 w-4 text-orange-600" /> : <User className="h-4 w-4 text-gray-600" />
      default:
        // Fallback: show external icon for external users, user icon for internal users
        return isExternal ? <ExternalLink className="h-4 w-4 text-orange-600" /> : <User className="h-4 w-4 text-gray-600" />
    }
  }

  const getRoleBadgeVariant = (role?: string) => {
    if (!role) return 'secondary'
    switch (role.toLowerCase()) {
      case 'admin':
        return 'default' as const
      case 'content_creator':
        return 'secondary' as const
      case 'reviewer':
        return 'outline' as const
      case 'reviewer_creator':
        return 'outline' as const
      default:
        return 'secondary' as const
    }
  }

  const formatRole = (role?: string) => {
    if (!role) return null

    // Skip system-level authentication roles - we want functional roles only
    if (role.toUpperCase() === 'EXTERNAL_PARTICIPANT') {
      return null
    }

    // Handle functional hub participant roles
    const roleKey = role.toLowerCase() as keyof typeof keys.collaborationHubs.roles
    return t(keys.collaborationHubs.roles[roleKey]) || role.replace('_', ' ')
  }

  const handleEmailClick = () => {
    if (user.email) {
      window.open(`mailto:${user.email}`, '_blank')
    }
  }

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        {trigger}
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="p-4">
          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <Avatar className="h-12 w-12">
              <AvatarFallback className="text-sm font-medium">
                {getInitials(user.name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-base truncate">
                  {user.name || t(keys.ui.userProfilePopup.unknownUser)}
                </h3>
                {getRoleIcon(user.role, user.is_external)}
              </div>
              <div className="flex items-center gap-2 mt-1 flex-wrap">
                {/* Functional Role Badge - prioritize meaningful roles */}
                {user.role && formatRole(user.role) && (
                  <Badge variant={getRoleBadgeVariant(user.role)} className="text-xs">
                    <div className="flex items-center gap-1">
                      {getRoleIcon(user.role, user.is_external)}
                      <span>{formatRole(user.role)}</span>
                    </div>
                  </Badge>
                )}

                {/* Subtle external indicator - only show when no functional role is available */}
                {user.is_external && (!user.role || !formatRole(user.role)) && (
                  <Badge variant="outline" className="text-xs text-muted-foreground">
                    {t(keys.ui.userProfilePopup.externalUser)}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          {user.email && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground truncate">
                  {user.email}
                </span>
              </div>
            </div>
          )}

          {/* Actions */}
          {user.email && (
            <div className="flex gap-2 mt-4 pt-4 border-t">
              <Button
                variant="outline"
                size="sm"
                className="flex-1"
                onClick={handleEmailClick}
              >
                <Mail className="h-4 w-4 mr-2" />
                {t(keys.ui.userProfilePopup.sendEmail)}
              </Button>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}
