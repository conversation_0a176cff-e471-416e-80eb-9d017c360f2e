import { forwardRef, useRef, useImperativeHandle, useCallback } from 'react'
import { Textarea } from '@/components/ui/textarea'
import { EmojiPicker } from '@/components/ui/emoji-picker'
import { cn } from '@/lib/utils'
import { useTranslations } from '@/lib/i18n/typed-translations'

interface TextareaWithEmojiProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'> {
  value?: string
  onChange?: (value: string) => void
  disabled?: boolean
  className?: string
}

export const TextareaWithEmoji = forwardRef<HTMLTextAreaElement, TextareaWithEmojiProps>(
  ({ value = '', onChange, disabled, className, ...props }, ref) => {
    const { t, keys } = useTranslations()
    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const cursorPositionRef = useRef<{ start: number; end: number }>({ start: 0, end: 0 })

    // Forward the ref to the textarea element
    useImperativeHandle(ref, () => textareaRef.current as HTMLTextAreaElement, [])

    // Store cursor position when textarea is focused or selection changes
    const updateCursorPosition = useCallback(() => {
      if (textareaRef.current) {
        const start = textareaRef.current.selectionStart || 0
        const end = textareaRef.current.selectionEnd || 0
        cursorPositionRef.current = { start, end }
      }
    }, [])

    const handleEmojiSelect = useCallback((emoji: string) => {
      if (!textareaRef.current || disabled) return

      const textarea = textareaRef.current
      const currentValue = value || ''

      // Use stored cursor position
      const { start, end } = cursorPositionRef.current

      // Insert emoji at cursor position
      const newValue = currentValue.slice(0, start) + emoji + currentValue.slice(end)

      // Update the value
      onChange?.(newValue)

      // Use setTimeout to ensure the DOM has updated before setting cursor position
      setTimeout(() => {
        if (textarea && textareaRef.current) {
          const newCursorPosition = start + emoji.length
          textarea.focus()
          textarea.setSelectionRange(newCursorPosition, newCursorPosition)
          // Update stored cursor position
          cursorPositionRef.current = { start: newCursorPosition, end: newCursorPosition }
        }
      }, 0)
    }, [value, onChange, disabled])

    const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onChange?.(e.target.value)
      // Update cursor position after text change
      updateCursorPosition()
    }

    return (
      <div className="relative">
        <Textarea
          ref={textareaRef}
          value={value}
          onChange={handleTextareaChange}
          onSelect={updateCursorPosition}
          onFocus={updateCursorPosition}
          onClick={updateCursorPosition}
          onKeyUp={updateCursorPosition}
          disabled={disabled}
          className={cn("pr-10", className)}
          {...props}
        />
        <div className="absolute top-2 right-2">
          <EmojiPicker
            onEmojiSelect={handleEmojiSelect}
            disabled={disabled}
            title={t(keys.ui.emojiPicker.addEmoji)}
          />
        </div>
      </div>
    )
  }
)

TextareaWithEmoji.displayName = 'TextareaWithEmoji'
