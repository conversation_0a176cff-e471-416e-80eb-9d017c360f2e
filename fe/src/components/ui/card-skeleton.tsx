import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { ResponsiveSkeletonGrid } from "@/components/ui/responsive-card-grid"

/**
 * Reusable card skeleton component for loading states.
 * Matches the approximate layout and size of actual cards across the application.
 */
export function CardSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {/* Icon placeholder */}
            <div className="p-2 bg-muted rounded-lg">
              <Skeleton className="h-5 w-5" />
            </div>
            <div className="space-y-2">
              {/* Title */}
              <Skeleton className="h-5 w-32" />
              {/* Subtitle/Badge */}
              <Skeleton className="h-4 w-20" />
            </div>
          </div>
          {/* Action button placeholder */}
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Content lines */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-40" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-36" />
          </div>
        </div>

        {/* Additional content section */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-28" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>

        {/* Footer section */}
        <div className="pt-2 border-t">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Container for multiple skeleton cards with the same responsive grid layout
 * used across all pages (1 card on mobile, 2 on medium, 3 on large screens).
 */
interface CardSkeletonGridProps {
  count?: number
}

export function CardSkeletonGrid({ count = 6 }: CardSkeletonGridProps) {
  return (
    <ResponsiveSkeletonGrid count={count}>
      <CardSkeleton />
    </ResponsiveSkeletonGrid>
  )
}
