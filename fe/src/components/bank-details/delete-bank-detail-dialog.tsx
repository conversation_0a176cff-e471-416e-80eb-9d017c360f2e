import { AlertTriangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useDeleteBankDetail } from "@/hooks/bank-details"
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { DeleteBankDetailDialogProps } from '@/components/bank-details/types';

export function DeleteBankDetailDialog({ bankDetail, open, onClose, onSuccess }: DeleteBankDetailDialogProps) {
  const { mutate: deleteBankDetail, isPending, error, isError } = useDeleteBankDetail()
  const { t, keys } = useTranslations()

  const handleDelete = () => {
    if (!bankDetail) return

    deleteBankDetail({
      params: { path: { id: bankDetail.id } },
    }, {
      onSuccess: () => {
        onClose()
        onSuccess?.()
      },
    })
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="w-[calc(100vw-1rem)] max-w-md mx-2 sm:mx-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            {t(keys.bankDetails.confirmDelete)}
          </DialogTitle>
          <DialogDescription>
            {t(keys.bankDetails.deleteDescription)}
          </DialogDescription>
        </DialogHeader>

        {bankDetail && (
          <div className="py-4">
            <div className="p-3 bg-muted rounded-lg">
              <p className="font-medium">{bankDetail.name}</p>
              {bankDetail.bank_name && (
                <p className="text-sm text-muted-foreground">{bankDetail.bank_name}</p>
              )}
              {bankDetail.iban && (
                <p className="text-xs font-mono text-muted-foreground mt-1">{bankDetail.iban}</p>
              )}
            </div>
          </div>
        )}

        {isError && error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error?.error?.code || t(keys.bankDetails.error)}
            </AlertDescription>
          </Alert>
        )}

        <DialogFooter className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-2">
          <Button variant="outline" onClick={onClose} disabled={isPending} className="min-h-[44px]">
            {t(keys.bankDetails.cancel)}
          </Button>
          <Button variant="destructive" onClick={handleDelete} disabled={isPending} className="min-h-[44px]">
            {isPending ? t(keys.bankDetails.deleting) : t(keys.bankDetails.delete)}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
