import { useEffect, useMemo } from "react"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import * as yup from "yup"
import { AlertTriangle } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useCreateBankDetail, useUpdateBankDetail } from "@/hooks/bank-details"
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useIsMobile } from '@/hooks/use-mobile'
import { cn } from '@/lib/utils'
import type {
  EditBankDetailDialogProps,
  BankDetailFormValues
} from '@/components/bank-details/types';
import { 
  toBankDetailCreateRequest, 
  toBankDetailUpdateRequest, 
  toBankDetailFormValues 
} from '@/components/bank-details/types';

export function EditBankDetailDialog({ bankDetail, open, onClose, onSuccess }: EditBankDetailDialogProps) {
  const isEditing = !!bankDetail
  const { mutate: createBankDetail, isPending: isCreating, error: createError, isError: isCreateError } = useCreateBankDetail()
  const { mutate: updateBankDetail, isPending: isUpdating, error: updateError, isError: isUpdateError } = useUpdateBankDetail()
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()

  const schema: yup.ObjectSchema<BankDetailFormValues> = useMemo(() => yup.object({
    name: yup
      .string()
      .required(t(keys.bankDetails.validation.nameRequired))
      .max(255, t(keys.bankDetails.validation.nameTooLong)),
    bank_name: yup
      .string()
      .max(255, t(keys.bankDetails.validation.bankNameTooLong))
      .optional(),
    iban: yup
      .string()
      .test('iban-length', t(keys.bankDetails.validation.ibanTooShort), function(value) {
        if (!value || value.trim() === '') return true; // Allow empty
        return value.length >= 15;
      })
      .max(34, t(keys.bankDetails.validation.ibanTooLong))
      .optional(),
    bic_swift: yup
      .string()
      .test('bic-length', t(keys.bankDetails.validation.bicSwiftTooShort), function(value) {
        if (!value || value.trim() === '') return true; // Allow empty
        return value.length >= 8;
      })
      .max(11, t(keys.bankDetails.validation.bicSwiftTooLong))
      .optional(),
  }), [t, keys])

  const isPending = isCreating || isUpdating
  const error = createError || updateError
  const isError = isCreateError || isUpdateError

  const form = useForm<BankDetailFormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: "",
      bank_name: "",
      iban: "",
      bic_swift: "",
    }
  })

  // Reset form when dialog opens/closes or bankDetail changes
  useEffect(() => {
    if (open) {
      if (isEditing && bankDetail) {
        // Editing existing bank detail
        const formValues = toBankDetailFormValues(bankDetail)
        form.reset(formValues)
      } else {
        // Creating new bank detail
        form.reset({
          name: "",
          bank_name: "",
          iban: "",
          bic_swift: "",
        })
      }
    }
  }, [open, isEditing, bankDetail, form])

  const onSubmit = (data: BankDetailFormValues) => {
    if (isEditing && bankDetail) {
      // Update existing bank detail
      updateBankDetail({
        params: { path: { id: bankDetail.id } },
        body: toBankDetailUpdateRequest(data),
      }, {
        onSuccess: () => {
          onClose()
          onSuccess?.()
        },
      })
    } else {
      // Create new bank detail
      createBankDetail({
        body: toBankDetailCreateRequest(data),
      }, {
        onSuccess: () => {
          onClose()
          onSuccess?.()
        },
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={cn(
        "w-[calc(100vw-1rem)] max-w-2xl max-h-[calc(100vh-2rem)] md:max-h-[80vh] overflow-y-auto mx-2 md:mx-auto",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 mx-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            isMobile && "text-base"
          )}>
            {isEditing ? (t(keys.bankDetails.editTitle)) : (t(keys.bankDetails.createTitle))}
          </DialogTitle>
        </DialogHeader>

        <div className={cn(isMobile && "px-4")}>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(keys.bankDetails.name)} <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} placeholder="e.g., Main Business Account" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="bank_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(keys.bankDetails.bankName)}
                      </FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} placeholder="e.g., Chase Bank" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="iban"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(keys.bankDetails.iban)}
                      </FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} placeholder="GB29 NWBK 6016 1331 9268 19" className="font-mono" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="bic_swift"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(keys.bankDetails.bicSwift)}
                      </FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} placeholder="NWBKGB2L" className="font-mono" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {isError && error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {error?.error?.code || (t(keys.bankDetails.error))}
                </AlertDescription>
              </Alert>
            )}

              <div className={cn(
                "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-2 pt-4",
                isMobile && "pb-4"
              )}>
                <Button type="button" variant="outline" onClick={onClose} disabled={isPending} className="min-h-[44px]">
                  {t(keys.bankDetails.cancel)}
                </Button>
                <Button type="submit" disabled={isPending} className="min-h-[44px]">
                  {isPending
                    ? (isEditing ? (t(keys.bankDetails.updating)) : (t(keys.bankDetails.creating)))
                    : (t(keys.bankDetails.save))
                  }
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
