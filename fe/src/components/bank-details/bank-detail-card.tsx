import { <PERSON><PERSON>ard, Building2, <PERSON><PERSON>, <PERSON>, MoreHorizontal, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import { Card, CardContent, CardHeader } from "@/components/ui/card.tsx"
import { Button } from "@/components/ui/button.tsx"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu.tsx"
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { BankDetailDisplayData } from '@/components/bank-details/types';

interface BankDetailCardProps {
  bankDetail: BankDetailDisplayData
  onEdit: () => void
  onDelete: () => void
}

export function BankDetailCard({ bankDetail, onEdit, onDelete }: BankDetailCardProps) {
  const { t, keys } = useTranslations();

  return (
    <Card className="w-full hover:shadow-lg transition-shadow duration-200 relative">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <CreditCard className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-lg leading-tight">{bankDetail.name}</h3>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={onEdit}>
                <Pencil className="h-4 w-4 mr-2" />
                {t(keys.bankDetails.edit)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onDelete}
                className="text-destructive focus:text-destructive focus:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {t(keys.bankDetails.delete)}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Bank Information */}
        <div className="space-y-2">
          {/* Additional Bank Information */}
          {bankDetail.bank_name && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Building2 className="h-4 w-4" />
                <span className="font-medium">Bank:</span>
                <span>{bankDetail.bank_name}</span>
            </div>
          )}

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Hash className="h-4 w-4" />
            <span className="font-medium">IBAN:</span>
            {bankDetail.iban ? (
              <span className="font-mono text-xs">{bankDetail.iban}</span>
            ) : (
              <span>—</span>
            )}
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Code className="h-4 w-4" />
            <span className="font-medium">BIC/SWIFT:</span>
            {bankDetail.bic_swift ? (
              <span className="font-mono text-xs">{bankDetail.bic_swift}</span>
            ) : (
              <span>—</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
