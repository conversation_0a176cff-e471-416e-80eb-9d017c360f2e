/**
 * Component-specific TypeScript types for Bank Details feature.
 * 
 * These types are optimized for UI/UX needs and are separate from the OpenAPI-generated
 * types in fe/src/lib/api/v1.ts. This separation provides:
 * - Better type safety for component interfaces
 * - UI-specific type customizations without API coupling
 * - Cleaner component props and state management
 * - Flexibility for frontend-specific data transformations
 */

import type {
  BankDetailsResponse,
  BankDetailsCreateRequest,
  BankDetailsUpdateRequest
} from '@/lib/types/api';

// ============================================================================
// Core Component Types
// ============================================================================

/**
 * Bank detail data optimized for display in UI components.
 * Extends the API response with UI-specific properties and transformations.
 */
export interface BankDetailDisplayData extends BankDetailsResponse {
  // Add any UI-specific computed properties here if needed
  // For example: displayName, formattedIban, etc.
  displayName?: string;
}

/**
 * Alias for the main bank detail type used throughout components.
 * This makes it easy to switch between different data sources if needed.
 */
export type BankDetail = BankDetailDisplayData;

// ============================================================================
// Form Types
// ============================================================================

/**
 * Form values for creating and editing bank details.
 * Based on the API request types but optimized for form handling.
 */
export interface BankDetailFormValues {
  name: string;
  bank_name?: string;
  iban?: string;
  bic_swift?: string;
}

/**
 * Type for form validation errors.
 * Maps form field names to error messages.
 */
export interface BankDetailFormErrors {
  name?: string;
  bank_name?: string;
  iban?: string;
  bic_swift?: string;
}

// ============================================================================
// Component Props Types
// ============================================================================

/**
 * Props for the BankDetailCard component.
 */
export interface BankDetailCardProps {
  bankDetail: BankDetail;
  onEdit: () => void;
  onDelete: () => void;
}

/**
 * Props for the EditBankDetailDialog component.
 */
export interface EditBankDetailDialogProps {
  bankDetail: BankDetail | null; // null when creating new
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

/**
 * Props for the DeleteBankDetailDialog component.
 */
export interface DeleteBankDetailDialogProps {
  bankDetail: BankDetail | null;
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Type for bank detail list operations.
 */
export interface BankDetailListState {
  editingBankDetail: BankDetail | null;
  deletingBankDetail: BankDetail | null;
  isCreating: boolean;
}

/**
 * Type for bank detail actions.
 */
export interface BankDetailActions {
  handleCreateNew: () => void;
  handleEdit: (bankDetail: BankDetail) => void;
  handleDelete: (bankDetail: BankDetail) => void;
  handleCloseDialog: () => void;
  handleSuccess: () => void;
}

// ============================================================================
// Type Converters
// ============================================================================

/**
 * Converts API response to display data.
 * Add any transformations needed for UI display here.
 */
export function toBankDetailDisplayData(response: BankDetailsResponse): BankDetailDisplayData {
  return {
    ...response,
    // Add any UI-specific transformations here
  };
}

/**
 * Converts form values to create request.
 */
export function toBankDetailCreateRequest(formValues: BankDetailFormValues): BankDetailsCreateRequest {
  return {
    name: formValues.name,
    bank_name: formValues.bank_name?.trim() || undefined,
    iban: formValues.iban?.trim() || undefined,
    bic_swift: formValues.bic_swift?.trim() || undefined,
  };
}

/**
 * Converts form values to update request.
 */
export function toBankDetailUpdateRequest(formValues: BankDetailFormValues): BankDetailsUpdateRequest {
  return {
    name: formValues.name || undefined,
    bank_name: formValues.bank_name?.trim() || undefined,
    iban: formValues.iban?.trim() || undefined,
    bic_swift: formValues.bic_swift?.trim() || undefined,
  };
}

/**
 * Converts bank detail to form values for editing.
 */
export function toBankDetailFormValues(bankDetail: BankDetail): BankDetailFormValues {
  return {
    name: bankDetail.name || '',
    bank_name: bankDetail.bank_name || '',
    iban: bankDetail.iban || '',
    bic_swift: bankDetail.bic_swift || '',
  };
}
