import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { generateNotificationDeepLink } from '@/lib/notification-navigation';
import type { components } from '@/lib/api/v1';
import { NotificationResponseType, NotificationResponseStatus } from '@/lib/api/v1';

type NotificationResponse = components['schemas']['NotificationResponse'];

/**
 * Test component to demonstrate deep linking functionality.
 * This component shows how different notification types generate different deep links.
 */
export function DeepLinkTest() {
  // Sample notifications for testing
  const sampleNotifications: NotificationResponse[] = [
    {
      id: 1,
      userId: 1,
      type: NotificationResponseType.CHAT_MENTION,
      title: 'You were mentioned in chat',
      message: '<PERSON> mentioned you in the general chat',
      status: NotificationResponseStatus.UNREAD,
      collaborationHubId: 123,
      chatChannelId: 456,
      createdAt: new Date().toISOString(),
    },
    {
      id: 2,
      userId: 1,
      type: NotificationResponseType.COMMENT_ADDED,
      title: 'New comment on your post',
      message: '<PERSON> commented on your latest post',
      status: NotificationResponseStatus.UNREAD,
      collaborationHubId: 123,
      postId: 789,
      commentId: 101,
      createdAt: new Date().toISOString(),
    },
    {
      id: 3,
      userId: 1,
      type: NotificationResponseType.BRIEF_CREATED,
      title: 'New brief created',
      message: 'A new brief has been created for the campaign',
      status: NotificationResponseStatus.UNREAD,
      collaborationHubId: 123,
      briefId: 202,
      createdAt: new Date().toISOString(),
    },
    {
      id: 4,
      userId: 1,
      type: NotificationResponseType.POST_REVIEWED,
      title: 'Post reviewed',
      message: 'Your post has been reviewed and approved',
      status: NotificationResponseStatus.UNREAD,
      collaborationHubId: 123,
      postId: 789,
      createdAt: new Date().toISOString(),
    },
  ];

  const handleTestDeepLink = (notification: NotificationResponse) => {
    const deepLink = generateNotificationDeepLink(notification);

    // In a real scenario, this would navigate to the URL
    // For testing, we'll just show an alert
    alert(`Deep link generated: ${deepLink}`);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Deep Link Test</CardTitle>
        <CardDescription>
          Test the deep linking functionality for different notification types.
          Click the buttons to see the generated deep links.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {sampleNotifications.map((notification) => (
          <div key={notification.id} className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex-1">
              <p className="font-medium">{notification.title}</p>
              <p className="text-sm text-muted-foreground">{notification.message}</p>
              <p className="text-xs text-muted-foreground mt-1">
                Type: <code>{notification.type}</code>
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleTestDeepLink(notification)}
            >
              Test Deep Link
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
