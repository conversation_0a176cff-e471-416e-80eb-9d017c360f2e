import { Loader2 } from 'lucide-react';
import { useTranslations } from '@/lib/i18n/typed-translations';

interface LoadingScreenProps {
  message?: string;
  className?: string;
}

/**
 * Loading screen component for authentication initialization and other loading states.
 * Shows a centered spinner with an optional message.
 *
 * Demonstrates both traditional and typed translation approaches.
 */
export function LoadingScreen({
  message,
  className = ''
}: LoadingScreenProps) {
  const { t, keys } = useTranslations();

  const defaultMessage = message || t(keys.common.loading);
  return (
    <div className={`flex min-h-svh w-full items-center justify-center p-6 ${className}`}>
      <div className="flex flex-col items-center gap-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">{defaultMessage}</p>
      </div>
    </div>
  );
}

export default LoadingScreen;
