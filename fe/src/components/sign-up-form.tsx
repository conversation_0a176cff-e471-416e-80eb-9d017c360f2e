import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useRegister } from "@/hooks/auth";
import { useNavigate } from 'react-router';
import { useState, useMemo } from 'react';
import { ROUTES } from '@/router/routes';
import { useTranslations } from '@/lib/i18n/typed-translations';

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";

export function SignUpForm() {
  const navigate = useNavigate();
  const { mutate, isPending, error, isError, isSuccess, data } = useRegister();
  const [showSuccess, setShowSuccess] = useState(false);
  const { t, keys } = useTranslations();

  // Validation schema matching backend requirements
  const schema = useMemo(() => yup.object({
    accountName: yup
      .string()
      .required(t(keys.validation.required))
      .min(2, t(keys.validation.accountName.tooShort))
      .max(100, t(keys.validation.accountName.tooLong)),
    email: yup
      .string()
      .required(t(keys.validation.required))
      .email(t(keys.validation.email.invalid))
      .max(255, t(keys.validation.email.tooLong)),
    password: yup
      .string()
      .required(t(keys.validation.required))
      .min(8, t(keys.validation.password.tooShort))
      .max(255, t(keys.validation.password.tooLong))
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,
        t(keys.validation.password.requirements)
      ),
    displayName: yup
      .string()
      .required(t(keys.validation.displayName.required))
      .min(1, t(keys.validation.displayName.required))
      .max(255, t(keys.validation.displayName.tooLong)),
  }), [t, keys]);

  type SignUpFormValues = yup.InferType<typeof schema>;

  const form = useForm<SignUpFormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      accountName: "",
      email: "",
      password: "",
      displayName: "",
    },
  });

  const onSubmit = (formData: SignUpFormValues) => {
    mutate({
      body: {
        account_name: formData.accountName,
        email: formData.email,
        password: formData.password,
        display_name: formData.displayName,
      }
    }, {
      onSuccess: () => {
        setShowSuccess(true);
        form.reset();
      },
    });
  };

  // Show success message if registration was successful
  if (showSuccess && isSuccess && data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            {t(keys.auth.register.success.title)}
          </CardTitle>
          <CardDescription>
            {t(keys.auth.register.success.description)}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              {data.message || t(keys.auth.register.success.checkEmail)}
            </AlertDescription>
          </Alert>

          <div className="text-sm text-muted-foreground">
            <p>{t(keys.messages.emailSentTo)}</p>
            <p className="font-medium">{data.email}</p>
          </div>

          <div className="space-y-2">
            <Button
              onClick={() => navigate(ROUTES.LOGIN)}
              className="w-full"
            >
              {t(keys.auth.register.success.goToLogin)}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t(keys.auth.register.title)}</CardTitle>
        <CardDescription>
          {t(keys.auth.register.description)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="accountName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t(keys.auth.register.accountName)}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t(keys.placeholders.accountName)}
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="displayName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t(keys.auth.register.displayName)}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t(keys.placeholders.displayName)}
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t(keys.auth.register.email)}</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={t(keys.placeholders.email)}
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t(keys.auth.register.password)}</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={t(keys.placeholders.password)}
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                  <div className="text-xs text-muted-foreground">
                    {t(keys.auth.register.passwordHint)}
                  </div>
                </FormItem>
              )}
            />

            {isError && error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {t(keys.auth.register.error)}
                </AlertDescription>
              </Alert>
            )}

            <Button type="submit" className="w-full" disabled={isPending}>
              {isPending ? t(keys.auth.register.submitting) : t(keys.auth.register.submit)}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
