import { useSearchParams } from "react-router-dom"
import { FileText, MessageCircle, BookOpen, BarChart3 } from "lucide-react"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { DeepLinkUtils } from "@/lib/notification-navigation"
import { PostsTab } from "./tabs/posts-tab"
import { ChatTab } from "./tabs/chat-tab"
import { BriefsTab } from "./tabs/briefs-tab"
import { OverviewTab } from "./tabs/overview-tab"

interface HubTabsProps {
  hubId: number
}

export function HubTabs({ hubId }: HubTabsProps) {
  const [searchParams, setSearchParams] = useSearchParams()
  const { t, keys } = useTranslations()

  // Get active tab from URL params, default to "posts"
  const activeTab = DeepLinkUtils.getActiveTab(searchParams)

  // Handle tab change by updating URL params
  const handleTabChange = (newTab: string) => {
    const newParams = DeepLinkUtils.updateSearchParams(searchParams, {
      tab: newTab === "posts" ? null : newTab, // Remove tab param if it's the default
    })
    setSearchParams(newParams)
  }

  // Mock notification counts - in real implementation these would come from API
  const notificationCounts = {
    posts: 3,
    chat: 12,
    briefs: 0,
    overview: 0
  }

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="flex-1 flex flex-col">
      <TabsList className="grid w-full grid-cols-4 mb-6">
        <TabsTrigger value="posts" className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          <span className="hidden sm:inline">{t(keys.collaborationHubs.tabs.posts)}</span>
          {notificationCounts.posts > 0 && (
            <Badge variant="destructive" className="h-5 w-5 p-0 text-xs flex items-center justify-center">
              {notificationCounts.posts}
            </Badge>
          )}
        </TabsTrigger>

        <TabsTrigger value="chat" className="flex items-center gap-2">
          <MessageCircle className="h-4 w-4" />
          <span className="hidden sm:inline">{t(keys.collaborationHubs.tabs.chat)}</span>
          {notificationCounts.chat > 0 && (
            <Badge variant="destructive" className="h-5 w-5 p-0 text-xs flex items-center justify-center">
              {notificationCounts.chat}
            </Badge>
          )}
        </TabsTrigger>

        <TabsTrigger value="briefs" className="flex items-center gap-2">
          <BookOpen className="h-4 w-4" />
          <span className="hidden sm:inline">{t(keys.collaborationHubs.tabs.briefs)}</span>
          {notificationCounts.briefs > 0 && (
            <Badge variant="destructive" className="h-5 w-5 p-0 text-xs flex items-center justify-center">
              {notificationCounts.briefs}
            </Badge>
          )}
        </TabsTrigger>

        <TabsTrigger value="overview" className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          <span className="hidden sm:inline">{t(keys.collaborationHubs.tabs.overview)}</span>
          {notificationCounts.overview > 0 && (
            <Badge variant="destructive" className="h-5 w-5 p-0 text-xs flex items-center justify-center">
              {notificationCounts.overview}
            </Badge>
          )}
        </TabsTrigger>
      </TabsList>

      <TabsContent value="posts" className="flex-1 mt-0">
        <PostsTab hubId={hubId} />
      </TabsContent>
      
      <TabsContent value="chat" className="flex-1 mt-0">
        <ChatTab hubId={hubId} />
      </TabsContent>
      
      <TabsContent value="briefs" className="flex-1 mt-0">
        <BriefsTab hubId={hubId} />
      </TabsContent>
      
      <TabsContent value="overview" className="flex-1 mt-0">
        <OverviewTab hubId={hubId} />
      </TabsContent>
    </Tabs>
  )
}
