import { AlertTriangle } from "lucide-react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useDeleteCollaborationHub } from "@/hooks/collaboration-hubs"
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useIsMobile } from '@/hooks/use-mobile'
import { cn } from '@/lib/utils'
import type { CollaborationHubListItemDto } from '@/lib/types/api'

interface DeleteCollaborationHubDialogProps {
  hub: CollaborationHubListItemDto | null
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export function DeleteCollaborationHubDialog({ 
  hub, 
  open, 
  onClose, 
  onSuccess 
}: DeleteCollaborationHubDialogProps) {
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()
  const deleteHub = useDeleteCollaborationHub()

  const handleDelete = async () => {
    if (!hub) return

    try {
      await deleteHub.mutateAsync({
        params: { path: { id: hub.id } }
      })
      
      onSuccess()
      onClose()
    } catch (__error) {
      // Error handling is managed by the mutation hook and will show appropriate error messages
    }
  }

  if (!hub) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={cn(
        "w-[calc(100vw-1rem)] max-w-[425px] mx-2 sm:mx-auto",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 mx-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-destructive/10 rounded-lg">
              <AlertTriangle className={cn("h-5 w-5 text-destructive", isMobile && "h-4 w-4")} />
            </div>
            <div>
              <DialogTitle className={cn(
                isMobile && "text-base"
              )}>{t(keys.collaborationHubs.confirmDelete)}</DialogTitle>
              <DialogDescription className={cn(
                "mt-1",
                isMobile && "text-xs"
              )}>
                {t(keys.collaborationHubs.deleteDescription)}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className={cn(
          "py-4",
          isMobile && "px-4"
        )}>
          <div className="bg-muted/50 rounded-lg p-4 space-y-2">
            <div className="font-medium text-sm">
              {t(keys.collaborationHubs.hubToDelete)}
            </div>
            <div className="space-y-1">
              <div className="font-semibold">{hub.name}</div>
              <div className="text-sm text-muted-foreground">
                {hub.brandName}
              </div>
              <div className="text-xs text-muted-foreground">
                {t(keys.collaborationHubs.created)}: {new Date(hub.createdAt).toLocaleDateString()}
              </div>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-destructive/5 border border-destructive/20 rounded-lg">
            <div className="text-sm text-destructive font-medium">
              {t(keys.collaborationHubs.deleteWarning)}
            </div>
            <div className="text-xs text-destructive/80 mt-1">
              {t(keys.collaborationHubs.deleteWarningDetails)}
            </div>
          </div>
        </div>

        <DialogFooter className={cn(
          "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-2",
          isMobile && "pt-2 pb-4 px-4"
        )}>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={deleteHub.isPending}
            className="min-h-[44px]"
          >
            {t(keys.collaborationHubs.cancel)}
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteHub.isPending}
            className="min-h-[44px]"
          >
            {deleteHub.isPending
              ? t(keys.collaborationHubs.deleting)
              : t(keys.collaborationHubs.delete)
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
