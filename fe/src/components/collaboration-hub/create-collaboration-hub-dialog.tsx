import { useState } from "react"
import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useCreateCollaborationHub } from "@/hooks/collaboration-hubs"
import { useBrands } from "@/hooks/brands"
import { useTranslations, type UseTranslationsReturn } from "@/lib/i18n/typed-translations"
import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

const createHubSchema = (t: UseTranslationsReturn['t'], keys: UseTranslationsReturn['keys']) => z.object({
  name: z.string()
    .min(1, t(keys.collaborationHubs.createDialog.validation.hubNameRequired))
    .max(255, t(keys.collaborationHubs.createDialog.validation.hubNameTooLong)),
  brandId: z.number({ required_error: t(keys.collaborationHubs.createDialog.validation.brandRequired) }),
  description: z.string()
    .max(1000, t(keys.collaborationHubs.createDialog.validation.descriptionTooLong))
    .optional(),
})

type CreateHubFormData = {
  name: string
  brandId: number
  description?: string
}

interface CreateCollaborationHubDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CreateCollaborationHubDialog({ open, onOpenChange }: CreateCollaborationHubDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()

  const form = useForm<CreateHubFormData>({
    resolver: zodResolver(createHubSchema(t, keys)),
    defaultValues: {
      name: "",
      description: "",
    },
  })

  const createMutation = useCreateCollaborationHub()
  const { data: brandsResponse } = useBrands()

  const onSubmit = async (data: CreateHubFormData) => {
    setIsSubmitting(true)
    try {
      await createMutation.mutateAsync({
        body: {
          name: data.name,
          brandId: data.brandId,
          description: data.description || undefined,
        },
      })

      toast.success(t(keys.collaborationHubs.createDialog.successMessage))
      form.reset()
      onOpenChange(false)
    } catch (__error) {
      toast.error(t(keys.collaborationHubs.createDialog.errorMessage))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !isSubmitting) {
      form.reset()
    }
    onOpenChange(newOpen)
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className={cn(
        "sm:max-w-[500px]",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            isMobile && "text-base"
          )}>{t(keys.collaborationHubs.createDialog.title)}</DialogTitle>
          <DialogDescription className={cn(
            isMobile && "text-xs"
          )}>
            {t(keys.collaborationHubs.createDialog.description)}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className={cn(
          "max-h-[60vh]",
          isMobile && "h-[calc(100dvh-160px)] px-4"
        )}>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className={cn(
              "space-y-4 px-1",
              isMobile && "px-0"
            )}>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.collaborationHubs.createDialog.hubNameLabel)}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(keys.collaborationHubs.createDialog.hubNamePlaceholder)}
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="brandId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.collaborationHubs.createDialog.brandLabel)}</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t(keys.collaborationHubs.createDialog.brandPlaceholder)} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {brandsResponse?.content?.map((brand) => (
                          <SelectItem key={brand.id} value={brand.id.toString()}>
                            {brand.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.collaborationHubs.createDialog.descriptionLabel)}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t(keys.collaborationHubs.createDialog.descriptionPlaceholder)}
                        className="resize-none w-full"
                        rows={3}
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </ScrollArea>

        <DialogFooter className={cn(
          isMobile && "pt-2 pb-4 px-4"
        )}>
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={isSubmitting}
          >
            {t(keys.collaborationHubs.createDialog.cancel)}
          </Button>
          <Button
            type="submit"
            onClick={form.handleSubmit(onSubmit)}
            disabled={isSubmitting}
          >
            {isSubmitting ? t(keys.collaborationHubs.createDialog.creating) : t(keys.collaborationHubs.createDialog.createHub)}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
