import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, FileText, MessageCircle, Clock, CheckCircle, AlertCircle } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import { useTranslations } from "@/lib/i18n/typed-translations"

interface OverviewTabProps {
  hubId: number
}

export function OverviewTab({ hubId: _hubId }: OverviewTabProps) {
  const { t, keys } = useTranslations()

  // Mock activity data
  const recentActivity = [
    {
      id: 1,
      type: "post_created",
      user: { name: "<PERSON>", avatar: null },
      action: "created a new post",
      target: "Summer Campaign Launch Post",
      timestamp: "2024-01-15T10:30:00Z"
    },
    {
      id: 2,
      type: "post_approved",
      user: { name: "<PERSON>", avatar: null },
      action: "approved",
      target: "Product Showcase Video",
      timestamp: "2024-01-15T09:45:00Z"
    },
    {
      id: 3,
      type: "comment_added",
      user: { name: "Lisa Park", avatar: null },
      action: "commented on",
      target: "User Generated Content Feature",
      timestamp: "2024-01-15T09:15:00Z"
    },
    {
      id: 4,
      type: "participant_joined",
      user: { name: "Alex Rivera", avatar: null },
      action: "joined the hub",
      target: null,
      timestamp: "2024-01-14T16:20:00Z"
    },
    {
      id: 5,
      type: "brief_updated",
      user: { name: "Mike Chen", avatar: null },
      action: "updated",
      target: "Brand Guidelines Brief",
      timestamp: "2024-01-14T14:30:00Z"
    }
  ]

  // Mock stats data
  const stats = {
    totalPosts: 12,
    pendingReviews: 3,
    approvedPosts: 8,
    rejectedPosts: 1,
    totalParticipants: 8,
    activeParticipants: 6,
    totalMessages: 47,
    totalBriefs: 4
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "post_created": return <FileText className="h-4 w-4 text-blue-500" />
      case "post_approved": return <CheckCircle className="h-4 w-4 text-green-500" />
      case "comment_added": return <MessageCircle className="h-4 w-4 text-purple-500" />
      case "participant_joined": return <Users className="h-4 w-4 text-orange-500" />
      case "brief_updated": return <FileText className="h-4 w-4 text-indigo-500" />
      default: return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return t(keys.collaborationHubs.overview.justNow)
    if (diffInHours < 24) return t(keys.collaborationHubs.overview.hoursAgo, { hours: diffInHours })
    return t(keys.collaborationHubs.overview.daysAgo, { days: Math.floor(diffInHours / 24) })
  }

  const approvalRate = Math.round((stats.approvedPosts / (stats.approvedPosts + stats.rejectedPosts)) * 100)
  const participationRate = Math.round((stats.activeParticipants / stats.totalParticipants) * 100)

  return (
    <ScrollArea className="h-full">
      <div className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.totalPosts}</p>
                  <p className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.totalPosts)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                  <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.pendingReviews}</p>
                  <p className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.pendingReviews)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.approvedPosts}</p>
                  <p className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.approved)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                  <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.totalParticipants}</p>
                  <p className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.participants)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {t(keys.collaborationHubs.overview.approvalRate)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.contentApproval)}</span>
                <span className="text-sm font-medium">{approvalRate}%</span>
              </div>
              <Progress value={approvalRate} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {t(keys.collaborationHubs.overview.approvedOutOf, {
                  approved: stats.approvedPosts,
                  total: stats.approvedPosts + stats.rejectedPosts
                })}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t(keys.collaborationHubs.overview.participationRate)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.activeParticipants)}</span>
                <span className="text-sm font-medium">{participationRate}%</span>
              </div>
              <Progress value={participationRate} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {t(keys.collaborationHubs.overview.activeOutOf, {
                  active: stats.activeParticipants,
                  total: stats.totalParticipants
                })}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {t(keys.collaborationHubs.overview.recentActivity)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3">
                  <div className="p-1.5 bg-muted rounded-lg">
                    {getActivityIcon(activity.type)}
                  </div>
                  
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={activity.user.avatar || undefined} />
                        <AvatarFallback className="text-xs">
                          {getInitials(activity.user.name)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">{activity.user.name}</span>
                      <span className="text-sm text-muted-foreground">
                        {activity.type === 'post_created' ? t(keys.collaborationHubs.overview.activities.created) :
                         activity.type === 'post_approved' ? t(keys.collaborationHubs.overview.activities.approved) :
                         activity.type === 'comment_added' ? t(keys.collaborationHubs.overview.activities.commentedOn) :
                         activity.type === 'participant_joined' ? t(keys.collaborationHubs.overview.activities.joinedHub) :
                         activity.type === 'brief_updated' ? t(keys.collaborationHubs.overview.activities.updated) :
                         activity.action}
                      </span>
                      {activity.target && (
                        <span className="text-sm font-medium">{activity.target}</span>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(activity.timestamp)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <MessageCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-2xl font-bold">{stats.totalMessages}</p>
              <p className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.messagesSent)}</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-2xl font-bold">{stats.totalBriefs}</p>
              <p className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.briefsCreated)}</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-2xl font-bold">{stats.rejectedPosts}</p>
              <p className="text-sm text-muted-foreground">{t(keys.collaborationHubs.overview.needsRework)}</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </ScrollArea>
  )
}
