import { useState, useEffect } from "react"
import { useSearchParams } from "react-router-dom"
import { BookOpen, Plus, Search, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { useBriefs, useDeleteBrief } from "@/hooks/collaboration-hub-briefs"
import { BriefFormDialog } from "@/components/collaboration-hub/brief-form-dialog"
import { BriefViewDialog } from "@/components/collaboration-hub/brief-view-dialog"
import { BriefCard } from "@/components/collaboration-hub/brief-card"
import { useDebounce } from "@/hooks/use-debounce"
import { DeepLinkUtils } from "@/lib/notification-navigation"
import { toast } from "sonner"


interface BriefsTabProps {
  hubId: number
}

export function BriefsTab({ hubId }: BriefsTabProps) {
  const [searchParams, setSearchParams] = useSearchParams()
  const [searchQuery, setSearchQuery] = useState("")
  const [page, setPage] = useState(0)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [editingBriefId, setEditingBriefId] = useState<number | null>(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [viewingBriefId, setViewingBriefId] = useState<number | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deletingBriefId, setDeletingBriefId] = useState<number | null>(null)

  const { t, keys } = useTranslations()

  // Debounce search query to avoid too many API calls
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Fetch briefs with real API
  const { data: briefsResponse, isLoading, error } = useBriefs(
    hubId,
    debouncedSearchQuery || undefined,
    page,
    20
  )

  // Delete mutation
  const deleteBriefMutation = useDeleteBrief()

  // Reset page when search query changes
  useEffect(() => {
    setPage(0)
  }, [debouncedSearchQuery])

  const briefs = briefsResponse?.content || []

  // Handle deep linking to specific briefs
  useEffect(() => {
    const briefId = DeepLinkUtils.getBriefToFocus(searchParams)

    if (briefId && briefs.length > 0) {
      // Check if the specified brief exists in the current page
      const targetBrief = briefs.find(brief => brief.id === briefId)
      if (targetBrief) {
        setViewingBriefId(briefId)
        setViewDialogOpen(true)

        // Clear the brief parameter from URL after opening
        const newParams = DeepLinkUtils.updateSearchParams(searchParams, {
          brief: null,
        })
        setSearchParams(newParams, { replace: true })
      }
    }
  }, [briefs, searchParams, setSearchParams])
  const totalPages = briefsResponse?.total_pages || 0
  const hasNextPage = briefsResponse?.has_next || false
  const hasPreviousPage = briefsResponse?.has_previous || false
  // Handle view brief
  const handleViewBrief = (briefId: number) => {
    setViewingBriefId(briefId)
    setViewDialogOpen(true)
  }

  // Handle edit brief
  const handleEditBrief = (briefId: number) => {
    setEditingBriefId(briefId)
    setEditDialogOpen(true)
  }

  // Handle delete brief
  const handleDeleteBrief = (briefId: number) => {
    setDeletingBriefId(briefId)
    setDeleteDialogOpen(true)
  }

  // Confirm delete brief
  const confirmDeleteBrief = async () => {
    if (!deletingBriefId) return

    try {
      await deleteBriefMutation.mutateAsync({
        params: {
          path: { hubId, briefId: deletingBriefId }
        }
      })

      toast.success(t(keys.collaborationHubs.briefs.actions.deleteSuccess))
      setDeleteDialogOpen(false)
      setDeletingBriefId(null)
    } catch (__error) {
      // Error handling for brief deletion
    }
  }



  return (
    <div className="flex flex-col h-full">
      {/* Header with search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t(keys.collaborationHubs.briefs.searchPlaceholder)}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {t(keys.collaborationHubs.briefs.createBrief)}
        </Button>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading briefs...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <p className="text-muted-foreground">Failed to load briefs. Please try again.</p>
          </div>
        </div>
      )}

      {/* Briefs Grid */}
      {!isLoading && !error && (
        <ScrollArea className="flex-1">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {briefs.map((brief) => (
              <BriefCard
                key={brief.id}
                brief={brief}
                onView={handleViewBrief}
                onEdit={handleEditBrief}
                onDelete={handleDeleteBrief}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={!hasPreviousPage}
              >
                Previous
              </Button>

              <span className="text-sm text-muted-foreground">
                Page {page + 1} of {totalPages}
              </span>

              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={!hasNextPage}
              >
                Next
              </Button>
            </div>
          )}
        </ScrollArea>
      )}

      {/* Empty state */}
      {!isLoading && !error && briefs.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">{t(keys.collaborationHubs.briefs.noBriefs)}</h3>
          <p className="text-muted-foreground mb-4 max-w-md">
            {t(keys.collaborationHubs.briefs.noBriefsDescription)}
          </p>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            {t(keys.collaborationHubs.briefs.createFirstBrief)}
          </Button>
        </div>
      )}

      {/* Create Brief Dialog */}
      <BriefFormDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        hubId={hubId}
        onSuccess={() => {
          // Refresh will happen automatically via React Query invalidation
        }}
      />

      {/* View Brief Dialog */}
      <BriefViewDialog
        open={viewDialogOpen}
        onOpenChange={(open) => {
          setViewDialogOpen(open)
          if (!open) {
            setViewingBriefId(null)
          }
        }}
        hubId={hubId}
        briefId={viewingBriefId}
      />

      {/* Edit Brief Dialog */}
      <BriefFormDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        hubId={hubId}
        briefId={editingBriefId}
        onSuccess={() => {
          setEditingBriefId(null)
          // Refresh will happen automatically via React Query invalidation
        }}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t(keys.collaborationHubs.briefs.actions.confirmDelete)}</AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.briefs.actions.confirmDeleteDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeletingBriefId(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteBrief}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={deleteBriefMutation.isPending}
            >
              {deleteBriefMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
