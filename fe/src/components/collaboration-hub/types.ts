/**
 * Re-exports of OpenAPI-generated types for Collaboration Hub components.
 *
 * Components should use these OpenAPI types directly for consistency with the backend API.
 * For component-specific form types, define them locally using zod schemas for better validation.
 * For complex UI state, define types within the component files where they're used.
 */

// Re-export OpenAPI generated types from decoupled API types
export type {
  CollaborationHubListItemDto,
  CollaborationHubResponse,
  CollaborationHubCreateRequest,
  CollaborationHubUpdateRequest,
  PageResponseCollaborationHubListItemDto,
  HubParticipantResponse,
  HubParticipantInviteRequest,
  HubParticipantInviteResponse,
  HubParticipantUpdateRoleRequest,
  ParticipantInviteItem,
  InvitedParticipant,
  HubParticipantListResponse,
  // HubStatsDto, // Not available in current API
  // Brand types for participant invitations
  BrandContactResponse,
  // Re-export enums
  CollaborationHubResponseMyRole,
  CollaborationHubListItemDtoMyRole,
  HubParticipantResponseRole,
  HubParticipantUpdateRoleRequestRole,
  ParticipantInviteItemRole,
  ParticipantFiltersAvailableRoles
} from '@/lib/types/api';
