import { useState, useCallback, useRef, useEffect } from 'react';
import { AlertCircle, RefreshCw, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useMediaUrlRefresh } from '@/hooks/media/use-media-url-refresh';

interface MediaItem {
  url: string;
  type: 'image' | 'video';
  alt?: string;
}

interface MediaCarouselProps {
  media: MediaItem[];
  hubId: number;
  postId?: number;
  className?: string;
  enableRetry?: boolean;
  onImageClick?: () => void;
}

/**
 * Production-grade media carousel component with optional error recovery.
 * Handles presigned URL expiration gracefully when retry is enabled.
 */
export function MediaCarousel({
  media,
  hubId,
  postId,
  className = '',
  enableRetry = false,
  onImageClick
}: MediaCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [errors, setErrors] = useState<Map<number, string>>(new Map());
  const [retrying, setRetrying] = useState<Map<number, boolean>>(new Map());
  const [retryCounts, setRetryCounts] = useState<Map<number, number>>(new Map());
  const { handleMediaError, verifyAndRefreshUrl } = useMediaUrlRefresh();
  const retryTimeouts = useRef<NodeJS.Timeout[]>([]);
  const maxRetries = 3;

  // Cleanup timeouts on unmount
  useEffect(() => {
    const timeouts = retryTimeouts.current;
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  const handleError = useCallback(async (
    event: React.SyntheticEvent<HTMLImageElement | HTMLVideoElement>,
    index: number
  ) => {
    if (!enableRetry) return;

    const currentRetryCount = retryCounts.get(index) || 0;
    
    // Handle the error through our media refresh system
    await handleMediaError(event.nativeEvent, hubId, postId);
    
    if (currentRetryCount < maxRetries) {
      setRetrying(prev => new Map(prev).set(index, true));
      
      // Exponential backoff: 1s, 2s, 4s
      const delay = Math.pow(2, currentRetryCount) * 1000;
      
      const timeout = setTimeout(async () => {
        try {
          // Verify URL is accessible before retrying
          const isAccessible = await verifyAndRefreshUrl(media[index].url, hubId, postId);
          
          if (isAccessible || currentRetryCount === maxRetries - 1) {
            // Clear error and increment retry count
            setErrors(prev => {
              const newErrors = new Map(prev);
              newErrors.delete(index);
              return newErrors;
            });
            setRetryCounts(prev => new Map(prev).set(index, currentRetryCount + 1));
          } else {
            setRetryCounts(prev => new Map(prev).set(index, currentRetryCount + 1));
          }
        } catch (__error) {
          // Error handling for media retry
          setRetryCounts(prev => new Map(prev).set(index, currentRetryCount + 1));
        } finally {
          setRetrying(prev => {
            const newRetrying = new Map(prev);
            newRetrying.delete(index);
            return newRetrying;
          });
        }
      }, delay);
      
      retryTimeouts.current.push(timeout);
    } else {
      setErrors(prev => new Map(prev).set(index, 'Failed to load media. The URL may have expired.'));
    }
  }, [enableRetry, retryCounts, handleMediaError, verifyAndRefreshUrl, hubId, postId, media]);

  const handleManualRetry = useCallback((index: number) => {
    if (!enableRetry) return;

    setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(index);
      return newErrors;
    });
    setRetryCounts(prev => new Map(prev).set(index, 0));
    setRetrying(prev => new Map(prev).set(index, true));
    
    // Force immediate refresh of post data
    handleMediaError(new Event('error'), hubId, postId).then(() => {
      setRetrying(prev => {
        const newRetrying = new Map(prev);
        newRetrying.delete(index);
        return newRetrying;
      });
    });
  }, [enableRetry, handleMediaError, hubId, postId]);

  const handleLoadSuccess = useCallback((index: number) => {
    setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(index);
      return newErrors;
    });
    setRetryCounts(prev => {
      const newRetryCounts = new Map(prev);
      newRetryCounts.delete(index);
      return newRetryCounts;
    });
    setRetrying(prev => {
      const newRetrying = new Map(prev);
      newRetrying.delete(index);
      return newRetrying;
    });
  }, []);

  const nextSlide = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % media.length);
  }, [media.length]);

  const prevSlide = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + media.length) % media.length);
  }, [media.length]);

  if (!media || media.length === 0) {
    return null;
  }

  const renderMediaItem = (item: MediaItem, index: number) => {
    const error = errors.get(index);
    const isRetrying = retrying.get(index);

    if (error && !isRetrying && enableRetry) {
      return (
        <div className="flex flex-col items-center justify-center p-8 bg-muted rounded-lg h-full">
          <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-sm text-muted-foreground text-center mb-4">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleManualRetry(index)}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
      );
    }

    if (isRetrying && enableRetry) {
      const retryCount = retryCounts.get(index) || 0;
      return (
        <div className="flex flex-col items-center justify-center p-8 bg-muted rounded-lg h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
          <p className="text-sm text-muted-foreground">
            Refreshing media... (Attempt {retryCount + 1}/{maxRetries})
          </p>
        </div>
      );
    }

    if (item.type === 'video') {
      return (
        <video
          src={item.url}
          className="w-full h-full object-cover rounded-lg"
          controls
          onError={(e) => handleError(e, index)}
          onLoadedData={() => handleLoadSuccess(index)}
          onClick={onImageClick}
          preload="metadata"
        >
          Your browser does not support the video tag.
        </video>
      );
    }

    return (
      <img
        src={item.url}
        alt={item.alt}
        className="w-full h-full object-cover rounded-lg cursor-pointer"
        onError={(e) => handleError(e, index)}
        onLoad={() => handleLoadSuccess(index)}
        onClick={onImageClick}
        loading="lazy"
      />
    );
  };

  // Single media item
  if (media.length === 1) {
    return (
      <div className={`relative ${className}`}>
        {renderMediaItem(media[0], 0)}
      </div>
    );
  }

  // Multiple media items - carousel
  return (
    <div className={`relative ${className}`}>
      {/* Main media display */}
      <div className="relative overflow-hidden rounded-lg">
        <div 
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {media.map((item, index) => (
            <div key={index} className="w-full flex-shrink-0">
              <div className="aspect-square">
                {renderMediaItem(item, index)}
              </div>
            </div>
          ))}
        </div>

        {/* Navigation arrows */}
        {media.length > 1 && (
          <>
            <Button
              variant="outline"
              size="icon"
              className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white"
              onClick={prevSlide}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white"
              onClick={nextSlide}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </>
        )}

        {/* Dots indicator */}
        {media.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {media.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-white' : 'bg-white/50'
                }`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Media counter */}
      {media.length > 1 && (
        <div className="absolute top-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
          {currentIndex + 1} / {media.length}
        </div>
      )}
    </div>
  );
}
