import { useEffect, useMemo, useRef } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { FileText, Loader2, AlertCircle, Lock } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { InvoiceRecipientRequestType, InvoiceRecipientRequestSource, InvoiceResponseStatus } from '@/lib/types/api';
import {
  useCreateInvoice,
  useUpdateInvoice,
  useInvoice
} from '@/hooks/invoices';
import type { InvoiceFormDialogProps } from './types';
import {
  convertInvoiceToFormValues,
  convertToCreateRequest,
  convertToUpdateRequest,
  createDefaultFormValues,
  type InvoiceFormValues,
} from './converters';

// Import optimized form sections
import {
  BasicInfoSection,
  ClientInfoSection,
  LineItemsSection,
  RecipientsSection,
  TotalsSection,
  AdditionalDetailsSection,
} from './form-sections';

/**
 * Comprehensive invoice form dialog for creating and editing invoices.
 *
 * Features:
 * - If invoiceId is provided, fetches and edits that invoice
 * - Otherwise creates a new invoice with default values
 * - Optimized form sections to prevent unnecessary re-renders
 * - Comprehensive validation matching backend constraints
 * - Large desktop dialog with responsive design
 * - Type-safe integration with OpenAPI schemas
 */
export function InvoiceFormDialog({
  invoiceId,
  open,
  onClose,
  onSuccess,
}: InvoiceFormDialogProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();

  // Determine if we're editing (has invoiceId) or creating (no invoiceId)
  const isEditing = Boolean(invoiceId);

  // API hooks
  const createInvoiceMutation = useCreateInvoice();
  const updateInvoiceMutation = useUpdateInvoice();

  // Fetch invoice data for edit mode (only when invoiceId is provided)
  const {
    data: invoiceData,
    isLoading: isFetchingInvoice,
    error: fetchError,
    isError: isFetchError
  } = useInvoice(isEditing ? invoiceId : null);

  // Check if invoice is editable (only DRAFT invoices can be edited)
  const isInvoiceEditable = !isEditing || (invoiceData?.status === InvoiceResponseStatus.draft);
  const isInvoiceReadOnly = isEditing && !isInvoiceEditable;
  
  const isPending = createInvoiceMutation.isPending || updateInvoiceMutation.isPending;
  const error = createInvoiceMutation.error || updateInvoiceMutation.error || fetchError;
  const isError = createInvoiceMutation.isError || updateInvoiceMutation.isError || isFetchError;

  // Validation schema with proper TypeScript types
  const schema: yup.ObjectSchema<InvoiceFormValues> = useMemo(() => yup.object({
    issuer_id: yup
      .number()
      .min(1, t(keys.invoices.form.validation.issuerRequired))
      .required(t(keys.invoices.form.validation.issuerRequired)),
    recipient_id: yup
      .number()
      .min(1, t(keys.invoices.form.validation.recipientRequired))
      .required(t(keys.invoices.form.validation.recipientRequired)),
    bank_details_id: yup
      .number()
      .optional(),
    invoice_number: yup
      .string()
      .required(t(keys.invoices.form.validation.invoiceNumberRequired))
      .max(50, t(keys.invoices.form.validation.invoiceNumberTooLong)),
    currency: yup
      .string()
      .required(t(keys.invoices.form.validation.currencyRequired))
      .length(3, 'Currency must be 3 characters'),
    issue_date: yup
      .string()
      .required(t(keys.invoices.form.validation.issueDateRequired)),
    due_date: yup
      .string()
      .required(t(keys.invoices.form.validation.dueDateRequired))
      .test('after-issue', t(keys.invoices.form.validation.dueDateAfterIssue), function(value) {
        const { issue_date } = this.parent;
        if (!value || !issue_date) return true;
        return new Date(value) >= new Date(issue_date);
      }),
    notes: yup
      .string()
      .max(2000, t(keys.invoices.form.validation.notesTooLong))
      .optional(),
    items: yup
      .array()
      .of(yup.object({
        description: yup
          .string()
          .required(t(keys.invoices.form.validation.itemDescriptionRequired))
          .max(500, t(keys.invoices.form.validation.itemDescriptionTooLong)),
        quantity: yup
          .number()
          .required(t(keys.invoices.form.validation.itemQuantityRequired))
          .min(0.01, t(keys.invoices.form.validation.itemQuantityMin)),
        unit_price: yup
          .number()
          .required(t(keys.invoices.form.validation.itemUnitPriceRequired))
          .min(0, t(keys.invoices.form.validation.itemUnitPriceMin)),
        vat_rate: yup
          .number()
          .required(t(keys.invoices.form.validation.itemVatRateRequired))
          .min(0, t(keys.invoices.form.validation.itemVatRateMin))
          .max(1, t(keys.invoices.form.validation.itemVatRateMax)),
      }))
      .min(1, t(keys.invoices.form.validation.itemsRequired))
      .required(),
    recipients: yup
      .array()
      .of(yup.object({
        email: yup
          .string()
          .required(t(keys.invoices.form.validation.emailRequired))
          .email(t(keys.invoices.form.validation.emailInvalid))
          .max(255, t(keys.invoices.form.validation.emailTooLong)),
        type: yup
          .mixed<InvoiceRecipientRequestType>()
          .oneOf(Object.values(InvoiceRecipientRequestType))
          .required(t(keys.invoices.form.validation.recipientTypeRequired)),
        source: yup
          .mixed<InvoiceRecipientRequestSource>()
          .oneOf(Object.values(InvoiceRecipientRequestSource))
          .default(InvoiceRecipientRequestSource.manual),
        brand_contact_id: yup
          .number()
          .optional(),
      }))
      .min(1, t(keys.invoices.form.validation.recipientsRequired))
      .required(),
  }), [t, keys]);

  const form = useForm<InvoiceFormValues>({
    resolver: yupResolver(schema),
    defaultValues: createDefaultFormValues(),
    mode: 'onSubmit', // Only validate on submit, not on change
    reValidateMode: 'onChange', // Re-validate on change after first submit
  });

  // Watch for recipient_id changes to clear email recipients when brand changes
  const recipientId = useWatch({
    control: form.control,
    name: 'recipient_id',
  });

  // Track the previous recipient_id to detect changes
  const previousRecipientId = useRef<number | null>(null);

  // Clear email recipients when brand (recipient) changes
  useEffect(() => {
    // Only clear recipients if:
    // 1. Dialog is open
    // 2. We have a previous recipient ID (not initial load)
    // 3. The recipient ID actually changed
    // 4. The new recipient ID is valid (not 0)
    if (open &&
        previousRecipientId.current !== null &&
        previousRecipientId.current !== recipientId &&
        recipientId && recipientId > 0) {

      // Clear all email recipients and reset to a single empty recipient
      form.setValue('recipients', [
        {
          email: '',
          type: InvoiceRecipientRequestType.original,
          source: InvoiceRecipientRequestSource.manual,
        },
      ]);
    }

    // Update the previous recipient ID
    previousRecipientId.current = recipientId;
  }, [recipientId, open, form]);

  // Reset form when dialog opens/closes or invoice data changes
  useEffect(() => {
    if (open) {
      if (isEditing && invoiceData) {
        // Populate form with invoice data for editing
        const formValues = convertInvoiceToFormValues(invoiceData);
        form.reset(formValues);
        // Set the initial recipient ID for tracking changes
        previousRecipientId.current = formValues.recipient_id;
      } else if (!isEditing) {
        // Reset to default values for creating
        const defaultValues = createDefaultFormValues();
        form.reset(defaultValues);
        // Reset the previous recipient ID
        previousRecipientId.current = null;
      }
    }
  }, [open, isEditing, invoiceData, form]);

  const handleSubmit = async (data: InvoiceFormValues) => {
    try {
      if (isEditing && invoiceId) {
        const updateData = convertToUpdateRequest(data);

        await updateInvoiceMutation.mutateAsync({
          params: { path: { id: invoiceId } },
          body: updateData,
        });
      } else {
        const createData = convertToCreateRequest(data);

        await createInvoiceMutation.mutateAsync({
          body: createData,
        });
      }

      onClose();
      onSuccess?.();
    } catch (__error) {
      // Error handling is managed by the mutation hooks
      // Error handling for invoice save
    }
  };

  const dialogTitle = isEditing 
    ? (t(keys.invoices.form.editTitle))
    : (t(keys.invoices.form.createTitle));

  // Show loading state while fetching invoice data
  if (isEditing && isFetchingInvoice) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className={cn(
          "w-[calc(100vw-1rem)] max-w-7xl max-h-[calc(100vh-2rem)] md:max-h-[90vh] overflow-hidden mx-2 md:mx-auto",
          isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 mx-0"
        )}>
          <DialogHeader className={cn(
            isMobile && "pb-2 pt-4 px-4"
          )}>
            <DialogTitle className={cn(
              "flex items-center gap-2",
              isMobile && "text-base"
            )}>
              <FileText className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
              {dialogTitle}
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex items-center justify-center py-12">
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">
                {t(keys.invoices.form.loadingInvoice)}
              </p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={cn(
        "w-[calc(100vw-1rem)] max-w-[1024px] max-h-[calc(100vh-2rem)] md:max-h-[95vh] overflow-y-auto mx-2 md:mx-auto",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 mx-0"
      )}>
      <DialogHeader className={cn(
        isMobile && "pb-2 pt-4 px-4"
      )}>
          <DialogTitle className={cn(
            "flex items-center gap-2",
            isMobile && "text-base"
          )}>
            <FileText className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
            {dialogTitle}
          </DialogTitle>
        </DialogHeader>

        <div className={cn(isMobile && "px-4")}>
          {isError && error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error?.error?.message || (t(keys.invoices.form.error))}
              </AlertDescription>
            </Alert>
          )}

          {isInvoiceReadOnly && (
            <Alert>
              <Lock className="h-4 w-4" />
              <AlertDescription>
                {t(keys.invoices.form.readOnlyMessage)}
              </AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <BasicInfoSection
              control={form.control}
              isPending={isPending || isFetchingInvoice || isInvoiceReadOnly}
              setValue={form.setValue}
              getValues={form.getValues}
              isEditing={isEditing}
              isDialogOpen={open}
            />

            {/* Client Information */}
            <ClientInfoSection
              control={form.control}
              isPending={isPending || isFetchingInvoice || isInvoiceReadOnly}
            />

            {/* Line Items */}
            <LineItemsSection
              control={form.control}
              isPending={isPending || isFetchingInvoice || isInvoiceReadOnly}
            />

            {/* Totals */}
            <TotalsSection
              control={form.control}
            />

            {/* Recipients */}
            <RecipientsSection
              control={form.control}
              isPending={isPending || isFetchingInvoice || isInvoiceReadOnly}
            />

            {/* Additional Details */}
            <AdditionalDetailsSection
              control={form.control}
              isPending={isPending || isFetchingInvoice || isInvoiceReadOnly}
            />

              {/* Form Actions */}
              <div className={cn(
                "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-3 pt-6 border-t",
                isMobile && "pb-4"
              )}>
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isPending}
                  className="min-h-[44px]"
                >
                  {isInvoiceReadOnly ? t(keys.invoices.form.close) : t(keys.invoices.form.cancel)}
                </Button>
                {!isInvoiceReadOnly && (
                  <Button
                    type="submit"
                    disabled={isPending || isFetchingInvoice}
                    className="min-w-[120px] min-h-[44px]"
                  >
                    {isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        {isEditing
                          ? (t(keys.invoices.form.updating))
                          : (t(keys.invoices.form.creating))
                        }
                      </>
                    ) : (
                      isEditing
                        ? (t(keys.invoices.form.updateInvoice))
                        : (t(keys.invoices.form.createInvoice))
                    )}
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
