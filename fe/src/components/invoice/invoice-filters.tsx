import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceFilters } from './types';
import { INVOICE_STATUS_CONFIG } from './types';

interface InvoiceFiltersProps {
  filters: InvoiceFilters;
  onFiltersChange: (filters: InvoiceFilters) => void;
}

export function InvoiceFilters({
  filters,
  onFiltersChange
}: InvoiceFiltersProps) {
  const { t, keys } = useTranslations();
  const [localFilters, setLocalFilters] = useState<InvoiceFilters>(filters);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof InvoiceFilters, value: InvoiceFilters[keyof InvoiceFilters]) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    // Apply filters immediately for better UX
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters: InvoiceFilters = {
      page: 0,
      size: filters.size || 20,
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = Boolean(
    filters.status ||
    filters.fromDate ||
    filters.toDate
  );

  const statusOptions = Object.values(INVOICE_STATUS_CONFIG);

  return (
    <div className="space-y-4">
      {/* Filters Row */}
      <div className="flex flex-col gap-4 lg:flex-row lg:items-end lg:gap-4">
        {/* Status Filter */}
        <div className="space-y-2 w-full lg:w-[160px]">
          <Label htmlFor="status-filter">
            {t(keys.invoices.filters.status)}
          </Label>
          <Select
            value={localFilters.status || 'all'}
            onValueChange={(value) =>
              handleFilterChange('status', value === 'all' ? undefined : value)
            }
          >
            <SelectTrigger id="status-filter" className="h-10 w-full">
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All statuses</SelectItem>
              {statusOptions.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* From Date Filter */}
        <div className="space-y-2 w-full lg:w-[160px]">
          <Label htmlFor="from-date">
            {t(keys.invoices.filters.fromDate)}
          </Label>
          <Input
            id="from-date"
            type="date"
            value={localFilters.fromDate || ''}
            onChange={(e) => handleFilterChange('fromDate', e.target.value || undefined)}
            className="h-10"
          />
        </div>

        {/* To Date Filter */}
        <div className="space-y-2 w-full lg:w-[160px]">
          <Label htmlFor="to-date">
            {t(keys.invoices.filters.toDate)}
          </Label>
          <Input
            id="to-date"
            type="date"
            value={localFilters.toDate || ''}
            onChange={(e) => handleFilterChange('toDate', e.target.value || undefined)}
            className="h-10"
          />
        </div>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <div className="flex items-end w-full lg:w-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFilters}
              className="flex items-center gap-2 h-10 w-full lg:w-auto"
            >
              <X className="h-4 w-4" />
              {t(keys.invoices.filters.clearAll)}
            </Button>
          </div>
        )}
      </div>

      {/* Active Filters Indicator */}
      {hasActiveFilters && (
        <div className="text-sm text-muted-foreground">
          {t(keys.invoices.filters.activeFilters)} {' '}
          {[
            filters.status && `Status: ${statusOptions.find(s => s.value === filters.status)?.label}`,
            filters.fromDate && `From: ${filters.fromDate}`,
            filters.toDate && `To: ${filters.toDate}`
          ].filter(Boolean).join(', ')}
        </div>
      )}
    </div>
  );
}
