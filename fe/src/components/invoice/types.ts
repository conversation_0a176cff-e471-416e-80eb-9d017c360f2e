// Re-export OpenAPI generated types from decoupled API types
export type {
  InvoiceResponse,
  InvoiceListItemDto,
  InvoiceCreateRequest,
  InvoiceUpdateRequest,
  InvoiceItemRequest,
  InvoiceItemResponse,
  InvoiceRecipientRequest,
  InvoiceRecipientResponse,
  InvoiceEmailResponse,
  NextInvoiceNumberResponse,
  PageResponseInvoiceListItemDto
} from '@/lib/types/api';

// Re-export enums as values from the original API file
export {
  InvoiceRecipientRequestType,
  InvoiceRecipientRequestSource,
  InvoiceResponseStatus
} from '@/lib/api/v1';

// Import types for converter functions
import type { InvoiceListItemDto } from '@/lib/types/api';
import { InvoiceResponseStatus } from '@/lib/api/v1';

// Enums are now exported from the main export above

// Type alias for invoice status (for backward compatibility)
export type InvoiceStatus = InvoiceResponseStatus;

// ============================================================================
// Dialog Props Types
// ============================================================================

/**
 * Props for the main invoice form dialog component.
 * If invoiceId is provided, the dialog will fetch and edit that invoice.
 * Otherwise, it will create a new invoice.
 */
export interface InvoiceFormDialogProps {
  /** Invoice ID for edit mode (if provided, will fetch and edit) */
  invoiceId?: number;
  /** Whether the dialog is open */
  open: boolean;
  /** Callback when dialog should close */
  onClose: () => void;
  /** Callback when operation succeeds */
  onSuccess?: () => void;
}

// Component-optimized types for UI display
export type InvoiceDisplayData = {
  id: number;
  invoice_number: string;
  status: InvoiceStatus;
  recipient_name: string;
  total_amount: number;
  currency: string;
  issue_date: string;
  due_date: string;
  days_until_due?: number;
  is_overdue?: boolean;
  created_at: string;
  updated_at: string;
};

export type InvoiceFilters = {
  status?: InvoiceResponseStatus;
  fromDate?: string;
  toDate?: string;
  page?: number;
  size?: number;
};

export type InvoiceStatusOption = {
  value: InvoiceResponseStatus;
  label: string;
  color: 'default' | 'secondary' | 'destructive' | 'outline';
};

// Status configuration for UI display
export const INVOICE_STATUS_CONFIG: Record<InvoiceResponseStatus, InvoiceStatusOption> = {
  [InvoiceResponseStatus.draft]: {
    value: InvoiceResponseStatus.draft,
    label: 'Draft',
    color: 'secondary',
  },
  [InvoiceResponseStatus.sent]: {
    value: InvoiceResponseStatus.sent,
    label: 'Sent',
    color: 'default',
  },
  [InvoiceResponseStatus.paid]: {
    value: InvoiceResponseStatus.paid,
    label: 'Paid',
    color: 'outline',
  },
  [InvoiceResponseStatus.overdue]: {
    value: InvoiceResponseStatus.overdue,
    label: 'Overdue',
    color: 'destructive',
  },
};

// Type converters between OpenAPI and component types
export function convertToDisplayData(invoice: InvoiceListItemDto): InvoiceDisplayData {
  return {
    id: invoice.id,
    invoice_number: invoice.invoice_number,
    status: invoice.status as unknown as InvoiceStatus,
    recipient_name: invoice.recipient_name || 'Unknown Recipient',
    total_amount: Number(invoice.total_amount),
    currency: invoice.currency,
    issue_date: invoice.issue_date,
    due_date: invoice.due_date,
    days_until_due: invoice.days_until_due,
    is_overdue: invoice.is_overdue,
    created_at: '', // Not available in list DTO
    updated_at: '', // Not available in list DTO
  };
}

// ============================================================================
// Export main dialog component
// ============================================================================

export { InvoiceFormDialog } from './invoice-form-dialog';
