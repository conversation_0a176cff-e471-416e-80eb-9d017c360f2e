import { AlertTriangle } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useDeleteInvoice } from '@/hooks/invoices';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceDisplayData } from './types';

interface DeleteInvoiceDialogProps {
  invoice: InvoiceDisplayData | null;
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function DeleteInvoiceDialog({ 
  invoice, 
  open, 
  onClose, 
  onSuccess 
}: DeleteInvoiceDialogProps) {
  const { t, keys } = useTranslations();
  const deleteInvoiceMutation = useDeleteInvoice();

  const handleDelete = async () => {
    if (!invoice) return;

    try {
      await deleteInvoiceMutation.mutateAsync({
        params: { path: { id: invoice.id } }
      });
      
      onSuccess();
      onClose();
    } catch (__error) {
      // Error handling is managed by the mutation hook
      // Error handling for invoice deletion
    }
  };

  if (!invoice) return null;

  return (
    <AlertDialog open={open} onOpenChange={onClose}>
      <AlertDialogContent className="w-[calc(100vw-1rem)] max-w-lg mx-2 sm:mx-auto">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <AlertDialogTitle>
              {t(keys.invoices.confirmDelete)}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription>
            {t(keys.invoices.deleteDescription)}
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <div className="py-4">
          <div className="rounded-lg border p-4 bg-muted/50">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  {t(keys.invoices.invoiceNumber)}:
                </span>
                <span className="text-sm font-medium">{invoice.invoice_number}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  {t(keys.invoices.recipient)}:
                </span>
                <span className="text-sm font-medium">{invoice.recipient_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  {t(keys.invoices.totalAmount)}:
                </span>
                <span className="text-sm font-medium">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: invoice.currency,
                  }).format(invoice.total_amount)}
                </span>
              </div>
            </div>
          </div>
        </div>

        <AlertDialogFooter className="flex flex-col-reverse gap-2 sm:flex-row sm:gap-2">
          <AlertDialogCancel disabled={deleteInvoiceMutation.isPending} className="min-h-[44px]">
            {t(keys.invoices.cancel)}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteInvoiceMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 min-h-[44px]"
          >
            {deleteInvoiceMutation.isPending
              ? t(keys.invoices.deleting)
              : t(keys.invoices.delete)
            }
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
