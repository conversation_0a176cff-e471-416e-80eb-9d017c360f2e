/**
 * Invoice data converters for the Collaboration Hub frontend.
 * 
 * This module provides type-safe conversion functions between:
 * - API responses and form values
 * - Form values and API requests
 * - Default values for new invoices
 * 
 * All converters use strict TypeScript types from the OpenAPI schema
 * and provide safe defaults for missing or undefined values.
 */

export {
  convertInvoiceToFormValues,
  convertToCreateRequest,
  convertToUpdateRequest,
  createDefaultFormValues,
  type InvoiceFormValues,
} from './form-converters';
