import type {
  InvoiceResponse,
  InvoiceCreateRequest,
  InvoiceUpdateRequest,
  InvoiceItemRequest,
  InvoiceRecipientRequest
} from '@/lib/types/api';

import {
  InvoiceRecipientRequestSource,
  InvoiceRecipientRequestType,
  InvoiceRecipientResponseSource,
  InvoiceRecipientResponseType
} from '@/lib/api/v1';

/**
 * Helper functions to convert between Response and Request enum types.
 * The OpenAPI schema generates separate enums for Request and Response types,
 * but they have the same values, so we can safely convert between them.
 */
function convertResponseTypeToRequestType(responseType: InvoiceRecipientResponseType): InvoiceRecipientRequestType {
  // Both enums have the same values: 'original' | 'copy'
  return responseType as unknown as InvoiceRecipientRequestType;
}

function convertResponseSourceToRequestSource(responseSource: InvoiceRecipientResponseSource): InvoiceRecipientRequestSource {
  // Both enums have the same values: 'brand_contact' | 'manual'
  return responseSource as unknown as InvoiceRecipientRequestSource;
}

/**
 * Form values interface for invoice forms.
 * This matches the structure expected by react-hook-form.
 */
export interface InvoiceFormValues {
  issuer_id: number;
  recipient_id: number;
  bank_details_id?: number;
  invoice_number: string;
  currency: string;
  issue_date: string;
  due_date: string;
  notes?: string;
  items: Array<{
    description: string;
    quantity: number;
    unit_price: number;
    vat_rate: number;
  }>;
  recipients: Array<{
    email: string;
    type: InvoiceRecipientRequestType;
    source: InvoiceRecipientRequestSource;
    brand_contact_id?: number;
  }>;
}

/**
 * Converts an InvoiceResponse from the API to form values for editing.
 * Provides safe defaults for all required fields.
 * Now uses typed snapshot responses with guaranteed ID fields.
 */
export function convertInvoiceToFormValues(invoice: InvoiceResponse): InvoiceFormValues {
  // Extract IDs from typed snapshot responses
  const issuerId = invoice.issuer_id || 0;
  const recipientId = invoice.recipient_id || 0;
  const bankDetailsId = invoice.bank_details_id || undefined;

  return {
    issuer_id: issuerId,
    recipient_id: recipientId,
    bank_details_id: bankDetailsId,
    invoice_number: invoice.invoice_number,
    currency: invoice.currency,
    issue_date: invoice.issue_date,
    due_date: invoice.due_date,
    notes: invoice.notes || '',
    items: invoice.items.map(item => ({
      description: item.description,
      quantity: item.quantity,
      unit_price: item.unit_price,
      vat_rate: item.vat_rate || 0,
    })),
    recipients: invoice.recipients.map(recipient => ({
      email: recipient.email,
      type: convertResponseTypeToRequestType(recipient.type),
      source: convertResponseSourceToRequestSource(recipient.source),
      brand_contact_id: recipient.brand_contact_id,
    })),
  };
}

/**
 * Converts form values to an InvoiceCreateRequest for the API.
 */
export function convertToCreateRequest(formData: InvoiceFormValues): InvoiceCreateRequest {
  return {
    issuer_id: formData.issuer_id,
    recipient_id: formData.recipient_id,
    bank_details_id: formData.bank_details_id || undefined,
    invoice_number: formData.invoice_number,
    template: 'default',
    currency: formData.currency,
    issue_date: formData.issue_date,
    due_date: formData.due_date,
    notes: formData.notes,
    items: formData.items.map((item, index): InvoiceItemRequest => ({
      description: item.description,
      quantity: item.quantity,
      unit_price: item.unit_price,
      vat_rate: item.vat_rate,
      sort_order: index,
    })),
    recipients: formData.recipients.map((recipient): InvoiceRecipientRequest => ({
      email: recipient.email,
      type: recipient.type,
      source: recipient.source,
      brand_contact_id: recipient.brand_contact_id,
    })),
  };
}

/**
 * Converts form values to an InvoiceUpdateRequest for the API.
 * Includes all fields that can be updated.
 */
export function convertToUpdateRequest(formData: InvoiceFormValues): InvoiceUpdateRequest {
  return {
    issuer_id: formData.issuer_id,
    recipient_id: formData.recipient_id,
    bank_details_id: formData.bank_details_id || undefined,
    invoice_number: formData.invoice_number,
    currency: formData.currency,
    issue_date: formData.issue_date,
    due_date: formData.due_date,
    notes: formData.notes,
    template: 'default',
    items: formData.items?.map((item, index): InvoiceItemRequest => ({
      description: item.description,
      quantity: item.quantity,
      unit_price: item.unit_price,
      vat_rate: item.vat_rate,
      sort_order: index,
    })),
    recipients: formData.recipients?.map((recipient): InvoiceRecipientRequest => ({
      email: recipient.email,
      type: recipient.type,
      source: recipient.source,
      brand_contact_id: recipient.brand_contact_id,
    })),
  };
}

/**
 * Creates default form values for creating a new invoice.
 */
export function createDefaultFormValues(): InvoiceFormValues {
  const today = new Date().toISOString().split('T')[0];
  const dueDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

  return {
    issuer_id: 0,
    recipient_id: 0,
    bank_details_id: undefined,
    invoice_number: '',
    currency: 'EUR',
    issue_date: today,
    due_date: dueDate,
    notes: '',
    items: [
      {
        description: '',
        quantity: 1,
        unit_price: 0,
        vat_rate: 0.20, // Default 20% VAT
      },
    ],
    recipients: [
      {
        email: '',
        type: InvoiceRecipientRequestType.original,
        source: InvoiceRecipientRequestSource.manual,
      },
    ],
  };
}
