import { MoreH<PERSON>zontal, FileText, Send, Download, Edit, Trash2, Calendar, DollarSign } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceDisplayData } from './types';
import { INVOICE_STATUS_CONFIG, InvoiceResponseStatus } from './types';

interface InvoiceCardProps {
  invoice: InvoiceDisplayData;
  onEdit: () => void;
  onDelete: () => void;
  onSend: () => void;
  onDownload: () => void;
  onViewDetails: () => void;
  onUpdateStatus: () => void;
}

export function InvoiceCard({
  invoice,
  onEdit,
  onDelete,
  onSend,
  onDownload,
  onViewDetails,
  onUpdateStatus
}: InvoiceCardProps) {
  const { t, keys } = useTranslations();

  const statusConfig = INVOICE_STATUS_CONFIG[invoice.status];
  const isOverdue = invoice.is_overdue;
  const daysUntilDue = invoice.days_until_due;
  const isEditable = invoice.status === InvoiceResponseStatus.draft;

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getDueDateDisplay = () => {
    if (isOverdue && daysUntilDue !== undefined) {
      const daysOverdue = Math.abs(daysUntilDue);
      return (
        <span className="text-destructive font-medium">
          {t(keys.invoices.daysOverdue, { days: daysOverdue })}
        </span>
      );
    }
    
    if (daysUntilDue === 0) {
      return (
        <span className="text-orange-600 font-medium">
          {t(keys.invoices.dueToday)}
        </span>
      );
    }
    
    if (daysUntilDue && daysUntilDue > 0) {
      return (
        <span className="text-muted-foreground">
          {t(keys.invoices.dueIn, { days: daysUntilDue })}
        </span>
      );
    }
    
    return null;
  };

  return (
    <Card
      onClick={onViewDetails}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="font-semibold text-lg">{invoice.invoice_number}</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={statusConfig.color}>
              {statusConfig.label}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger onClick={(e) => e.stopPropagation()}>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onViewDetails(); }}>
                  <FileText className="mr-2 h-4 w-4" />
                  {t(keys.invoices.viewDetails)}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => { e.stopPropagation(); onEdit(); }}
                  disabled={!isEditable}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  {t(keys.invoices.edit)}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onUpdateStatus(); }}>
                  <Edit className="mr-2 h-4 w-4" />
                  {t(keys.invoices.statusUpdate.title)}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onSend(); }}>
                  <Send className="mr-2 h-4 w-4" />
                  {t(keys.invoices.send)}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onDownload(); }}>
                  <Download className="mr-2 h-4 w-4" />
                  {t(keys.invoices.download)}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={(e) => { e.stopPropagation(); onDelete(); }}
                  className="text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t(keys.invoices.delete)}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Client Name */}
          <div>
            <p className="font-medium text-foreground">{invoice.recipient_name}</p>
          </div>
          
          {/* Dates */}
          <div className="flex flex-col items-start gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                {t(keys.invoices.issueDate)}: {formatDate(invoice.issue_date)}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                {t(keys.invoices.dueDate)}: {formatDate(invoice.due_date)}
              </span>
            </div>
          </div>
          
          {/* Amount */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-2xl font-bold">
                {formatCurrency(invoice.total_amount, invoice.currency)}
              </span>
            </div>
            {getDueDateDisplay()}
          </div>

        </div>
      </CardContent>
    </Card>
  );
}
