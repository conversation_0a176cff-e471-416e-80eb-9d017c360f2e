import { AlertTriangle, Mail } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceDisplayData } from './types';

interface SendConfirmationDialogProps {
  invoice: InvoiceDisplayData | null;
  open: boolean;
  onClose: () => void;
  onConfirm: (force: boolean) => void;
  isAlreadySent: boolean;
  isPending?: boolean;
}

/**
 * Confirmation dialog for sending invoices.
 * Shows different messaging based on whether the invoice has already been sent.
 * Provides option to force send if invoice was already sent.
 */
export function SendConfirmationDialog({
  invoice,
  open,
  onClose,
  onConfirm,
  isAlreadySent,
  isPending = false,
}: SendConfirmationDialogProps) {
  const { t, keys } = useTranslations();

  if (!invoice) return null;

  const handleSend = () => {
    onConfirm(false);
  };

  const handleForceSend = () => {
    onConfirm(true);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {isAlreadySent
              ? t(keys.invoices.sendConfirmation.confirmResendTitle)
              : t(keys.invoices.sendConfirmation.confirmSendTitle)
            }
          </DialogTitle>
          <DialogDescription>
            {isAlreadySent
              ? t(keys.invoices.sendConfirmation.confirmResendDescription, { invoiceNumber: invoice.invoice_number })
              : t(keys.invoices.sendConfirmation.confirmSendDescription, { invoiceNumber: invoice.invoice_number })
            }
          </DialogDescription>
        </DialogHeader>

        {isAlreadySent && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t(keys.invoices.sendConfirmation.alreadySentWarning)}
            </AlertDescription>
          </Alert>
        )}

        <DialogFooter className="flex-col-reverse gap-2 sm:flex-row sm:gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isPending}
          >
            {t(keys.invoices.sendConfirmation.cancel)}
          </Button>

          {isAlreadySent ? (
            <Button
              type="button"
              onClick={handleForceSend}
              disabled={isPending}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {t(keys.invoices.sendConfirmation.forceSend)}
            </Button>
          ) : (
            <Button
              type="button"
              onClick={handleSend}
              disabled={isPending}
            >
              {t(keys.invoices.sendConfirmation.send)}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
