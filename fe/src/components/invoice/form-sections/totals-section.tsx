import { memo, useMemo } from 'react';
import { type Control, useWatch } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceFormValues } from '../converters';

interface TotalsSectionProps {
  control: Control<InvoiceFormValues>;
}

/**
 * Invoice totals section for invoice forms.
 * 
 * Contains:
 * - Total excluding VAT
 * - Total VAT amount
 * - Total including VAT
 * 
 * Calculates totals dynamically based on line items and displays them
 * with proper currency formatting.
 */
export const TotalsSection = memo(function TotalsSection({
  control,
}: TotalsSectionProps) {
  const { t, keys } = useTranslations();
  
  // Watch items and currency for total calculations
  const items = useWatch({
    control,
    name: 'items',
  });
  
  const currency = useWatch({
    control,
    name: 'currency',
  });

  const totals = useMemo(() => {
    if (!items || items.length === 0) {
      return {
        subtotal: 0,
        totalVat: 0,
        total: 0,
      };
    }

    // Helper function to round to avoid floating point precision issues
    const roundToDecimals = (num: number, decimals: number = 2) => {
      return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
    };

    let subtotal = 0;
    let totalVat = 0;

    items.forEach((item) => {
      const quantity = item?.quantity || 0;
      const unitPrice = item?.unit_price || 0;
      const vatRate = item?.vat_rate || 0;

      const lineSubtotal = roundToDecimals(quantity * unitPrice);
      const lineVat = roundToDecimals(lineSubtotal * vatRate);

      subtotal = roundToDecimals(subtotal + lineSubtotal);
      totalVat = roundToDecimals(totalVat + lineVat);
    });

    const total = roundToDecimals(subtotal + totalVat);

    return {
      subtotal,
      totalVat,
      total,
    };
  }, [items]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'EUR',
    }).format(amount);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          {t(keys.invoices.form.totals)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {/* Subtotal */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">
              Total Excl. VAT
            </span>
            <span className="font-medium">
              {formatCurrency(totals.subtotal)}
            </span>
          </div>

          {/* VAT Total */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">
              VAT Amount
            </span>
            <span className="font-medium">
              {formatCurrency(totals.totalVat)}
            </span>
          </div>

          <Separator />

          {/* Total */}
          <div className="flex justify-between items-center">
            <span className="text-base font-semibold">
              Total Incl. VAT
            </span>
            <span className="text-lg font-bold">
              {formatCurrency(totals.total)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
