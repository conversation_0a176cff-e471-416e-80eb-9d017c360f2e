/**
 * Invoice form sections for the Collaboration Hub frontend.
 *
 * These components provide optimized form sections for invoice creation and editing:
 * - BasicInfoSection: Invoice number, currency, dates
 * - ClientInfoSection: Issuer, recipient, bank details selection
 * - LineItemsSection: Dynamic line items with calculations
 * - RecipientsSection: Email recipients with type selection
 * - TotalsSection: Invoice totals with real-time calculations
 * - AdditionalDetailsSection: Notes and additional information
 *
 * All sections are memoized to prevent unnecessary re-renders and use
 * react-hook-form for optimal performance.
 */

// Export all form sections
export { BasicInfoSection } from './basic-info-section';
export { ClientInfoSection } from './client-info-section';
export { LineItemsSection } from './line-items-section';
export { RecipientsSection } from './recipients-section';
export { TotalsSection } from './totals-section';
export { AdditionalDetailsSection } from './additional-details-section';

// Re-export form values type from converters
export type { InvoiceFormValues } from '../converters';
