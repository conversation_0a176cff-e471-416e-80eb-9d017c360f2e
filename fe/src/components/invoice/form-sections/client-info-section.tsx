import { memo, useState, useMemo, useCallback } from 'react';
import type { Control } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Combobox, type ComboboxOption } from '@/components/ui/combobox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useAccountCompanies } from '@/hooks/account-companies';
import { useBrands } from '@/hooks/brands';
import { useBankDetails } from '@/hooks/bank-details';
import { useDebounce } from '@/lib/utils';
import type { InvoiceFormValues } from '../converters';

interface ClientInfoSectionProps {
  control: Control<InvoiceFormValues>;
  isPending: boolean;
}

/**
 * Client information section for invoice forms.
 *
 * Contains:
 * - Issuer company selection (from account companies)
 * - Recipient brand selection (from brands)
 * - Bank details selection
 *
 * Memoized to prevent unnecessary re-renders when other form fields change.
 * Uses separate API hooks for companies, brands, and bank details.
 */
export const ClientInfoSection = memo(function ClientInfoSection({
  control,
  isPending,
}: ClientInfoSectionProps) {
  const { t, keys } = useTranslations();

  // Search states for each combobox
  const [issuerSearch, setIssuerSearch] = useState('');
  const [brandSearch, setBrandSearch] = useState('');
  const [bankDetailsSearch, setBankDetailsSearch] = useState('');

  // Debounced search values
  const debouncedIssuerSearch = useDebounce(issuerSearch, 300);
  const debouncedBrandSearch = useDebounce(brandSearch, 300);
  const debouncedBankDetailsSearch = useDebounce(bankDetailsSearch, 300);

  const { data: companies, isLoading: companiesLoading } = useAccountCompanies();
  const { data: brandsPage, isLoading: brandsLoading } = useBrands(debouncedBrandSearch || undefined, 0, 100);
  const { data: bankDetails, isLoading: bankDetailsLoading } = useBankDetails();

  const isLoading = companiesLoading || brandsLoading || bankDetailsLoading;

  // Convert data to combobox options
  const issuerOptions: ComboboxOption[] = useMemo(() => {
    if (!companies) return [];

    // Filter companies based on search query
    const filteredCompanies = debouncedIssuerSearch
      ? companies.filter(company =>
          company.company_name.toLowerCase().includes(debouncedIssuerSearch.toLowerCase()) ||
          (company.email && company.email.toLowerCase().includes(debouncedIssuerSearch.toLowerCase()))
        )
      : companies;

    return filteredCompanies.map(company => ({
      value: company.id.toString(),
      label: company.company_name,
      description: company.email || undefined,
    }));
  }, [companies, debouncedIssuerSearch]);

  const brandOptions: ComboboxOption[] = useMemo(() => {
    const brands = brandsPage?.content || [];
    return brands.map(brand => ({
      value: brand.id.toString(),
      label: brand.name,
      description: brand.email || undefined,
    }));
  }, [brandsPage?.content]);

  const bankDetailsOptions: ComboboxOption[] = useMemo(() => {
    if (!bankDetails) return [];

    // Filter bank details based on search query
    const filteredBankDetails = debouncedBankDetailsSearch
      ? bankDetails.filter(bank =>
          bank.name.toLowerCase().includes(debouncedBankDetailsSearch.toLowerCase()) ||
          (bank.bank_name && bank.bank_name.toLowerCase().includes(debouncedBankDetailsSearch.toLowerCase())) ||
          (bank.iban && bank.iban.toLowerCase().includes(debouncedBankDetailsSearch.toLowerCase()))
        )
      : bankDetails;

    return filteredBankDetails.map(bank => ({
      value: bank.id.toString(),
      label: bank.name,
      description: bank.bank_name ? `${bank.bank_name}${bank.iban ? ` • ${bank.iban.slice(0, 8)}...` : ''}` : bank.iban ? `${bank.iban.slice(0, 8)}...` : undefined,
    }));
  }, [bankDetails, debouncedBankDetailsSearch]);

  // Search handlers
  const handleIssuerSearch = useCallback((query: string) => {
    setIssuerSearch(query);
  }, []);

  const handleBrandSearch = useCallback((query: string) => {
    setBrandSearch(query);
  }, []);

  const handleBankDetailsSearch = useCallback((query: string) => {
    setBankDetailsSearch(query);
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          {t(keys.invoices.form.clientInfo)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Company Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <FormField
            control={control}
            name="issuer_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.issuer)}</FormLabel>
                <FormControl>
                  <Combobox
                    value={field.value?.toString()}
                    onChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                    options={issuerOptions}
                    placeholder={t(keys.invoices.form.placeholders.selectIssuer)}
                    searchPlaceholder={t(keys.invoices.form.placeholders.searchIssuers)}
                    disabled={isPending || isLoading}
                    loading={companiesLoading}
                    emptyMessage={t(keys.invoices.form.noIssuersFound)}
                    onSearch={handleIssuerSearch}
                    className="h-11 w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="recipient_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.recipient)}</FormLabel>
                <FormControl>
                  <Combobox
                    value={field.value?.toString()}
                    onChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                    options={brandOptions}
                    placeholder={t(keys.invoices.form.placeholders.selectRecipient)}
                    searchPlaceholder={t(keys.invoices.form.placeholders.searchBrands)}
                    disabled={isPending || isLoading}
                    loading={brandsLoading}
                    emptyMessage={t(keys.invoices.form.noBrandsFound)}
                    onSearch={handleBrandSearch}
                    className="h-11 w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Bank Details - Full Width */}
        <div className="w-full">
          <FormField
            control={control}
            name="bank_details_id"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>
                  {t(keys.invoices.form.myBankDetails)}
                  <span className="text-muted-foreground ml-1">({t(keys.invoices.form.optional)})</span>
                </FormLabel>
                <FormControl>
                  <Combobox
                    value={field.value?.toString()}
                    onChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                    options={bankDetailsOptions}
                    placeholder={t(keys.invoices.form.placeholders.selectBankDetails)}
                    searchPlaceholder={t(keys.invoices.form.placeholders.searchBankDetails)}
                    disabled={isPending || isLoading}
                    loading={bankDetailsLoading}
                    emptyMessage={t(keys.invoices.form.noBankDetailsFound)}
                    onSearch={handleBankDetailsSearch}
                    className="h-11 w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
});
