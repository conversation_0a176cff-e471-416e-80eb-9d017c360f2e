import { memo, useMemo, useState, useCallback } from 'react';
import { Check, ChevronsUpDown, Mail, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useBrandContacts } from '@/hooks/brands';

interface EmailComboboxProps {
  value?: string;
  onChange?: (value: string, contactId?: number) => void;
  brandId?: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

interface ContactOption {
  email: string;
  name: string;
  contactId: number;
  displayText: string;
}

/**
 * Enhanced email combobox that shows brand contacts when a brand is selected.
 *
 * Features:
 * - Shows brand contacts in dropdown when brandId is provided
 * - Displays contact name + email in dropdown options
 * - Shows only email when selected (to prevent UI clutter)
 * - Allows manual email input if desired contact is not in list
 * - Searchable with real-time filtering
 */
export const EmailCombobox = memo(function EmailCombobox({
  value = '',
  onChange,
  brandId,
  placeholder = 'Enter email address...',
  disabled = false,
  className,
}: EmailComboboxProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch brand contacts only when brandId is provided and valid
  // This prevents premature fetching when no brand is selected yet
  const shouldFetchContacts = brandId !== undefined && brandId !== null && brandId > 0;
  const { data: contacts, isLoading: contactsLoading } = useBrandContacts(
    shouldFetchContacts ? brandId : undefined,
    shouldFetchContacts
  );

  // Convert contacts to options - show ALL brand contacts
  const allContactOptions: ContactOption[] = useMemo(() => {
    if (!contacts || contacts.length === 0) return [];

    return contacts.map(contact => ({
      email: contact.email,
      name: contact.name,
      contactId: contact.id,
      displayText: `${contact.name} <${contact.email}>`,
    }));
  }, [contacts]);

  // Filter contacts by search query for display
  const filteredContactOptions: ContactOption[] = useMemo(() => {
    if (!searchQuery) return allContactOptions;

    const query = searchQuery.toLowerCase();
    return allContactOptions.filter(option =>
      option.name.toLowerCase().includes(query) ||
      option.email.toLowerCase().includes(query)
    );
  }, [allContactOptions, searchQuery]);



  const handleSelect = useCallback((email: string, contactId?: number) => {
    onChange?.(email, contactId);
    setOpen(false);
    setSearchQuery('');
  }, [onChange]);

  const handleManualInput = useCallback((email: string) => {
    onChange?.(email);
    setOpen(false);
    setSearchQuery('');
  }, [onChange]);

  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Always show manual input option when user has typed something
  const showManualInput = searchQuery.length > 0;

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && searchQuery) {
      e.preventDefault();
      if (showManualInput) {
        handleManualInput(searchQuery);
        setOpen(false);
      }
    }
  }, [searchQuery, showManualInput, handleManualInput]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Button
          type="button"
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-11 w-full justify-between text-left font-normal",
            !value && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <Mail className="h-4 w-4 shrink-0 text-muted-foreground" />
            <span className="truncate">
              {value || placeholder}
            </span>
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0" align="start">
        <Command>
          <CommandInput
            placeholder="Search contacts or enter email..."
            value={searchQuery}
            onValueChange={handleSearchChange}
            onKeyDown={handleKeyDown}
            className="h-9"
          />
          <CommandList>
            {contactsLoading ? (
              <CommandEmpty>Loading contacts...</CommandEmpty>
            ) : (
              <CommandGroup>
                {/* Manual email input option - ALWAYS FIRST when user types */}
                {showManualInput && (
                  <CommandItem
                    value={`manual-${searchQuery}`}
                    onSelect={() => handleManualInput(searchQuery)}
                  >
                    <Mail className="mr-2 h-4 w-4 text-blue-500" />
                    <div className="flex flex-col">
                      <span className="font-medium">
                        Use "{searchQuery}"
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Enter this email address manually
                      </span>
                    </div>
                  </CommandItem>
                )}

                {/* Brand contacts - show filtered results or all contacts */}
                {filteredContactOptions.map((option) => (
                  <CommandItem
                    key={option.contactId}
                    value={option.email}
                    onSelect={() => handleSelect(option.email, option.contactId)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === option.email ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <User className="mr-2 h-4 w-4 text-muted-foreground" />
                    <div className="flex flex-col min-w-0 flex-1">
                      <span className="font-medium truncate">{option.name}</span>
                      <span className="text-xs text-muted-foreground truncate">{option.email}</span>
                    </div>
                  </CommandItem>
                ))}

                {/* Show empty state when no contacts available */}
                {!showManualInput && filteredContactOptions.length === 0 && !contactsLoading && (
                  !shouldFetchContacts ? (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      <div className="flex items-center justify-center gap-2">
                        <Mail className="h-4 w-4" />
                        <span>Select a brand first to see contacts, or type an email address</span>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      No contacts found for this brand.
                    </div>
                  )
                )}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
});
