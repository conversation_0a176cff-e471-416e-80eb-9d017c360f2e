import { memo } from 'react';
import type { Control } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceFormValues } from '../converters';

interface AdditionalDetailsSectionProps {
  control: Control<InvoiceFormValues>;
  isPending: boolean;
}

/**
 * Additional details section for invoice forms.
 * 
 * Contains:
 * - Notes/Comments field
 * 
 * Memoized to prevent unnecessary re-renders when other form fields change.
 */
export const AdditionalDetailsSection = memo(function AdditionalDetailsSection({
  control,
  isPending,
}: AdditionalDetailsSectionProps) {
  const { t, keys } = useTranslations();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          {t(keys.invoices.form.additionalDetails)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <FormField
          control={control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t(keys.invoices.form.notes)}
                <span className="text-muted-foreground ml-1">
                  ({t(keys.invoices.form.optional)})
                </span>
              </FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  disabled={isPending}
                  placeholder={t(keys.invoices.form.placeholders.notes)}
                  className="min-h-[120px] resize-none"
                  maxLength={2000}
                />
              </FormControl>
              <div className="flex justify-between items-center">
                <FormMessage />
                <span className="text-xs text-muted-foreground">
                  {field.value?.length || 0}/2000
                </span>
              </div>
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
});
