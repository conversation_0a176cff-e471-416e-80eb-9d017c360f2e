import { memo, useCallback } from 'react';
import { type Control, useFieldArray, useWatch, useFormContext } from 'react-hook-form';
import { Plus, Trash2, Mail } from 'lucide-react';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { InvoiceRecipientRequestType, InvoiceRecipientRequestSource } from '@/lib/types/api';
import type { InvoiceFormValues } from '../converters';
import { EmailCombobox } from './email-combobox';

interface RecipientsSectionProps {
  control: Control<InvoiceFormValues>;
  isPending: boolean;
}

interface RecipientItemProps {
  control: Control<InvoiceFormValues>;
  index: number;
  isPending: boolean;
  canRemove: boolean;
  onRemove: () => void;
}

const RECIPIENT_TYPE_OPTIONS = [
  { 
    value: InvoiceRecipientRequestType.original, 
    label: 'Primary',
    description: 'Main recipient of the invoice',
    variant: 'default' as const
  },
  { 
    value: InvoiceRecipientRequestType.copy, 
    label: 'CC',
    description: 'Receives a copy of the invoice',
    variant: 'secondary' as const
  },
] as const;

/**
 * Individual recipient item component.
 * Memoized to prevent re-renders when other recipients change.
 */
const RecipientItem = memo(function RecipientItem({
  control,
  index,
  isPending,
  canRemove,
  onRemove,
}: RecipientItemProps) {
  const { t, keys } = useTranslations();
  const { setValue } = useFormContext<InvoiceFormValues>();

  // Watch the selected brand to enable brand contact integration
  const selectedBrandId = useWatch({
    control,
    name: 'recipient_id',
  });

  return (
    <div className="p-4 border rounded-lg bg-card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Mail className="h-4 w-4 text-muted-foreground" />
          <h4 className="font-medium text-sm">
            {t(keys.invoices.form.recipient)} {index + 1}
          </h4>
        </div>
        {canRemove && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onRemove}
            disabled={isPending}
            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <FormField
          control={control}
          name={`recipients.${index}.email`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t(keys.invoices.form.email)}</FormLabel>
              <FormControl>
                <EmailCombobox
                  value={field.value}
                  onChange={(email, contactId) => {
                    field.onChange(email);
                    // Update the brand_contact_id and source based on selection
                    if (contactId) {
                      setValue(`recipients.${index}.brand_contact_id`, contactId);
                      setValue(`recipients.${index}.source`, InvoiceRecipientRequestSource.brand_contact);
                    } else {
                      setValue(`recipients.${index}.brand_contact_id`, undefined);
                      setValue(`recipients.${index}.source`, InvoiceRecipientRequestSource.manual);
                    }
                  }}
                  brandId={selectedBrandId}
                  placeholder={t(keys.invoices.form.placeholders.recipientEmail)}
                  disabled={isPending}
                  className="h-11 w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name={`recipients.${index}.type`}
          render={({ field }) => {
            const selectedOption = RECIPIENT_TYPE_OPTIONS.find(opt => opt.value === field.value);
            return (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.recipientType)}</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} disabled={isPending}>
                  <FormControl>
                    <SelectTrigger className="h-11 w-full">
                      <SelectValue placeholder={t(keys.invoices.form.placeholders.selectRecipientType)}>
                        {selectedOption && (
                          <Badge variant={selectedOption.variant} className="text-xs">
                            {selectedOption.label}
                          </Badge>
                        )}
                      </SelectValue>
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {RECIPIENT_TYPE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2">
                            <Badge variant={option.variant} className="text-xs">
                              {option.label}
                            </Badge>
                          </div>
                          <span className="text-xs text-muted-foreground">{option.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      </div>
    </div>
  );
});

/**
 * Recipients section for invoice forms.
 * 
 * Contains:
 * - Dynamic list of email recipients
 * - Add/remove recipient functionality
 * - Recipient type selection (Primary/CC)
 * 
 * Optimized to prevent unnecessary re-renders using memoization.
 */
export const RecipientsSection = memo(function RecipientsSection({
  control,
  isPending,
}: RecipientsSectionProps) {
  const { t, keys } = useTranslations();
  
  const { fields: recipientFields, append: appendRecipient, remove: removeRecipient } = useFieldArray({
    control,
    name: 'recipients',
  });

  const handleAddRecipient = useCallback(() => {
    appendRecipient({
      email: '',
      type: InvoiceRecipientRequestType.copy,
      source: InvoiceRecipientRequestSource.manual,
    });
  }, [appendRecipient]);

  const handleRemoveRecipient = useCallback((index: number) => {
    removeRecipient(index);
  }, [removeRecipient]);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            {t(keys.invoices.form.recipients)}
          </CardTitle>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleAddRecipient}
            disabled={isPending}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t(keys.invoices.form.addRecipient)}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        {recipientFields.map((field, index) => (
          <RecipientItem
            key={field.id}
            control={control}
            index={index}
            isPending={isPending}
            canRemove={recipientFields.length > 1}
            onRemove={() => handleRemoveRecipient(index)}
          />
        ))}
      </CardContent>
    </Card>
  );
});
