import { memo, useCallback, useMemo } from 'react';
import { type Control, useFieldArray, useWatch } from 'react-hook-form';
import { Plus, Trash2 } from 'lucide-react';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceFormValues } from '../converters';

interface LineItemsSectionProps {
  control: Control<InvoiceFormValues>;
  isPending: boolean;
}

interface LineItemProps {
  control: Control<InvoiceFormValues>;
  index: number;
  isPending: boolean;
  canRemove: boolean;
  onRemove: () => void;
  currency: string;
}

/**
 * Individual line item component.
 * Memoized to prevent re-renders when other items change.
 */
const LineItem = memo(function LineItem({
  control,
  index,
  isPending,
  canRemove,
  onRemove,
  currency,
}: LineItemProps) {
  const { t, keys } = useTranslations();
  
  // Watch only the specific item to minimize re-renders
  const item = useWatch({
    control,
    name: `items.${index}`,
  });

  const lineTotal = useMemo(() => {
    const quantity = item?.quantity || 0;
    const unitPrice = item?.unit_price || 0;
    const vatRate = item?.vat_rate || 0;

    // Helper function to round to avoid floating point precision issues
    const roundToDecimals = (num: number, decimals: number = 2) => {
      return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
    };

    const subtotal = roundToDecimals(quantity * unitPrice);
    const vat = roundToDecimals(subtotal * vatRate);
    return roundToDecimals(subtotal + vat);
  }, [item?.quantity, item?.unit_price, item?.vat_rate]);

  return (
    <div className="p-4 border rounded-lg bg-card">
      <div className="flex items-center justify-between mb-4">
        <h4 className="font-medium text-sm">
          {t(keys.invoices.form.lineItem)} {index + 1}
        </h4>
        {canRemove && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onRemove}
            disabled={isPending}
            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className="space-y-4">
        <FormField
          control={control}
          name={`items.${index}.description`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t(keys.invoices.form.description)}</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  disabled={isPending}
                  placeholder={t(keys.invoices.form.placeholders.itemDescription)}
                  className="h-11"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <FormField
            control={control}
            name={`items.${index}.quantity`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.quantity)}</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    step="1"
                    min="1"
                    disabled={isPending}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value === '' || value === '0') {
                        field.onChange('');
                      } else {
                        field.onChange(parseInt(value) || 1);
                      }
                    }}
                    value={field.value === 0 ? '' : field.value}
                    className="h-11"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name={`items.${index}.unit_price`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.unitPrice)}</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    step="0.01"
                    min="0"
                    disabled={isPending}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value === '') {
                        field.onChange('');
                      } else {
                        field.onChange(parseFloat(value) || 0);
                      }
                    }}
                    value={field.value}
                    className="h-11"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name={`items.${index}.vat_rate`}
            render={({ field }) => {
              // Helper function to round to avoid floating point precision issues
              const roundToDecimals = (num: number, decimals: number = 4) => {
                return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
              };

              // Convert decimal to percentage for display (0.20 -> 20)
              const displayValue = field.value ? roundToDecimals(field.value * 100, 2) : '';

              return (
                <FormItem>
                  <FormLabel>{t(keys.invoices.form.vatRate)} (%)</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        disabled={isPending}
                        onChange={(e) => {
                          const inputValue = e.target.value;
                          if (inputValue === '') {
                            field.onChange(0);
                          } else {
                            const percentageValue = parseFloat(inputValue);
                            if (!isNaN(percentageValue)) {
                              // Convert percentage to decimal for backend (20% -> 0.20)
                              // Round to avoid floating point precision issues
                              const decimalValue = roundToDecimals(percentageValue / 100, 4);
                              field.onChange(decimalValue);
                            }
                          }
                        }}
                        value={displayValue}
                        placeholder="20"
                        className="h-11 pr-8"
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
                        %
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          
          <div className="flex items-end">
            <div className="w-full">
              <FormLabel>{t(keys.invoices.form.lineTotal)}</FormLabel>
              <div className="mt-2 p-3 bg-muted rounded-md text-right font-medium h-11 flex items-center justify-end">
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: currency || 'EUR',
                }).format(lineTotal)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

/**
 * Line items section for invoice forms.
 * 
 * Contains:
 * - Dynamic list of line items
 * - Add/remove item functionality
 * - Real-time total calculations
 * 
 * Optimized to prevent unnecessary re-renders using memoization
 * and selective watching of form fields.
 */
export const LineItemsSection = memo(function LineItemsSection({
  control,
  isPending,
}: LineItemsSectionProps) {
  const { t, keys } = useTranslations();
  
  const { fields: itemFields, append: appendItem, remove: removeItem } = useFieldArray({
    control,
    name: 'items',
  });

  // Watch currency for total calculations
  const currency = useWatch({
    control,
    name: 'currency',
  });

  const handleAddItem = useCallback(() => {
    appendItem({
      description: '',
      quantity: 1,
      unit_price: 0,
      vat_rate: 0.20, // Default 20% VAT
    });
  }, [appendItem]);

  const handleRemoveItem = useCallback((index: number) => {
    removeItem(index);
  }, [removeItem]);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            {t(keys.invoices.form.lineItems)}
          </CardTitle>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleAddItem}
            disabled={isPending}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t(keys.invoices.form.addItem)}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {itemFields.map((field, index) => (
          <LineItem
            key={field.id}
            control={control}
            index={index}
            isPending={isPending}
            canRemove={itemFields.length > 1}
            onRemove={() => handleRemoveItem(index)}
            currency={currency || 'EUR'}
          />
        ))}
      </CardContent>
    </Card>
  );
});
