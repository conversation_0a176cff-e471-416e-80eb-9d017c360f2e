// Re-export OpenAPI generated types from decoupled API types
export type {
  BrandResponse,
  BrandListItemDto,
  BrandCreateRequest,
  BrandUpdateRequest,
  BrandContactResponse,
  BrandContactRequest,
  PageResponseBrandListItemDto as BrandListPageResponse
} from '@/lib/types/api';

// Import types for converter functions
import type { BrandListItemDto } from '@/lib/types/api';

// Component-optimized types for UI display
export type Brand = {
  id: number;
  name: string;
  company_name: string;
  phone?: string;
  email?: string;
  website?: string;
  address_street?: string;
  address_city?: string;
  address_postal_code?: string;
  address_country?: string;
  vat_number?: string;
  registration_number?: string;
  account_id: number;
  created_at: string;
  updated_at: string;
  contacts: BrandContact[];
};

export type BrandContact = {
  id: number;
  name: string;
  email: string;
  notes?: string;
  brand_id: number;
  account_id: number;
  created_at: string;
  updated_at: string;
};

// Display data type optimized for brand cards
export type BrandDisplayData = {
  id: number;
  name: string;
  company_name: string;
  email?: string;
  phone?: string;
  address_city?: string;
  address_country?: string;
  website?: string;
  vat_number?: string;
  contact_count: number;
  primary_contact_email?: string;
};

// Form data types for create/edit operations
export type BrandFormData = {
  name: string;
  company_name: string;
  phone?: string;
  email?: string;
  website?: string;
  address_street?: string;
  address_city?: string;
  address_postal_code?: string;
  address_country?: string;
  vat_number?: string;
  registration_number?: string;
  contacts: BrandContactFormData[];
};

export type BrandContactFormData = {
  id?: number;
  name: string;
  email: string;
  notes?: string;
};

// BrandListPageResponse is now exported from the main export above

// Type converter for brand list items to display data
export function convertBrandListToDisplayData(brand: BrandListItemDto): BrandDisplayData {
  return {
    id: brand.id,
    name: brand.name,
    company_name: brand.company_name,
    email: brand.email,
    phone: brand.phone,
    website: brand.website,
    // Fields not available in list DTO - set defaults
    address_city: undefined,
    address_country: undefined,
    vat_number: undefined,
    contact_count: 0, // Not available in lightweight DTO
    primary_contact_email: brand.email, // Use brand email as fallback
  };
}
