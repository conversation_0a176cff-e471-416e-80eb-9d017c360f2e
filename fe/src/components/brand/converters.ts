import type { 
  BrandResponse, 
  BrandCreateRequest, 
  BrandUpdateRequest,
  BrandContactResponse,
  BrandContactRequest,
  Brand, 
  BrandDisplayData, 
  BrandFormData,
  BrandContact,
  BrandContactFormData 
} from './types';

/**
 * Convert OpenAPI BrandResponse to component Brand type
 */
export function convertBrandResponseToBrand(response: BrandResponse): Brand {
  return {
    id: response.id,
    name: response.name,
    company_name: response.company_name,
    phone: response.phone,
    email: response.email,
    website: response.website,
    address_street: response.address_street,
    address_city: response.address_city,
    address_postal_code: response.address_postal_code,
    address_country: response.address_country,
    vat_number: response.vat_number,
    registration_number: response.registration_number,
    account_id: response.account_id,
    created_at: response.created_at,
    updated_at: response.updated_at,
    contacts: response.contacts.map(convertBrandContactResponseToBrandContact),
  };
}

/**
 * Convert OpenAPI BrandContactResponse to component BrandContact type
 */
export function convertBrandContactResponseToBrandContact(response: BrandContactResponse): BrandContact {
  return {
    id: response.id,
    name: response.name,
    email: response.email,
    notes: response.notes,
    brand_id: response.brand_id,
    account_id: response.account_id,
    created_at: response.created_at,
    updated_at: response.updated_at,
  };
}

/**
 * Convert Brand to BrandDisplayData for card display
 */
export function convertBrandToBrandDisplayData(brand: Brand): BrandDisplayData {
  return {
    id: brand.id,
    name: brand.name,
    company_name: brand.company_name,
    email: brand.email,
    phone: brand.phone,
    address_city: brand.address_city,
    address_country: brand.address_country,
    website: brand.website,
    vat_number: brand.vat_number,
    contact_count: brand.contacts.length,
    primary_contact_email: brand.contacts[0]?.email,
  };
}

/**
 * Convert Brand to BrandFormData for form editing
 */
export function convertBrandToBrandFormData(brand: Brand): BrandFormData {
  return {
    name: brand.name,
    company_name: brand.company_name,
    phone: brand.phone || '',
    email: brand.email || '',
    website: brand.website || '',
    address_street: brand.address_street || '',
    address_city: brand.address_city || '',
    address_postal_code: brand.address_postal_code || '',
    address_country: brand.address_country || '',
    vat_number: brand.vat_number || '',
    registration_number: brand.registration_number || '',
    contacts: brand.contacts.map(convertBrandContactToBrandContactFormData),
  };
}

/**
 * Convert BrandContact to BrandContactFormData for form editing
 */
export function convertBrandContactToBrandContactFormData(contact: BrandContact): BrandContactFormData {
  return {
    id: contact.id,
    name: contact.name,
    email: contact.email,
    notes: contact.notes || '',
  };
}

/**
 * Convert BrandFormData to BrandCreateRequest for API calls
 */
export function convertBrandFormDataToBrandCreateRequest(formData: BrandFormData): BrandCreateRequest {
  return {
    name: formData.name,
    company_name: formData.company_name,
    phone: formData.phone || undefined,
    email: formData.email || undefined,
    website: formData.website || undefined,
    address_street: formData.address_street || undefined,
    address_city: formData.address_city || undefined,
    address_postal_code: formData.address_postal_code || undefined,
    address_country: formData.address_country || undefined,
    vat_number: formData.vat_number || undefined,
    registration_number: formData.registration_number || undefined,
    contacts: formData.contacts.map(convertBrandContactFormDataToBrandContactRequest),
  };
}

/**
 * Convert BrandFormData to BrandUpdateRequest for API calls
 */
export function convertBrandFormDataToBrandUpdateRequest(formData: BrandFormData): BrandUpdateRequest {
  return {
    name: formData.name,
    company_name: formData.company_name,
    phone: formData.phone || undefined,
    email: formData.email || undefined,
    website: formData.website || undefined,
    address_street: formData.address_street || undefined,
    address_city: formData.address_city || undefined,
    address_postal_code: formData.address_postal_code || undefined,
    address_country: formData.address_country || undefined,
    vat_number: formData.vat_number || undefined,
    registration_number: formData.registration_number || undefined,
    contacts: formData.contacts.map(convertBrandContactFormDataToBrandContactRequest),
  };
}

/**
 * Convert BrandContactFormData to BrandContactRequest for API calls
 */
export function convertBrandContactFormDataToBrandContactRequest(formData: BrandContactFormData): BrandContactRequest {
  return {
    id: formData.id,
    name: formData.name,
    email: formData.email,
    notes: formData.notes || undefined,
  };
}

/**
 * Create empty BrandFormData for new brand creation
 */
export function createEmptyBrandFormData(): BrandFormData {
  return {
    name: '',
    company_name: '',
    phone: '',
    email: '',
    website: '',
    address_street: '',
    address_city: '',
    address_postal_code: '',
    address_country: '',
    vat_number: '',
    registration_number: '',
    contacts: [],
  };
}

/**
 * Create empty BrandContactFormData for new contact creation
 */
export function createEmptyBrandContactFormData(): BrandContactFormData {
  return {
    name: '',
    email: '',
    notes: '',
  };
}
