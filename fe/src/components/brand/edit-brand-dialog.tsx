import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form } from "@/components/ui/form"
import { useCreateBrand, useUpdateBrand } from "@/hooks/brands"
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useIsMobile } from '@/hooks/use-mobile'
import { cn } from '@/lib/utils'
import { BrandFormContent } from './brand-form-content'
import type { Brand } from './types'
import { ScrollArea } from '@/components/ui/scroll-area.tsx';

// Form validation schema matching backend constraints
const brandFormSchema = z.object({
  name: z.string()
    .min(1, "Brand name is required")
    .max(255, "Brand name cannot exceed 255 characters"),
  company_name: z.string()
    .min(1, "Company name is required")
    .max(255, "Company name cannot exceed 255 characters"),
  phone: z.string()
    .max(50, "Phone cannot exceed 50 characters")
    .optional()
    .or(z.literal("")),
  email: z.string()
    .email("Email must be valid")
    .max(255, "Email cannot exceed 255 characters")
    .optional()
    .or(z.literal("")),
  website: z.string()
    .max(500, "Website cannot exceed 500 characters")
    .optional()
    .or(z.literal("")),
  address_street: z.string()
    .max(500, "Street address cannot exceed 500 characters")
    .optional()
    .or(z.literal("")),
  address_city: z.string()
    .max(100, "City cannot exceed 100 characters")
    .optional()
    .or(z.literal("")),
  address_postal_code: z.string()
    .max(20, "Postal code cannot exceed 20 characters")
    .optional()
    .or(z.literal("")),
  address_country: z.string()
    .max(100, "Country cannot exceed 100 characters")
    .optional()
    .or(z.literal("")),
  vat_number: z.string()
    .max(50, "VAT number cannot exceed 50 characters")
    .optional()
    .or(z.literal("")),
  registration_number: z.string()
    .max(100, "Registration number cannot exceed 100 characters")
    .optional()
    .or(z.literal("")),
  contacts: z.array(z.object({
    id: z.number().optional(),
    name: z.string()
      .min(1, "Contact name is required")
      .max(255, "Contact name cannot exceed 255 characters"),
    email: z.string()
      .min(1, "Contact email is required")
      .email("Contact email must be valid")
      .max(255, "Contact email cannot exceed 255 characters"),
    notes: z.string()
      .max(2000, "Notes cannot exceed 2000 characters")
      .optional()
      .or(z.literal("")),
  })),
})

type FormData = z.infer<typeof brandFormSchema>

interface EditBrandDialogProps {
  brand: Brand | null
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export function EditBrandDialog({ brand, open, onClose, onSuccess }: EditBrandDialogProps) {
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()
  const createBrand = useCreateBrand()
  const updateBrand = useUpdateBrand()
  
  const isEditing = !!brand

  const form = useForm<FormData>({
    resolver: zodResolver(brandFormSchema),
    defaultValues: {
      name: "",
      company_name: "",
      phone: "",
      email: "",
      website: "",
      address_street: "",
      address_city: "",
      address_postal_code: "",
      address_country: "",
      vat_number: "",
      registration_number: "",
      contacts: [],
    },
  })

  // Reset form when brand changes or dialog opens/closes
  useEffect(() => {
    if (open) {
      if (brand) {
        // Editing existing brand
        form.reset({
          name: brand.name,
          company_name: brand.company_name,
          phone: brand.phone || "",
          email: brand.email || "",
          website: brand.website || "",
          address_street: brand.address_street || "",
          address_city: brand.address_city || "",
          address_postal_code: brand.address_postal_code || "",
          address_country: brand.address_country || "",
          vat_number: brand.vat_number || "",
          registration_number: brand.registration_number || "",
          contacts: brand.contacts.map(contact => ({
            id: contact.id,
            name: contact.name,
            email: contact.email,
            notes: contact.notes || "",
          })),
        })
      } else {
        // Creating new brand
        form.reset({
          name: "",
          company_name: "",
          phone: "",
          email: "",
          website: "",
          address_street: "",
          address_city: "",
          address_postal_code: "",
          address_country: "",
          vat_number: "",
          registration_number: "",
          contacts: [],
        })
      }
    }
  }, [brand, open, form])

  const onSubmit = async (data: FormData) => {
    try {
      // Convert form data to API format
      const requestData = {
        name: data.name,
        company_name: data.company_name,
        phone: data.phone || undefined,
        email: data.email || undefined,
        website: data.website || undefined,
        address_street: data.address_street || undefined,
        address_city: data.address_city || undefined,
        address_postal_code: data.address_postal_code || undefined,
        address_country: data.address_country || undefined,
        vat_number: data.vat_number || undefined,
        registration_number: data.registration_number || undefined,
        contacts: data.contacts.map(contact => ({
          id: contact.id,
          name: contact.name,
          email: contact.email,
          notes: contact.notes || undefined,
        })),
      }

      if (isEditing) {
        await updateBrand.mutateAsync({
          params: { path: { id: brand.id } },
          body: requestData,
        })
      } else {
        await createBrand.mutateAsync({
          body: requestData,
        })
      }

      onSuccess()
      onClose()
    } catch (__error) {
      // Error handling is managed by the mutation hooks
    }
  }

  const isPending = createBrand.isPending || updateBrand.isPending

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={cn(
        "w-[calc(100vw-1rem)] max-w-4xl max-h-[calc(100vh-2rem)] md:max-h-[90vh] flex flex-col mx-2 md:mx-auto",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 mx-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            isMobile && "text-base"
          )}>
            {isEditing
              ? (t(keys.brands.editTitle))
              : (t(keys.brands.createTitle))
            }
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className={cn(
          "flex-1 overflow-y-auto px-1",
          isMobile && "px-4"
        )}>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className={cn(
              "space-y-6",
              isMobile && "px-0"
            )}>
              <BrandFormContent form={form} />

              <div className={cn(
                "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-3 pt-4 border-t",
                isMobile && "pb-4"
              )}>
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isPending}
                  className="min-h-[44px]"
                >
                  {t(keys.brands.cancel)}
                </Button>
                <Button
                  type="submit"
                  disabled={isPending}
                  className="min-h-[44px]"
                >
                  {isPending
                    ? (isEditing
                        ? (t(keys.brands.updating))
                        : (t(keys.brands.creating))
                      )
                    : (t(keys.brands.save))
                  }
                </Button>
              </div>
            </form>
          </Form>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
