import { useEffect, useState } from 'react'
import { PostFilterChips } from './post-filter-chips'
import type { PostFilterType } from './post-filter-chips'

interface FilterEventBus {
  subscribe: (callback: (filter: PostFilterType) => void) => () => void;
}

declare global {
  interface Window {
    filterEventBus?: FilterEventBus;
  }
}

/**
 * Demo component to test the high-performance PostFilterChips functionality
 * This demonstrates the event-driven architecture with minimal re-renders
 */
export function PostFilterChipsDemo() {
  const [currentFilter, setCurrentFilter] = useState<PostFilterType>('all')

  // Subscribe to filter changes from the event system
  useEffect(() => {
    const filterEventBus = window.filterEventBus
    if (filterEventBus) {
      const unsubscribe = filterEventBus.subscribe((filter: PostFilterType) => {
        setCurrentFilter(filter)
      })
      return unsubscribe
    }
  }, [])

  return (
    <div className="p-6 max-w-2xl mx-auto space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">High-Performance Filter Chips Demo</h2>
        <p className="text-muted-foreground mb-6">
          Test the event-driven filter chips with minimal re-renders.
        </p>
      </div>

      <PostFilterChips />

      <div className="bg-muted p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Current Filter State:</h3>
        <div className="space-y-1 text-sm">
          <p><strong>Post Filter:</strong> {currentFilter}</p>
        </div>
      </div>

      <div className="bg-muted p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Performance Benefits:</h3>
        <div className="space-y-1 text-sm">
          <p>✅ Individual chip state management</p>
          <p>✅ Zero re-renders in parent components</p>
          <p>✅ Event-driven communication</p>
          <p>✅ Optimal React performance</p>
        </div>
      </div>
    </div>
  )
}
