/**
 * High-Performance Post Filter Chips with Individual State Management
 *
 * Architecture:
 * - Each chip manages its own state independently
 * - Zero re-renders in parent components
 * - Event-driven communication
 * - Optimal React performance
 */

import React from "react"
import { Button } from "@/components/ui/button"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { usePermissions } from "@/hooks/use-permissions"
import { useFilterChip } from "@/hooks/posts/use-post-filters"
import { cn } from "@/lib/utils"

// Post filter types based on backend PostFilter enum
export type PostFilterType = 'all' | 'assigned_to_me' | 'needs_review' | 'my_pending' | 'my_approved' | 'my_rework' | 'reviewed_by_me'

interface PostFilterChipsProps {
  className?: string
}

// ============================================================================
// FILTER DEFINITIONS - Role-based filter sets
// ============================================================================

// Content Creator filters (for users who can create posts)
const CREATOR_FILTERS: { value: PostFilterType; labelKey: string }[] = [
  { value: 'all', labelKey: 'all' },
  { value: 'my_pending', labelKey: 'myPending' },
  { value: 'my_approved', labelKey: 'myApproved' },
  { value: 'my_rework', labelKey: 'myRework' },
]

// Reviewer filters (for users who can review posts)
const REVIEWER_FILTERS: { value: PostFilterType; labelKey: string }[] = [
  { value: 'all', labelKey: 'all' },
  { value: 'assigned_to_me', labelKey: 'assignedToMe' },
  { value: 'reviewed_by_me', labelKey: 'reviewedByMe' },
  { value: 'needs_review', labelKey: 'needsReview' },
]

// Basic filters (fallback)
const BASIC_FILTERS: { value: PostFilterType; labelKey: string }[] = [
  { value: 'all', labelKey: 'all' },
  { value: 'assigned_to_me', labelKey: 'assignedToMe' },
  { value: 'needs_review', labelKey: 'needsReview' },
]

// ============================================================================
// MAIN COMPONENT - Zero re-renders, optimal performance
// ============================================================================

export function PostFilterChips({ className }: PostFilterChipsProps) {
  const { canCreatePost, canReviewPost } = usePermissions()

  // Determine which filters to show based on user permissions (memoized)
  const filtersToShow = React.useMemo(() => {
    if (canCreatePost() && canReviewPost()) {
      // User can both create and review - show all relevant filters
      return [
        ...CREATOR_FILTERS,
        ...REVIEWER_FILTERS.filter(f => f.value !== 'all') // Remove duplicate 'all'
      ]
    } else if (canCreatePost()) {
      // Content creator - show creator-focused filters
      return CREATOR_FILTERS
    } else if (canReviewPost()) {
      // Reviewer - show reviewer-focused filters
      return REVIEWER_FILTERS
    } else {
      // Basic user - show basic filters
      return BASIC_FILTERS
    }
  }, [canCreatePost, canReviewPost])

  return (
    <div className={cn("flex items-center gap-2 flex-wrap", className)}>
      {filtersToShow.map((filter) => (
        <IndividualFilterChip
          key={filter.value}
          filterValue={filter.value}
          labelKey={filter.labelKey}
        />
      ))}
    </div>
  )
}

// ============================================================================
// INDIVIDUAL FILTER CHIP - Manages own state, minimal re-renders
// ============================================================================

interface IndividualFilterChipProps {
  filterValue: PostFilterType
  labelKey: string
}

const IndividualFilterChip = React.memo(function IndividualFilterChip({
  filterValue,
  labelKey
}: IndividualFilterChipProps) {
  const { t, keys } = useTranslations()
  const { isSelected, onClick } = useFilterChip(filterValue)

  const label = t(keys.collaborationHubs.posts.filters.types[labelKey as keyof typeof keys.collaborationHubs.posts.filters.types])

  return (
    <Button
      variant={isSelected ? "default" : "outline"}
      size="sm"
      onClick={onClick}
      className={cn(
        "h-8 px-4 text-sm font-medium transition-all duration-75",
        isSelected
          ? "bg-primary text-primary-foreground shadow-sm"
          : "bg-background text-muted-foreground border-border hover:bg-muted hover:text-foreground"
      )}
    >
      {label}
    </Button>
  )
})
