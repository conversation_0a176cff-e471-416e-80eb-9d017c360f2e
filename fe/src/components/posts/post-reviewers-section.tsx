import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, ChevronRight, Users, Clock, CheckCircle, XCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useTranslations } from '@/lib/i18n/typed-translations'
import { ReviewerCard } from './reviewer-card'
import type { PostReviewer } from '@/lib/types/api'

interface PostReviewersSectionProps {
  reviewers: PostReviewer[]
  compact?: boolean
  showTimeline?: boolean
  className?: string
}

export function PostReviewersSection({ 
  reviewers, 
  compact = false, 
  showTimeline = false,
  className 
}: PostReviewersSectionProps) {
  const [isExpanded, setIsExpanded] = useState(!compact)
  const { t, keys } = useTranslations()

  if (!reviewers || reviewers.length === 0) {
    return (
      <div className={cn("text-sm text-muted-foreground", className)}>
No reviewers assigned
      </div>
    )
  }

  // Calculate review statistics
  const totalReviewers = reviewers.length
  const approvedCount = reviewers.filter(r => r.status === 'approved').length
  const reworkCount = reviewers.filter(r => r.status === 'rework').length
  const pendingCount = reviewers.filter(r => r.status === 'pending').length
  const reviewsWithNotes = reviewers.filter(r =>
    r.review_notes && r.review_notes.trim().length > 0
  ).length

  const getOverallStatus = () => {
    if (reworkCount > 0) return 'rework'
    if (pendingCount > 0) return 'pending'
    if (approvedCount === totalReviewers) return 'approved'
    return 'mixed'
  }

  const getOverallStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-200 dark:border-green-800'
      case 'rework': return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-200 dark:border-red-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-200 dark:border-yellow-800'
      default: return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-200 dark:border-blue-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-3 w-3" />
      case 'rework': return <XCircle className="h-3 w-3" />
      case 'pending': return <Clock className="h-3 w-3" />
      default: return <Users className="h-3 w-3" />
    }
  }

  const overallStatus = getOverallStatus()

  // Compact view for post cards
  if (compact) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Users className="h-4 w-4" />
            <span>{t(keys.collaborationHubs.posts.reviewers)}</span>
          </div>
          <Badge variant="outline" className={getOverallStatusColor(overallStatus)}>
            {getStatusIcon(overallStatus)}
            <span className="ml-1">
              {approvedCount}/{totalReviewers} {t(keys.collaborationHubs.posts.status.approved).toLowerCase()}
            </span>
          </Badge>
        </div>

        {/* Quick status breakdown */}
        <div className="flex items-center gap-2 text-xs text-muted-foreground flex-wrap">
          {approvedCount > 0 && (
            <span className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              {approvedCount} {t(keys.collaborationHubs.posts.status.approved).toLowerCase()}
            </span>
          )}
          {pendingCount > 0 && (
            <span className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-yellow-500" />
              {pendingCount} {t(keys.collaborationHubs.posts.review.statusPending).toLowerCase()}
            </span>
          )}
          {reworkCount > 0 && (
            <span className="flex items-center gap-1">
              <XCircle className="h-3 w-3 text-red-500" />
              {reworkCount} {t(keys.collaborationHubs.posts.rework).toLowerCase()}
            </span>
          )}
          {reviewsWithNotes > 0 && (
            <span className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
              💬 {reviewsWithNotes} with feedback
            </span>
          )}
        </div>

        {/* Expandable reviewer list */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger>
            <Button variant="ghost" size="sm" className="w-full justify-between p-2 h-auto">
              <span className="text-sm">
                {isExpanded ? "Hide reviewers" : "Show reviewers"}
              </span>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="space-y-2 pt-2">
            {reviewers.map((reviewer) => (
              <ReviewerCard
                key={reviewer.id}
                reviewer={reviewer}
                compact={true}
                showDetails={false}
              />
            ))}
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  // Full view for post dialogs
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-sm flex items-center gap-2">
          <Users className="h-4 w-4" />
Assigned Reviewers ({totalReviewers})
        </h4>
        <Badge variant="outline" className={getOverallStatusColor(overallStatus)}>
          {getStatusIcon(overallStatus)}
          <span className="ml-1">
            {approvedCount}/{totalReviewers} {t(keys.collaborationHubs.posts.status.approved).toLowerCase()}
          </span>
        </Badge>
      </div>

      {/* Status summary */}
      <div className="flex items-center gap-4 text-sm flex-wrap">
        {approvedCount > 0 && (
          <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
            <CheckCircle className="h-4 w-4" />
            <span>{approvedCount} {t(keys.collaborationHubs.posts.status.approved).toLowerCase()}</span>
          </div>
        )}
        {pendingCount > 0 && (
          <div className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400">
            <Clock className="h-4 w-4" />
            <span>{pendingCount} {t(keys.collaborationHubs.posts.review.statusPending).toLowerCase()}</span>
          </div>
        )}
        {reworkCount > 0 && (
          <div className="flex items-center gap-1 text-red-600 dark:text-red-400">
            <XCircle className="h-4 w-4" />
            <span>{reworkCount} {t(keys.collaborationHubs.posts.needsRework).toLowerCase()}</span>
          </div>
        )}
        {reviewsWithNotes > 0 && (
          <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400 font-medium">
            💬 <span>{reviewsWithNotes} with feedback</span>
          </div>
        )}
      </div>

      {/* Individual reviewer cards */}
      <div className="space-y-3">
        {showTimeline ? (
          // Sort by status for timeline view (for now, until API types are updated)
          [...reviewers]
            .sort((a, b) => {
              // Sort by status: rework, pending, approved
              const statusOrder = { rework: 0, pending: 1, approved: 2 }
              const aOrder = statusOrder[a.status as keyof typeof statusOrder] ?? 3
              const bOrder = statusOrder[b.status as keyof typeof statusOrder] ?? 3
              return aOrder - bOrder
            })
            .map((reviewer) => (
              <ReviewerCard
                key={reviewer.id}
                reviewer={reviewer}
                showDetails={true}
              />
            ))
        ) : (
          // Default order: status priority (rework, pending, approved)
          [...reviewers]
            .sort((a, b) => {
              const statusOrder = { rework: 0, pending: 1, approved: 2 }
              const aOrder = statusOrder[a.status as keyof typeof statusOrder] ?? 3
              const bOrder = statusOrder[b.status as keyof typeof statusOrder] ?? 3
              return aOrder - bOrder
            })
            .map((reviewer) => (
              <ReviewerCard
                key={reviewer.id}
                reviewer={reviewer}
                showDetails={true}
              />
            ))
        )}
      </div>
    </div>
  )
}
