import { useState } from "react"
import { ChevronDown, ChevronUp, MessageCircle, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { CommentForm } from "./comment-form"
import { CommentItem } from "./comment-item"
import { useCommentsInfinite } from "@/hooks/comments"
import { useTranslations } from "@/lib/i18n/typed-translations"

interface CommentsSectionProps {
  postId: number
  hubId: number
  commentCount?: number
  canComment?: boolean
  className?: string
  defaultExpanded?: boolean
}

export function CommentsSection({
  postId,
  hubId,
  commentCount = 0,
  canComment = false,
  className,
  defaultExpanded = false
}: CommentsSectionProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const { t, keys } = useTranslations()

  // Use infinite query for comments
  const {
    data,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch
  } = useCommentsInfinite(postId, 20, {
    enabled: isExpanded
  })

  // Flatten all comments from all pages
  const allComments = data?.pages.flatMap(page => page.comments) || []
  const totalCount = data?.pages[0]?.total_count || commentCount

  const handleToggle = () => {
    if (defaultExpanded) return // Don't allow toggling when defaultExpanded
    setIsExpanded(!isExpanded)
  }

  const handleLoadMore = () => {
    fetchNextPage()
  }

  const handleCommentCreated = () => {
    // Refetch comments to show the new comment
    refetch()
  }

  if (totalCount === 0 && !canComment) {
    return null
  }

  return (
    <div className={className}>
      {!defaultExpanded && <Separator className="my-3" />}

      {/* Comments Toggle Button - Hidden when defaultExpanded */}
      {!defaultExpanded && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggle}
          className="w-full justify-between p-2 h-auto text-muted-foreground hover:text-foreground"
        >
          <div className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            <span className="text-sm">
              {totalCount === 0
                ? t(keys.collaborationHubs.posts.comments.noComments)
                : `${totalCount} ${totalCount === 1 ? t(keys.collaborationHubs.posts.comments.comment) : t(keys.collaborationHubs.posts.comments.commentsPlural)}`
              }
            </span>
          </div>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
      )}

      {/* Expanded Comments Section */}
      {isExpanded && (
        <div className="mt-4 space-y-4">
          {/* Comment Form */}
          {canComment && (
            <CommentForm
              postId={postId}
              hubId={hubId}
              onCommentCreated={handleCommentCreated}
            />
          )}

          {/* Comments List */}
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2 text-sm text-muted-foreground">
                {t(keys.collaborationHubs.posts.comments.loadingComments)}
              </span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground mb-2">
                {t(keys.collaborationHubs.posts.comments.failedToLoad)}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
              >
                {t(keys.collaborationHubs.posts.comments.tryAgain)}
              </Button>
            </div>
          ) : allComments.length === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">
                {canComment
                  ? t(keys.collaborationHubs.posts.comments.beFirstToComment)
                  : t(keys.collaborationHubs.posts.comments.noComments)
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {allComments.map((comment) => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  hubId={hubId}
                />
              ))}

              {/* Load More Button */}
              {hasNextPage && (
                <div className="text-center pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleLoadMore}
                    disabled={isFetchingNextPage}
                    className="gap-2"
                  >
                    {isFetchingNextPage ? (
                      <>
                        <Loader2 className="h-3 w-3 animate-spin" />
                        {t(keys.collaborationHubs.posts.comments.loading)}
                      </>
                    ) : (
                      t(keys.collaborationHubs.posts.comments.loadMore)
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
