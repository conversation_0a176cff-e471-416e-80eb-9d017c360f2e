import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function PostCardSkeleton() {
  return (
    <Card>
      {/* Header Skeleton */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Avatar */}
            <Skeleton className="h-8 w-8 rounded-full" />
            <div className="space-y-1">
              {/* Name */}
              <Skeleton className="h-4 w-24" />
              <div className="flex items-center gap-2">
                {/* Status badge */}
                <Skeleton className="h-5 w-16 rounded-full" />
                {/* Date */}
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
          </div>
          {/* Actions menu */}
          <Skeleton className="h-8 w-8 rounded" />
        </div>
      </CardHeader>

      {/* Content Skeleton */}
      <CardContent className="space-y-4">
        {/* Media placeholder */}
        <Skeleton className="w-full h-64 rounded-lg" />
        
        {/* Caption lines */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>

        {/* Reviewers section */}
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-16" />
          <div className="flex items-center gap-1">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>
        </div>

        {/* Stats section */}
        <div className="flex items-center gap-4 pt-2 border-t">
          <div className="flex items-center gap-1">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-12" />
          </div>
          <div className="flex items-center gap-1">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-16" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
