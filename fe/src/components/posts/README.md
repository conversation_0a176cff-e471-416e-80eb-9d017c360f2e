# Posts Components

This directory contains React components for managing posts in collaboration hubs.

## Components

### PostFormDialog
Main dialog component for creating and editing posts with comprehensive form validation and media upload capabilities.

**Features:**
- Single dialog handles both create and edit modes
- Media upload with drag-and-drop support
- Multi-select reviewer assignment
- Form validation using react-hook-form and zod
- Mobile-responsive design
- Progress tracking for uploads
- Proper error handling and loading states

**Props:**
```typescript
interface PostFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  hubId: number;
  postId?: number | null; // If provided, dialog is in edit mode
  onSuccess?: () => void;
}
```

### MediaUpload
Drag-and-drop media upload component with progress tracking and file validation.

**Features:**
- Drag-and-drop file upload
- Multiple file selection
- Progress indicators during upload
- File type and size validation
- Media preview with thumbnails
- Reordering capability (visual indicators)
- Error handling with retry functionality
- Support for images (10MB max) and videos (100MB max)

**Props:**
```typescript
interface MediaUploadProps {
  value: string[];
  onChange: (urls: string[]) => void;
  hubId: number;
  maxFiles?: number;
  className?: string;
  disabled?: boolean;
}
```

### ReviewerMultiSelect
Multi-select component for assigning reviewers to posts with role-based filtering.

**Features:**
- Searchable dropdown with participant filtering
- Role-based participant display (admin, reviewer, reviewer_creator)
- Visual role indicators and badges
- Selected reviewers display with removal capability
- Avatar support with fallback initials
- Mobile-friendly interface

**Props:**
```typescript
interface ReviewerMultiSelectProps {
  hubId: number;
  value: number[];
  onChange: (reviewerIds: number[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}
```

## Form Validation

The PostFormDialog uses comprehensive validation:

```typescript
const postFormSchema = z.object({
  caption: z.string().max(2200, "Caption cannot exceed 2200 characters").optional(),
  media_uris: z.array(z.string().url("Invalid media URL")).optional().default([]),
  reviewer_notes: z.string().max(1000, "Reviewer notes cannot exceed 1000 characters").optional(),
  reviewer_ids: z.array(z.number()).max(10, "Cannot assign more than 10 reviewers").optional().default([]),
}).refine((data) => {
  // Post must have either caption or media content (or both)
  return (data.caption && data.caption.trim().length > 0) || 
         (data.media_uris && data.media_uris.length > 0)
}, {
  message: "Post must have either a caption or media content",
  path: ["caption"],
})
```

## File Upload Constraints

- **Images**: JPEG, PNG, GIF, WebP - Maximum 10MB each
- **Videos**: MP4, QuickTime, AVI - Maximum 100MB each
- **Total Files**: Up to 10 files per post
- **Validation**: Client-side and server-side validation
- **Security**: MIME type validation and file extension checking

## Integration

### With Posts Tab
```typescript
import { PostFormDialog } from '@/components/posts';

// In your component
const [createDialogOpen, setCreateDialogOpen] = useState(false);
const [editDialogOpen, setEditDialogOpen] = useState(false);
const [editingPostId, setEditingPostId] = useState<number | null>(null);

// Create post
<PostFormDialog
  open={createDialogOpen}
  onOpenChange={setCreateDialogOpen}
  hubId={hubId}
  onSuccess={() => console.log('Post created')}
/>

// Edit post
<PostFormDialog
  open={editDialogOpen}
  onOpenChange={setEditDialogOpen}
  hubId={hubId}
  postId={editingPostId}
  onSuccess={() => console.log('Post updated')}
/>
```

### With API Hooks
The components integrate seamlessly with the posts hooks:

```typescript
import { useCreatePost, useUpdatePost, useUploadMedia } from '@/hooks/posts';

// Hooks are used internally by the components
// No manual integration required
```

## Mobile Responsiveness

All components are mobile-responsive:

- **Dialog**: Full-screen on mobile (< 768px), modal on desktop
- **Media Upload**: Touch-friendly drag areas and buttons
- **Multi-Select**: Mobile-optimized dropdown and selection interface
- **Form Fields**: Proper touch targets and spacing

## Error Handling

Comprehensive error handling throughout:

- **Upload Errors**: File size, type, and network errors with retry capability
- **Validation Errors**: Field-level validation with clear error messages
- **API Errors**: Network and server errors with user-friendly messages
- **Form State**: Proper loading states and disabled states during operations

## Accessibility

Components follow accessibility best practices:

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Readers**: Proper ARIA labels and descriptions
- **Focus Management**: Logical focus flow and visible focus indicators
- **Color Contrast**: Sufficient contrast ratios for all text and UI elements

## Performance

Optimized for performance:

- **Lazy Loading**: Components only render when needed
- **Memoization**: Expensive operations are memoized
- **Debounced Search**: Search inputs use debouncing to reduce API calls
- **Optimistic Updates**: UI updates immediately with rollback on errors
