import { useState } from "react"
import { useSearch<PERSON>ara<PERSON>, useLocation } from "react-router-dom"
import { <PERSON>Circle, MoreHorizontal, Edit, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { MediaCarousel } from "@/components/media/media-carousel"
import { PostDialog } from "./post-dialog"
import { PostReviewForm } from "./post-review-form"
import { PostReviewersSection } from "./post-reviewers-section"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { useDeletePost } from "@/hooks/posts"
import { toast } from "sonner"
import type { PostListItemResponse, MediaItem } from "@/lib/types/api"
import { DeepLinkUtils } from "@/lib/notification-navigation"

interface PostCardProps {
  post: PostListItemResponse
  hubId: number
  onEdit?: (postId: number) => void
  className?: string
}

export function PostCard({ post, hubId, onEdit, className }: PostCardProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [searchParams, setSearchParams] = useSearchParams()
  const location = useLocation()
  const { t, keys } = useTranslations()
  const deletePostMutation = useDeletePost()

  // URL-driven dialog state (consistent with PostsTab)
  const urlPostId = DeepLinkUtils.getPostToOpen(searchParams)
  const postDialogOpen = urlPostId === post.id
  // Check for comment scrolling from hash (aligns with notification system)
  const commentId = DeepLinkUtils.getCommentToScrollTo(location.hash)
  const scrollToComments = location.hash === '#post-comments-section' || !!commentId

  // Function to open post dialog by updating URL (integrates with URL-driven system)
  const openPostDialog = (scrollToComments = false) => {
    if (!post.id) return // Guard against undefined post.id

    // Update URL with post parameter using proper URL management
    const newParams = DeepLinkUtils.updateSearchParams(searchParams, {
      tab: 'posts',
      post: post.id.toString(),
    })
    setSearchParams(newParams, { replace: true })

    // If scrolling to comments, add hash fragment after URL update
    if (scrollToComments) {
      // Use a small delay to ensure URL update completes first
      setTimeout(() => {
        window.location.hash = '#post-comments-section'
      }, 50) // Small delay to coordinate with URL update
    }
  }

  // Function to close post dialog by removing URL parameter (integrates with URL-driven system)
  const closePostDialog = () => {
    const newParams = DeepLinkUtils.updateSearchParams(searchParams, {
      post: null,
    })
    setSearchParams(newParams, { replace: true })

    // Also clear any comment hash fragments
    if (window.location.hash.startsWith('#comment-') || window.location.hash === '#post-comments-section') {
      window.history.replaceState(null, '', window.location.pathname + window.location.search)
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "approved": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "rework": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const getInitials = (name?: string) => {
    if (!name) return '??'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const highlightHashtagsAndMentions = (text: string) => {
    return text
      .replace(/\n/g, '<br>')
      .replace(
        /(#[^\s]+|@[^\s]+)/g,
        '<span class="text-blue-600 hover:text-blue-700 font-medium cursor-pointer">$1</span>'
      )
  }

  // Convert API MediaItem to MediaCarousel format
  const convertMediaItems = (mediaItems?: MediaItem[]) => {
    if (!mediaItems || mediaItems.length === 0) return []

    return mediaItems.map((item, index) => ({
      id: `media-${index}`,
      type: (item.type === 'video' ? 'video' : 'image') as 'image' | 'video',
      url: item.url || '',
      alt: `Media ${index + 1}`
    }))
  }

  // Use server-provided permissions instead of client-side calculation
  const canEdit = post.can_edit

  const handleEdit = () => {
    if (onEdit && post.id) {
      onEdit(post.id)
    }
  }

  const handleDeleteConfirm = async () => {
    if (!post.id) return

    try {
      await deletePostMutation.mutateAsync({
        params: { path: { postId: post.id } }
      })
      
      toast.success(t(keys.collaborationHubs.posts.postDeleted))
      setDeleteDialogOpen(false)
    } catch (__error) {
      // Error handling for post deletion
      toast.error('Failed to delete post. Please try again.')
    }
  }

  return (
    <>
      <Card className={className}>
        {/* Post Header */}
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={undefined} />
                <AvatarFallback className="text-sm">
                  {getInitials(post.creator?.name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-semibold text-sm">{post.creator?.name || 'Unknown'}</p>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(post.review_status)} variant="outline">
                    {t(keys.collaborationHubs.posts.status[post.review_status as keyof typeof keys.collaborationHubs.posts.status])}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {formatDate(post.created_at)}
                  </span>
                </div>
              </div>
            </div>

            {canEdit && (
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    {t(keys.collaborationHubs.posts.actions.edit)}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setDeleteDialogOpen(true)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t(keys.collaborationHubs.posts.actions.delete)}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>

        {/* Post Content */}
        <CardContent className="space-y-4">
          {/* Media Display */}
          {post.media_uris && post.media_uris.length > 0 && (
            <MediaCarousel
              media={convertMediaItems(post.media_uris)}
              hubId={hubId}
              postId={post.id}
              className="w-full"
              enableRetry={true}
              onImageClick={() => openPostDialog()}
            />
          )}

          {/* Caption */}
          {post.caption && (
            <div 
              className="text-sm leading-relaxed"
              dangerouslySetInnerHTML={{ 
                __html: highlightHashtagsAndMentions(post.caption) 
              }}
            />
          )}

          {/* Enhanced Reviewers Summary */}
          {(post.assigned_reviewers?.length ?? 0) > 0 && (
            <PostReviewersSection
              reviewers={post.assigned_reviewers || []}
              compact={true}
            />
          )}

          {/* Post Review Form */}
          {post.can_review && (
            <PostReviewForm post={post} compact={true} />
          )}

          {/* Post Stats */}
          <div className="flex items-center gap-4 pt-2 border-t">
            <button
              onClick={() => openPostDialog(true)}
              className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              <MessageCircle className="h-4 w-4" />
              <span>{post.comment_count || 0} {t(keys.collaborationHubs.posts.comments.title)}</span>
            </button>
            {(post.media_uris?.length ?? 0) > 0 && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>{post.media_uris?.length ?? 0} media</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Post Dialog */}
      {post.id && (
        <PostDialog
          postId={post.id}
          hubId={hubId}
          open={postDialogOpen}
          onOpenChange={(open) => {
            if (!open) {
              closePostDialog()
            }
          }}
          scrollToComments={scrollToComments}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t(keys.collaborationHubs.posts.confirmDelete)}</AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.posts.deleteDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t(keys.common.cancel)}</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              disabled={deletePostMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deletePostMutation.isPending 
                ? t(keys.collaborationHubs.posts.deleting)
                : t(keys.collaborationHubs.posts.actions.delete)
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
