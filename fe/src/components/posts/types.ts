/**
 * Re-exports of OpenAPI-generated types for Posts components.
 *
 * Components should use these OpenAPI types directly for consistency with the backend API.
 * For component-specific form types, define them locally using zod schemas for better validation.
 * For complex UI state, define types within the component files where they're used.
 */

// Re-export OpenAPI generated types from decoupled API types
export type {
  PostResponse,
  PostListItemResponse,
  PostCreateRequest,
  PostUpdateRequest,
  PostCreator,
  MediaItem,
  PostListResponse,
  FileValidationResponse,
  ValidationDetails,
  // Post reviewer types
  PostReviewer,
  // Enums
  PostListItemResponseReview_status,
  PostListItemResponseMy_review_status,
} from '@/lib/types/api';
