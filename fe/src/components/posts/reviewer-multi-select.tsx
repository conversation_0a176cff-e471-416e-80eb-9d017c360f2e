import { useState } from 'react'
import { Check, ChevronsUpDown, User, Crown, Eye, Camera, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { useHubParticipants } from '@/hooks/posts'
import { useTranslations } from '@/lib/i18n/typed-translations'

interface ReviewerMultiSelectProps {
  hubId: number
  value: number[]
  onChange: (reviewerIds: number[]) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function ReviewerMultiSelect({
  hubId,
  value = [],
  onChange,
  placeholder,
  disabled = false,
  className
}: ReviewerMultiSelectProps) {
  const { t, keys } = useTranslations()
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  const { data: participantsData, isLoading } = useHubParticipants(hubId, undefined, {
    enabled: !!hubId && !disabled
  })

  // Extract participants array from the response
  const participants = participantsData || []

  // Filter participants based on search query
  const filteredParticipants = participants.filter(participant =>
    participant.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    participant.email?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const selectedParticipants = participants.filter(participant =>
    participant.id && value.includes(participant.id)
  )

  const handleSelect = (participantId: number) => {
    const newValue = value.includes(participantId)
      ? value.filter(id => id !== participantId)
      : [...value, participantId]
    
    onChange(newValue)
  }

  const handleRemove = (participantId: number) => {
    onChange(value.filter(id => id !== participantId))
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-3 w-3" />
      case 'reviewer':
        return <Eye className="h-3 w-3" />
      case 'reviewer_creator':
        return <Camera className="h-3 w-3" />
      default:
        return <User className="h-3 w-3" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return t(keys.collaborationHubs.roles.admin)
      case 'reviewer':
        return t(keys.collaborationHubs.roles.reviewer)
      case 'reviewer_creator':
        return t(keys.collaborationHubs.roles.reviewer_creator)
      case 'content_creator':
        return t(keys.collaborationHubs.roles.content_creator)
      default:
        return role
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'default'
      case 'reviewer':
        return 'secondary'
      case 'reviewer_creator':
        return 'outline'
      default:
        return 'outline'
    }
  }

  return (
    <div className={cn("space-y-2 w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger>
          <Button
            type="button"
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between text-left font-normal",
              !selectedParticipants.length && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            {selectedParticipants.length > 0 ? (
              <span>
                {selectedParticipants.length} {selectedParticipants.length === 1
                  ? t(keys.ui.reviewerMultiSelect.reviewer)
                  : t(keys.ui.reviewerMultiSelect.reviewers)} {t(keys.ui.reviewerMultiSelect.reviewersSelected)}
              </span>
            ) : (
              placeholder || t(keys.ui.reviewerMultiSelect.selectReviewers)
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0" align="start">
          <Command>
            <CommandInput
              placeholder={t(keys.ui.reviewerMultiSelect.searchReviewers)}
              value={searchQuery}
              onValueChange={setSearchQuery}
              className="h-9"
            />
            <CommandList>
              {isLoading ? (
                <CommandEmpty>{t(keys.ui.reviewerMultiSelect.loadingReviewers)}</CommandEmpty>
              ) : filteredParticipants.length === 0 ? (
                <CommandEmpty>
                  {searchQuery ? t(keys.ui.reviewerMultiSelect.noReviewersFound) : t(keys.ui.reviewerMultiSelect.noReviewersAvailable)}
                </CommandEmpty>
              ) : (
                <CommandGroup>
                  <ScrollArea className="max-h-64">
                    {filteredParticipants.map((participant) => (
                      <CommandItem
                        key={participant.id}
                        value={participant.id?.toString()}
                        onSelect={() => participant.id && handleSelect(participant.id)}
                        className="flex items-center gap-3 p-3"
                      >
                        <Check
                          className={cn(
                            "h-4 w-4",
                            participant.id && value.includes(participant.id) 
                              ? "opacity-100" 
                              : "opacity-0"
                          )}
                        />
                        
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={undefined} />
                          <AvatarFallback className="text-xs">
                            {participant.name ? getInitials(participant.name) : 'U'}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <p className="font-medium text-sm truncate">
                              {participant.name || 'Unknown'}
                            </p>
                            {participant.role && (
                              <div className="flex items-center gap-1">
                                {getRoleIcon(participant.role)}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <p className="text-xs text-muted-foreground truncate">
                              {participant.email}
                            </p>
                            {participant.role && (
                              <Badge 
                                variant={getRoleBadgeVariant(participant.role)}
                                className="text-xs px-1 py-0"
                              >
                                {getRoleLabel(participant.role)}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </CommandItem>
                    ))}
                  </ScrollArea>
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected Reviewers Display */}
      {selectedParticipants.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedParticipants.map((participant) => (
            <Badge
              key={participant.id}
              variant="secondary"
              className="flex items-center gap-2 px-2 py-1"
            >
              <Avatar className="h-4 w-4">
                <AvatarImage src={undefined} />
                <AvatarFallback className="text-xs">
                  {participant.name ? getInitials(participant.name) : 'U'}
                </AvatarFallback>
              </Avatar>
              <span className="text-xs">
                {participant.name || 'Unknown'}
              </span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => participant.id && handleRemove(participant.id)}
                disabled={disabled}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
