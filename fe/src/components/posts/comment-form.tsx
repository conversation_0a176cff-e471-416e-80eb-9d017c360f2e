import { UnifiedInput } from "@/components/ui/unified-input"

interface CommentFormProps {
  postId: number
  hubId: number
  onCommentCreated?: () => void
  className?: string
}

export function CommentForm({ postId, hubId, onCommentCreated, className }: CommentFormProps) {
  return (
    <div className={className}>
      <UnifiedInput
        variant="comment"
        hubId={hubId}
        postId={postId}
        onSuccess={onCommentCreated}
      />
    </div>
  )
}
