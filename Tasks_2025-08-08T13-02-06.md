[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:# Task 1 — Notification Preferences DESCRIPTION:# Task 1 — Notification Preferences

## Purpose
Allow users to opt in or out of specific notification types for each delivery channel (in-app, email).

## Requirements
- Internal customers: Can toggle both `IN_APP` and `EMAIL`.
- External customers: Can toggle only `EMAIL`.
- Default: All notifications enabled on signup.

## Database
**Table:** `notification_preference`
| Column     | Type   | Notes |
|------------|--------|-------|
| id         | UUID   | PK    |
| user_id    | UUID   | FK to `user` |
| type       | ENUM   | One of: `INVITE_TO_HUB`, `ASSIGNED_AS_REVIEWER`, `POST_REVIEWED`, `COMMENT_ADDED`, `COMMENT_MENTION`, `CHAT_MENTION`, `CHAT_ADDED` |
| channel    | ENUM   | `IN_APP`, `EMAIL` |
| enabled    | BOOL   | Default `true` |

**Unique index**: `(user_id, type, channel)`

## API
- `GET /notification-preferences`
- `PUT /notification-preferences`

## UI
- Settings page section with per-type, per-channel toggles.
- Hide in-app toggle for external customers.

Ask me to regenrate JOOQ models.
-[ ] NAME:# Task 2 — Event-to-Notification Mapping DESCRIPTION:# Task 2 — Event-to-Notification Mapping

## Purpose
Map domain events to notification objects with correct recipients and channels.

## Flow
1. Domain event occurs (e.g., "Post Reviewed").
2. Notification service receives event.
3. Find recipients.
4. Filter by `notification_preference`.
5. Create `notification` records.
6. Queue for delivery.

## Mapping Table
| Event                  | Type Enum            | In-App | Email | Batch? |
|------------------------|----------------------|--------|-------|--------|
| Invite to Hub          | INVITE_TO_HUB        | ✅ Immediate | ✅ Immediate | ❌ |
| Assigned as Reviewer   | ASSIGNED_AS_REVIEWER | ✅ Immediate | ✅ Immediate | ❌ |
| Post Reviewed          | POST_REVIEWED        | ✅ Immediate | ✅ Immediate | ❌ |
| Comment Added          | COMMENT_ADDED        | ✅ Immediate | ✅ 10-min batch | ✅ |
| Comment Mention        | COMMENT_MENTION      | ✅ Immediate | ✅ Immediate | ❌ |
| Chat Mention           | CHAT_MENTION         | ✅ Immediate | ✅ Immediate | ❌ |
| Chat Added             | CHAT_ADDED           | ✅ Immediate | ✅ Immediate | ❌ |

## Notes
- Batching applies only to `COMMENT_ADDED`.
- Mentions are always immediate.
-[ ] NAME:Task 3 — Notification Storage DESCRIPTION:# Task 3 — Notification Storage  ## Purpose Persist notifications for in-app feed and deduplication.  ## Database **Table:** `notification` | Column      | Type   | Notes | |-------------|--------|-------| | id          | UUID   | PK | | user_id     | UUID   | FK to `user` | | type        | ENUM   | Same as in preferences | | title       | TEXT   | Short text | | body        | TEXT   | Main message | | link        | TEXT   | Relative URL to target | | channels    | JSONB  | Array of `IN_APP`, `EMAIL` | | status      | ENUM   | `PENDING`, `SENT`, `READ` | | created_at  | TIMESTAMP | Default now |  ## API - `GET /notifications` → Returns latest unread + read. - `PATCH /notifications/{id}/read` → Marks as read.  ## UI - In-app dropdown or page listing. - Read/unread states.
-[ ] NAME:Task 4 — Email Delivery & Flood Control DESCRIPTION:# Task 4 — Email Delivery & Flood Control

## Purpose
Send email notifications without spamming users.

## Rules
- In-app: always immediate.
- Email: batch `COMMENT_ADDED` within 10 min.
- Mentions and direct actions: immediate.
- External customers: only email channel.
- No duplicate sends within same batch.

## Database
**Table:** `notification_delivery`
| Column          | Type   | Notes |
|-----------------|--------|-------|
| id              | UUID   | PK |
| notification_id | UUID   | FK to `notification` |
| channel         | ENUM   | `EMAIL` |
| sent_at         | TIMESTAMP | Nullable until sent |

## Processing
1. Background job runs every 5–10 min.
2. Groups pending notifications by user + batch rules.
3. Sends via Amazon SES (or chosen provider).
4. Marks as SENT.

## Email Templates
- One SES template per type.
- Batching template for multiple comments:
  - `"3 new comments on your post 'Title'"`
-[ ] NAME:# Task 5 — Internal vs External Handling DESCRIPTION:# Task 5 — Internal vs External Handling

## Purpose
Different delivery channels based on user type.

## Rules
- Internal:
  - Can receive in-app + email (if enabled).
- External:
  - Email only.
  - Preferences page hides in-app toggles.

## Implementation
- `user` table contains `customer_type` ENUM: `INTERNAL`, `EXTERNAL`.
- Notification service filters available channels before storage.

## Edge Cases
- If an external customer somehow has `IN_APP` enabled in DB → ignore and send only email.
-[ ] NAME: DESCRIPTION: