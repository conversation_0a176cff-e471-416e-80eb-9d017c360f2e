HELP.md
be/.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### Frontend (Node.js/React) ###
fe/node_modules/
fe/dist/
fe/build/
fe/coverage/
fe/.env
fe/.env.local
fe/.env.development.local
fe/.env.test.local
fe/.env.production.local
fe/npm-debug.log*
fe/yarn-debug.log*
fe/yarn-error.log*
fe/pnpm-debug.log*
fe/.DS_Store
fe/Thumbs.db
fe/*.tsbuildinfo
